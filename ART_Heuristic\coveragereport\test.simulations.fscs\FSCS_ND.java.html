<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>FSCS_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.fscs</a> &gt; <span class="el_source">FSCS_ND.java</span></div><h1>FSCS_ND.java</h1><pre class="source lang-java linenums">package test.simulations.fscs;
/*
 * n维实现,包含1维2维等
 * */

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import test.simulations.rrt.RRT_ND;
import test.simulations.rt.RT_ND;
import util.data.ZeroOneCreator;

public class FSCS_ND extends ART {
	public static void main(String[] args) {
<span class="nc" id="L19">		testFm();</span>
		//testTCTime(2,5000);
		//testEm(1, 0.005);
<span class="nc" id="L22">	}</span>
<span class="nc" id="L23">	private int s = 10;// 表示候选集的数量初始值为10</span>
<span class="nc" id="L24">	private ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>

	public FSCS_ND(double[] min, double[] max, int s, FailurePattern pattern, Random random) {
<span class="nc" id="L27">		super(min, max, random, pattern);</span>
<span class="nc" id="L28">		this.s = s;</span>
<span class="nc" id="L29">	}</span>

	public double calTwoPointDistance(NPoint p1, NPoint p2) {
<span class="nc" id="L32">		double[] p1xn = p1.getXn();</span>
<span class="nc" id="L33">		double[] p2xn = p2.getXn();</span>
<span class="nc" id="L34">		double distance = 0.0;</span>
<span class="nc bnc" id="L35" title="All 2 branches missed.">		for (int i = 0; i &lt; p1xn.length; i++) {</span>
<span class="nc" id="L36">			distance += Math.pow((p2xn[i] - p1xn[i]), 2);</span>
		}
<span class="nc" id="L38">		distance = Math.sqrt(distance);</span>
<span class="nc" id="L39">		return distance;</span>
	}
	
	@Override
	public NPoint generateNextTC() {
<span class="nc" id="L44">		NPoint p = null;</span>
<span class="nc bnc" id="L45" title="All 2 branches missed.">		if (tests.size() == 0) {</span>
<span class="nc" id="L46">			p = randomCreator.randomPoint();</span>
<span class="nc" id="L47">			tests.add(p);</span>
<span class="nc" id="L48">		} else {</span>
<span class="nc" id="L49">			p = new NPoint();</span>
<span class="nc" id="L50">			double maxDistance = -1.0;</span>
<span class="nc" id="L51">			NPoint bestCandidate = null;</span>
<span class="nc bnc" id="L52" title="All 2 branches missed.">			for (int i = 0; i &lt; s; i++) {</span>
<span class="nc" id="L53">				NPoint candidate = randomCreator.randomPoint();</span>
				// 计算两个点的距离
<span class="nc" id="L55">				double minDistance = Double.MAX_VALUE;</span>

<span class="nc bnc" id="L57" title="All 2 branches missed.">				for (int j = 0; j &lt; this.tests.size(); j++) {</span>
<span class="nc" id="L58">					double tempDistance = calTwoPointDistance(candidate, tests.get(j));</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">					if (tempDistance &lt; minDistance) {</span>
<span class="nc" id="L60">						minDistance = tempDistance;</span>
					}
				}
<span class="nc bnc" id="L63" title="All 2 branches missed.">				if (maxDistance &lt; minDistance) {</span>
<span class="nc" id="L64">					maxDistance = minDistance;</span>
<span class="nc" id="L65">					bestCandidate = candidate;</span>
				}
			}
<span class="nc" id="L68">			p = bestCandidate;</span>
<span class="nc" id="L69">			tests.add(p);</span>
		}
<span class="nc" id="L71">		return p;</span>
	}

	
	public void time() {
<span class="nc" id="L76">		int count = 0;</span>
<span class="nc" id="L77">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">		while (count &lt;= tcCount) {</span>
<span class="nc" id="L79">			count++;</span>
<span class="nc" id="L80">			tests.add(p);</span>
<span class="nc" id="L81">			p = new NPoint();</span>
<span class="nc" id="L82">			double maxDistance = -1.0;</span>
<span class="nc" id="L83">			NPoint bestCandidate = null;</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">			for (int i = 0; i &lt; s; i++) {</span>
<span class="nc" id="L85">				NPoint candidate =randomCreator.randomPoint();</span>
				// 计算两个点的距离
<span class="nc" id="L87">				double minDistance = Double.MAX_VALUE;</span>

<span class="nc bnc" id="L89" title="All 2 branches missed.">				for (int j = 0; j &lt; this.tests.size(); j++) {</span>
<span class="nc" id="L90">					double tempDistance = calTwoPointDistance(candidate, tests.get(j));</span>
<span class="nc bnc" id="L91" title="All 2 branches missed.">					if (tempDistance &lt; minDistance) {</span>
<span class="nc" id="L92">						minDistance = tempDistance;</span>
					}
				}
<span class="nc bnc" id="L95" title="All 2 branches missed.">				if (maxDistance &lt; minDistance) {</span>
<span class="nc" id="L96">					maxDistance = minDistance;</span>
<span class="nc" id="L97">					bestCandidate = candidate;</span>
				}
			}
<span class="nc" id="L100">			p = null;</span>
<span class="nc" id="L101">			p = bestCandidate;</span>
		}
<span class="nc" id="L103">	}</span>
	public static double testFm() {
<span class="nc" id="L105">		int d = 2;</span>
<span class="nc" id="L106">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L107">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L108">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L110">		int times = 2000;</span>

<span class="nc" id="L112">		int s=10;</span>
		
<span class="nc" id="L114">		int temp = 0;</span>
<span class="nc" id="L115">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L116">		failurePattern.fail_rate = 0.005;</span>
<span class="nc" id="L117">		long sums = 0;</span>
<span class="nc" id="L118">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L120">			FSCS_ND rt = new FSCS_ND(min, max, s, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L121">			temp = rt.run();</span>
<span class="nc" id="L122">			sums += temp;</span>
		}
<span class="nc" id="L124">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L125">		double fm = sums / (double) times;</span>
<span class="nc" id="L126">		System.out.println(&quot;fm:&quot; + fm + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
<span class="nc" id="L127">		return fm;</span>
	}
	
	
	public static double[] testEm(int dimension, double failrate) {
<span class="nc" id="L132">		int d = dimension;</span>
<span class="nc" id="L133">		int emTime = 6;</span>
<span class="nc" id="L134">		double result[] = new double[emTime];</span>
<span class="nc" id="L135">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L136">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L137">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L139">		int times = 2000;</span>

<span class="nc" id="L141">		int temp = 0;</span>
<span class="nc" id="L142">		int kk = 10;</span>
<span class="nc" id="L143">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L144">		failurePattern.fail_rate = failrate;</span>
<span class="nc bnc" id="L145" title="All 2 branches missed.">		for (int k = 0; k &lt; emTime; k++) {</span>
<span class="nc" id="L146">			long sums = 0;</span>
<span class="nc" id="L147">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L148" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L149">				FSCS_ND rt = new FSCS_ND(min, max, kk, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L150">				rt.emCount = (k + 1) * 500;</span>
<span class="nc" id="L151">				temp = rt.em();</span>
<span class="nc" id="L152">				sums += temp;</span>
			}
<span class="nc" id="L154">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L155">			double em = sums / (double) times;</span>
<span class="nc" id="L156">			result[k] = em;</span>
<span class="nc" id="L157">			System.out.println(&quot;em:&quot; + em + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
		}
<span class="nc" id="L159">		System.out.println();</span>
<span class="nc" id="L160">		return result;</span>
	}
	public static double testTCTime(int d, int tcCount) {
<span class="nc" id="L163">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L164">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L165">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L167">		int times = 1;</span>

<span class="nc" id="L169">		int s = 10;</span>
<span class="nc" id="L170">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L171">		failurePattern.fail_rate = 0.001;</span>
<span class="nc" id="L172">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L173" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L174">			FSCS_ND rt = new FSCS_ND(min, max, s, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L175">			rt.tcCount = tcCount;</span>
<span class="nc" id="L176">			rt.time2();</span>
		}
<span class="nc" id="L178">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L179">		System.out.println((endTime - startTime) / (double) times);</span>
<span class="nc" id="L180">		return ((endTime - startTime) / (double) times);</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>