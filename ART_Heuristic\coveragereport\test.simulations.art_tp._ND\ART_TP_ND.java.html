<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._ND</a> &gt; <span class="el_source">ART_TP_ND.java</span></div><h1>ART_TP_ND.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._ND;
/*
 * 原生的ART_TP，不包括ART_Etp，ART_Btp，ART_RPtp
 * 
 * **/

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.TPInfo2;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tpp.ART_TPP;
import util.X3;
import util.data.ZeroOneCreator;

/*
 * 测试
 * 生成二维的ART_TP，用来探索n维的测试用例
 * 其中使用二维特殊化的，均有二维特殊化提醒
 * */
public class ART_TP_ND extends ART {
	public static void main(String[] args) {
		// start,end 表示边界，from to表示Anbn int d = 2; double[] min =
		 //testTCTime(2,10000);
		//testEm(1, 0.01);
<span class="nc" id="L32">		testFm();</span>
<span class="nc" id="L33">	}</span>

<span class="nc" id="L35">	ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L37">	ArrayList&lt;TPInfo2&gt; regions = new ArrayList&lt;&gt;();</span>

	double C;// 常数

	public ART_TP_ND(double[] min, double[] max, FailurePattern pattern, Random random) {
<span class="nc" id="L42">		super(min, max, random, pattern);</span>
<span class="nc" id="L43">	}</span>

	public void addFromAndTo(TPInfo2 region) {
<span class="nc" id="L46">		double[] start = region.start.getXn();</span>
<span class="nc" id="L47">		double[] end = region.end.getXn();</span>
<span class="nc" id="L48">		double[] fromarr = new double[this.dimension];</span>
<span class="nc" id="L49">		double[] toarr = new double[this.dimension];</span>
<span class="nc bnc" id="L50" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L51">			double from = 0.0;</span>
<span class="nc bnc" id="L52" title="All 2 branches missed.">			if (start[i] == min[i]) {</span>
<span class="nc" id="L53">				from = random.nextDouble() * (end[i] - min[i]) + (2 * min[i] - end[i]);</span>
<span class="nc" id="L54">			} else {</span>
<span class="nc" id="L55">				from = start[i];</span>
			}
<span class="nc" id="L57">			fromarr[i] = from;</span>
<span class="nc" id="L58">			double to = 0.0;</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">			if (end[i] == max[i]) {</span>
<span class="nc" id="L60">				to = random.nextDouble() * (max[i] - end[i]) + max[i];</span>
<span class="nc" id="L61">			} else {</span>
<span class="nc" id="L62">				to = end[i];</span>
			}
<span class="nc" id="L64">			toarr[i] = to;</span>
		}
<span class="nc" id="L66">		region.from = new NPoint(fromarr);</span>
<span class="nc" id="L67">		region.to = new NPoint(toarr);</span>
<span class="nc" id="L68">	}</span>

	public void addRegions(double[] min, double[] max, double[] xn) {
<span class="nc" id="L71">		int count = (int) Math.pow(2, this.dimension);</span>
<span class="nc" id="L72">		ArrayList&lt;double[]&gt; lists = new ArrayList&lt;&gt;(this.dimension);</span>
<span class="nc bnc" id="L73" title="All 2 branches missed.">		for (int i = 0; i &lt; min.length; i++) {</span>
<span class="nc" id="L74">			lists.add(new double[] { min[i], xn[i] });</span>
		}
<span class="nc" id="L76">		ArrayList&lt;double[]&gt; lists2 = new ArrayList&lt;&gt;(this.dimension);</span>
<span class="nc bnc" id="L77" title="All 2 branches missed.">		for (int i = 0; i &lt; min.length; i++) {</span>
<span class="nc" id="L78">			lists2.add(new double[] { xn[i], max[i] });</span>
		}
<span class="nc" id="L80">		ArrayList&lt;double[]&gt; result1 = new ArrayList&lt;&gt;(count);</span>
<span class="nc" id="L81">		addRegionsRec(this.dimension, 0, new ArrayList&lt;Double&gt;(), lists, result1);</span>

<span class="nc" id="L83">		ArrayList&lt;double[]&gt; result2 = new ArrayList&lt;&gt;(count);</span>
<span class="nc" id="L84">		addRegionsRec(this.dimension, 0, new ArrayList&lt;Double&gt;(), lists2, result2);</span>

		// for( int i=0;i&lt;result1.size();i++){
		// System.out.println(Arrays.toString(result1.get(i))+&quot;
		// &quot;+Arrays.toString(result2.get(i)));
		// }
<span class="nc bnc" id="L90" title="All 2 branches missed.">		for (int i = 0; i &lt; result1.size(); i++) {</span>
<span class="nc" id="L91">			TPInfo2 info = new TPInfo2();</span>
<span class="nc" id="L92">			info.start = new NPoint(result1.get(i));</span>
<span class="nc" id="L93">			info.end = new NPoint(result2.get(i));</span>
<span class="nc" id="L94">			addFromAndTo(info);</span>
<span class="nc" id="L95">			this.regions.add(info);</span>
		}
<span class="nc" id="L97">	}</span>

	public void addRegionsRec(int n, int k, List&lt;Double&gt; list, List&lt;double[]&gt; lists, ArrayList&lt;double[]&gt; result1) {
<span class="nc bnc" id="L100" title="All 2 branches missed.">		if (list.size() == n) {</span>
			// double[] temp=list.toArray();
<span class="nc" id="L102">			double[] temp = new double[n];</span>
<span class="nc bnc" id="L103" title="All 2 branches missed.">			for (int i = 0; i &lt; temp.length; i++) {</span>
<span class="nc" id="L104">				temp[i] = list.get(i);</span>
			}
<span class="nc" id="L106">			result1.add(temp);</span>
<span class="nc" id="L107">		} else {</span>
<span class="nc bnc" id="L108" title="All 2 branches missed.">			for (int i = 0; i &lt; 2; i++) {</span>
<span class="nc" id="L109">				List&lt;Double&gt; list2 = new ArrayList&lt;Double&gt;(list);</span>
<span class="nc" id="L110">				list2.add(lists.get(k)[i]);</span>
<span class="nc" id="L111">				addRegionsRec(n, ++k, list2, lists, result1);</span>
<span class="nc" id="L112">				k--;</span>
			}
		}
<span class="nc" id="L115">	}</span>

	public double calEachIntEC(double s, double e, double f, double t) {
		// int(x-f)*(t-x) s,t
<span class="nc" id="L119">		return (-1.0 / 6.0) * (e - s)</span>
<span class="nc" id="L120">				* (e * (-3 * f + 2 * s - 3 * t) - 3 * f * s + 6 * f * t + 2 * s * s - 3 * s * t + 2 * e * e);</span>
	}

	public double calEachRegion() {
<span class="nc" id="L124">		double tempC = 0.0;</span>
		// System.out.println(&quot;each region cdf:&quot;);
<span class="nc bnc" id="L126" title="All 2 branches missed.">		for (int i = 0; i &lt; regions.size(); i++) {</span>
			// 二维特殊化
			// NRectRegion temp=regions.get(i);
<span class="nc" id="L129">			double[] start = regions.get(i).start.getXn();</span>
<span class="nc" id="L130">			double[] end = regions.get(i).end.getXn();</span>
<span class="nc" id="L131">			double[] from = regions.get(i).from.getXn();</span>
<span class="nc" id="L132">			double[] to = regions.get(i).to.getXn();</span>
<span class="nc" id="L133">			double probality = 1.0;</span>
<span class="nc" id="L134">			regions.get(i).eachProbality = new double[this.dimension];</span>
<span class="nc bnc" id="L135" title="All 2 branches missed.">			for (int j = 0; j &lt; start.length; j++) {</span>
<span class="nc" id="L136">				double temp = calEachIntEC(start[j], end[j], from[j], to[j]);</span>
<span class="nc" id="L137">				probality *= temp;</span>
<span class="nc" id="L138">				regions.get(i).eachProbality[j] = temp;</span>
			}
			// double b = calEachIntEC(start.getXn()[1], end.getXn()[1],
			// from.getXn()[1], to.getXn()[1]);
<span class="nc" id="L142">			regions.get(i).probality = probality;</span>
			// regions.get(i).proa = a;
			// regions.get(i).prob = b;
<span class="nc" id="L145">			tempC += probality;</span>
			// System.out.println(&quot;int (x-&quot; + from.getXn()[0] + &quot;)*(&quot; +
			// to.getXn()[0] + &quot;-x&quot; + &quot;) from &quot; + start.getXn()[0]
			// + &quot; to &quot; + end.getXn()[0]);
			// System.out.println(&quot;int (x-&quot; + from.getXn()[1] + &quot;)*(&quot; +
			// to.getXn()[1] + &quot;-x&quot; + &quot;) from &quot; + start.getXn()[1]
			// + &quot; to &quot; + end.getXn()[1]);
			// System.out.println(&quot;from:&quot;+(from1)+&quot;,&quot;+from2+&quot; to:&quot;+to1+&quot;,&quot;+to2);
			// System.out.println(&quot;start:&quot;+(start.getXn()[0])+&quot;,&quot;+start.getXn()[1]+&quot;
			// to:&quot;+end.getXn()[0]+&quot;,&quot;+end.getXn()[1]);

			// System.out.println(&quot;eachValue:&quot; + (a) + &quot;,&quot; + b + &quot; multi:&quot; + (a
			// * b));
			// System.out.println(&quot;*********&quot;);
		}
		// System.out.println(&quot;tempC:&quot; + tempC);
		// System.out.println(&quot;------------&quot;);
<span class="nc" id="L162">		return tempC;</span>
	}

	public double genNextEachDimension(double start, double end, double from, double to, double C, double aorb,
			double Pre, double T) {

		// System.out.println(&quot;cal next test case&quot;);
		// System.out.println(&quot;start:&quot;+start+&quot;,end:&quot;+end+&quot;,from:&quot;+from+&quot;,to:&quot;+to+&quot;,C:&quot;+C+&quot;,aorb:&quot;+aorb+&quot;,Pre:&quot;+Pre+&quot;,T:&quot;+T);
		// pre+c*(a|b)*int(start to x)((x-from)*(to-x))=T;
<span class="nc" id="L171">		double A = (-1.0 / 3.0) * C * aorb;</span>
<span class="nc" id="L172">		double B = 0.5 * (from + to) * (C) * (aorb);</span>
<span class="nc" id="L173">		double C1 = -from * to * C * aorb;</span>
<span class="nc" id="L174">		double D = C * aorb</span>
<span class="nc" id="L175">				* ((1.0 / 3.0) * (start * start * start) - 0.5 * (start * start) * (from + to) + from * to * start) - T</span>
<span class="nc" id="L176">				+ Pre;</span>
<span class="nc" id="L177">		double[] roots = X3.shengjinFormula(A, B, C1, D);</span>
		// System.out.println(&quot;roots:&quot;+Arrays.toString(roots));
<span class="nc" id="L179">		double next = -1.0;</span>
<span class="nc" id="L180">		boolean flag = false;</span>
<span class="nc bnc" id="L181" title="All 2 branches missed.">		for (int i = 0; i &lt; roots.length; i++) {</span>
<span class="nc bnc" id="L182" title="All 4 branches missed.">			if (roots[i] &gt; start &amp;&amp; roots[i] &lt; end) {</span>
<span class="nc" id="L183">				flag = true;</span>
<span class="nc" id="L184">				next = roots[i];</span>
<span class="nc" id="L185">				break;</span>
			}
		}
<span class="nc bnc" id="L188" title="All 2 branches missed.">		if (!flag) {</span>
			// System.out.println(&quot;x3 error!&quot;);
			// next=genNextEachDimension(start, end, from, to, C1, aorb, Pre,
			// next);
<span class="nc" id="L192">			next = random.nextDouble();</span>
			// return Double.MIN_VALUE;
		}
<span class="nc" id="L195">		return next;</span>
	}

	public NPoint genNextTestCase(int index, double PreIntegral) {

<span class="nc" id="L200">		double[] start = this.regions.get(index).start.getXn();</span>
<span class="nc" id="L201">		double[] end = this.regions.get(index).end.getXn();</span>
<span class="nc" id="L202">		double[] from = this.regions.get(index).from.getXn();</span>
<span class="nc" id="L203">		double[] to = this.regions.get(index).to.getXn();</span>
<span class="nc" id="L204">		double[] result = new double[this.dimension];</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc" id="L206">			double T = random.nextDouble() * (regions.get(index).probality * C) + PreIntegral;</span>
<span class="nc" id="L207">			double temp = genNextEachDimension(start[i], end[i], from[i], to[i], C,</span>
<span class="nc" id="L208">					this.regions.get(index).probality / this.regions.get(index).eachProbality[i], PreIntegral, T);</span>
<span class="nc" id="L209">			result[i] = temp;</span>
		}
<span class="nc" id="L211">		NPoint p = new NPoint(result);</span>
<span class="nc" id="L212">		return p;</span>
	}

	public void updateRegions(NPoint p, int index) {
<span class="nc bnc" id="L216" title="All 2 branches missed.">		if (regions.size() == 0) {</span>
<span class="nc" id="L217">			addRegions(this.min, this.max, p.getXn());</span>
<span class="nc" id="L218">		} else {</span>
<span class="nc" id="L219">			TPInfo2 info = regions.remove(index);</span>
<span class="nc" id="L220">			addRegions(info.start.getXn(), info.end.getXn(), p.getXn());</span>
		}
<span class="nc" id="L222">	}</span>
	public boolean isInRegion(NPoint start1, NPoint end1, NPoint p) {
<span class="nc" id="L224">		boolean flag = true;</span>
<span class="nc" id="L225">		double[] pxn = p.getXn();</span>
<span class="nc" id="L226">		double[] start = start1.getXn();</span>
<span class="nc" id="L227">		double[] end = end1.getXn();</span>
<span class="nc bnc" id="L228" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc bnc" id="L229" title="All 4 branches missed.">			if (pxn[i] &lt; start[i] || pxn[i] &gt; end[i]) {</span>
<span class="nc" id="L230">				flag = false;</span>
			}
		}
<span class="nc" id="L233">		return flag;</span>
	}
	@Override
	public NPoint generateNextTC() {
<span class="nc" id="L237">		NPoint p=null;</span>
		
<span class="nc" id="L239">		int temp=0;</span>
<span class="nc bnc" id="L240" title="All 2 branches missed.">		if(tests.size()==0){</span>
<span class="nc" id="L241">			p=randomCreator.randomPoint();</span>
<span class="nc" id="L242">			tests.add(p);</span>
<span class="nc" id="L243">			updateRegions(p, temp);</span>
<span class="nc" id="L244">		}else{</span>
<span class="nc" id="L245">			C = 1.0 / calEachRegion();</span>
			// 确定在哪一个区域
<span class="nc" id="L247">			double T = random.nextDouble();</span>
<span class="nc" id="L248">			double PreIntegral = 0.0;</span>
<span class="nc" id="L249">			double SumIntegral = 0.0;// 积分值总和</span>
			// int temp = 0;// 落在哪个区间
<span class="nc" id="L251">			temp = 0;</span>
<span class="nc bnc" id="L252" title="All 2 branches missed.">			for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc bnc" id="L253" title="All 2 branches missed.">				if (SumIntegral &lt; T) {</span>
<span class="nc" id="L254">					PreIntegral = SumIntegral;</span>
<span class="nc" id="L255">					temp = i;</span>
				}
<span class="nc" id="L257">				SumIntegral += regions.get(i).probality * C;</span>
			}

<span class="nc" id="L260">			p = null;</span>
<span class="nc" id="L261">			p = genNextTestCase(temp, PreIntegral);</span>
<span class="nc" id="L262">			tests.add(p);</span>
<span class="nc" id="L263">			updateRegions(p, temp);</span>
		}
<span class="nc" id="L265">		return p;</span>
	}
	
	public void time() {
<span class="nc" id="L269">		int count = 0;</span>
<span class="nc" id="L270">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc" id="L271">		int temp = 0;</span>
<span class="nc bnc" id="L272" title="All 2 branches missed.">		while (count &lt;= tcCount) {</span>
<span class="nc" id="L273">			count++;</span>
<span class="nc" id="L274">			tests.add(p);</span>
<span class="nc" id="L275">			updateRegions(p, temp);</span>
<span class="nc" id="L276">			C = 1.0 / calEachRegion();</span>
			// 确定在哪一个区域
<span class="nc" id="L278">			double T = random.nextDouble();</span>
<span class="nc" id="L279">			double PreIntegral = 0.0;</span>
<span class="nc" id="L280">			double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L281">			temp = 0;</span>
<span class="nc bnc" id="L282" title="All 2 branches missed.">			for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc bnc" id="L283" title="All 2 branches missed.">				if (SumIntegral &lt; T) {</span>
<span class="nc" id="L284">					PreIntegral = SumIntegral;</span>
<span class="nc" id="L285">					temp = i;</span>
				}
<span class="nc" id="L287">				SumIntegral += regions.get(i).probality * C;</span>
			}

<span class="nc" id="L290">			p = null;</span>
<span class="nc" id="L291">			p = genNextTestCase(temp, PreIntegral);</span>
		}
<span class="nc" id="L293">	}</span>
	public static double testFm() {
<span class="nc" id="L295">		int d = 2;</span>
<span class="nc" id="L296">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L297">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L298">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L300">		int times = 2000;</span>

<span class="nc" id="L302">		int temp = 0;</span>
<span class="nc" id="L303">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L304">		failurePattern.fail_rate = 0.002;</span>
<span class="nc" id="L305">		long sums = 0;</span>
<span class="nc" id="L306">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L307" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L308">			ART_TP_ND rt = new ART_TP_ND(min, max, failurePattern, new Random(i * 3 + 3));</span>
<span class="nc" id="L309">			temp = rt.run();</span>
<span class="nc" id="L310">			sums += temp;</span>
		}
<span class="nc" id="L312">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L313">		double fm = sums / (double) times;</span>
<span class="nc" id="L314">		System.out.println(&quot;fm:&quot; + fm + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
<span class="nc" id="L315">		return fm;</span>
	}
	public static double testTCTime(int d, int tcCount) {
<span class="nc" id="L318">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L319">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L320">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L322">		int times = 1;</span>

<span class="nc" id="L324">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L325">		failurePattern.fail_rate = 0.001;</span>
<span class="nc" id="L326">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L327" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L328">			ART_TP_ND rt = new ART_TP_ND(min, max, failurePattern, new Random(i * 3 + 3));</span>
<span class="nc" id="L329">			rt.tcCount = tcCount;</span>
<span class="nc" id="L330">			rt.time2();</span>
		}
<span class="nc" id="L332">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L333">		System.out.println((endTime - startTime) / (double) times);</span>
<span class="nc" id="L334">		return ((endTime - startTime) / (double) times);</span>
	}

	public static double[] testEm(int dimension, double failrate) {
<span class="nc" id="L338">		int d = dimension;</span>
<span class="nc" id="L339">		int emTime = 6;</span>
<span class="nc" id="L340">		double[] result = new double[emTime];</span>
<span class="nc" id="L341">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L342">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L343">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L345">		int times = 2000;</span>

<span class="nc" id="L347">		int temp = 0;</span>
<span class="nc" id="L348">		int kk = 10;</span>
<span class="nc" id="L349">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L350">		failurePattern.fail_rate = failrate;</span>
<span class="nc bnc" id="L351" title="All 2 branches missed.">		for (int k = 0; k &lt; emTime; k++) {</span>
<span class="nc" id="L352">			long sums = 0;</span>
<span class="nc" id="L353">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L354" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L355">				ART_TP_ND rt = new ART_TP_ND(min, max, failurePattern, new Random(i * 3 + 3));</span>
<span class="nc" id="L356">				rt.emCount = (k + 1) * 500;</span>
<span class="nc" id="L357">				temp = rt.em();</span>
<span class="nc" id="L358">				sums += temp;</span>
			}
<span class="nc" id="L360">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L361">			double em = sums / (double) times;</span>
<span class="nc" id="L362">			result[k] = em;</span>
<span class="nc" id="L363">			System.out.println(&quot;em:&quot; + em + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
		}
<span class="nc" id="L365">		System.out.println();</span>
<span class="nc" id="L366">		return result;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>