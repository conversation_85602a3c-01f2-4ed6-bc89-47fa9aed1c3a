<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>erfcc1.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">erfcc1.java</span></div><h1>erfcc1.java</h1><pre class="source lang-java linenums">package tested;

<span class="nc" id="L3">public class erfcc1 {</span>
	public double wrong(double x) {
		double t, z, ans;

<span class="nc" id="L7">		z = Math.abs(x);</span>
<span class="nc" id="L8">		t = 1.0 / (1.0 + 0.5 * z);</span>
<span class="nc" id="L9">		ans = t * Math.exp(-z * z - 1.26551223 + t * (1.00002368 + t * (0.37409196 + t * (0.09678418 +</span>
		/* ERROR */
		/* t*(-0.18628806+t*(0.27886807+t*(-1.13520398+t*(1.48851587+ */
<span class="nc" id="L12">				t * (-0.18628806 + t * (0.27886807 + z * (-1.13520398 + t * (1.48851587 +</span>
				/* ERROR */
				/* t*(-0.82215223+t*0.17087277))))))))); */
<span class="nc" id="L15">						t * (-0.82215223 - t * 0.17087277)))))))));</span>
		/* ERROR */
		/* return x &gt;= 0.0 ? ans : 2.0-ans; */
<span class="nc bnc" id="L18" title="All 2 branches missed.">		return x &gt; 0.1 ? ans : 2.0 - ans;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>