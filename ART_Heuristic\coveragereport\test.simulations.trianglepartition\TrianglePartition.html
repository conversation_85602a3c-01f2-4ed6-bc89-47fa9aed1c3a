<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>TrianglePartition</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.trianglepartition</a> &gt; <span class="el_class">TrianglePartition</span></div><h1>TrianglePartition</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,247 of 1,247</td><td class="ctr2">0%</td><td class="bar">28 of 28</td><td class="ctr2">0%</td><td class="ctr1">25</td><td class="ctr2">25</td><td class="ctr1">173</td><td class="ctr2">173</td><td class="ctr1">11</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a8"><a href="TrianglePartition.java.html#L224" class="el_method">run()</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="345" alt="345"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h0">49</td><td class="ctr2" id="i0">49</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="TrianglePartition.java.html#L133" class="el_method">randomTC2(Random, TriangleRegion, double)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="86" height="10" title="249" alt="249"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h1">31</td><td class="ctr2" id="i1">31</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a7"><a href="TrianglePartition.java.html#L194" class="el_method">randomTC3(Random, TriangleRegion, double)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="73" height="10" title="210" alt="210"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h2">22</td><td class="ctr2" id="i2">22</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="TrianglePartition.java.html#L105" class="el_method">randomTC2(Random, TriangleRegion)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="32" height="10" title="92" alt="92"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h4">12</td><td class="ctr2" id="i4">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a6"><a href="TrianglePartition.java.html#L181" class="el_method">randomTC3(Random, TriangleRegion)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="26" height="10" title="77" alt="77"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a1"><a href="TrianglePartition.java.html#L12" class="el_method">main(String[])</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="22" height="10" title="66" alt="66"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h5">11</td><td class="ctr2" id="i5">11</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a3"><a href="TrianglePartition.java.html#L77" class="el_method">randomTC(Random, TriangleRegion)</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="22" height="10" title="66" alt="66"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h3">17</td><td class="ctr2" id="i3">17</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a0"><a href="TrianglePartition.java.html#L55" class="el_method">isCorrect(TestCase)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="16" height="10" title="46" alt="46"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h9">5</td><td class="ctr2" id="i9">5</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a2"><a href="TrianglePartition.java.html#L67" class="el_method">randomTC(Random)</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="16" height="10" title="46" alt="46"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">6</td><td class="ctr2" id="i8">6</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a10"><a href="TrianglePartition.java.html#L44" class="el_method">TrianglePartition(double[], double[], long, double)</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="14" height="10" title="42" alt="42"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h6">9</td><td class="ctr2" id="i6">9</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a9"><a href="TrianglePartition.java.html#L311" class="el_method">smallerRegion(TriangleRegion)</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="2" height="10" title="8" alt="8"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">3</td><td class="ctr2" id="i10">3</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>