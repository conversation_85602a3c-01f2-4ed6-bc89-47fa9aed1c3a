<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>Bi_Test3.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.bi_t</a> &gt; <span class="el_source">Bi_Test3.java</span></div><h1>Bi_Test3.java</h1><pre class="source lang-java linenums">package test.simulations.bi_t;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import test.ART;
import tested.*;
import util.PaiLie;
import util.data.ZeroOneCreator;

public class Bi_Test3 extends ART {

	public Bi_Test3(double[] min, double[] max, Random random, FailurePattern failurePattern) {
<span class="nc" id="L20">		super(min, max, random, failurePattern);</span>
<span class="nc" id="L21">	}</span>

<span class="nc" id="L23">	public ArrayList&lt;NRectRegion&gt; regions = new ArrayList&lt;&gt;();</span>

	@Override
	public int run() {
<span class="nc" id="L27">		int count = 0;</span>
		// NPoint p = randomCreator.randomPoint();
<span class="nc" id="L29">		count++;</span>
<span class="nc" id="L30">		NRectRegion region = new NRectRegion(new NPoint(min), new NPoint(max));</span>
<span class="nc" id="L31">		NPoint p = midPoint(region);</span>

		try {
<span class="nc" id="L34">			addRegionsInND(region, p);</span>
<span class="nc" id="L35">		} catch (Exception e) {</span>
<span class="nc" id="L36">			e.printStackTrace();</span>
		}
		//
<span class="nc" id="L39">		while (true) {</span>
<span class="nc" id="L40">			ArrayList&lt;NRectRegion&gt; backups = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L41" title="All 2 branches missed.">			while (this.regions.size() != 0) {</span>
				// 产生方式
<span class="nc" id="L43">				int index = random.nextInt(this.regions.size());</span>
				// 测试
<span class="nc" id="L45">				NRectRegion tempRegion = this.regions.remove(index);</span>
<span class="nc" id="L46">				backups.add(tempRegion);</span>

<span class="nc" id="L48">				count++;</span>
<span class="nc" id="L49">				boolean flag = this.failPattern.isCorrect(midPoint(tempRegion));</span>
<span class="nc bnc" id="L50" title="All 2 branches missed.">				if (!flag) {</span>
<span class="nc" id="L51">					return count;</span>
				}
			}
			// 分割
<span class="nc bnc" id="L55" title="All 2 branches missed.">			for (int i = 0; i &lt; backups.size(); i++) {</span>
				try {
<span class="nc" id="L57">					addRegionsInND(backups.get(i), midPoint(backups.get(i)));</span>
<span class="nc" id="L58">				} catch (Exception e) {</span>
<span class="nc" id="L59">					e.printStackTrace();</span>
				}
			}
		}
		// return 0;
	}

	//
	public NPoint midPoint(NRectRegion region) {
<span class="nc" id="L68">		double[] pn = region.getStart().getXn();</span>
<span class="nc" id="L69">		double[] t2n = region.getEnd().getXn();</span>
<span class="nc" id="L70">		double[] mid = new double[pn.length];</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">		for (int i = 0; i &lt; mid.length; i++) {</span>
<span class="nc" id="L72">			mid[i] = (pn[i] + t2n[i]) / 2.0;</span>
		}
<span class="nc" id="L74">		return new NPoint(mid);</span>
	}

	@Override
	public int em() {
<span class="nc" id="L79">		return 0;</span>
	}

	public List&lt;List&lt;Double&gt;&gt; splitRegions(double[] start, double[] end) {
<span class="nc" id="L83">		ArrayList&lt;double[]&gt; values = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L85">			double[] temp = new double[2];</span>

<span class="nc" id="L87">			temp[0] = start[i];</span>
<span class="nc" id="L88">			temp[1] = end[i];</span>
<span class="nc" id="L89">			values.add(temp);</span>
		}

<span class="nc" id="L92">		ArrayList&lt;List&lt;Double&gt;&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L93">		PaiLie.per(values, 0, new ArrayList&lt;&gt;(), result);</span>
<span class="nc" id="L94">		return result;</span>
	}

	public void addRegionsInND(NRectRegion region, NPoint p) throws Exception {
		// int regions=(int) Math.pow(2, this.dimension);
<span class="nc" id="L99">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L100">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L101">		double[] pxn = p.getXn();</span>
<span class="nc" id="L102">		List&lt;List&lt;Double&gt;&gt; result1 = splitRegions(start, pxn);</span>
<span class="nc" id="L103">		List&lt;List&lt;Double&gt;&gt; result2 = splitRegions(pxn, end);</span>
		// System.out.println(result1.size());
<span class="nc bnc" id="L105" title="All 2 branches missed.">		if (result1.size() != result2.size()) {</span>
<span class="nc" id="L106">			throw new Exception(&quot;result1's size!=result2's size ,split region wrong&quot;);</span>
		}
<span class="nc bnc" id="L108" title="All 2 branches missed.">		for (int i = 0; i &lt; result1.size(); i++) {</span>
<span class="nc" id="L109">			List&lt;Double&gt; temp1 = result1.get(i);</span>
<span class="nc" id="L110">			List&lt;Double&gt; temp2 = result2.get(i);</span>
<span class="nc" id="L111">			double[] newStart = new double[temp1.size()];</span>
<span class="nc" id="L112">			double[] newEnd = new double[temp2.size()];</span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">			for (int j = 0; j &lt; temp1.size(); j++) {</span>
<span class="nc" id="L114">				newStart[j] = temp1.get(j);</span>
<span class="nc" id="L115">				newEnd[j] = temp2.get(j);</span>
			}

<span class="nc" id="L118">			NRectRegion tempRegion = new NRectRegion(new NPoint(newStart), new NPoint(newEnd));</span>
<span class="nc" id="L119">			this.regions.add(tempRegion);</span>
		}
<span class="nc" id="L121">	}</span>

	public static void main(String[] args) {
<span class="nc" id="L124">		testReality();</span>
<span class="nc" id="L125">	}</span>

	public static void testSimulation() {
<span class="nc" id="L128">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L129">		double[] min = dataCreator.minCreator(2);</span>
<span class="nc" id="L130">		double[] max = dataCreator.maxCreator(2);</span>

<span class="nc" id="L132">		int times = 200;</span>
<span class="nc" id="L133">		FailurePattern pattern = new BlockPattern();</span>
<span class="nc" id="L134">		pattern.fail_rate = 0.01;</span>

<span class="nc" id="L136">		long fm = 0;</span>

<span class="nc bnc" id="L138" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L139">			Bi_Test3 test = new Bi_Test3(min, max, new Random(i * 3), pattern);</span>
<span class="nc" id="L140">			int temp = test.run();</span>
<span class="nc" id="L141">			fm += temp;</span>
		}

<span class="nc" id="L144">		System.out.println(fm / (double) times);</span>
<span class="nc" id="L145">	}</span>

	public static void testReality() {
<span class="nc" id="L148">		tanh tested = new tanh();</span>
		// ZeroOneCreator dataCreator=new ZeroOneCreator();
<span class="nc" id="L150">		double[] min = tested.min;</span>
<span class="nc" id="L151">		double[] max = tested.max;</span>

<span class="nc" id="L153">		int times = 1000;</span>
<span class="nc" id="L154">		FailurePattern pattern = new RealityFailPattern(tested.getClass().getSimpleName());</span>
		// pattern.fail_rate=0.01;

<span class="nc" id="L157">		long fm = 0;</span>

<span class="nc bnc" id="L159" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L160">			Bi_Test3 test = new Bi_Test3(min, max, new Random(i * 3), pattern);</span>
<span class="nc" id="L161">			int temp = test.run();</span>
<span class="nc" id="L162">			fm += temp;</span>
		}

<span class="nc" id="L165">		System.out.println(fm / (double) times);</span>
<span class="nc" id="L166">	}</span>

	@Override
	public NPoint generateNextTC() {
		// TODO Auto-generated method stub
<span class="nc" id="L171">		return null;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>