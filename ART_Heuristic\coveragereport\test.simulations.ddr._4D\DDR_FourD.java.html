<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>DDR_FourD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.ddr._4D</a> &gt; <span class="el_source">DDR_FourD.java</span></div><h1>DDR_FourD.java</h1><pre class="source lang-java linenums">package test.simulations.ddr._4D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class DDR_FourD {
	public static void main(String[] args) {
<span class="nc" id="L10">		int times = 1;</span>
<span class="nc" id="L11">		long sums = 0;</span>
<span class="nc" id="L12">		int temp = 0;</span>
<span class="nc" id="L13">		int s = 10;</span>
		//////////////
<span class="nc" id="L15">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L16" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L17">			double min[] = { 0.0, 0.0, 0.0, 0.0 };</span>
<span class="nc" id="L18">			double max[] = { 1.0, 1.0, 1.0, 1.0 };</span>
<span class="nc" id="L19">			DDR_FourD rrt_od = new DDR_FourD(min, max, 0.75, s, 0.000690, i * 3);</span>
<span class="nc" id="L20">			temp = rrt_od.run();</span>
<span class="nc" id="L21">			sums += temp;</span>
		}
<span class="nc" id="L23">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L24">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L25">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L26">	}</span>
	double[] min;
	double[] max;
	double[] fail_start;
	double fail_rate;
	double R;
	int s;
	int randomseed;
	double fail_regionS;
	double failPeriodLength;

<span class="nc" id="L37">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	public DDR_FourD(double[] min, double[] max, double r, int s, double fail_rate, int randomseed) {
<span class="nc" id="L40">		super();</span>
<span class="nc" id="L41">		this.min = min;</span>
<span class="nc" id="L42">		this.max = max;</span>
<span class="nc" id="L43">		R = r;</span>
<span class="nc" id="L44">		this.s = s;</span>
<span class="nc" id="L45">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L46">		this.randomseed = randomseed;</span>
<span class="nc" id="L47">		this.fail_start = new double[this.min.length];</span>
<span class="nc" id="L48">		this.fail_regionS = fail_rate;</span>
<span class="nc bnc" id="L49" title="All 2 branches missed.">		for (int i = 0; i &lt; this.min.length; i++) {</span>
<span class="nc" id="L50">			this.fail_regionS *= (max[i] - min[i]);</span>
		}
<span class="nc" id="L52">		failPeriodLength = Math.pow(this.fail_regionS, 1.0 / (double) this.min.length);</span>
<span class="nc" id="L53">	}</span>

	public boolean isCorrect(TestCase p) {
<span class="nc" id="L56">		boolean isCorrect = true;</span>
<span class="nc bnc" id="L57" title="All 4 branches missed.">		if (p.p &gt; fail_start[0] &amp;&amp; p.p &lt; fail_start[0] + failPeriodLength) {</span>
<span class="nc bnc" id="L58" title="All 4 branches missed.">			if (p.q &gt; fail_start[1] &amp;&amp; p.q &lt; fail_start[1] + failPeriodLength) {</span>
<span class="nc bnc" id="L59" title="All 4 branches missed.">				if (p.m &gt; fail_start[2] &amp;&amp; p.m &lt; fail_start[2] + failPeriodLength) {</span>
<span class="nc bnc" id="L60" title="All 4 branches missed.">					if (p.n &gt; fail_start[3] &amp;&amp; p.n &lt; fail_start[3] + failPeriodLength)</span>
<span class="nc" id="L61">						isCorrect = false;</span>
				}
			}
		}
<span class="nc" id="L65">		return isCorrect;</span>
	}

	public TestCase randomTC(Random random) {
<span class="nc" id="L69">		TestCase temp = new TestCase();</span>
<span class="nc" id="L70">		double p = random.nextDouble() * (max[0] - min[0]) + min[0];</span>
<span class="nc" id="L71">		double q = random.nextDouble() * (max[1] - min[1]) + min[1];</span>
<span class="nc" id="L72">		double m = random.nextDouble() * (max[2] - min[2]) + min[2];</span>
<span class="nc" id="L73">		double n = random.nextDouble() * (max[3] - min[3]) + min[3];</span>
<span class="nc" id="L74">		temp.p = p;</span>
<span class="nc" id="L75">		temp.q = q;</span>
<span class="nc" id="L76">		temp.m = m;</span>
<span class="nc" id="L77">		temp.n = n;</span>
<span class="nc" id="L78">		return temp;</span>
	}

	public int run() {
<span class="nc" id="L82">		Random random = new Random(randomseed);</span>
<span class="nc bnc" id="L83" title="All 2 branches missed.">		for (int i = 0; i &lt; this.min.length; i++) {</span>
<span class="nc" id="L84">			fail_start[i] = random.nextDouble() * (max[i] - min[i] - failPeriodLength) + min[i];</span>
			// System.out.print(fail_start[i]+&quot; &quot;);
		}
		// System.out.println();
<span class="nc" id="L88">		int count = 0;</span>
<span class="nc" id="L89">		int _10CandidateCount = 0;</span>
<span class="nc" id="L90">		TestCase p = randomTC(random);// randomTC</span>
		// System.out.println(&quot;p0:&quot; + p.toString() + isCorrect(p));
		// while (isCorrect(p)) {
<span class="nc bnc" id="L93" title="All 2 branches missed.">		while (count &lt; 1000) {</span>
<span class="nc" id="L94">			double InputDomainA = 1.0;</span>
<span class="nc bnc" id="L95" title="All 2 branches missed.">			for (int i = 0; i &lt; this.min.length; i++) {</span>
<span class="nc" id="L96">				InputDomainA *= max[i] - min[i];</span>
			}

<span class="nc" id="L99">			double radius = Math.pow((2.0 * InputDomainA * R) / ((_10CandidateCount + s) * Math.PI * Math.PI),</span>
<span class="nc" id="L100">					1.0 / 4.0);</span>
			// System.out.println(&quot;radius:&quot;+radius);
<span class="nc" id="L102">			p = new TestCase();</span>
<span class="nc" id="L103">			boolean all_s_has_E_flag = true;</span>
<span class="nc" id="L104">			double TS2C[] = new double[s];</span>
<span class="nc" id="L105">			double Pvalue[] = new double[s];</span>
<span class="nc" id="L106">			double Qvalue[] = new double[s];</span>
<span class="nc" id="L107">			double Mvalue[] = new double[s];</span>
<span class="nc" id="L108">			double Nvalue[] = new double[s];</span>
<span class="nc bnc" id="L109" title="All 2 branches missed.">			for (int k = 0; k &lt; s; k++) {</span>
<span class="nc" id="L110">				TestCase ck = randomTC(random);</span>
<span class="nc" id="L111">				boolean this_ck_has_E_flag = false;</span>
<span class="nc" id="L112">				double min = Double.MAX_VALUE;</span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">				for (int i = 0; i &lt; tests.size(); i++) {</span>
					// 没有在圈之中
<span class="nc" id="L115">					double distance = Math.pow(</span>
<span class="nc" id="L116">							(Math.pow((ck.p - tests.get(i).p), 2.0)) + (Math.pow(ck.q - tests.get(i).q, 2.0))</span>
<span class="nc" id="L117">									+ (Math.pow(ck.m - tests.get(i).m, 2.0)) + Math.pow(ck.n - tests.get(i).n, 2.0),</span>
<span class="nc" id="L118">							0.5);// distance</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">					if (distance &lt; radius) {</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">						if (min &gt; distance) {</span>
<span class="nc" id="L121">							min = distance;</span>
<span class="nc" id="L122">							TS2C[k] = min;</span>
<span class="nc" id="L123">							Pvalue[k] = ck.p;</span>
<span class="nc" id="L124">							Qvalue[k] = ck.q;</span>
<span class="nc" id="L125">							Mvalue[k] = ck.m;</span>
<span class="nc" id="L126">							Nvalue[k] = ck.n;</span>
						}
<span class="nc" id="L128">						this_ck_has_E_flag = true;</span>
					}
				}
<span class="nc bnc" id="L131" title="All 2 branches missed.">				if (!this_ck_has_E_flag) {</span>
<span class="nc" id="L132">					all_s_has_E_flag = false;</span>
<span class="nc" id="L133">					p = new TestCase();</span>
<span class="nc" id="L134">					p.p = ck.p;</span>
<span class="nc" id="L135">					p.q = ck.q;</span>
<span class="nc" id="L136">					p.m = ck.m;</span>
<span class="nc" id="L137">					p.n = ck.n;</span>
					// System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.toString() + isCorrect(p) + &quot; rrt&quot;);
<span class="nc bnc" id="L139" title="All 2 branches missed.">					if (!isCorrect(p)) {</span>
						// return count;
<span class="nc" id="L141">						continue;</span>
					} else {
<span class="nc" id="L143">						count++;</span>
<span class="nc" id="L144">						tests.add(p);</span>
					}
				}
			}
<span class="nc bnc" id="L148" title="All 2 branches missed.">			if (all_s_has_E_flag) {</span>
<span class="nc" id="L149">				double max = 0;</span>
<span class="nc" id="L150">				int index = 0;</span>
<span class="nc bnc" id="L151" title="All 2 branches missed.">				for (int i = 0; i &lt; TS2C.length; i++) {</span>
<span class="nc bnc" id="L152" title="All 2 branches missed.">					if (max &lt; TS2C[i]) {</span>
<span class="nc" id="L153">						max = TS2C[i];</span>
<span class="nc" id="L154">						index = i;</span>
					}
				}
<span class="nc" id="L157">				p = new TestCase();</span>
<span class="nc" id="L158">				p.p = Pvalue[index];</span>
<span class="nc" id="L159">				p.q = Qvalue[index];</span>
<span class="nc" id="L160">				p.m = Mvalue[index];</span>
<span class="nc" id="L161">				p.n = Nvalue[index];</span>
				// System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.toString() + isCorrect(p) + &quot;
				// fscs&quot;);
<span class="nc bnc" id="L164" title="All 2 branches missed.">				if (!isCorrect(p)) {</span>
					// return count;
<span class="nc" id="L166">					continue;</span>
				} else {
<span class="nc" id="L168">					count++;</span>
<span class="nc" id="L169">					tests.add(p);</span>
				}
			}
<span class="nc" id="L172">			_10CandidateCount++;</span>
		}
<span class="nc" id="L174">		return count;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>