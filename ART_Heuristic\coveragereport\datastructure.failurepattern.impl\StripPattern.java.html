<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>StripPattern.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.failurepattern.impl</a> &gt; <span class="el_source">StripPattern.java</span></div><h1>StripPattern.java</h1><pre class="source lang-java linenums">package datastructure.failurepattern.impl;

import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;


<span class="nc" id="L10">public class StripPattern extends FailurePattern {</span>

<span class="nc" id="L12">	public double a=2;//1:1...1:10</span>
	
	NPoint start;
	NPoint end;
	@Override
	public void genFailurePattern() {
<span class="nc" id="L18">		double totalArea = 1.0;</span>
<span class="nc bnc" id="L19" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc" id="L20">			totalArea *= (max[i] - min[i]);</span>
		}
<span class="nc" id="L22">		double x=Math.pow(((this.fail_rate*totalArea)/(double)a),1.0/(double)this.dimension);</span>
		
<span class="nc" id="L24">		start=new NPoint();</span>
<span class="nc" id="L25">		start.setXn(new double[this.dimension]);</span>
<span class="nc" id="L26">		end=new NPoint();</span>
<span class="nc" id="L27">		end.setXn(new double[this.dimension]);</span>
<span class="nc bnc" id="L28" title="All 2 branches missed.">		for(int i=0;i&lt;dimension;i++){</span>
<span class="nc" id="L29">			double length=x;</span>
<span class="nc bnc" id="L30" title="All 2 branches missed.">			if(i==dimension-1){</span>
<span class="nc" id="L31">				length=a*x;</span>
			}
			//System.out.println(length);
<span class="nc" id="L34">			double temp=random.nextDouble()*(this.max[i]-this.min[i]-length)+this.min[i];</span>
<span class="nc" id="L35">			start.getXn()[i]=temp;</span>
<span class="nc" id="L36">			end.getXn()[i]=temp+length;</span>
		}
		
<span class="nc" id="L39">	}</span>

	@Override
	public boolean isCorrect(NPoint p) {
<span class="nc" id="L43">		NRectRegion region=new NRectRegion(start, end);</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">		return !region.isPointInRegion(p);</span>
		//return false;
	}

	@Override
	public void showFailurePattern() {
<span class="nc" id="L50">		System.out.println(start+&quot;|&quot;+end);</span>
<span class="nc" id="L51">	}</span>
	public static void main(String[] args) {
<span class="nc" id="L53">		StripPattern pattern=new StripPattern();</span>
<span class="nc" id="L54">		pattern.min=new double[]{0,0,0};</span>
<span class="nc" id="L55">		pattern.max=new double[]{1,1,1};</span>
<span class="nc" id="L56">		pattern.dimension=3;</span>
		
<span class="nc" id="L58">		pattern.random=new Random(3);</span>
		
<span class="nc" id="L60">		pattern.fail_rate=0.005;</span>
		
<span class="nc" id="L62">		pattern.genFailurePattern();</span>
		
<span class="nc" id="L64">		System.out.println(new NRectRegion(pattern.start, pattern.end).size());</span>
<span class="nc" id="L65">		pattern.showFailurePattern();</span>
		
<span class="nc" id="L67">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>