<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>cel.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">cel.java</span></div><h1>cel.java</h1><pre class="source lang-java linenums">package tested;

import util.TestProgram;

<span class="nc" id="L5">public class cel {</span>
<span class="nc" id="L6">	public  static double[] min = { 0.001, 0.001, 0.001, 0.001 };</span>
<span class="nc" id="L7">	public static  double[] max = { 1.0, 300.0, 10000.0, 1000.0 };</span>
<span class="nc" id="L8">	public static  double failureRate = 0.000332;</span>
<span class="nc" id="L9">	public  static int Dimension = 4;</span>
	// 1/0.000332=3012

	public boolean isCorrect(double x, double y, double m, double n) {
		// System.out.println(&quot;correct:&quot;+correct(x));
		// System.out.println(&quot;wrong:&quot;+wrong(x));
<span class="nc" id="L15">		return TestProgram.test_cel(x, y, m, n);</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>