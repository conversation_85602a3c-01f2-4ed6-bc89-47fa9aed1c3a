<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MainMethodEm.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">a_main</a> &gt; <span class="el_source">MainMethodEm.java</span></div><h1>MainMethodEm.java</h1><pre class="source lang-java linenums">package a_main;

import java.awt.Dimension;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;
import java.nio.Buffer;
import java.util.Random;

import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.PointPattern;
import datastructure.failurepattern.impl.PointPatternIn1D;
import datastructure.failurepattern.impl.PointPatternIn2D;
import datastructure.failurepattern.impl.PointPatternIn3D;
import datastructure.failurepattern.impl.PointPatternIn4D;
import datastructure.failurepattern.impl.StripPattern;
import datastructure.failurepattern.impl.StripPatternIn2D;
import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tp._ND.ART_TP_ND;
import test.simulations.art_tpp.ART_TPP;
import test.simulations.fscs.FSCS_ND;
import test.simulations.rrt.RRT_ND;
import test.simulations.rrttp.hilbert.RRTtp1D;
import test.simulations.rrttp.hilbert.RRTtpND_H;
import test.simulations.rt.RT_ND;
import util.data.ZeroOneCreator;
import util.file.FileUtils;

<span class="nc" id="L33">public class MainMethodEm {</span>
	// TODO 完成参数化
	public static void main(String[] args) throws Exception {
<span class="nc" id="L36">		double failrates[] = { 0.000005, 0.000001 };</span>
<span class="nc" id="L37">		String path = &quot;C:\\Users\\<USER>\\Desktop\\研究方向和内容\\小论文\\补充数据\\em\\simulation&quot;;</span>

<span class="nc bnc" id="L39" title="All 2 branches missed.">		for (int j = 0; j &lt; failrates.length; j++) {</span>
<span class="nc" id="L40">			double failrate = failrates[j];</span>
<span class="nc" id="L41">			System.out.println(&quot;failure rate:&quot; + failrate);</span>

<span class="nc" id="L43">			int d = 1;</span>

<span class="nc" id="L45">			File rtf = FileUtils.createNewFile(path, d + &quot;维-&quot; + failrate + &quot;all.txt&quot;);</span>
<span class="nc" id="L46">			BufferedWriter writer = get(rtf);</span>

<span class="nc" id="L48">			ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L49">			double[] min = dataCreator.minCreator(d);</span>
<span class="nc" id="L50">			double[] max = dataCreator.maxCreator(d);</span>

			// FailurePattern failurePattern = new BlockPattern();
			// FailurePattern failurePattern=new StripPattern();
<span class="nc" id="L54">			FailurePattern failurePattern = new PointPatternIn4D();</span>
<span class="nc" id="L55">			failurePattern.fail_rate = failrate;</span>
<span class="nc" id="L56">			failurePattern.min = min;</span>
<span class="nc" id="L57">			failurePattern.max = max;</span>
<span class="nc" id="L58">			failurePattern.dimension = d;</span>

			// rt
<span class="nc" id="L61">			double[] em = RT_ND.testEm(d, failrate);</span>
<span class="nc" id="L62">			writerEm(&quot;rt&quot;,em, writer);</span>
			// rrt
<span class="nc" id="L64">			em = RRT_ND.testEm(d, failrate);</span>
<span class="nc" id="L65">			writerEm(&quot;rrt&quot;,em, writer);</span>
			// fscs
<span class="nc" id="L67">			em = FSCS_ND.testEm(d, failrate);</span>
<span class="nc" id="L68">			writerEm(&quot;fscs&quot;,em, writer);</span>
			// art_b
			// em=ART_B_ND.testEm(d, failrate);
			// art_rp
<span class="nc" id="L72">			em = ART_RP_ND.testEm(d, failrate);</span>
<span class="nc" id="L73">			writerEm(&quot;rp&quot;,em, writer);</span>
			// art_tp
<span class="nc" id="L75">			em = ART_TP_ND.testEm(d, failrate);</span>
<span class="nc" id="L76">			writerEm(&quot;tp&quot;,em, writer);</span>
			// art_tpp
<span class="nc" id="L78">			em = ART_TPP.testEm(d, failrate);</span>
<span class="nc" id="L79">			writerEm(&quot;tpp&quot;,em, writer);</span>
			// my method
			// 1dimension
<span class="nc" id="L82">			em = RRTtpND_H.testEm(d, failrate);</span>
<span class="nc" id="L83">			writerEm(&quot;laz&quot;,em, writer);</span>
<span class="nc" id="L84">			writer.close();</span>
		}
<span class="nc" id="L86">	}</span>

	public static void writerEm(String message, double[] em, BufferedWriter writer) {
		try {
<span class="nc" id="L90">			writer.write(message);</span>

<span class="nc" id="L92">			writer.newLine();</span>

<span class="nc bnc" id="L94" title="All 2 branches missed.">			for (int i = 0; i &lt; em.length; i++) {</span>
<span class="nc" id="L95">				writer.write(em[i] + &quot;&quot;);</span>
<span class="nc" id="L96">				writer.newLine();</span>
			}
<span class="nc" id="L98">			writer.newLine();</span>
<span class="nc" id="L99">			writer.flush();</span>
<span class="nc" id="L100">		} catch (IOException e) {</span>
<span class="nc" id="L101">			e.printStackTrace();</span>
		}
<span class="nc" id="L103">	}</span>

	public static BufferedWriter get(File f) throws Exception {
<span class="nc" id="L106">		return new BufferedWriter(new OutputStreamWriter(new FileOutputStream(f), &quot;UTF-8&quot;));</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>