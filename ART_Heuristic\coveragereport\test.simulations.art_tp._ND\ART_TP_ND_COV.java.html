<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_ND_COV.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._ND</a> &gt; <span class="el_source">ART_TP_ND_COV.java</span></div><h1>ART_TP_ND_COV.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._ND;
/*
 * 原生的ART_TP，不包括ART_Etp，ART_Btp，ART_RPtp
 * 
 * **/

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.TPInfo2;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tpp.ART_TPP;
import util.X3;
import util.data.ZeroOneCreator;

//TODO 加入覆盖率的信息进行增强

public class ART_TP_ND_COV extends ART {
	public static void main(String[] args) {
		// start,end 表示边界，from to表示Anbn int d = 2; double[] min =
		 //testTCTime(2,10000);
		//testEm(1, 0.01);
<span class="nc" id="L29">		testFm();</span>
<span class="nc" id="L30">	}</span>

<span class="nc" id="L32">	ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L34">	ArrayList&lt;TPInfo2&gt; regions = new ArrayList&lt;&gt;();</span>

	double C;// 常数

	public ART_TP_ND_COV(double[] min, double[] max, FailurePattern pattern, Random random) {
<span class="nc" id="L39">		super(min, max, random, pattern);</span>
<span class="nc" id="L40">	}</span>

	public void addFromAndTo(TPInfo2 region) {
<span class="nc" id="L43">		double[] start = region.start.getXn();</span>
<span class="nc" id="L44">		double[] end = region.end.getXn();</span>
<span class="nc" id="L45">		double[] fromarr = new double[this.dimension];</span>
<span class="nc" id="L46">		double[] toarr = new double[this.dimension];</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L48">			double from = 0.0;</span>
<span class="nc bnc" id="L49" title="All 2 branches missed.">			if (start[i] == min[i]) {</span>
<span class="nc" id="L50">				from = random.nextDouble() * (end[i] - min[i]) + (2 * min[i] - end[i]);</span>
<span class="nc" id="L51">			} else {</span>
<span class="nc" id="L52">				from = start[i];</span>
			}
<span class="nc" id="L54">			fromarr[i] = from;</span>
<span class="nc" id="L55">			double to = 0.0;</span>
<span class="nc bnc" id="L56" title="All 2 branches missed.">			if (end[i] == max[i]) {</span>
<span class="nc" id="L57">				to = random.nextDouble() * (max[i] - end[i]) + max[i];</span>
<span class="nc" id="L58">			} else {</span>
<span class="nc" id="L59">				to = end[i];</span>
			}
<span class="nc" id="L61">			toarr[i] = to;</span>
		}
<span class="nc" id="L63">		region.from = new NPoint(fromarr);</span>
<span class="nc" id="L64">		region.to = new NPoint(toarr);</span>
<span class="nc" id="L65">	}</span>

	public void addRegions(double[] min, double[] max, double[] xn) {
<span class="nc" id="L68">		int count = (int) Math.pow(2, this.dimension);</span>
<span class="nc" id="L69">		ArrayList&lt;double[]&gt; lists = new ArrayList&lt;&gt;(this.dimension);</span>
<span class="nc bnc" id="L70" title="All 2 branches missed.">		for (int i = 0; i &lt; min.length; i++) {</span>
<span class="nc" id="L71">			lists.add(new double[] { min[i], xn[i] });</span>
		}
<span class="nc" id="L73">		ArrayList&lt;double[]&gt; lists2 = new ArrayList&lt;&gt;(this.dimension);</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">		for (int i = 0; i &lt; min.length; i++) {</span>
<span class="nc" id="L75">			lists2.add(new double[] { xn[i], max[i] });</span>
		}
<span class="nc" id="L77">		ArrayList&lt;double[]&gt; result1 = new ArrayList&lt;&gt;(count);</span>
<span class="nc" id="L78">		addRegionsRec(this.dimension, 0, new ArrayList&lt;Double&gt;(), lists, result1);</span>

<span class="nc" id="L80">		ArrayList&lt;double[]&gt; result2 = new ArrayList&lt;&gt;(count);</span>
<span class="nc" id="L81">		addRegionsRec(this.dimension, 0, new ArrayList&lt;Double&gt;(), lists2, result2);</span>

		// for( int i=0;i&lt;result1.size();i++){
		// System.out.println(Arrays.toString(result1.get(i))+&quot;
		// &quot;+Arrays.toString(result2.get(i)));
		// }
<span class="nc bnc" id="L87" title="All 2 branches missed.">		for (int i = 0; i &lt; result1.size(); i++) {</span>
<span class="nc" id="L88">			TPInfo2 info = new TPInfo2();</span>
<span class="nc" id="L89">			info.start = new NPoint(result1.get(i));</span>
<span class="nc" id="L90">			info.end = new NPoint(result2.get(i));</span>
<span class="nc" id="L91">			addFromAndTo(info);</span>
<span class="nc" id="L92">			this.regions.add(info);</span>
		}
<span class="nc" id="L94">	}</span>

	public void addRegionsRec(int n, int k, List&lt;Double&gt; list, List&lt;double[]&gt; lists, ArrayList&lt;double[]&gt; result1) {
<span class="nc bnc" id="L97" title="All 2 branches missed.">		if (list.size() == n) {</span>
			// double[] temp=list.toArray();
<span class="nc" id="L99">			double[] temp = new double[n];</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">			for (int i = 0; i &lt; temp.length; i++) {</span>
<span class="nc" id="L101">				temp[i] = list.get(i);</span>
			}
<span class="nc" id="L103">			result1.add(temp);</span>
<span class="nc" id="L104">		} else {</span>
<span class="nc bnc" id="L105" title="All 2 branches missed.">			for (int i = 0; i &lt; 2; i++) {</span>
<span class="nc" id="L106">				List&lt;Double&gt; list2 = new ArrayList&lt;Double&gt;(list);</span>
<span class="nc" id="L107">				list2.add(lists.get(k)[i]);</span>
<span class="nc" id="L108">				addRegionsRec(n, ++k, list2, lists, result1);</span>
<span class="nc" id="L109">				k--;</span>
			}
		}
<span class="nc" id="L112">	}</span>

	public double calEachIntEC(double s, double e, double f, double t) {
		// int(x-f)*(t-x) s,t
<span class="nc" id="L116">		return (-1.0 / 6.0) * (e - s)</span>
<span class="nc" id="L117">				* (e * (-3 * f + 2 * s - 3 * t) - 3 * f * s + 6 * f * t + 2 * s * s - 3 * s * t + 2 * e * e);</span>
	}

	public double calEachRegion() {
<span class="nc" id="L121">		double tempC = 0.0;</span>
		// System.out.println(&quot;each region cdf:&quot;);
<span class="nc bnc" id="L123" title="All 2 branches missed.">		for (int i = 0; i &lt; regions.size(); i++) {</span>
			// 二维特殊化
			// NRectRegion temp=regions.get(i);
<span class="nc" id="L126">			double[] start = regions.get(i).start.getXn();</span>
<span class="nc" id="L127">			double[] end = regions.get(i).end.getXn();</span>
<span class="nc" id="L128">			double[] from = regions.get(i).from.getXn();</span>
<span class="nc" id="L129">			double[] to = regions.get(i).to.getXn();</span>
<span class="nc" id="L130">			double probality = 1.0;</span>
<span class="nc" id="L131">			regions.get(i).eachProbality = new double[this.dimension];</span>
<span class="nc bnc" id="L132" title="All 2 branches missed.">			for (int j = 0; j &lt; start.length; j++) {</span>
<span class="nc" id="L133">				double temp = calEachIntEC(start[j], end[j], from[j], to[j]);</span>
<span class="nc" id="L134">				probality *= temp;</span>
<span class="nc" id="L135">				regions.get(i).eachProbality[j] = temp;</span>
			}
			// double b = calEachIntEC(start.getXn()[1], end.getXn()[1],
			// from.getXn()[1], to.getXn()[1]);
<span class="nc" id="L139">			regions.get(i).probality = probality;</span>
			// regions.get(i).proa = a;
			// regions.get(i).prob = b;
<span class="nc" id="L142">			tempC += probality;</span>
			// System.out.println(&quot;int (x-&quot; + from.getXn()[0] + &quot;)*(&quot; +
			// to.getXn()[0] + &quot;-x&quot; + &quot;) from &quot; + start.getXn()[0]
			// + &quot; to &quot; + end.getXn()[0]);
			// System.out.println(&quot;int (x-&quot; + from.getXn()[1] + &quot;)*(&quot; +
			// to.getXn()[1] + &quot;-x&quot; + &quot;) from &quot; + start.getXn()[1]
			// + &quot; to &quot; + end.getXn()[1]);
			// System.out.println(&quot;from:&quot;+(from1)+&quot;,&quot;+from2+&quot; to:&quot;+to1+&quot;,&quot;+to2);
			// System.out.println(&quot;start:&quot;+(start.getXn()[0])+&quot;,&quot;+start.getXn()[1]+&quot;
			// to:&quot;+end.getXn()[0]+&quot;,&quot;+end.getXn()[1]);

			// System.out.println(&quot;eachValue:&quot; + (a) + &quot;,&quot; + b + &quot; multi:&quot; + (a
			// * b));
			// System.out.println(&quot;*********&quot;);
		}
		// System.out.println(&quot;tempC:&quot; + tempC);
		// System.out.println(&quot;------------&quot;);
<span class="nc" id="L159">		return tempC;</span>
	}

	public double genNextEachDimension(double start, double end, double from, double to, double C, double aorb,
			double Pre, double T) {

		// System.out.println(&quot;cal next test case&quot;);
		// System.out.println(&quot;start:&quot;+start+&quot;,end:&quot;+end+&quot;,from:&quot;+from+&quot;,to:&quot;+to+&quot;,C:&quot;+C+&quot;,aorb:&quot;+aorb+&quot;,Pre:&quot;+Pre+&quot;,T:&quot;+T);
		// pre+c*(a|b)*int(start to x)((x-from)*(to-x))=T;
<span class="nc" id="L168">		double A = (-1.0 / 3.0) * C * aorb;</span>
<span class="nc" id="L169">		double B = 0.5 * (from + to) * (C) * (aorb);</span>
<span class="nc" id="L170">		double C1 = -from * to * C * aorb;</span>
<span class="nc" id="L171">		double D = C * aorb</span>
<span class="nc" id="L172">				* ((1.0 / 3.0) * (start * start * start) - 0.5 * (start * start) * (from + to) + from * to * start) - T</span>
<span class="nc" id="L173">				+ Pre;</span>
<span class="nc" id="L174">		double[] roots = X3.shengjinFormula(A, B, C1, D);</span>
		// System.out.println(&quot;roots:&quot;+Arrays.toString(roots));
<span class="nc" id="L176">		double next = -1.0;</span>
<span class="nc" id="L177">		boolean flag = false;</span>
<span class="nc bnc" id="L178" title="All 2 branches missed.">		for (int i = 0; i &lt; roots.length; i++) {</span>
<span class="nc bnc" id="L179" title="All 4 branches missed.">			if (roots[i] &gt; start &amp;&amp; roots[i] &lt; end) {</span>
<span class="nc" id="L180">				flag = true;</span>
<span class="nc" id="L181">				next = roots[i];</span>
<span class="nc" id="L182">				break;</span>
			}
		}
<span class="nc bnc" id="L185" title="All 2 branches missed.">		if (!flag) {</span>
			// System.out.println(&quot;x3 error!&quot;);
			// next=genNextEachDimension(start, end, from, to, C1, aorb, Pre,
			// next);
<span class="nc" id="L189">			next = random.nextDouble();</span>
			// return Double.MIN_VALUE;
		}
<span class="nc" id="L192">		return next;</span>
	}

	public NPoint genNextTestCase(int index, double PreIntegral) {

<span class="nc" id="L197">		double[] start = this.regions.get(index).start.getXn();</span>
<span class="nc" id="L198">		double[] end = this.regions.get(index).end.getXn();</span>
<span class="nc" id="L199">		double[] from = this.regions.get(index).from.getXn();</span>
<span class="nc" id="L200">		double[] to = this.regions.get(index).to.getXn();</span>
<span class="nc" id="L201">		double[] result = new double[this.dimension];</span>
<span class="nc bnc" id="L202" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc" id="L203">			double T = random.nextDouble() * (regions.get(index).probality * C) + PreIntegral;</span>
<span class="nc" id="L204">			double temp = genNextEachDimension(start[i], end[i], from[i], to[i], C,</span>
<span class="nc" id="L205">					this.regions.get(index).probality / this.regions.get(index).eachProbality[i], PreIntegral, T);</span>
<span class="nc" id="L206">			result[i] = temp;</span>
		}
<span class="nc" id="L208">		NPoint p = new NPoint(result);</span>
<span class="nc" id="L209">		return p;</span>
	}

	public void updateRegions(NPoint p, int index) {
<span class="nc bnc" id="L213" title="All 2 branches missed.">		if (regions.size() == 0) {</span>
<span class="nc" id="L214">			addRegions(this.min, this.max, p.getXn());</span>
<span class="nc" id="L215">		} else {</span>
<span class="nc" id="L216">			TPInfo2 info = regions.remove(index);</span>
<span class="nc" id="L217">			addRegions(info.start.getXn(), info.end.getXn(), p.getXn());</span>
		}
<span class="nc" id="L219">	}</span>
	public boolean isInRegion(NPoint start1, NPoint end1, NPoint p) {
<span class="nc" id="L221">		boolean flag = true;</span>
<span class="nc" id="L222">		double[] pxn = p.getXn();</span>
<span class="nc" id="L223">		double[] start = start1.getXn();</span>
<span class="nc" id="L224">		double[] end = end1.getXn();</span>
<span class="nc bnc" id="L225" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc bnc" id="L226" title="All 4 branches missed.">			if (pxn[i] &lt; start[i] || pxn[i] &gt; end[i]) {</span>
<span class="nc" id="L227">				flag = false;</span>
			}
		}
<span class="nc" id="L230">		return flag;</span>
	}
	@Override
	public NPoint generateNextTC() {
<span class="nc" id="L234">		NPoint p=null;</span>
		
<span class="nc" id="L236">		int temp=0;</span>
<span class="nc bnc" id="L237" title="All 2 branches missed.">		if(tests.size()==0){</span>
<span class="nc" id="L238">			p=randomCreator.randomPoint();</span>
<span class="nc" id="L239">			tests.add(p);</span>
<span class="nc" id="L240">			updateRegions(p, temp);</span>
<span class="nc" id="L241">		}else{</span>
<span class="nc" id="L242">			C = 1.0 / calEachRegion();</span>
			// 确定在哪一个区域
<span class="nc" id="L244">			double T = random.nextDouble();</span>
<span class="nc" id="L245">			double PreIntegral = 0.0;</span>
<span class="nc" id="L246">			double SumIntegral = 0.0;// 积分值总和</span>
			// int temp = 0;// 落在哪个区间
<span class="nc" id="L248">			temp = 0;</span>
<span class="nc bnc" id="L249" title="All 2 branches missed.">			for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc bnc" id="L250" title="All 2 branches missed.">				if (SumIntegral &lt; T) {</span>
<span class="nc" id="L251">					PreIntegral = SumIntegral;</span>
<span class="nc" id="L252">					temp = i;</span>
				}
<span class="nc" id="L254">				SumIntegral += regions.get(i).probality * C;</span>
			}

<span class="nc" id="L257">			p = null;</span>
<span class="nc" id="L258">			p = genNextTestCase(temp, PreIntegral);</span>
<span class="nc" id="L259">			tests.add(p);</span>
<span class="nc" id="L260">			updateRegions(p, temp);</span>
		}
<span class="nc" id="L262">		return p;</span>
	}
	
	public void time() {
<span class="nc" id="L266">		int count = 0;</span>
<span class="nc" id="L267">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc" id="L268">		int temp = 0;</span>
<span class="nc bnc" id="L269" title="All 2 branches missed.">		while (count &lt;= tcCount) {</span>
<span class="nc" id="L270">			count++;</span>
<span class="nc" id="L271">			tests.add(p);</span>
<span class="nc" id="L272">			updateRegions(p, temp);</span>
<span class="nc" id="L273">			C = 1.0 / calEachRegion();</span>
			// 确定在哪一个区域
<span class="nc" id="L275">			double T = random.nextDouble();</span>
<span class="nc" id="L276">			double PreIntegral = 0.0;</span>
<span class="nc" id="L277">			double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L278">			temp = 0;</span>
<span class="nc bnc" id="L279" title="All 2 branches missed.">			for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc bnc" id="L280" title="All 2 branches missed.">				if (SumIntegral &lt; T) {</span>
<span class="nc" id="L281">					PreIntegral = SumIntegral;</span>
<span class="nc" id="L282">					temp = i;</span>
				}
<span class="nc" id="L284">				SumIntegral += regions.get(i).probality * C;</span>
			}

<span class="nc" id="L287">			p = null;</span>
<span class="nc" id="L288">			p = genNextTestCase(temp, PreIntegral);</span>
		}
<span class="nc" id="L290">	}</span>
	public static double testFm() {
<span class="nc" id="L292">		int d = 2;</span>
<span class="nc" id="L293">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L294">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L295">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L297">		int times = 2000;</span>

<span class="nc" id="L299">		int temp = 0;</span>
<span class="nc" id="L300">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L301">		failurePattern.fail_rate = 0.002;</span>
<span class="nc" id="L302">		long sums = 0;</span>
<span class="nc" id="L303">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L304" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L305">			ART_TP_ND_COV rt = new ART_TP_ND_COV(min, max, failurePattern, new Random(i * 3 + 3));</span>
<span class="nc" id="L306">			temp = rt.run();</span>
<span class="nc" id="L307">			sums += temp;</span>
		}
<span class="nc" id="L309">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L310">		double fm = sums / (double) times;</span>
<span class="nc" id="L311">		System.out.println(&quot;fm:&quot; + fm + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
<span class="nc" id="L312">		return fm;</span>
	}
	public static double testTCTime(int d, int tcCount) {
<span class="nc" id="L315">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L316">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L317">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L319">		int times = 1;</span>

<span class="nc" id="L321">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L322">		failurePattern.fail_rate = 0.001;</span>
<span class="nc" id="L323">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L324" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L325">			ART_TP_ND_COV rt = new ART_TP_ND_COV(min, max, failurePattern, new Random(i * 3 + 3));</span>
<span class="nc" id="L326">			rt.tcCount = tcCount;</span>
<span class="nc" id="L327">			rt.time2();</span>
		}
<span class="nc" id="L329">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L330">		System.out.println((endTime - startTime) / (double) times);</span>
<span class="nc" id="L331">		return ((endTime - startTime) / (double) times);</span>
	}

	public static double[] testEm(int dimension, double failrate) {
<span class="nc" id="L335">		int d = dimension;</span>
<span class="nc" id="L336">		int emTime = 6;</span>
<span class="nc" id="L337">		double[] result = new double[emTime];</span>
<span class="nc" id="L338">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L339">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L340">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L342">		int times = 2000;</span>

<span class="nc" id="L344">		int temp = 0;</span>
<span class="nc" id="L345">		int kk = 10;</span>
<span class="nc" id="L346">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L347">		failurePattern.fail_rate = failrate;</span>
<span class="nc bnc" id="L348" title="All 2 branches missed.">		for (int k = 0; k &lt; emTime; k++) {</span>
<span class="nc" id="L349">			long sums = 0;</span>
<span class="nc" id="L350">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L351" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L352">				ART_TP_ND_COV rt = new ART_TP_ND_COV(min, max, failurePattern, new Random(i * 3 + 3));</span>
<span class="nc" id="L353">				rt.emCount = (k + 1) * 500;</span>
<span class="nc" id="L354">				temp = rt.em();</span>
<span class="nc" id="L355">				sums += temp;</span>
			}
<span class="nc" id="L357">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L358">			double em = sums / (double) times;</span>
<span class="nc" id="L359">			result[k] = em;</span>
<span class="nc" id="L360">			System.out.println(&quot;em:&quot; + em + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
		}
<span class="nc" id="L362">		System.out.println();</span>
<span class="nc" id="L363">		return result;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>