<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_CenterAll_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_center</a> &gt; <span class="el_source">ART_CenterAll_ND.java</span></div><h1>ART_CenterAll_ND.java</h1><pre class="source lang-java linenums">package test.simulations.art_center;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import util.PaiLie;
import util.data.ZeroOneCreator;

public class ART_CenterAll_ND extends ART {

	// public int allParts = 1000;
	// public int startParts = 425;
	// public int endParts = 575;

<span class="nc" id="L21">	public double howManyParts = 0.220;</span>

<span class="nc" id="L23">	public ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L24">	public ArrayList&lt;NRectRegion&gt; regions = new ArrayList&lt;&gt;();</span>

	public ART_CenterAll_ND(double[] min, double[] max, Random random, FailurePattern failurePattern) {
<span class="nc" id="L27">		super(min, max, random, failurePattern);</span>
<span class="nc" id="L28">	}</span>

	@Override
	public int run() {
<span class="nc" id="L32">		int count = 0;</span>
<span class="nc" id="L33">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc" id="L34">		NRectRegion maxRegion = new NRectRegion(new NPoint(min), new NPoint(max));</span>
<span class="nc" id="L35">		this.regions.add(maxRegion);</span>
<span class="nc" id="L36">		int index = 0;</span>

<span class="nc bnc" id="L38" title="All 2 branches missed.">		while (this.failPattern.isCorrect(p)) {</span>
			// count++;
<span class="nc" id="L40">			count++;</span>

			// add tests
<span class="nc" id="L43">			this.tests.add(p);</span>

			// split region
<span class="nc" id="L46">			splitRegion(index, p);</span>

			// //make the region min
			// NRectRegion
			// smallerRegion=makeMaxRegionSmall(this.regions.get(index));
			// another point
			// p=new NPoint();
<span class="nc" id="L53">			ArrayList result = randomPoint();</span>
<span class="nc" id="L54">			p = (NPoint) result.get(0);</span>
<span class="nc" id="L55">			index = (int) result.get(1);</span>
		}
<span class="nc" id="L57">		return count;</span>
	}

	public ArrayList randomPoint() {
		// 将p填入
		// NPoint p = new NPoint();
<span class="nc" id="L63">		double Size = 0.0;</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">		for (int i = 0; i &lt; this.regions.size(); i++) {</span>
<span class="nc" id="L65">			Size += this.regions.get(i).size();</span>
		}
<span class="nc" id="L67">		double T = random.nextDouble() * Size;</span>
<span class="nc" id="L68">		double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L69">		double PreIntegral = 0.0;</span>
<span class="nc" id="L70">		int temp = 0;</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">		for (int i = 0; i &lt; this.regions.size(); i++) {</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">			if (SumIntegral &lt; T) {</span>
<span class="nc" id="L73">				PreIntegral = SumIntegral;</span>
<span class="nc" id="L74">				temp = i;</span>
			}
<span class="nc" id="L76">			SumIntegral += this.regions.get(i).size();</span>
		}
		// 在temp处生成下一个随机点
<span class="nc" id="L79">		NPoint p = randomCreator.randomPoint(makeMaxRegionSmall(this.regions.get(temp)));</span>
		// NRectRegion tempRegion=region[temp];
		// return randomPoint(tempRegion);
<span class="nc" id="L82">		ArrayList list = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L83">		list.add(p);</span>
<span class="nc" id="L84">		list.add(temp);</span>
<span class="nc" id="L85">		return list;</span>
	}

	// 只生成测试用例的方法
	public NPoint generateTC() {
<span class="nc" id="L90">		NPoint p = null;</span>

<span class="nc bnc" id="L92" title="All 2 branches missed.">		if (this.tests.size() == 0) {</span>
<span class="nc" id="L93">			p = randomCreator.randomPoint();</span>
<span class="nc" id="L94">			this.regions.add(new NRectRegion(new NPoint(min), new NPoint(max)));</span>
<span class="nc" id="L95">			splitRegion(0, p);</span>
<span class="nc" id="L96">		} else {</span>
<span class="nc" id="L97">			ArrayList list = randomPoint();</span>
<span class="nc" id="L98">			p = (NPoint) list.get(0);</span>
<span class="nc" id="L99">			int index = (int) list.get(1);</span>
<span class="nc" id="L100">			splitRegion(index, p);</span>
		}
<span class="nc" id="L102">		this.tests.add(p);</span>

<span class="nc" id="L104">		return p;</span>
	}

	public NRectRegion makeMaxRegionSmall(NRectRegion maxregion) {
<span class="nc" id="L108">		double[] start = maxregion.getStart().getXn();</span>
<span class="nc" id="L109">		double[] end = maxregion.getEnd().getXn();</span>
<span class="nc" id="L110">		double[] s1 = new double[start.length];</span>
<span class="nc" id="L111">		double[] e1 = new double[end.length];</span>

		// double eachtemplength = 1.0 / (double)allParts;
<span class="nc bnc" id="L114" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L115">			double length = (end[i] - start[i]);</span>

<span class="nc" id="L117">			s1[i] = 0.5 * (end[i] + start[i]) - howManyParts * length * 0.5;</span>
<span class="nc" id="L118">			e1[i] = 0.5 * (end[i] + start[i]) + howManyParts * length * 0.5;</span>

		}

<span class="nc" id="L122">		return new NRectRegion(new NPoint(s1), new NPoint(e1));</span>

	}

	public void splitRegion(int index, NPoint p) {
<span class="nc bnc" id="L127" title="All 4 branches missed.">		if (index &lt; 0 || index &gt;= this.regions.size()) {</span>
<span class="nc" id="L128">			System.out.println(&quot;split region error! index not correct!&quot;);</span>
<span class="nc" id="L129">			return;</span>
		}
		// first remove it
<span class="nc" id="L132">		NRectRegion region = this.regions.remove(index);</span>
		try {
			// add regions;
<span class="nc" id="L135">			addRegionsInND(region, p);</span>
<span class="nc" id="L136">		} catch (Exception e) {</span>
<span class="nc" id="L137">			System.out.println(&quot;split region error in split region rec&quot;);</span>
		}
<span class="nc" id="L139">	}</span>

	public void addRegionsInND(NRectRegion region, NPoint p) throws Exception {
<span class="nc" id="L142">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L143">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L144">		double[] pxn = p.getXn();</span>
<span class="nc" id="L145">		List&lt;List&lt;Double&gt;&gt; result1 = splitRegions(start, pxn);</span>
<span class="nc" id="L146">		List&lt;List&lt;Double&gt;&gt; result2 = splitRegions(pxn, end);</span>
		// System.out.println(result1.size());
<span class="nc bnc" id="L148" title="All 2 branches missed.">		if (result1.size() != result2.size()) {</span>
<span class="nc" id="L149">			throw new Exception(&quot;result1's size!=result2's size ,split region wrong&quot;);</span>
		}
<span class="nc bnc" id="L151" title="All 2 branches missed.">		for (int i = 0; i &lt; result1.size(); i++) {</span>
<span class="nc" id="L152">			List&lt;Double&gt; temp1 = result1.get(i);</span>
<span class="nc" id="L153">			List&lt;Double&gt; temp2 = result2.get(i);</span>
<span class="nc" id="L154">			double[] newStart = new double[temp1.size()];</span>
<span class="nc" id="L155">			double[] newEnd = new double[temp2.size()];</span>
<span class="nc bnc" id="L156" title="All 2 branches missed.">			for (int j = 0; j &lt; temp1.size(); j++) {</span>
<span class="nc" id="L157">				newStart[j] = temp1.get(j);</span>
<span class="nc" id="L158">				newEnd[j] = temp2.get(j);</span>
			}

<span class="nc" id="L161">			NRectRegion tempRegion = new NRectRegion(new NPoint(newStart), new NPoint(newEnd));</span>
<span class="nc" id="L162">			this.regions.add(tempRegion);</span>
		}
<span class="nc" id="L164">	}</span>

	public List&lt;List&lt;Double&gt;&gt; splitRegions(double[] start, double[] end) {
<span class="nc" id="L167">		ArrayList&lt;double[]&gt; values = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L168" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L169">			double[] temp = new double[2];</span>

<span class="nc" id="L171">			temp[0] = start[i];</span>
<span class="nc" id="L172">			temp[1] = end[i];</span>
<span class="nc" id="L173">			values.add(temp);</span>
		}

<span class="nc" id="L176">		ArrayList&lt;List&lt;Double&gt;&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L177">		PaiLie.per(values, 0, new ArrayList&lt;&gt;(), result);</span>
<span class="nc" id="L178">		return result;</span>
	}

	public int maxRegion() {
<span class="nc" id="L182">		int index = 0;</span>
<span class="nc" id="L183">		double maxRegionSize = 0.0;</span>
<span class="nc bnc" id="L184" title="All 2 branches missed.">		for (int i = 0; i &lt; this.regions.size(); i++) {</span>
<span class="nc" id="L185">			double tempRegionSize = this.regions.get(i).size();</span>
<span class="nc bnc" id="L186" title="All 2 branches missed.">			if (tempRegionSize &gt; maxRegionSize) {</span>
<span class="nc" id="L187">				maxRegionSize = tempRegionSize;</span>
<span class="nc" id="L188">				index = i;</span>
			}
		}
<span class="nc" id="L191">		return index;</span>
	}

	public static void main(String[] args) {

<span class="nc" id="L196">		double howmany = 0.50;</span>

<span class="nc" id="L198">		int fm = 0;</span>
<span class="nc" id="L199">		int times = 2000;</span>

<span class="nc" id="L201">		int d = 2;</span>
<span class="nc" id="L202">		double failure_rate = 0.001;</span>

<span class="nc" id="L204">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L205">		double[] min = dataCreator.minCreator(d);</span>
<span class="nc" id="L206">		double[] max = dataCreator.maxCreator(d);</span>

<span class="nc bnc" id="L208" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L209">			FailurePattern pattern = new BlockPattern();</span>
<span class="nc" id="L210">			pattern.fail_rate = failure_rate;</span>
<span class="nc" id="L211">			ART_CenterAll_ND center = new ART_CenterAll_ND(min, max, new Random(i * 3), pattern);</span>
<span class="nc" id="L212">			center.howManyParts = howmany;</span>
<span class="nc" id="L213">			int temp = center.run();</span>
<span class="nc" id="L214">			fm += temp;</span>
		}
<span class="nc" id="L216">		System.out.println(fm / (double) times);</span>
<span class="nc" id="L217">	}</span>

	@Override
	public int em() {
		// TODO Auto-generated method stub
<span class="nc" id="L222">		return 0;</span>
	}

	@Override
	public NPoint generateNextTC() {
		// TODO Auto-generated method stub
<span class="nc" id="L228">		return null;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>