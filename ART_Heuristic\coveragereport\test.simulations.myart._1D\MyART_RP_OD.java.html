<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MyART_RP_OD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.myart._1D</a> &gt; <span class="el_source">MyART_RP_OD.java</span></div><h1>MyART_RP_OD.java</h1><pre class="source lang-java linenums">package test.simulations.myart._1D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class MyART_RP_OD {
<span class="nc" id="L9">	public static int ZUOQUXIAN = 1;</span>
<span class="nc" id="L10">	public static int YOUQUXIAN = 2;</span>
	public static void main(String[] args) {
<span class="nc" id="L12">		int times = 3000;</span>
<span class="nc" id="L13">		long sums = 0;</span>
<span class="nc" id="L14">		int temp = 0;</span>
<span class="nc" id="L15">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L16" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L17">			MyART_RP_OD myART_RP_OD = new MyART_RP_OD(0, 1, 0.001, 10, i * 3);</span>
<span class="nc" id="L18">			temp = myART_RP_OD.test();</span>
<span class="nc" id="L19">			sums += temp;</span>
		}
<span class="nc" id="L21">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L22">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L23">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L24">	}</span>
	double min;
	double max;
	int seed;
	double beta;
	double fail_rate;
	double fail_start;

<span class="nc" id="L32">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L34">	public MyART_RP_OD(double min, double max, double fail_rate, double beta, int seed) {</span>
<span class="nc" id="L35">		this.min = min;</span>
<span class="nc" id="L36">		this.max = max;</span>
<span class="nc" id="L37">		this.seed = seed;</span>
<span class="nc" id="L38">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L39">		this.beta = beta;</span>
<span class="nc" id="L40">	}</span>

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L43" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L44">			return false;</span>
		} else {
<span class="nc" id="L46">			return true;</span>
		}
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L51">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L52" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L53">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L54" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L55">				low = mid + 1;</span>
<span class="nc" id="L56">			} else {</span>
<span class="nc" id="L57">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L60" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L61">			mid = mid - 1;</span>
		}
<span class="nc" id="L63">		tests.add(mid + 1, p);</span>
<span class="nc" id="L64">	}</span>

	public int test() {
<span class="nc" id="L67">		Random random = new Random(seed);</span>
<span class="nc" id="L68">		int count = 0;</span>
<span class="nc" id="L69">		fail_start = random.nextDouble() * (max - min - fail_rate);</span>
<span class="nc" id="L70">		TestCase p = new TestCase();</span>
		// 执行第一个测试用例
<span class="nc" id="L72">		p.p = random.nextDouble() * (max - min) + min;</span>
		// System.out.println(&quot;po:&quot; + p.p);
<span class="nc bnc" id="L74" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L75">			count++;</span>
<span class="nc bnc" id="L76" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L77">				tests.add(p);</span>
<span class="nc" id="L78">			} else</span>
<span class="nc" id="L79">				sortTestCases(p);</span>
			double datum_line;// 基准线，待会求出
			// System.out.println(&quot;radius:&quot;+radius);
<span class="nc" id="L82">			ArrayList&lt;double[]&gt; integrals = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L83">			double[] informations = null;</span>
			/// 下面产生下一个测试用例,根据自己的概率曲线图
			// 先求第一段
<span class="nc" id="L86">			double e1 = tests.get(0).p;// first node</span>
<span class="nc" id="L87">			double en = tests.get(tests.size() - 1).p;// last node</span>
			// 求出最大的一段
			// from to 積分的上下限
<span class="nc" id="L90">			double from[] = null;</span>
<span class="nc" id="L91">			double to[] = null;</span>
<span class="nc" id="L92">			double tempInt[] = null;</span>
<span class="nc" id="L93">			double maxdistance = 0;</span>
			// 第一段
<span class="nc bnc" id="L95" title="All 2 branches missed.">			if (tests.size() == 1) {</span>
<span class="nc" id="L96">				maxdistance = max - min;</span>
<span class="nc" id="L97">				from = new double[] { min, e1 };</span>
<span class="nc" id="L98">				to = new double[] { e1, max };</span>
<span class="nc" id="L99">				tempInt = new double[] { Math.pow((tests.get(0).p - min), 2.0) / (beta + 1.0),</span>
<span class="nc" id="L100">						Math.pow((max - en), 2.0) / (beta + 1.0) };</span>
<span class="nc" id="L101">			} else {</span>
<span class="nc bnc" id="L102" title="All 2 branches missed.">				for (int i = 0; i &lt; tests.size(); i++) {</span>
					double distance;
<span class="nc bnc" id="L104" title="All 2 branches missed.">					if (i == 0) {</span>
<span class="nc" id="L105">						distance = (tests.get(1).p + e1) / 2.0 - min;</span>
<span class="nc bnc" id="L106" title="All 2 branches missed.">						if (distance &gt; maxdistance) {</span>
<span class="nc" id="L107">							maxdistance = distance;</span>
<span class="nc" id="L108">							from = new double[] { min, e1 };</span>
<span class="nc" id="L109">							to = new double[] { e1, (tests.get(1).p + e1) / 2.0 };</span>
<span class="nc" id="L110">							tempInt = new double[] { Math.pow((tests.get(0).p - min), 2.0) / (beta + 1.0),</span>
<span class="nc" id="L111">									Math.pow(((tests.get(i + 1).p - tests.get(i).p) / 2.0), 2.0)</span>
<span class="nc" id="L112">											* (1.0 / (beta + 1.0)) };</span>
						}
<span class="nc bnc" id="L114" title="All 2 branches missed.">					} else if (i == tests.size() - 1) {</span>
<span class="nc" id="L115">						distance = max - ((en + tests.get(tests.size() - 2).p) / 2.0);</span>
<span class="nc bnc" id="L116" title="All 2 branches missed.">						if (distance &gt; maxdistance) {</span>
<span class="nc" id="L117">							maxdistance = distance;</span>
<span class="nc" id="L118">							from = new double[] { (en + tests.get(tests.size() - 2).p) / 2.0, en };</span>
<span class="nc" id="L119">							to = new double[] { en, max };</span>
<span class="nc" id="L120">							tempInt = new double[] {</span>
<span class="nc" id="L121">									Math.pow(((tests.get(i).p - tests.get(i - 1).p) / 2.0), 2.0) * (1.0 / (beta + 1.0)),</span>
<span class="nc" id="L122">									Math.pow((max - tests.get(tests.size() - 1).p), 2.0) / (beta + 1.0) };</span>
						}
<span class="nc" id="L124">					} else {</span>
<span class="nc" id="L125">						double ei_1 = tests.get(i - 1).p;</span>
<span class="nc" id="L126">						double ei = tests.get(i).p;</span>
<span class="nc" id="L127">						double ei1 = tests.get(i + 1).p;</span>
<span class="nc" id="L128">						distance = (ei1 + ei) / 2.0 - ((ei + ei_1) / 2.0);</span>
<span class="nc bnc" id="L129" title="All 2 branches missed.">						if (distance &gt; maxdistance) {</span>
<span class="nc" id="L130">							maxdistance = distance;</span>
<span class="nc" id="L131">							from = new double[] { (ei_1 + ei) / 2.0, ei };</span>
<span class="nc" id="L132">							to = new double[] { ei, (ei + ei1) / 2.0 };</span>
<span class="nc" id="L133">							tempInt = new double[] {</span>
<span class="nc" id="L134">									Math.pow(((tests.get(i).p - tests.get(i - 1).p) / 2.0), 2.0) * (1.0 / (beta + 1.0)),</span>
<span class="nc" id="L135">									Math.pow(((tests.get(i + 1).p - tests.get(i).p) / 2.0), 2.0)</span>
<span class="nc" id="L136">											* (1.0 / (beta + 1.0)) };</span>
						}
					}

				}
			}
			// System.out.println(&quot;from:&quot; + (from == null) + &quot; to:&quot; + (to == null) + &quot;
			// temp:&quot; + (tempInt == null) + &quot; &quot;);
			// System.out.println(&quot;maxdistance:&quot; + maxdistance);
			// System.out.println(from[0] + &quot; -&gt; &quot; + to[0]);
			// System.out.println(from[1] + &quot; -&gt; &quot; + to[1]);
			// System.out.println(tempInt[0] + &quot; , &quot; + tempInt[1]);
<span class="nc" id="L148">			informations = new double[7];</span>
<span class="nc" id="L149">			informations[0] = tempInt[0];</span>
<span class="nc" id="L150">			informations[1] = ZUOQUXIAN;</span>
<span class="nc" id="L151">			informations[2] = from[0];</span>
<span class="nc" id="L152">			informations[3] = to[0];</span>
<span class="nc" id="L153">			informations[4] = beta;</span>
<span class="nc" id="L154">			integrals.add(informations);</span>
<span class="nc" id="L155">			informations = new double[7];</span>
<span class="nc" id="L156">			informations[0] = tempInt[1];</span>
<span class="nc" id="L157">			informations[1] = YOUQUXIAN;</span>
<span class="nc" id="L158">			informations[2] = from[1];</span>
<span class="nc" id="L159">			informations[3] = to[1];</span>
<span class="nc" id="L160">			informations[4] = beta;</span>
<span class="nc" id="L161">			integrals.add(informations);</span>
			// 求出int

<span class="nc" id="L164">			datum_line = 1.0 / (tempInt[0] + tempInt[1]);</span>
<span class="nc" id="L165">			double T = random.nextDouble() * 1.0;</span>
			// 确定在哪一段，然后求解出下一个点
<span class="nc" id="L167">			double SumIntegral = 0.0;</span>
<span class="nc" id="L168">			double PreIntegral = 0.0;</span>
<span class="nc" id="L169">			int temp = 0;</span>
			// 这里有问题，temp的值不一定就是前面一个的temp,理解错了，其实没有错
<span class="nc bnc" id="L171" title="All 2 branches missed.">			for (int i = 0; i &lt; integrals.size(); i++) {</span>
<span class="nc bnc" id="L172" title="All 2 branches missed.">				if (SumIntegral &lt; T) {</span>
<span class="nc" id="L173">					PreIntegral = SumIntegral;</span>
<span class="nc" id="L174">					temp = i;</span>
				}
<span class="nc" id="L176">				SumIntegral += integrals.get(i)[0] * datum_line;</span>
			}
			// 求下一个测试用例
<span class="nc" id="L179">			int type = (int) integrals.get(temp)[1];</span>
<span class="nc" id="L180">			double start = integrals.get(temp)[2];</span>
<span class="nc" id="L181">			double end = integrals.get(temp)[3];</span>
			// System.out.println(&quot;type:&quot;+type+&quot; start:&quot;+start+&quot; end:&quot;+end);
<span class="nc bnc" id="L183" title="All 2 branches missed.">			if (type == ZUOQUXIAN) {</span>
<span class="nc" id="L184">				double temp1 = end - start;</span>
<span class="nc" id="L185">				double temp2 = integrals.get(temp)[4] + 1.0;</span>
<span class="nc" id="L186">				p = new TestCase();</span>
<span class="nc" id="L187">				double temp3 = (1.0 - (T - PreIntegral) * (temp2) / ((datum_line) * (Math.pow(temp1, 2.0))));</span>
<span class="nc" id="L188">				p.p = end - temp1 * Math.pow(temp3, (1.0 / temp2));</span>
<span class="nc bnc" id="L189" title="All 4 branches missed.">				if (!(p.p &gt; start &amp;&amp; p.p &lt; end)) {</span>
<span class="nc" id="L190">					System.out.println(&quot;error left&quot;);</span>
				}
<span class="nc" id="L192">			} else {</span>
<span class="nc" id="L193">				double temp1 = end - start;</span>
<span class="nc" id="L194">				double temp2 = integrals.get(temp)[4] + 1.0;</span>
<span class="nc" id="L195">				p = new TestCase();</span>
<span class="nc" id="L196">				p.p = start + temp1</span>
<span class="nc" id="L197">						* Math.pow((T - PreIntegral) * temp2 / (datum_line * Math.pow(temp1, 2.0)), (1.0 / temp2));</span>
<span class="nc bnc" id="L198" title="All 4 branches missed.">				if (!(p.p &gt; start &amp;&amp; p.p &lt; end)) {</span>
<span class="nc" id="L199">					System.out.println(&quot;error right&quot;);</span>
				}
			}
<span class="nc bnc" id="L202" title="All 6 branches missed.">			if (Double.isNaN(p.p) || p.p &lt; min || p.p &gt; max) {</span>
<span class="nc" id="L203">				System.out.println(&quot;Interrupt!!&quot;);</span>
			}
		}
<span class="nc" id="L206">		return count;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>