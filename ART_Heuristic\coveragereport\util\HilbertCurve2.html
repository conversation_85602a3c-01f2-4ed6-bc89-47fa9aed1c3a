<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>HilbertCurve2</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">util</a> &gt; <span class="el_class">HilbertCurve2</span></div><h1>HilbertCurve2</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,102 of 2,102</td><td class="ctr2">0%</td><td class="bar">144 of 144</td><td class="ctr2">0%</td><td class="ctr1">90</td><td class="ctr2">90</td><td class="ctr1">335</td><td class="ctr2">335</td><td class="ctr1">15</td><td class="ctr2">15</td></tr></tfoot><tbody><tr><td id="a8"><a href="HilbertCurve2.java.html#L375" class="el_method">HilbertCode2Coordinates(String, long)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="444" alt="444"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="40" alt="40"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">21</td><td class="ctr2" id="g0">21</td><td class="ctr1" id="h0">81</td><td class="ctr2" id="i0">81</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="HilbertCurve2.java.html#L528" class="el_method">HilbertCode2Coordinates2(String, long)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="120" height="10" title="444" alt="444"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="120" height="10" title="40" alt="40"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">21</td><td class="ctr2" id="g1">21</td><td class="ctr1" id="h1">81</td><td class="ctr2" id="i1">81</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="HilbertCurve2.java.html#L111" class="el_method">convert_hilbert_key(long, int, RefObject, RefObject)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="81" height="10" title="300" alt="300"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="33" height="10" title="11" alt="11"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">8</td><td class="ctr2" id="g3">8</td><td class="ctr1" id="h3">34</td><td class="ctr2" id="i3">34</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="HilbertCurve2.java.html#L202" class="el_method">convert_hilbert_key_V1(long, int, RefObject, RefObject)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="78" height="10" title="290" alt="290"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="33" height="10" title="11" alt="11"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">8</td><td class="ctr2" id="g4">8</td><td class="ctr1" id="h2">35</td><td class="ctr2" id="i2">35</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a11"><a href="HilbertCurve2.java.html#L23" class="el_method">main(String[])</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="47" height="10" title="177" alt="177"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h6">12</td><td class="ctr2" id="i6">12</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="HilbertCurve2.java.html#L325" class="el_method">Double2Bin(double)</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="31" height="10" title="116" alt="116"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="48" height="10" title="16" alt="16"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f2">9</td><td class="ctr2" id="g2">9</td><td class="ctr1" id="h4">27</td><td class="ctr2" id="i4">27</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a6"><a href="HilbertCurve2.java.html#L301" class="el_method">decimal2Bin(String, int)</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="14" height="10" title="53" alt="53"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h5">13</td><td class="ctr2" id="i5">13</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a12"><a href="HilbertCurve2.java.html#L682" class="el_method">oneD_2_nD(double, int)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="12" height="10" title="46" alt="46"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h8">9</td><td class="ctr2" id="i8">9</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a13"><a href="HilbertCurve2.java.html#L706" class="el_method">oneD_2_nD2(double, int, long[])</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="12" height="10" title="46" alt="46"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"><img src="../.resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h9">9</td><td class="ctr2" id="i9">9</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a5"><a href="HilbertCurve2.java.html#L284" class="el_method">decimal2Bin(double, int)</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="11" height="10" title="43" alt="43"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"><img src="../.resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">3</td><td class="ctr2" id="g9">3</td><td class="ctr1" id="h7">12</td><td class="ctr2" id="i7">12</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a14"><a href="HilbertCurve2.java.html#L733" class="el_method">oneD_2_nD3(String, int)</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="11" height="10" title="41" alt="41"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d11"><img src="../.resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h11">7</td><td class="ctr2" id="i11">7</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a0"><a href="HilbertCurve2.java.html#L65" class="el_method">ADD(int, int, int, double[][], double[][])</a></td><td class="bar" id="b11"><img src="../.resources/redbar.gif" width="10" height="10" title="37" alt="37"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">3</td><td class="ctr2" id="i12">3</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a1"><a href="HilbertCurve2.java.html#L74" class="el_method">ADD(int, int, int, long[][], long[][])</a></td><td class="bar" id="b12"><img src="../.resources/redbar.gif" width="8" height="10" title="33" alt="33"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">3</td><td class="ctr2" id="i13">3</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a2"><a href="HilbertCurve2.java.html#L79" class="el_method">Bin2Double(String)</a></td><td class="bar" id="b13"><img src="../.resources/redbar.gif" width="7" height="10" title="29" alt="29"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d10"><img src="../.resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f10">3</td><td class="ctr2" id="g10">3</td><td class="ctr1" id="h10">8</td><td class="ctr2" id="i10">8</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a10"><a href="HilbertCurve2.java.html#L10" class="el_method">HilbertCurve2()</a></td><td class="bar" id="b14"/><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>