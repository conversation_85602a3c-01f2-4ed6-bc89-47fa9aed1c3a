<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>test.simulations.rrttp.partion</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <span class="el_package">test.simulations.rrttp.partion</span></div><h1>test.simulations.rrttp.partion</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">4,628 of 4,628</td><td class="ctr2">0%</td><td class="bar">254 of 254</td><td class="ctr2">0%</td><td class="ctr1">165</td><td class="ctr2">165</td><td class="ctr1">493</td><td class="ctr2">493</td><td class="ctr1">38</td><td class="ctr2">38</td><td class="ctr1">4</td><td class="ctr2">4</td></tr></tfoot><tbody><tr><td id="a3"><a href="RRTtpRegion.java.html" class="el_source">RRTtpRegion.java</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="2,569" alt="2,569"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="182" alt="182"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">100</td><td class="ctr2" id="g0">100</td><td class="ctr1" id="h0">168</td><td class="ctr2" id="i0">168</td><td class="ctr1" id="j2">9</td><td class="ctr2" id="k2">9</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a0"><a href="RRTtp2D.java.html" class="el_source">RRTtp2D.java</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="39" height="10" title="856" alt="856"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="23" height="10" title="36" alt="36"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">25</td><td class="ctr2" id="g1">25</td><td class="ctr1" id="h1">116</td><td class="ctr2" id="i1">116</td><td class="ctr1" id="j3">7</td><td class="ctr2" id="k3">7</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a1"><a href="RRTtpAll2D.java.html" class="el_source">RRTtpAll2D.java</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="29" height="10" title="624" alt="624"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="13" height="10" title="20" alt="20"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">21</td><td class="ctr2" id="g2">21</td><td class="ctr1" id="h2">108</td><td class="ctr2" id="i2">108</td><td class="ctr1" id="j0">11</td><td class="ctr2" id="k0">11</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a2"><a href="RRTtpMax2D.java.html" class="el_source">RRTtpMax2D.java</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="27" height="10" title="579" alt="579"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">19</td><td class="ctr2" id="g3">19</td><td class="ctr1" id="h3">101</td><td class="ctr2" id="i3">101</td><td class="ctr1" id="j1">11</td><td class="ctr2" id="k1">11</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>