<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>HilbertCurve.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util</a> &gt; <span class="el_source">HilbertCurve.java</span></div><h1>HilbertCurve.java</h1><pre class="source lang-java linenums">package util;

import java.util.ArrayList;

<span class="nc" id="L5">public class HilbertCurve {</span>

	// public final double[] oneD_2_twoD(double value_10jinzhi){
	// String bin_str=Double2Bin(value_10jinzhi);
	// RefObject&lt;Double&gt; x=new RefObject&lt;Double&gt;((double)-1);
	// RefObject&lt;Double&gt; y=new RefObject&lt;&gt;((double)-1);
	// int numOfbits=bin_str.length()-2;
	// String hiber_key_valid=null;
	// if(numOfbits&gt;32){numOfbits=32;}
	//
	// }
	// test only
	public static void main(String[] args) {
<span class="nc" id="L18">		HilbertCurve hilbertCurve = new HilbertCurve();</span>
<span class="nc" id="L19">		double results[] = hilbertCurve.oneD_2_nD(0.8886545227094472, 4);</span>
<span class="nc bnc" id="L20" title="All 2 branches missed.">		for (int i = 0; i &lt; results.length; i++) {</span>
<span class="nc" id="L21">			System.out.println(results[i]);</span>
		}
<span class="nc" id="L23">	}</span>

	public final void ADD(int dst, int u, int v, double[][] abcd, double[][] tmp) {
<span class="nc" id="L26">		abcd[(dst)][0] = (tmp[(u)][0] + tmp[v][0]) / 2.0;</span>
<span class="nc" id="L27">		abcd[(dst)][1] = (tmp[(u)][1] + tmp[(v)][1]) / 2.0;</span>
<span class="nc" id="L28">	}</span>

	// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no directv
	// equivalent in Java:
	// ORIGINAL LINE: public void ADD(int dst,int u, int v,uint[,] abcd,uint[,]
	// tmp)
	public final void ADD(int dst, int u, int v, int[][] abcd, int[][] tmp) {
<span class="nc" id="L35">		abcd[(dst)][0] = tmp[(u)][0] + tmp[v][0];</span>
<span class="nc" id="L36">		abcd[(dst)][1] = tmp[(u)][1] + tmp[(v)][1];</span>
<span class="nc" id="L37">	}</span>

	public final double Bin2Double(String bin_str) {
<span class="nc" id="L40">		double sum = 0;</span>
<span class="nc" id="L41">		double binbase = 1;</span>
<span class="nc" id="L42">		int len = bin_str.length();</span>
<span class="nc bnc" id="L43" title="All 2 branches missed.">		for (int i = 0; i &lt; len; i++) {</span>
<span class="nc" id="L44">			binbase = binbase * 0.5;</span>
<span class="nc bnc" id="L45" title="All 2 branches missed.">			if (bin_str.charAt(i) == '1') {</span>
<span class="nc" id="L46">				sum += binbase;</span>
			}

		}
<span class="nc" id="L50">		return sum;</span>

	}

	/**
	 * 0.[00001000]numbits为8，key为8
	 * 
	 * @param key
	 * @param numbits
	 * @param xx
	 * @param yy
	 */
	// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
	// equivalent in Java:
	// ORIGINAL LINE: public void convert_hilbert_key(uint key, int numbits, ref
	// uint xx,ref uint yy)
	public final void convert_hilbert_key(int key, int numbits, RefObject&lt;Integer&gt; xx, RefObject&lt;Integer&gt; yy) {

		// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
		// equivalent in Java:
		// ORIGINAL LINE: uint[,] abcd = new uint[4, 2] { { 0, 0 }, { 0, 1 }, { 1, 1 },
		// { 1, 0 } };
<span class="nc" id="L72">		int[][] abcd = new int[][] { { 0, 0 }, { 0, 1 }, { 1, 1 }, { 1, 0 } };</span>
		// ({0, 0}, {0,1}, {1, 1}, {1, 0});
		// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
		// equivalent in Java:
		// ORIGINAL LINE: uint[,] tmp = new uint[4, 2];
<span class="nc" id="L77">		int[][] tmp = new int[4][2];</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">		while (key &gt; 1) // should be &gt; 0, but this is safer for (invalid) odd numbers</span>
		{
			// uint[,] tmp=new uint[4,2]; /* save abcd here */
			byte subcell; // unsigned char subcell;
<span class="nc bnc" id="L82" title="All 2 branches missed.">			for (int j = 0; j &lt; 1; j++) {</span>
<span class="nc bnc" id="L83" title="All 2 branches missed.">				for (int k = 0; k &lt; 4; k++) {</span>
<span class="nc" id="L84">					tmp[k][j] = abcd[k][j];</span>
				}
			}
			// memcpy(tmp, abcd, sizeof tmp); /* save our 4 points with the new ones */
<span class="nc" id="L88">			numbits -= 2; // each subdivision decision takes 2 bits</span>
			// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no
			// direct equivalent in Java:
			// ORIGINAL LINE: uint u_subcell=(key &gt;&gt; numbits) &amp; 3;
<span class="nc" id="L92">			int u_subcell = (key &gt;&gt; numbits) &amp; 3;</span>
<span class="nc" id="L93">			subcell = Byte.parseByte((new Integer(u_subcell)).toString());</span>
			// namely these two (taken from the top)
<span class="nc" id="L95">			key &amp;= (int) ((1 &lt;&lt; numbits) - 1);</span>
			// update key by removing the bits we used
			// * Add the two points with indices u and v (in tmp) and store the result at
			// * index dst in abcd (for both x(0) and y(1)).
			// *
			/// #define ADD(dst, u, v) (abcd[(dst)][0] = tmp[(u)][0] +
			// tmp[(v)][0],
			// abcd[(dst)][1] = tmp[(u)][1] + tmp[(v)][1])

<span class="nc bnc" id="L104" title="All 5 branches missed.">			switch (subcell) {</span>
			// divide into subcells
			case 0:
				// h(key, numbits, a &lt;&lt; 1, a + d, a + c, a + b);
<span class="nc" id="L108">				ADD(0, 0, 0, abcd, tmp);</span>
<span class="nc" id="L109">				ADD(1, 0, 3, abcd, tmp);</span>
<span class="nc" id="L110">				ADD(2, 0, 2, abcd, tmp);</span>
<span class="nc" id="L111">				ADD(3, 0, 1, abcd, tmp);</span>
<span class="nc" id="L112">				break;</span>
			case 1:
				// h(key, numbits, b + a, b &lt;&lt; 1, b + c, b + d);
<span class="nc" id="L115">				ADD(0, 1, 0, abcd, tmp);</span>
<span class="nc" id="L116">				ADD(1, 1, 1, abcd, tmp);</span>
<span class="nc" id="L117">				ADD(2, 1, 2, abcd, tmp);</span>
<span class="nc" id="L118">				ADD(3, 1, 3, abcd, tmp);</span>
<span class="nc" id="L119">				break;</span>
			case 2:
				// h(key, numbits, c + a, c + b, c &lt;&lt; 1, c + d);
<span class="nc" id="L122">				ADD(0, 2, 0, abcd, tmp);</span>
<span class="nc" id="L123">				ADD(1, 2, 1, abcd, tmp);</span>
<span class="nc" id="L124">				ADD(2, 2, 2, abcd, tmp);</span>
<span class="nc" id="L125">				ADD(3, 2, 3, abcd, tmp);</span>
<span class="nc" id="L126">				break;</span>
			case 3:
				// h(key, numbits, d + c, d + b, d + a, d &lt;&lt; 1);
<span class="nc" id="L129">				ADD(0, 3, 2, abcd, tmp);</span>
<span class="nc" id="L130">				ADD(1, 3, 1, abcd, tmp);</span>
<span class="nc" id="L131">				ADD(2, 3, 0, abcd, tmp);</span>
<span class="nc" id="L132">				ADD(3, 3, 3, abcd, tmp);</span>
				break;
			}

			/// #undef ADD
		}
		// final result is the midpoint of the cell, i.e. (a + b + c + d) / 4
<span class="nc" id="L139">		xx.argvalue = (abcd[0][0] + abcd[1][0] + abcd[2][0] + abcd[3][0] + 1) &gt;&gt; 2;</span>
<span class="nc" id="L140">		yy.argvalue = (abcd[0][1] + abcd[1][1] + abcd[2][1] + abcd[3][1] + 1) &gt;&gt; 2;</span>
<span class="nc" id="L141">		System.out.println(</span>
<span class="nc" id="L142">				&quot;二维点为x=&quot; + (new Integer(xx.argvalue)).toString() + &quot;;y=&quot; + (new Integer(yy.argvalue)).toString());</span>
		// printf(&quot;x: %u y: %un&quot;, *xx, *yy);

<span class="nc" id="L145">	}</span>

	/**
	 * 0.[00001000]numbits为8，key为8
	 * 
	 * @param key
	 * @param numbits
	 * @param xx
	 * @param yy
	 */
	// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
	// equivalent in Java:
	// ORIGINAL LINE: public ArrayList convert_hilbert_key_V1(uint key, int
	// numbits, ref double xx,ref double yy)
	public final ArrayList convert_hilbert_key_V1(int key, int numbits, RefObject&lt;Double&gt; xx, RefObject&lt;Double&gt; yy) {

<span class="nc" id="L161">		double[][] abcd = new double[][] { { 0, 0 }, { 0, 1 }, { 1, 1 }, { 1, 0 } };</span>
		// ({0,0}, {0, 1}, {1, 1}, {1, 0});
		// unit square
<span class="nc" id="L164">		double[][] tmp = new double[4][2];</span>
<span class="nc" id="L165">		java.util.ArrayList nds = new java.util.ArrayList();</span>
<span class="nc bnc" id="L166" title="All 2 branches missed.">		while (numbits &gt; 1) // should be &gt; 0, but this is safer for (invalid) odd numbers</span>
		{
			// uint[,] tmp=new uint[4,2]; /* save abcd here */
			byte subcell; // unsigned char subcell;
<span class="nc bnc" id="L170" title="All 2 branches missed.">			for (int j = 0; j &lt; 2; j++) {</span>
<span class="nc bnc" id="L171" title="All 2 branches missed.">				for (int k = 0; k &lt; 4; k++) {</span>
<span class="nc" id="L172">					tmp[k][j] = abcd[k][j];</span>
				}
			}

			// memcpy(tmp, abcd, sizeof tmp); /* save our 4 points with the new
			// ones */

<span class="nc" id="L179">			numbits -= 2; // each subdivision decision takes 2 bits</span>
			// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no
			// direct equivalent in Java:
			// ORIGINAL LINE: uint u_subcell=(key &gt;&gt; numbits) &amp; 3;
<span class="nc" id="L183">			int u_subcell = (key &gt;&gt; numbits) &amp; 3;</span>
<span class="nc" id="L184">			subcell = Byte.parseByte((new Integer(u_subcell)).toString());</span>
			// namely these two (taken from the top)
			// key &amp;= (uint)((1&lt;&lt;numbits) - 1); /* update key by removing the
			// bits we used */

			// * Add the two points with indices u and v (in tmp) and store the
			// result at
			// * index dst in abcd (for both x(0) and y(1)).
			// *
			/// #define ADD(dst, u, v) (abcd[(dst)][0] = tmp[(u)][0] +
			// tmp[(v)][0],
			// abcd[(dst)][1] = tmp[(u)][1] + tmp[(v)][1])

<span class="nc bnc" id="L197" title="All 5 branches missed.">			switch (subcell) { // divide into subcells</span>
			case 0:
				// h(key, numbits, a &lt;&lt; 1, a + d, a + c, a + b);
<span class="nc" id="L200">				ADD(0, 0, 0, abcd, tmp);</span>
<span class="nc" id="L201">				ADD(1, 0, 3, abcd, tmp);</span>
<span class="nc" id="L202">				ADD(2, 0, 2, abcd, tmp);</span>
<span class="nc" id="L203">				ADD(3, 0, 1, abcd, tmp);</span>
<span class="nc" id="L204">				break;</span>
			case 1:
				// h(key, numbits, b + a, b &lt;&lt; 1, b + c, b + d);
<span class="nc" id="L207">				ADD(0, 1, 0, abcd, tmp);</span>
<span class="nc" id="L208">				ADD(1, 1, 1, abcd, tmp);</span>
<span class="nc" id="L209">				ADD(2, 1, 2, abcd, tmp);</span>
<span class="nc" id="L210">				ADD(3, 1, 3, abcd, tmp);</span>
<span class="nc" id="L211">				break;</span>
			case 2:
				// h(key, numbits, c + a, c + b, c &lt;&lt; 1, c + d);
<span class="nc" id="L214">				ADD(0, 2, 0, abcd, tmp);</span>
<span class="nc" id="L215">				ADD(1, 2, 1, abcd, tmp);</span>
<span class="nc" id="L216">				ADD(2, 2, 2, abcd, tmp);</span>
<span class="nc" id="L217">				ADD(3, 2, 3, abcd, tmp);</span>
<span class="nc" id="L218">				break;</span>
			case 3:
				// h(key, numbits, d + c, d + b, d + a, d &lt;&lt; 1);
<span class="nc" id="L221">				ADD(0, 3, 2, abcd, tmp);</span>
<span class="nc" id="L222">				ADD(1, 3, 1, abcd, tmp);</span>
<span class="nc" id="L223">				ADD(2, 3, 0, abcd, tmp);</span>
<span class="nc" id="L224">				ADD(3, 3, 3, abcd, tmp);</span>
				break;
			}

			/// #undef ADD
		}
		// final result is the midpoint of the cell, i.e. (a + b + c + d) / 4
<span class="nc" id="L231">		xx.argvalue = (abcd[0][0] + abcd[1][0] + abcd[2][0] + abcd[3][0]) / 4.0;</span>
<span class="nc" id="L232">		yy.argvalue = (abcd[0][1] + abcd[1][1] + abcd[2][1] + abcd[3][1]) / 4.0;</span>
<span class="nc" id="L233">		nds.add(xx.argvalue);</span>
<span class="nc" id="L234">		nds.add(yy.argvalue);</span>
<span class="nc" id="L235">		return nds;</span>
		// Console.WriteLine(&quot;二维点为x=&quot;+xx.ToString()+&quot;;y=&quot;+yy.ToString());
		// printf(&quot;x: %u y: %un&quot;, *xx, *yy);

	}

	/**
	 * 小于1的小数转换为二进制
	 * 
	 * @param val_double
	 * @return
	 */
	public final String Double2Bin(double val_double) {
<span class="nc" id="L248">		double ud = 2 * val_double;</span>
<span class="nc" id="L249">		StringBuilder bin_sb = new StringBuilder(&quot;&quot;);</span>
<span class="nc" id="L250">		double converVal = 0;</span>
		// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
		// equivalent in Java:
		// ORIGINAL LINE: uint i=1;
<span class="nc" id="L254">		int i = 1;</span>
<span class="nc" id="L255">		double eachbit = 0;</span>
<span class="nc" id="L256">		double resolution = 1e-22;</span>
<span class="nc bnc" id="L257" title="All 2 branches missed.">		if ((ud - 1) == 0) {</span>
<span class="nc" id="L258">			bin_sb.append(&quot;1&quot;);</span>

		}
<span class="nc bnc" id="L261" title="All 2 branches missed.">		while ((ud - 1) != 0) {</span>

<span class="nc bnc" id="L263" title="All 2 branches missed.">			if (ud &gt; 1) {</span>
<span class="nc" id="L264">				eachbit = 1;</span>
<span class="nc" id="L265">				bin_sb.append(&quot;1&quot;);</span>
<span class="nc bnc" id="L266" title="All 2 branches missed.">			} else if (ud &lt; 1) {</span>
<span class="nc" id="L267">				eachbit = 0;</span>
<span class="nc" id="L268">				bin_sb.append(&quot;0&quot;);</span>
			}
			// eachbit = Convert.ToDouble(ud.ToString().Substring(0, 1));
<span class="nc" id="L271">			converVal += eachbit * (1 / Math.pow(2, i));</span>
<span class="nc" id="L272">			i = i + 1;</span>

			// bin_sb.Append(eachbit.ToString().Substring(0,1));
<span class="nc bnc" id="L275" title="All 2 branches missed.">			if (ud &gt; 1) {</span>
<span class="nc" id="L276">				ud = (ud - 1) * 2;</span>
<span class="nc bnc" id="L277" title="All 2 branches missed.">			} else if (ud &lt; 1) {</span>
<span class="nc" id="L278">				ud = ud * 2;</span>
			}
<span class="nc bnc" id="L280" title="All 2 branches missed.">			if ((ud - 1) == 0) {</span>
<span class="nc" id="L281">				bin_sb.append(&quot;1&quot;);</span>
<span class="nc" id="L282">				break;</span>

			}

<span class="nc bnc" id="L286" title="All 2 branches missed.">			if (Math.abs(converVal - val_double) &lt; resolution) // 达到一定的精度</span>
			{

<span class="nc" id="L289">				break;</span>
			}

		}
<span class="nc" id="L293">		return &quot;0.&quot; + bin_sb.toString();</span>
		// Convert.ToUInt32(bin_sb.ToString(), 2);
	}

	/**
	 * 将一维转换为多维
	 * 
	 * @param hilbercode
	 * @param dim
	 * @return
	 */
	// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
	// equivalent in Java:
	// ORIGINAL LINE: public string[] HilbertCode2Coordinates(string
	// hilbercode,uint dim)
	public final String[] HilbertCode2Coordinates(String hilbercode, int dim) {
<span class="nc" id="L309">		final int Wordbits = 32;</span>
<span class="nc" id="L310">		final int OrderOfHilbert = 32;</span>
<span class="nc" id="L311">		int ndimbits = hilbercode.length();</span>
<span class="nc" id="L312">		int dim_int = Integer.parseInt((new Integer(dim)).toString());</span>

		// 填充补0使得hilbertcode为dim的整数倍位
<span class="nc" id="L315">		int paddings = -1;</span>
<span class="nc" id="L316">		int intNdim = ndimbits / dim_int;</span>
<span class="nc bnc" id="L317" title="All 2 branches missed.">		if ((ndimbits % dim_int) != 0) {</span>

<span class="nc" id="L319">			ndimbits = intNdim * dim_int + dim_int;</span>
<span class="nc" id="L320">			paddings = ndimbits - hilbercode.length();</span>
<span class="nc bnc" id="L321" title="All 2 branches missed.">			for (int kindex = 0; kindex &lt; paddings; kindex++) {</span>
<span class="nc" id="L322">				hilbercode += &quot;0&quot;;</span>
			}

		}
<span class="nc" id="L326">		String[] point = new String[dim];</span>

		// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
		// equivalent in Java:
		// ORIGINAL LINE: uint mask = (uint)1 &lt;&lt; Wordbits - 1;
<span class="nc" id="L331">		int mask = (int) 1 &lt;&lt; Wordbits - 1; // 31个1</span>
		// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
		// equivalent in Java:
		// ORIGINAL LINE: uint element, temp1, temp2, A, W = 0, S, tS, T, tT, J,
		// P = 0, xJ;
<span class="nc" id="L336">		int element, temp1, temp2, A, W = 0, S, tS, T, tT, J, P = 0, xJ;</span>

<span class="nc" id="L338">		int i = 0, j;</span>
<span class="nc bnc" id="L339" title="All 2 branches missed.">		for (int kindex = 0; kindex &lt; dim; kindex++) {</span>
<span class="nc" id="L340">			point[kindex] = &quot;&quot;;</span>
		}

		// --- P ---
<span class="nc" id="L344">		String p_str = hilbercode.substring(i, i + dim_int);</span>
<span class="nc" id="L345">		P = Integer.parseInt(p_str, 2);</span>
		// --- xJ ---
<span class="nc" id="L347">		J = dim;</span>
<span class="nc bnc" id="L348" title="All 2 branches missed.">		for (j = 1; j &lt; dim_int; j++) {</span>
<span class="nc bnc" id="L349" title="All 2 branches missed.">			if ((P &gt;&gt; j &amp; 1) == (P &amp; 1)) {</span>
				continue;
			} else {
				break;
			}
		}
<span class="nc bnc" id="L355" title="All 2 branches missed.">		if (j != dim_int) {</span>
<span class="nc" id="L356">			J = J - Integer.parseInt((new Integer(j)).toString());</span>
		}
<span class="nc" id="L358">		xJ = J - 1;</span>

		// --- S, tS, A ---
<span class="nc" id="L361">		A = S = tS = P ^ (P / 2); // 异或运算</span>

		// --- T ---
<span class="nc bnc" id="L364" title="All 2 branches missed.">		if (P &lt; 3) {</span>
<span class="nc" id="L365">			T = 0;</span>
<span class="nc" id="L366">		} else {</span>
<span class="nc bnc" id="L367" title="All 2 branches missed.">			if (P % 2 != 0) {</span>
<span class="nc" id="L368">				T = (P - 1) ^ (P - 1) / 2;</span>
<span class="nc" id="L369">			} else {</span>
<span class="nc" id="L370">				T = (P - 2) ^ (P - 2) / 2;</span>
			}
		}

		// --- tT ---
<span class="nc" id="L375">		tT = T;</span>

		// --- distrib bits to coords ---
		// for (j = DIM - 1; P &gt; 0; P &gt;&gt;=1, j--)
		// if (P &amp; 1)
		// pt.hcode[j] |= mask;
<span class="nc bnc" id="L381" title="All 2 branches missed.">		for (j = dim_int - 1; j &gt;= 0; A &gt;&gt;= 1, j--) {</span>
<span class="nc bnc" id="L382" title="All 2 branches missed.">			if ((A &amp; 1) != 0) {</span>
<span class="nc" id="L383">				point[j] = point[j] + &quot;1&quot;;</span>
<span class="nc" id="L384">			} else {</span>
<span class="nc" id="L385">				point[j] = point[j] + &quot;0&quot;;</span>
			}
		}

<span class="nc" id="L389">		int noOfshiftbits = 0;</span>
<span class="nc bnc" id="L390" title="All 2 branches missed.">		for (i = dim_int, mask &gt;&gt;= 1; i &lt; ndimbits; i = i + dim_int, mask &gt;&gt;= 1) {</span>
			// --- P ---
<span class="nc" id="L392">			p_str = hilbercode.substring(i, i + dim_int);</span>
<span class="nc" id="L393">			P = Integer.parseInt(p_str, 2);</span>

			// --- S ---
<span class="nc" id="L396">			S = P ^ (P / 2);</span>

			// --- tS ---
<span class="nc" id="L399">			noOfshiftbits = (int) (xJ % dim);</span>
<span class="nc bnc" id="L400" title="All 2 branches missed.">			if (xJ % dim != 0) {</span>
<span class="nc" id="L401">				temp1 = S &gt;&gt; (noOfshiftbits);</span>
<span class="nc" id="L402">				temp2 = S &lt;&lt; (dim_int - noOfshiftbits);</span>
<span class="nc" id="L403">				tS = temp1 | temp2;</span>
<span class="nc" id="L404">				tS &amp;= ((int) 1 &lt;&lt; dim_int) - 1;</span>
<span class="nc" id="L405">			} else {</span>
<span class="nc" id="L406">				tS = S;</span>
			}

			// --- W ---
<span class="nc" id="L410">			W ^= tT;</span>

			// --- A ---
<span class="nc" id="L413">			A = W ^ tS;</span>

			// --- distrib bits to coords ---
<span class="nc bnc" id="L416" title="All 2 branches missed.">			for (j = dim_int - 1; j &gt;= 0; A &gt;&gt;= 1, j--) {</span>
				// if ((A &amp; 1)!=0)
				// point[j] |= mask;
<span class="nc bnc" id="L419" title="All 2 branches missed.">				if ((A &amp; 1) != 0) {</span>
<span class="nc" id="L420">					point[j] = point[j] + &quot;1&quot;;</span>
<span class="nc" id="L421">				} else {</span>
<span class="nc" id="L422">					point[j] = point[j] + &quot;0&quot;;</span>
				}
			}

			// --- T ---
<span class="nc bnc" id="L427" title="All 2 branches missed.">			if (P &lt; 3) {</span>
<span class="nc" id="L428">				T = 0;</span>
<span class="nc" id="L429">			} else {</span>
<span class="nc bnc" id="L430" title="All 2 branches missed.">				if (P % 2 != 0) {</span>
<span class="nc" id="L431">					T = (P - 1) ^ (P - 1) / 2;</span>
<span class="nc" id="L432">				} else {</span>
<span class="nc" id="L433">					T = (P - 2) ^ (P - 2) / 2;</span>
				}
			}

			// --- tT ---
<span class="nc" id="L438">			noOfshiftbits = (int) (xJ % dim);</span>
<span class="nc bnc" id="L439" title="All 2 branches missed.">			if (xJ % dim != 0) {</span>
<span class="nc" id="L440">				temp1 = T &gt;&gt; noOfshiftbits;</span>
<span class="nc" id="L441">				temp2 = T &lt;&lt; (dim_int - noOfshiftbits);</span>
<span class="nc" id="L442">				tT = temp1 | temp2;</span>
<span class="nc" id="L443">				tT &amp;= ((int) 1 &lt;&lt; dim_int) - 1;</span>
<span class="nc" id="L444">			} else {</span>
<span class="nc" id="L445">				tT = T;</span>
			}

			// --- xJ ---
<span class="nc" id="L449">			J = dim;</span>
<span class="nc bnc" id="L450" title="All 2 branches missed.">			for (j = 1; j &lt; dim; j++) {</span>
<span class="nc bnc" id="L451" title="All 2 branches missed.">				if ((P &gt;&gt; j &amp; 1) == (P &amp; 1)) {</span>
					continue;
				} else {
					break;
				}
			}
<span class="nc bnc" id="L457" title="All 2 branches missed.">			if (j != dim) {</span>
<span class="nc" id="L458">				J -= (int) j;</span>
			}
<span class="nc" id="L460">			xJ += J - 1;</span>

		}
		// for (int kindex = 0; kindex &lt; dim_int; kindex++)
		// {
		// // point[kindex] = point[kindex] &gt;&gt; 27;
		// Console.WriteLine(kindex.ToString() + &quot;:&quot; +
		// point[kindex].ToString());
		// }
<span class="nc" id="L469">		return point;</span>

	}

	//// 下面这个方法将求解一维到多维进行封装 by XJX
	public final double[] oneD_2_nD(double value_10jinzhi, int dim) {
<span class="nc" id="L475">		double[] curvals = new double[dim];</span>
<span class="nc" id="L476">		String bin_str = Double2Bin(value_10jinzhi);</span>
<span class="nc" id="L477">		int numOfbits = bin_str.length() - 2;</span>
<span class="nc bnc" id="L478" title="All 2 branches missed.">		if (numOfbits &gt; 32) {</span>
<span class="nc" id="L479">			numOfbits = 32;</span>
		}
<span class="nc" id="L481">		String[] pointOfstrs = HilbertCode2Coordinates(bin_str.substring(2, 2 + numOfbits), (int) dim);</span>

<span class="nc bnc" id="L483" title="All 2 branches missed.">		for (int i = 0; i &lt; dim; i++) {</span>
<span class="nc" id="L484">			curvals[i] = Bin2Double(pointOfstrs[i]);</span>
		}
<span class="nc" id="L486">		return curvals;</span>
	}
}

// ----------------------------------------------------------------------------------------
// Copyright © 2006 - 2010 Tangible Software Solutions Inc.
// This class can be used by anyone provided that the copyright notice remains
// intact.
//
// This class is used to simulate the ability to pass arguments by reference in
// Java.
// ----------------------------------------------------------------------------------------
final class RefObject&lt;T&gt; {
	public T argvalue;

<span class="nc" id="L501">	public RefObject(T refarg) {</span>
<span class="nc" id="L502">		argvalue = refarg;</span>
<span class="nc" id="L503">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>