<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>X3.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util</a> &gt; <span class="el_source">X3.java</span></div><h1>X3.java</h1><pre class="source lang-java linenums">package util;

<span class="nc" id="L3">public class X3 {</span>
	public static double[] shengjinFormula(double acof, double bcof, double cof, double dof) {
<span class="nc" id="L5">		double A = bcof * bcof - 3.0 * acof * cof;// A=b^2-3ac</span>
<span class="nc" id="L6">		double B = bcof * cof - 9.0 * acof * dof;// B=bc-9ad</span>
<span class="nc" id="L7">		double C = cof * cof - 3.0 * bcof * dof;// C=c^2-3bd</span>
<span class="nc" id="L8">		double delta = B * B - 4.0 * A * C;</span>
<span class="nc" id="L9">		double root = 0.0;</span>
<span class="nc" id="L10">		double r1 = 0.0;</span>
<span class="nc" id="L11">		double r2 = 0.0;</span>
<span class="nc" id="L12">		double[] roots = new double[3];</span>
<span class="nc bnc" id="L13" title="All 2 branches missed.">		if (delta &gt; 0) {</span>
<span class="nc" id="L14">			double Y1 = A * bcof + 3.0 * acof * (-B + Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
<span class="nc" id="L15">			double Y2 = A * bcof + 3.0 * acof * (-B - Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
			double powY1;
			double powY2;
<span class="nc bnc" id="L18" title="All 2 branches missed.">			if (Y1 &lt; 0) {</span>
<span class="nc" id="L19">				powY1 = -Math.pow(-Y1, 1.0 / 3.0);</span>
<span class="nc" id="L20">			} else {</span>
<span class="nc" id="L21">				powY1 = Math.pow(Y1, 1.0 / 3.0);</span>
			}
<span class="nc bnc" id="L23" title="All 2 branches missed.">			if (Y2 &lt; 0) {</span>
<span class="nc" id="L24">				powY2 = -Math.pow(-Y2, 1.0 / 3.0);</span>
<span class="nc" id="L25">			} else {</span>
<span class="nc" id="L26">				powY2 = Math.pow(Y2, 1.0 / 3.0);</span>
			}
<span class="nc" id="L28">			root = (-bcof - powY1 - powY2) / (3.0 * acof);</span>
<span class="nc" id="L29">			r1 = root;</span>
<span class="nc" id="L30">			r2 = root;</span>
<span class="nc bnc" id="L31" title="All 2 branches missed.">		} else if (delta == 0) {</span>
<span class="nc" id="L32">			root = -bcof / acof + B / A;</span>
<span class="nc" id="L33">			r1 = -B / (2.0 * A);</span>
<span class="nc" id="L34">			r2 = r1;</span>

<span class="nc bnc" id="L36" title="All 2 branches missed.">		} else if (delta &lt; 0) {</span>
<span class="nc" id="L37">			double T = (2.0 * A * bcof - 3.0 * acof * B) / (2.0 * Math.pow(A, 3.0 / 2.0));</span>
<span class="nc" id="L38">			double theta = Math.acos(T);</span>
<span class="nc" id="L39">			root = (-bcof - 2.0 * Math.sqrt(A) * Math.cos(theta / 3.0)) / (3.0 * acof);</span>
<span class="nc" id="L40">			r1 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) + Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L41">					/ (3.0 * acof);</span>
<span class="nc" id="L42">			r2 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) - Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L43">					/ (3.0 * acof);</span>
		}
<span class="nc" id="L45">		roots[0] = root;</span>
<span class="nc" id="L46">		roots[1] = r1;</span>
<span class="nc" id="L47">		roots[2] = r2;</span>
<span class="nc" id="L48">		return roots;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>