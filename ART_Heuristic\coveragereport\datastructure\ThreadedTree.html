<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ThreadedTree</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">datastructure</a> &gt; <span class="el_class">ThreadedTree</span></div><h1>ThreadedTree</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">450 of 450</td><td class="ctr2">0%</td><td class="bar">82 of 82</td><td class="ctr2">0%</td><td class="ctr1">46</td><td class="ctr2">46</td><td class="ctr1">140</td><td class="ctr2">140</td><td class="ctr1">5</td><td class="ctr2">5</td></tr></tfoot><tbody><tr><td id="a0"><a href="ThreadedTree.java.html#L28" class="el_method">delete(double)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="199" alt="199"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="52" alt="52"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">27</td><td class="ctr2" id="g0">27</td><td class="ctr1" id="h0">59</td><td class="ctr2" id="i0">59</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="ThreadedTree.java.html#L105" class="el_method">insert(double)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="103" height="10" title="171" alt="171"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="41" height="10" title="18" alt="18"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">10</td><td class="ctr2" id="g1">10</td><td class="ctr1" id="h1">54</td><td class="ctr2" id="i1">54</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="ThreadedTree.java.html#L171" class="el_method">search(double)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="30" height="10" title="51" alt="51"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="27" height="10" title="12" alt="12"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g2">7</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i2">16</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="ThreadedTree.java.html#L12" class="el_method">main(String[])</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="13" height="10" title="23" alt="23"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">8</td><td class="ctr2" id="i3">8</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="ThreadedTree.java.html#L23" class="el_method">ThreadedTree()</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="3" height="10" title="6" alt="6"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">3</td><td class="ctr2" id="i4">3</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>