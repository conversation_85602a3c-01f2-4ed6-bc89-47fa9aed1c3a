<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>datastructure.failurepattern.impl</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <span class="el_package">datastructure.failurepattern.impl</span></div><h1>datastructure.failurepattern.impl</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">3,612 of 3,692</td><td class="ctr2">2%</td><td class="bar">219 of 223</td><td class="ctr2">2%</td><td class="ctr1">196</td><td class="ctr2">200</td><td class="ctr1">560</td><td class="ctr2">570</td><td class="ctr1">73</td><td class="ctr2">75</td><td class="ctr1">9</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a9"><a href="StripPatternIn2D.java.html" class="el_source">StripPatternIn2D.java</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="1,067" alt="1,067"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="78" height="10" title="40" alt="40"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">44</td><td class="ctr2" id="g0">44</td><td class="ctr1" id="h0">150</td><td class="ctr2" id="i0">150</td><td class="ctr1" id="j0">19</td><td class="ctr2" id="k0">19</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a2"><a href="PointPattern.java.html" class="el_source">PointPattern.java</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="53" height="10" title="477" alt="477"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="90" height="10" title="46" alt="46"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">32</td><td class="ctr2" id="g2">32</td><td class="ctr1" id="h2">73</td><td class="ctr2" id="i2">73</td><td class="ctr1" id="j5">6</td><td class="ctr2" id="k5">6</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a6"><a href="PointPatternIn4D.java.html" class="el_source">PointPatternIn4D.java</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="45" height="10" title="403" alt="403"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="19" height="10" title="10" alt="10"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">13</td><td class="ctr2" id="g3">13</td><td class="ctr1" id="h3">54</td><td class="ctr2" id="i3">54</td><td class="ctr1" id="j1">8</td><td class="ctr2" id="k1">8</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a5"><a href="PointPatternIn3D.java.html" class="el_source">PointPatternIn3D.java</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="40" height="10" title="359" alt="359"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="19" height="10" title="10" alt="10"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">13</td><td class="ctr2" id="g4">13</td><td class="ctr1" id="h4">51</td><td class="ctr2" id="i4">51</td><td class="ctr1" id="j2">8</td><td class="ctr2" id="k2">8</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a4"><a href="PointPatternIn2D.java.html" class="el_source">PointPatternIn2D.java</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="35" height="10" title="312" alt="312"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="19" height="10" title="10" alt="10"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">13</td><td class="ctr2" id="g5">13</td><td class="ctr1" id="h5">48</td><td class="ctr2" id="i5">48</td><td class="ctr1" id="j3">8</td><td class="ctr2" id="k3">8</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a7"><a href="RealityFailPattern.java.html" class="el_source">RealityFailPattern.java</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="34" height="10" title="305" alt="305"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="61" alt="61"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f1">40</td><td class="ctr2" id="g1">40</td><td class="ctr1" id="h1">77</td><td class="ctr2" id="i1">77</td><td class="ctr1" id="j7">4</td><td class="ctr2" id="k8">4</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a8"><a href="StripPattern.java.html" class="el_source">StripPattern.java</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="24" height="10" title="215" alt="215"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d8"><img src="../.resources/redbar.gif" width="15" height="10" title="8" alt="8"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f8">9</td><td class="ctr2" id="g9">9</td><td class="ctr1" id="h6">32</td><td class="ctr2" id="i7">32</td><td class="ctr1" id="j6">5</td><td class="ctr2" id="k6">5</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a0"><a href="BlockPattern.java.html" class="el_source">BlockPattern.java</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="18" height="10" title="165" alt="165"/><img src="../.resources/greenbar.gif" width="8" height="10" title="80" alt="80"/></td><td class="ctr2" id="c0">33%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="23" height="10" title="12" alt="12"/><img src="../.resources/greenbar.gif" width="7" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">25%</td><td class="ctr1" id="f9">9</td><td class="ctr2" id="g6">13</td><td class="ctr1" id="h8">25</td><td class="ctr2" id="i6">35</td><td class="ctr1" id="j9">3</td><td class="ctr2" id="k7">5</td><td class="ctr1" id="l9">0</td><td class="ctr2" id="m7">1</td></tr><tr><td id="a1"><a href="MultiBlockPattern.java.html" class="el_source">MultiBlockPattern.java</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="18" height="10" title="164" alt="164"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="27" height="10" title="14" alt="14"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f7">11</td><td class="ctr2" id="g8">11</td><td class="ctr1" id="h9">23</td><td class="ctr2" id="i9">23</td><td class="ctr1" id="j8">4</td><td class="ctr2" id="k9">4</td><td class="ctr1" id="l7">1</td><td class="ctr2" id="m8">1</td></tr><tr><td id="a3"><a href="PointPatternIn1D.java.html" class="el_source">PointPatternIn1D.java</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="16" height="10" title="145" alt="145"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"><img src="../.resources/redbar.gif" width="15" height="10" title="8" alt="8"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f6">12</td><td class="ctr2" id="g7">12</td><td class="ctr1" id="h7">27</td><td class="ctr2" id="i8">27</td><td class="ctr1" id="j4">8</td><td class="ctr2" id="k4">8</td><td class="ctr1" id="l8">1</td><td class="ctr2" id="m9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>