<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>Solution3</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.art_tp._2D</a> &gt; <span class="el_class">Solution3</span></div><h1>Solution3</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,294 of 1,294</td><td class="ctr2">0%</td><td class="bar">56 of 56</td><td class="ctr2">0%</td><td class="ctr1">40</td><td class="ctr2">40</td><td class="ctr1">178</td><td class="ctr2">178</td><td class="ctr1">12</td><td class="ctr2">12</td></tr></tfoot><tbody><tr><td id="a9"><span class="el_method">run()</span></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="292" alt="292"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h0">38</td><td class="ctr2" id="i0">38</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><span class="el_method">addFromAndTo(TPInfo)</span></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="81" height="10" title="199" alt="199"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h1">25</td><td class="ctr2" id="i1">25</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><span class="el_method">addTDRegions(double[], double[], NPoint)</span></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="74" height="10" title="181" alt="181"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h2">21</td><td class="ctr2" id="i2">21</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a10"><span class="el_method">Solution3(double[], double[], double, long)</span></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="47" height="10" title="115" alt="115"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h3">19</td><td class="ctr2" id="i3">19</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a3"><span class="el_method">calEachRegion()</span></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="44" height="10" title="108" alt="108"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h5">13</td><td class="ctr2" id="i5">13</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><span class="el_method">genNextEachDimension(double, double, double, double, double, double, double, double)</span></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="42" height="10" title="103" alt="103"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="96" height="10" title="8" alt="8"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h4">18</td><td class="ctr2" id="i4">18</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a11"><span class="el_method">updateRegions(NPoint)</span></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="25" height="10" title="62" alt="62"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h6">11</td><td class="ctr2" id="i6">11</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a7"><span class="el_method">main(String[])</span></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="25" height="10" title="61" alt="61"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d8"><img src="../.resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h7">10</td><td class="ctr2" id="i7">10</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a2"><span class="el_method">calEachIntEC2(double, double, double, double)</span></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="20" height="10" title="50" alt="50"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a8"><span class="el_method">randomTC()</span></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="18" height="10" title="46" alt="46"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"><img src="../.resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h9">7</td><td class="ctr2" id="i9">7</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a6"><span class="el_method">isInRegion(NPoint, NPoint, NPoint)</span></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="16" height="10" title="39" alt="39"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h8">8</td><td class="ctr2" id="i8">8</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a5"><span class="el_method">isCorrect(NPoint)</span></td><td class="bar" id="b11"><img src="../.resources/redbar.gif" width="15" height="10" title="38" alt="38"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h10">6</td><td class="ctr2" id="i10">6</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>