<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRT_DC</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.art_dc</a> &gt; <span class="el_class">RRT_DC</span></div><h1>RRT_DC</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">887 of 887</td><td class="ctr2">0%</td><td class="bar">50 of 50</td><td class="ctr2">0%</td><td class="ctr1">38</td><td class="ctr2">38</td><td class="ctr1">161</td><td class="ctr2">161</td><td class="ctr1">13</td><td class="ctr2">13</td></tr></tfoot><tbody><tr><td id="a1"><a href="RRT_DC.java.html#L64" class="el_method">addRegionsIn2D(NRectRegion)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="185" alt="185"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h0">21</td><td class="ctr2" id="i0">21</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a3"><a href="RRT_DC.java.html#L116" class="el_method">calculateRadius(NRectRegion, int)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="75" height="10" title="117" alt="117"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h3">17</td><td class="ctr2" id="i3">17</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="RRT_DC.java.html#L91" class="el_method">addRegionsInND(NRectRegion, NPoint)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="64" height="10" title="99" alt="99"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h2">18</td><td class="ctr2" id="i2">18</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a11"><a href="RRT_DC.java.html#L235" class="el_method">run()</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="57" height="10" title="88" alt="88"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h1">20</td><td class="ctr2" id="i1">20</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a8"><a href="RRT_DC.java.html#L188" class="el_method">randomTCByRRT(NRectRegion, ArrayList)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="48" height="10" title="74" alt="74"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">5</td><td class="ctr2" id="g1">5</td><td class="ctr1" id="h4">16</td><td class="ctr2" id="i4">16</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a6"><a href="RRT_DC.java.html#L22" class="el_method">main(String[])</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="42" height="10" title="65" alt="65"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h5">15</td><td class="ctr2" id="i5">15</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a4"><a href="RRT_DC.java.html#L144" class="el_method">findLeastTestCaseInD(ArrayList)</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="35" height="10" title="55" alt="55"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h6">12</td><td class="ctr2" id="i6">12</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a9"><a href="RRT_DC.java.html#L220" class="el_method">randomTCByRT(NRectRegion, ArrayList)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="33" height="10" title="51" alt="51"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h7">9</td><td class="ctr2" id="i7">9</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a12"><a href="RRT_DC.java.html#L276" class="el_method">splitRegions(double[], double[])</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="28" height="10" title="44" alt="44"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h8">9</td><td class="ctr2" id="i8">9</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a5"><a href="RRT_DC.java.html#L164" class="el_method">isTCInD(NRectRegion, NPoint)</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="27" height="10" title="42" alt="42"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h9">9</td><td class="ctr2" id="i9">9</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a7"><a href="RRT_DC.java.html#L178" class="el_method">midPoint(NPoint, NPoint)</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="22" height="10" title="35" alt="35"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d9"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h10">6</td><td class="ctr2" id="i10">6</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a10"><a href="RRT_DC.java.html#L43" class="el_method">RRT_DC(double[], double[], FailurePattern, Random, double, int)</a></td><td class="bar" id="b11"><img src="../.resources/redbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h12">4</td><td class="ctr2" id="i12">4</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a0"><a href="RRT_DC.java.html#L55" class="el_method">addNewRegions(ArrayList, NRectRegion)</a></td><td class="bar" id="b12"><img src="../.resources/redbar.gif" width="10" height="10" title="16" alt="16"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h11">5</td><td class="ctr2" id="i11">5</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>