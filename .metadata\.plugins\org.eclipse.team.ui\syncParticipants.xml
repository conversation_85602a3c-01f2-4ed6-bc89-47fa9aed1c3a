<?xml version="1.0" encoding="UTF-8"?>
<syncparticipants>
<participant displayName="Git (ART-ORBOFF)" id="org.eclipse.egit.ui.modelCompareParticipant" secondary_id="1534668700960">
<data forceFetch="false" root="false">
<org.eclipse.team.ui.MODEL_PARTICIPANT_SETTINGS org.eclipse.team.ui.MODEL_PARTICIPANT_DESCRIPTION="ART-ORBOFF" startupAction="none">
<org.eclipse.team.ui.MODEL_PARTICIPANT_REFRESH_SCHEDULE org.eclipse.team.ui.CTX_REFRESHSCHEDULE_ENABLED="false" org.eclipse.team.ui.CTX_REFRESHSCHEDULE_INTERVAL="3600" org.eclipse.team.ui.CTX_REFRESHSCHEDULE_RUNONCE="false"/>
<org.eclipse.team.ui.MODEL_PARTICIPANT_MAPPINGS modelProviderId="org.eclipse.core.resources.modelProvider">
<mappings>
<resources resourcePath="/ART-ORBOFF" resourceType="4"/>
</mappings>
</org.eclipse.team.ui.MODEL_PARTICIPANT_MAPPINGS>
</org.eclipse.team.ui.MODEL_PARTICIPANT_SETTINGS>
<gitSynchronizeData container="C:/Users/<USER>/java workspace/ART-ORBOFF" dstRev="HEAD" inludeLocal="true" srcRev="HEAD"/>
</data>
</participant>
</syncparticipants>