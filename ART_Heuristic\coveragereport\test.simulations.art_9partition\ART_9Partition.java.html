<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_9Partition.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_9partition</a> &gt; <span class="el_source">ART_9Partition.java</span></div><h1>ART_9Partition.java</h1><pre class="source lang-java linenums">package test.simulations.art_9partition;

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import test.ART;
/**
 * Michael's
 * 先写二维的吧
 * */
public class ART_9Partition extends ART {

<span class="nc" id="L16">	private ArrayList&lt;NRectRegion&gt; availableRegions=new ArrayList&lt;&gt;();</span>
<span class="nc" id="L17">	private ArrayList&lt;NRectRegion&gt; testedRegions=new ArrayList&lt;&gt;();</span>
<span class="nc" id="L18">	private ArrayList&lt;NPoint&gt; tests=new ArrayList&lt;&gt;();</span>
	private int index;
<span class="nc" id="L20">	private int round=1;</span>
<span class="nc" id="L21">	private int[] shunxu={4,9,2,7,6,1,8,3,5};</span>
	public ART_9Partition(double[] min, double[] max, Random random, FailurePattern failurePattern) {
<span class="nc" id="L23">		super(min, max, random, failurePattern);</span>
		//availableRegions
<span class="nc" id="L25">		splitRegions(new NRectRegion(new NPoint(min), new NPoint(max)));</span>
<span class="nc" id="L26">	}</span>

	public void splitRegions(NRectRegion region){
		//二维特殊化
<span class="nc bnc" id="L30" title="All 2 branches missed.">		for(int i=0;i&lt;3;i++){</span>
<span class="nc bnc" id="L31" title="All 2 branches missed.">			for(int j=0;j&lt;3;j++){</span>
				
			}
		}
<span class="nc" id="L35">	}</span>
	
	@Override
	public NPoint generateNextTC() {

<span class="nc" id="L40">		NPoint p=new NPoint();</span>
<span class="nc" id="L41">		int indexTemp=9;</span>
<span class="nc bnc" id="L42" title="All 2 branches missed.">		while(indexTemp&lt;(index+1)){</span>
<span class="nc" id="L43">			round++;</span>
<span class="nc" id="L44">			indexTemp+=Math.pow(9, round);</span>
		}
		//需要 round个数来表示
<span class="nc" id="L47">		int shang=(int) ((index-Math.pow(9,(round-1))/9));</span>
<span class="nc" id="L48">		int yushu=(int) ((index-Math.pow(9,(round-1))%9));</span>
		
		//三元组
		//(round,shang,yushu)
		
<span class="nc" id="L53">		NRectRegion region=availableRegions.get(shunxu[index]);</span>
<span class="nc" id="L54">		p=randomCreator.randomPoint(region);</span>
<span class="nc" id="L55">		tests.add(p);</span>
<span class="nc" id="L56">		index++;</span>
		
		
		
<span class="nc" id="L60">		return null;</span>
	}
	public static void main(String[] args) {
		/*int round=1;
		int index=9;
		int indexTemp=9;
		while(indexTemp&lt;(index+1)){
			round++;
			indexTemp+=Math.pow(9, round);
		}
		System.out.println(round);*/
		
<span class="nc" id="L72">		_9Region regions[]=new _9Region[9];</span>
<span class="nc" id="L73">		int count=0;</span>
<span class="nc" id="L74">		int round=0;</span>
<span class="nc bnc" id="L75" title="All 2 branches missed.">		while(count&lt;100){</span>
			
		}
<span class="nc" id="L78">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>