<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRTtp1D.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rrttp.hilbert</a> &gt; <span class="el_source">RRTtp1D.java</span></div><h1>RRTtp1D.java</h1><pre class="source lang-java linenums">package test.simulations.rrttp.hilbert;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import util.file.FileUtils;

/**
 * -Djava.library.path=&quot;${workspace_loc}/ART/Resource;${env_var:PATH}&quot;
 */
public class RRTtp1D {
	public static void main(String[] args) throws Exception {
<span class="nc" id="L18">		File file = FileUtils.createNewFile(&quot;RRTtp_OD&quot;);</span>
<span class="nc" id="L19">		BufferedWriter writer = new BufferedWriter(new OutputStreamWriter(new FileOutputStream(file), &quot;UTF-8&quot;));</span>
<span class="nc" id="L20">		double[] theta = { 0.005, 0.002, 0.0015, 0.001, 0.0005, 0.0001 };</span>
<span class="nc bnc" id="L21" title="All 2 branches missed.">		for (int k = 0; k &lt; theta.length; k++) {</span>
<span class="nc bnc" id="L22" title="All 2 branches missed.">			for (int j = 0; j &lt; 50; j++) {</span>
<span class="nc" id="L23">				int times = 2000;</span>
<span class="nc" id="L24">				long sums = 0;</span>
<span class="nc" id="L25">				int temp = 0;</span>
				// tan0 tested = new tan0();
<span class="nc" id="L27">				long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L28" title="All 2 branches missed.">				for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L29">					RRTtp1D rrt_od = new RRTtp1D(0, 1, 0.75, theta[k], i * 3 + j * 5);</span>
<span class="nc" id="L30">					temp = rrt_od.run();</span>
<span class="nc" id="L31">					sums += temp;</span>
				}
<span class="nc" id="L33">				long endTime = System.currentTimeMillis();</span>
				// System.out.print((sums / (double) times) + &quot;,&quot;);
<span class="nc" id="L35">				writer.write((sums / (double) times) + &quot;,&quot;);</span>
<span class="nc" id="L36">				writer.flush();</span>
			}
<span class="nc" id="L38">			writer.newLine();</span>
		}
<span class="nc" id="L40">		writer.close();</span>
<span class="nc" id="L41">	}</span>
	double min;
	double max;
	double fail_start;
	// String ClassName;
	double fail_rate;
	double R;
	long randomseed;

<span class="nc" id="L50">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	public RRTtp1D(double min, double max, double r, double fail_rate, long randomseed) {
<span class="nc" id="L53">		super();</span>
<span class="nc" id="L54">		this.min = min;</span>
<span class="nc" id="L55">		this.max = max;</span>
<span class="nc" id="L56">		R = r;</span>
<span class="nc" id="L57">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L58">		this.randomseed = randomseed;</span>
<span class="nc" id="L59">	}</span>

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L62" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L63">			return false;</span>
		} else {
<span class="nc" id="L65">			return true;</span>
		}
	}

	public TestCase randomTC(Random random) {
<span class="nc" id="L70">		TestCase temp = new TestCase();</span>
<span class="nc" id="L71">		double temp_value = random.nextDouble() * (max - min) + min;</span>
<span class="nc" id="L72">		temp.p = temp_value;</span>
<span class="nc" id="L73">		return temp;</span>
	}

	public int run() throws Exception {
<span class="nc" id="L77">		Random random = new Random(randomseed);</span>

<span class="nc" id="L79">		int count = 0;</span>
<span class="nc" id="L80">		TestCase p = new TestCase();</span>
<span class="nc" id="L81">		fail_start = random.nextDouble() * (1 - fail_rate);</span>
		// Class&lt;?&gt; classes = Class.forName(this.ClassName);
<span class="nc" id="L83">		double value = random.nextDouble() * (max - min) + min;</span>
<span class="nc" id="L84">		p.p = value;</span>

<span class="nc bnc" id="L86" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L87">			count++;</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L89">				tests.add(p);</span>
<span class="nc" id="L90">			} else {</span>
<span class="nc" id="L91">				sortTestCases(p);</span>
			}
			// generate next test case by rrttp
<span class="nc" id="L94">			double radius = R / (2 * tests.size());</span>
			// System.out.println(&quot;radius:&quot;+radius);
<span class="nc" id="L96">			double max = -1;</span>
<span class="nc" id="L97">			double start = 0.0;</span>
<span class="nc" id="L98">			double end = 0.0;</span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">			for (int i = 0; i &lt;= tests.size(); i++) {</span>
<span class="nc" id="L100">				double length = 0;</span>
<span class="nc" id="L101">				double tempstart = 0.0;</span>
<span class="nc" id="L102">				double tempend = 0.0;</span>
<span class="nc" id="L103">				boolean flag = true;</span>
				// System.out.println(&quot;i:&quot;+i);
<span class="nc bnc" id="L105" title="All 2 branches missed.">				if (i == 0) {</span>
<span class="nc bnc" id="L106" title="All 2 branches missed.">					if (tests.get(0).p - radius &gt; this.min) {</span>
<span class="nc" id="L107">						length = tests.get(0).p - radius - min;</span>
<span class="nc" id="L108">						tempstart = min;</span>
<span class="nc" id="L109">						tempend = tests.get(0).p - radius;</span>
						// System.out.println(&quot;temp:&quot;+length+&quot;,&quot;+tempstart+&quot;,&quot;+tempend);
<span class="nc" id="L111">					} else {</span>
<span class="nc" id="L112">						flag = false;</span>
					}
<span class="nc bnc" id="L114" title="All 2 branches missed.">				} else if (i == tests.size()) {</span>
<span class="nc bnc" id="L115" title="All 2 branches missed.">					if (tests.get(i - 1).p + radius &lt;= this.max) {</span>
<span class="nc" id="L116">						length = this.max - (tests.get(i - 1).p + radius);</span>
<span class="nc" id="L117">						tempstart = tests.get(i - 1).p + radius;</span>
<span class="nc" id="L118">						tempend = this.max;</span>
						// System.out.println(&quot;temp:&quot;+length+&quot;,&quot;+tempstart+&quot;,&quot;+tempend);
<span class="nc" id="L120">					} else {</span>
<span class="nc" id="L121">						flag = false;</span>
					}
<span class="nc" id="L123">				} else {</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">					if (tests.get(i).p - tests.get(i - 1).p &gt; 2 * radius) {</span>
<span class="nc" id="L125">						length = tests.get(i).p - radius - (tests.get(i - 1).p + radius);</span>
<span class="nc" id="L126">						tempstart = tests.get(i - 1).p + radius;</span>
<span class="nc" id="L127">						tempend = tests.get(i).p - radius;</span>
						// System.out.println(&quot;temp:&quot;+length+&quot;,&quot;+tempstart+&quot;,&quot;+tempend);
<span class="nc" id="L129">					} else {</span>
<span class="nc" id="L130">						flag = false;</span>
					}
				}
<span class="nc bnc" id="L133" title="All 2 branches missed.">				if (flag) {</span>
<span class="nc bnc" id="L134" title="All 2 branches missed.">					if (max &lt; length) {</span>
<span class="nc" id="L135">						max = length;</span>
<span class="nc" id="L136">						start = tempstart;</span>
<span class="nc" id="L137">						end = tempend;</span>
					}
				} else {
					continue;
				}
			}
			// System.out.println(&quot;start:&quot; + start);
			// System.out.println(&quot;end:&quot; + end);
			// 选取下一个测试用例
<span class="nc" id="L146">			p = new TestCase();</span>
<span class="nc" id="L147">			p.p = random.nextDouble() * (end - start) + start;</span>
			// System.out.println(&quot;p.p:&quot; + p.p);
			// System.out.println(&quot;_______________________&quot;);
		}
<span class="nc" id="L151">		return count;</span>
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L155">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L156" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L157">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L158" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L159">				low = mid + 1;</span>
<span class="nc" id="L160">			} else {</span>
<span class="nc" id="L161">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L164" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L165">			mid = mid - 1;</span>
		}
<span class="nc" id="L167">		tests.add(mid + 1, p);</span>
<span class="nc" id="L168">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>