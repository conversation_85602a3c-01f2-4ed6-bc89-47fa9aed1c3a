<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRTtpRegion.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rrttp.partion</a> &gt; <span class="el_source">RRTtpRegion.java</span></div><h1>RRTtpRegion.java</h1><pre class="source lang-java linenums">package test.simulations.rrttp.partion;

import java.util.ArrayList;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;

public class RRTtpRegion{
	public NRectRegion region;
	public NPoint p0;
	public NPoint p1;
	public NPoint p2;
	public NPoint p3;
	public double totalSize;
	public RRTtpRegion() {
<span class="nc" id="L16">		super();</span>
<span class="nc" id="L17">	}</span>
	public RRTtpRegion(NRectRegion region) {
<span class="nc" id="L19">		super();</span>
<span class="nc" id="L20">		this.region = region;</span>
<span class="nc" id="L21">		totalSize=region.size();</span>
<span class="nc" id="L22">	}</span>
	
	public double calAvailSize(double  radius){
		//2维
<span class="nc" id="L26">		double[] start=region.getStart().getXn();</span>
<span class="nc" id="L27">		double[] end=region.getEnd().getXn();</span>
<span class="nc" id="L28">		double xlength=end[0]-start[0];</span>
<span class="nc" id="L29">		double ylength=end[1]-start[1];</span>
<span class="nc" id="L30">		double size=0;</span>
<span class="nc bnc" id="L31" title="All 4 branches missed.">		if(p0!=null||p2!=null){</span>
			//only p0,p2
<span class="nc bnc" id="L33" title="All 4 branches missed.">			if(p0!=null&amp;&amp;p2!=null){</span>
				//p0,p2
<span class="nc" id="L35">				size=twoPointIn(xlength, ylength, radius);</span>
<span class="nc bnc" id="L36" title="All 2 branches missed.">			}else if(p0==null){</span>
				//p2
<span class="nc" id="L38">				size=onePointIn(xlength,ylength,radius);</span>
<span class="nc" id="L39">			}else{</span>
				//p0
<span class="nc" id="L41">				size=onePointIn(xlength, ylength, radius);</span>
			}
<span class="nc" id="L43">		}</span>
<span class="nc bnc" id="L44" title="All 4 branches missed.">		else if(p1!=null||p3!=null){</span>
			//only p1,p3
<span class="nc bnc" id="L46" title="All 4 branches missed.">			if(p1!=null&amp;&amp;p3!=null){</span>
				//p1,p3
<span class="nc" id="L48">				size=twoPointIn(xlength, ylength, radius);</span>
<span class="nc bnc" id="L49" title="All 2 branches missed.">			}else if(p1==null){</span>
				//p3
<span class="nc" id="L51">				size=onePointIn(xlength, ylength, radius);</span>
<span class="nc" id="L52">			}else{</span>
				//p1
<span class="nc" id="L54">				size=onePointIn(xlength, ylength, radius);</span>
			}
<span class="nc" id="L56">		}else{</span>
			//none
<span class="nc" id="L58">			size=region.size();</span>
		}
<span class="nc" id="L60">		return size;</span>
	}
	private double twoPointIn(double xlength,double ylength,double radius){
<span class="nc" id="L63">		double size=0;</span>
<span class="nc bnc" id="L64" title="All 8 branches missed.">		if((xlength&gt;2*radius&amp;&amp;ylength&gt;radius)||(ylength&gt;2*radius&amp;&amp;xlength&gt;radius)){</span>
			//两边都长
<span class="nc" id="L66">			double temp=region.size()-(radius*radius)*2;</span>
<span class="nc" id="L67">			size+=temp;</span>
<span class="nc bnc" id="L68" title="All 8 branches missed.">		}else if(xlength&gt;radius&amp;&amp;xlength&lt;2*radius&amp;&amp;ylength&gt;radius&amp;&amp;ylength&lt;2*radius){</span>
			//相交
<span class="nc" id="L70">			double temp=region.size()-2*(radius*radius);</span>
<span class="nc" id="L71">			double xtemp=2*radius-xlength;</span>
<span class="nc" id="L72">			double ytemp=2*radius-ylength;</span>
<span class="nc" id="L73">			temp+=xtemp*ytemp;</span>
<span class="nc" id="L74">			size=temp;</span>
<span class="nc bnc" id="L75" title="All 8 branches missed.">		}else if((xlength&lt;radius&amp;&amp;ylength&gt;2*radius)||(xlength&gt;2*radius&amp;&amp;ylength&lt;radius)){</span>
			//缺少一块
<span class="nc bnc" id="L77" title="All 2 branches missed.">			double min=xlength&gt;ylength?ylength:xlength;</span>
<span class="nc" id="L78">			double temp=region.size()-2*(radius*radius);</span>
<span class="nc" id="L79">			temp+=2*(radius-min)*radius;</span>
<span class="nc" id="L80">			size=temp;</span>
<span class="nc" id="L81">		}else{size=0;}</span>
<span class="nc" id="L82">		return size;</span>
	}
	private double onePointIn(double xlength,double ylength,double radius){
<span class="nc" id="L85">		double size=0;</span>
<span class="nc bnc" id="L86" title="All 4 branches missed.">		if(xlength&gt;radius&amp;&amp;ylength&gt;radius){</span>
<span class="nc" id="L87">			double temp=region.size()-radius*radius;</span>
<span class="nc" id="L88">			size=temp;</span>
<span class="nc bnc" id="L89" title="All 8 branches missed.">		}else if((xlength&gt;radius&amp;&amp;ylength&lt;radius)||(xlength&lt;radius&amp;&amp;ylength&gt;radius)){</span>
			//
<span class="nc bnc" id="L91" title="All 2 branches missed.">			double min=xlength&gt;ylength?ylength:xlength;</span>
<span class="nc" id="L92">			double temp=region.size()-radius*radius;</span>
<span class="nc" id="L93">			temp+=(radius-min)*radius;</span>
<span class="nc" id="L94">			size=temp;</span>
<span class="nc" id="L95">		}else{</span>
			//none
<span class="nc" id="L97">			size=0;</span>
		}
<span class="nc" id="L99">		return size;</span>
	}
	
	@Override
	public String toString() {
<span class="nc" id="L104">		return &quot;RRTtpRegion [region=&quot; + region + &quot;, p0=&quot; + p0 + &quot;, p1=&quot; + p1 + &quot;, p2=&quot; + p2 + &quot;, p3=&quot; + p3</span>
<span class="nc" id="L105">				+ &quot;, totalSize=&quot; + totalSize + &quot;]&quot;;</span>
	}
	
	public ArrayList&lt;NRectRegion&gt; twoPointRegion(double[] start,double[] end,double radius,int type){
<span class="nc" id="L109">		double xlength=end[0]-start[0];</span>
<span class="nc" id="L110">		double ylength=end[1]-start[1];</span>
		
<span class="nc" id="L112">		ArrayList&lt;NRectRegion&gt; regions=new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L113" title="All 4 branches missed.">		if((xlength&gt;2*radius&amp;&amp;ylength&gt;2*radius)){</span>
			//两边都长,分为三个区域
<span class="nc bnc" id="L115" title="All 2 branches missed.">			if( type==0){</span>
<span class="nc" id="L116">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{start[0]+radius,end[1]})));</span>
<span class="nc" id="L117">				regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc" id="L118">				regions.add(new NRectRegion(new NPoint(new double[]{end[0]-radius,start[1]}),new NPoint(new double[]{end[0],end[1]-radius})));</span>
<span class="nc" id="L119">			}else{</span>
<span class="nc" id="L120">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{start[0]+radius,end[1]-radius})));</span>
<span class="nc" id="L121">				regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc" id="L122">				regions.add(new NRectRegion(new NPoint(new double[]{end[0]-radius,start[1]+radius}),new NPoint(new double[]{end[0],end[1]})));</span>
			}
<span class="nc bnc" id="L124" title="All 12 branches missed.">		}else if((xlength&gt;2*radius&amp;&amp;ylength&gt;radius&amp;&amp;ylength&lt;2*radius)||(xlength&gt;radius&amp;&amp;xlength&lt;2*radius&amp;&amp;ylength&gt;2*radius)){</span>
			//一边长，一边适中
<span class="nc bnc" id="L126" title="All 2 branches missed.">			if(type==0){</span>
				//
<span class="nc bnc" id="L128" title="All 2 branches missed.">				if(xlength&gt;ylength){</span>
<span class="nc" id="L129">					regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{start[0]+radius,end[1]})));</span>
<span class="nc" id="L130">					regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc" id="L131">					regions.add(new NRectRegion(new NPoint(new double[]{end[0]-radius,start[1]}),new NPoint(new double[]{end[0],end[1]-radius})));</span>
<span class="nc" id="L132">				}else{</span>
<span class="nc" id="L133">					regions.add(new NRectRegion(new NPoint(new double[]{start[0],end[1]-radius}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc" id="L134">					regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{end[0],end[1]-radius})));</span>
<span class="nc" id="L135">					regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}),new NPoint(new double[]{end[0],start[1]+radius})));</span>
				}
<span class="nc" id="L137">			}else{</span>
<span class="nc bnc" id="L138" title="All 2 branches missed.">				if(xlength&gt;ylength){</span>
<span class="nc" id="L139">					regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{start[0]+radius,end[1]-radius})));</span>
<span class="nc" id="L140">					regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc" id="L141">					regions.add(new NRectRegion(new NPoint(new double[]{end[0]-radius,start[1]+radius}),new NPoint(new double[]{end[0],end[1]})));</span>
<span class="nc" id="L142">				}else{</span>
<span class="nc" id="L143">					regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{end[0]-radius,start[1]+radius})));</span>
<span class="nc" id="L144">					regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{end[0],end[1]-radius})));</span>
<span class="nc" id="L145">					regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,end[1]-radius}),new NPoint(new double[]{end[0],end[1]})));</span>
				}
			}
<span class="nc bnc" id="L148" title="All 8 branches missed.">		}else if(xlength&gt;radius&amp;&amp;xlength&lt;2*radius&amp;&amp;ylength&gt;radius&amp;&amp;ylength&lt;2*radius){</span>
			//相交
<span class="nc bnc" id="L150" title="All 2 branches missed.">			if(type==0){</span>
<span class="nc" id="L151">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc" id="L152">				regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}), new NPoint(new double[]{end[0],end[1]-radius})));</span>
<span class="nc" id="L153">			}else{</span>
<span class="nc" id="L154">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{end[0]-radius,end[1]-radius})));</span>
<span class="nc" id="L155">				regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]+radius}), new NPoint(new double[]{end[0],end[1]})));</span>
			}
<span class="nc bnc" id="L157" title="All 8 branches missed.">		}else if((xlength&lt;radius&amp;&amp;ylength&gt;2*radius)||(xlength&gt;2*radius&amp;&amp;ylength&lt;radius)){</span>
			//有一边是短的
<span class="nc bnc" id="L159" title="All 2 branches missed.">			if(type==0){</span>
<span class="nc bnc" id="L160" title="All 2 branches missed.">				if(xlength&gt;ylength){</span>
<span class="nc" id="L161">					regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc" id="L162">				}else{</span>
<span class="nc" id="L163">					regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{end[0],end[1]-radius})));</span>
				}
<span class="nc" id="L165">			}else{</span>
<span class="nc bnc" id="L166" title="All 2 branches missed.">				if(xlength&gt;ylength){</span>
<span class="nc" id="L167">					regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc" id="L168">				}else{</span>
<span class="nc" id="L169">					regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{end[0],end[1]-radius})));</span>
				}
			}
		}else{
			
		}
		
<span class="nc" id="L176">		return regions;</span>
	}

	public ArrayList&lt;NRectRegion&gt; onePointRegion(double[] start,double[] end,double radius,int type){
<span class="nc" id="L180">		ArrayList&lt;NRectRegion&gt; regions=new ArrayList&lt;&gt;();</span>
<span class="nc" id="L181">		double xlength=end[0]-start[0];</span>
<span class="nc" id="L182">		double ylength=end[1]-start[1];</span>
<span class="nc bnc" id="L183" title="All 2 branches missed.">		if(type==0){</span>
<span class="nc bnc" id="L184" title="All 4 branches missed.">			if(xlength&gt;radius&amp;&amp;ylength&gt;radius){</span>
<span class="nc" id="L185">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{end[0],end[1]})));</span>
<span class="nc" id="L186">				regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}), new NPoint(new double[]{end[0],start[1]+radius})));</span>
<span class="nc bnc" id="L187" title="All 4 branches missed.">			}else if(xlength&gt;radius&amp;&amp;ylength&lt;radius){</span>
<span class="nc" id="L188">				regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}), new NPoint(new double[]{end[0],end[1]})));</span>
				
<span class="nc bnc" id="L190" title="All 4 branches missed.">			}else if(xlength&lt;radius&amp;&amp;ylength&gt;radius){</span>
<span class="nc" id="L191">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{end[0],end[1]})));</span>
			}
<span class="nc bnc" id="L193" title="All 2 branches missed.">		}else if(type==1){</span>
<span class="nc bnc" id="L194" title="All 4 branches missed.">			if(xlength&gt;radius&amp;&amp;ylength&gt;radius){</span>
<span class="nc" id="L195">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{end[0]-radius,start[1]+radius})));</span>
<span class="nc" id="L196">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{end[0],end[1]})));</span>
<span class="nc bnc" id="L197" title="All 4 branches missed.">			}else if(xlength&gt;radius&amp;&amp;ylength&lt;radius){</span>
<span class="nc" id="L198">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc bnc" id="L199" title="All 4 branches missed.">			}else if(xlength&lt;radius&amp;&amp;ylength&gt;radius){</span>
<span class="nc" id="L200">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]+radius}), new NPoint(new double[]{end[0],end[1]})));</span>
			}
<span class="nc bnc" id="L202" title="All 2 branches missed.">		}else if(type==2){</span>
<span class="nc bnc" id="L203" title="All 4 branches missed.">			if(xlength&gt;radius&amp;&amp;ylength&gt;radius){</span>
<span class="nc" id="L204">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{end[0],end[1]-radius})));</span>
<span class="nc" id="L205">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],end[1]-radius}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc bnc" id="L206" title="All 4 branches missed.">			}else if(xlength&gt;radius&amp;&amp;ylength&lt;radius){</span>
<span class="nc" id="L207">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{end[0]-radius,end[1]})));</span>
<span class="nc bnc" id="L208" title="All 4 branches missed.">			}else if(xlength&lt;radius&amp;&amp;ylength&gt;radius){</span>
<span class="nc" id="L209">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{end[0],end[1]-radius})));</span>
			}
<span class="nc" id="L211">		}else{</span>
<span class="nc bnc" id="L212" title="All 4 branches missed.">			if(xlength&gt;radius&amp;&amp;ylength&gt;radius){</span>
<span class="nc" id="L213">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{end[0],end[1]-radius})));</span>
<span class="nc" id="L214">				regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,end[1]-radius}), new NPoint(new double[]{end[0],end[1]})));</span>
<span class="nc bnc" id="L215" title="All 4 branches missed.">			}else if(xlength&gt;radius&amp;&amp;ylength&lt;radius){</span>
<span class="nc" id="L216">				regions.add(new NRectRegion(new NPoint(new double[]{start[0]+radius,start[1]}), new NPoint(new double[]{end[0],end[1]})));</span>
<span class="nc bnc" id="L217" title="All 4 branches missed.">			}else if(xlength&lt;radius&amp;&amp;ylength&gt;radius){</span>
<span class="nc" id="L218">				regions.add(new NRectRegion(new NPoint(new double[]{start[0],start[1]}), new NPoint(new double[]{end[0],end[1]-radius})));</span>
				
			}
		}
<span class="nc" id="L222">		return regions;</span>
	}
	public ArrayList&lt;NRectRegion&gt; availRegions(double radius){
<span class="nc" id="L225">		double[] start=region.getStart().getXn();</span>
<span class="nc" id="L226">		double[] end=region.getEnd().getXn();</span>
<span class="nc" id="L227">		ArrayList&lt;NRectRegion&gt; results=null;</span>
<span class="nc bnc" id="L228" title="All 4 branches missed.">		if(p0!=null||p2!=null){</span>
			//only p0,p2
<span class="nc bnc" id="L230" title="All 4 branches missed.">			if(p0!=null&amp;&amp;p2!=null){</span>
				//p0,p2
<span class="nc" id="L232">				results=twoPointRegion(start, end, radius, 0);</span>
<span class="nc bnc" id="L233" title="All 2 branches missed.">			}else if(p0==null){</span>
				//p2
<span class="nc" id="L235">				results=onePointRegion(start, end, radius, 2);</span>
<span class="nc" id="L236">			}else{</span>
				//p0
<span class="nc" id="L238">				results=onePointRegion(start, end, radius, 0);</span>
			}
<span class="nc" id="L240">		}</span>
<span class="nc bnc" id="L241" title="All 4 branches missed.">		else if(p1!=null||p3!=null){</span>
			//only p1,p3
<span class="nc bnc" id="L243" title="All 4 branches missed.">			if(p1!=null&amp;&amp;p3!=null){</span>
				//p1,p3
<span class="nc" id="L245">				results=twoPointRegion(start, end, radius, 1);</span>
<span class="nc bnc" id="L246" title="All 2 branches missed.">			}else if(p1==null){</span>
				//p3
<span class="nc" id="L248">				results=onePointRegion(start, end, radius, 3);</span>
<span class="nc" id="L249">			}else{</span>
				//p1
<span class="nc" id="L251">				results=onePointRegion(start, end, radius, 1);</span>
			}
<span class="nc" id="L253">		}else{</span>
			//none
<span class="nc" id="L255">			results=new ArrayList&lt;&gt;();</span>
<span class="nc" id="L256">			results.add(region);</span>
		}
		
<span class="nc" id="L259">		return results;</span>
	}
	
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>