package test.simulations.art_b;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.StripPatternIn2D;
import datastructure.failurepattern.impl.PointPatternIn2D;
import test.ART;
import util.data.ZeroOneCreator;

public class ART_B_ND_SimEm extends ART {
	private int em;
	ArrayList<NRectRegion> untestedRegions = new ArrayList<>();
	ArrayList<NRectRegion> testedRegions = new ArrayList<>();

	ArrayList<NPoint> tests = new ArrayList<>();

	public ART_B_ND_SimEm(double[] min, double[] max, Random random, FailurePattern failurePattern, int em) {
		super(min, max, random, failurePattern);
		this.em = em;
	}

	public NRectRegion findRandomRegionAndDelete(ArrayList<NRectRegion> regions) {
		int T = random.nextInt(regions.size());
		return regions.remove(T);
	}

	public boolean hasPointInRegion(NRectRegion region) {
		boolean result = false;
		double[] start = region.getStart().getXn();
		double[] end = region.getEnd().getXn();

		for (int i = 0; i < tests.size(); i++) {
			double[] p = tests.get(i).getXn();
			boolean isPIn = true;
			for (int j = 0; j < p.length; j++) {
				if (p[j] < start[j] || p[j] > end[j]) {
					isPIn = false;
				}
			}
			if (isPIn) {
				result = true;
				break;
			}
		}
		return result;
	}

	@Override
	public int run() {
		int count = 0;
		int emCount = 0;
		untestedRegions.add(new NRectRegion(new NPoint(min), new NPoint(max)));

		while (count < em) {
			// while (true) {
			NPoint p = null;
			while (untestedRegions.size() != 0 &&count<em) {
				NRectRegion randomRegion = findRandomRegionAndDelete(untestedRegions);
				// System.out.println("randomRegion:"+randomRegion);
				p = randomCreator.randomPoint(randomRegion);
				// System.out.println("point:"+p);
				count++;
				tests.add(p);
				if (!this.failPattern.isCorrect(p)) {
					emCount++;
				} 
//				else {
				testedRegions.add(randomRegion);
//			}
			}
			ArrayList<NRectRegion> temp = new ArrayList<>();
			for (int i = 0; i < testedRegions.size(); i++) {
				// 找最长边
				NRectRegion tempRegion = testedRegions.get(i);
				double maxBian = 0.0;
				int maxIndex = 0;
				for (int j = 0; j < tempRegion.getStart().getXn().length; j++) {
					if (tempRegion.getEnd().getXn()[j] - tempRegion.getStart().getXn()[j] > maxBian) {
						maxBian = tempRegion.getEnd().getXn()[j] - tempRegion.getStart().getXn()[j];
						maxIndex = j;
					}
				}
				//
				NRectRegion region1 = new NRectRegion();
				NRectRegion region2 = new NRectRegion();

				region1.setStart(tempRegion.getStart());
				double[] end = Arrays.copyOf(tempRegion.getEnd().getXn(), tempRegion.getEnd().getXn().length);
				double midValue1 = 0.5
						* (tempRegion.getEnd().getXn()[maxIndex] + tempRegion.getStart().getXn()[maxIndex]);
				end[maxIndex] = midValue1;
				region1.setEnd(new NPoint(end));
				if (hasPointInRegion(region1)) {
					temp.add(region1);
				} else {
					untestedRegions.add(region1);
				}

				double[] start = Arrays.copyOf(tempRegion.getStart().getXn(), tempRegion.getStart().getXn().length);
				start[maxIndex] = midValue1;
				region2.setStart(new NPoint(start));
				region2.setEnd(tempRegion.getEnd());
				if (hasPointInRegion(region2)) {
					temp.add(region2);
				} else {
					untestedRegions.add(region2);
				}
			}
			testedRegions = temp;
		}
		// System.out.println(emCount);
		return emCount;
	}

	public static void main(String[] args) {
		// 一定要修改这个
		int d = 7;
		// m 维立方体
		ZeroOneCreator dataCreator = new ZeroOneCreator();
		double start[] = dataCreator.minCreator(d);
		double end[] = dataCreator.maxCreator(d);

		//int [] ems = {500, 1000, 1500, 2000, 2500, 3000};
		int [] ems = {1000, 2000, 3000, 4000, 5000, 6000, 7000};
		//int [] ems = {5000, 6000, 7000};
		for (int f = 0; f < ems.length; f++) {
		
		int times = 5000;
		//int em = 20000;
		long sums = 0;

		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			
			 //FailurePattern failurePattern = new BlockPattern();
			 FailurePattern failurePattern = new StripPatternIn2D();
			//FailurePattern failurePattern = new PointPatternIn2D();
			failurePattern.fail_rate = 0.001;

			ART_B_ND_SimEm art_b_nd = new ART_B_ND_SimEm(start, end, new Random(i * 3), failurePattern, ems[f]);
			int temp = art_b_nd.run();
			sums += temp;
		}
		long endTime = System.currentTimeMillis();
		System.out.println("Em for "+ ems[f] + " test cases: " + "Em:" + (sums / (double) times) + " time:" + ((endTime - startTime) / (double) times));
		//System.out.println("Fm: " + sums / (double) times);
		//System.out.println("Time: " + (endTime - startTime) / (double) times);
		}
	}
}
