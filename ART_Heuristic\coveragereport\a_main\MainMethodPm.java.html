<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MainMethodPm.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">a_main</a> &gt; <span class="el_source">MainMethodPm.java</span></div><h1>MainMethodPm.java</h1><pre class="source lang-java linenums">package a_main;

import java.util.Random;

import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tp._ND.ART_TP_ND;
import test.simulations.art_tpp.ART_TPP;
import test.simulations.fscs.FSCS_ND;
import test.simulations.rrt.RRT_ND;
import test.simulations.rrttp.hilbert.RRTtpND_H;
import test.simulations.rt.RT_ND;
import util.data.ZeroOneCreator;

<span class="nc" id="L17">public class MainMethodPm {</span>
	public static void main(String[] args) {
<span class="nc" id="L19">		int d=4;</span>
<span class="nc" id="L20">		double failrate=0.001;</span>
		
<span class="nc" id="L22">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L23">		double[] min = dataCreator.minCreator(d);</span>
<span class="nc" id="L24">		double[] max = dataCreator.maxCreator(d);</span>
<span class="nc" id="L25">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L26">		failurePattern.fail_rate = failrate;</span>
		
<span class="nc" id="L28">		int n = 1000;</span>
<span class="nc" id="L29">		int pm = 0;</span>
		
		//rt
<span class="nc" id="L32">		RT_ND rt1=new RT_ND(min,max,new Random(3),failurePattern);</span>
<span class="nc" id="L33">		System.out.println(rt1.pm());</span>
		
		
		//rrt
<span class="nc" id="L37">		pm=0;</span>
<span class="nc" id="L38">		double R = 0.75;</span>
<span class="nc bnc" id="L39" title="All 2 branches missed.">		if (d &gt; 1) {</span>
<span class="nc" id="L40">			R = 1.5;</span>
		}
		
<span class="nc bnc" id="L43" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++) {</span>
<span class="nc" id="L44">			RRT_ND rt = new RRT_ND(min, max, failurePattern, new Random(i * 5), R);</span>
<span class="nc bnc" id="L45" title="All 2 branches missed.">			if (rt.pm()) {</span>
<span class="nc" id="L46">				pm++;</span>
			}
		}
<span class="nc" id="L49">		System.out.println(pm/(double)n);</span>
		
		//fscs
<span class="nc" id="L52">		pm=0;</span>
<span class="nc" id="L53">		int s=10;</span>
<span class="nc bnc" id="L54" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++) {</span>
<span class="nc" id="L55">			FSCS_ND rt = new FSCS_ND(min, max, s, failurePattern, new Random(i * 3));</span>
<span class="nc bnc" id="L56" title="All 2 branches missed.">			if (rt.pm()) {</span>
<span class="nc" id="L57">				pm++;</span>
			}
		}
<span class="nc" id="L60">		System.out.println(pm/(double)n);</span>
		
		//b
<span class="nc" id="L63">		pm=0;</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++) {</span>
<span class="nc" id="L65">			ART_B_ND rt = new ART_B_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc bnc" id="L66" title="All 2 branches missed.">			if (rt.pm()) {</span>
<span class="nc" id="L67">				pm++;</span>
			}
		}
<span class="nc" id="L70">		System.out.println(pm/(double)n);</span>
		
		//rp
<span class="nc" id="L73">		pm=0;</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++) {</span>
<span class="nc" id="L75">			ART_RP_ND rt = new ART_RP_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc bnc" id="L76" title="All 2 branches missed.">			if (rt.pm()) {</span>
<span class="nc" id="L77">				pm++;</span>
			}
		}
<span class="nc" id="L80">		System.out.println(pm/(double)n);</span>
		//tpp
<span class="nc" id="L82">		pm=0;</span>
<span class="nc bnc" id="L83" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++) {</span>
<span class="nc" id="L84">			ART_TPP rt = new ART_TPP(min, max, new Random(i * 3), failurePattern, 10);</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">			if (rt.pm()) {</span>
<span class="nc" id="L86">				pm++;</span>
			}
		}
<span class="nc" id="L89">		System.out.println(pm/(double)n);</span>
		//tp
<span class="nc" id="L91">		pm=0;</span>
<span class="nc bnc" id="L92" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++) {</span>
<span class="nc" id="L93">			ART_TP_ND rt = new ART_TP_ND(min, max, failurePattern, new Random(i * 3 + 3));</span>
<span class="nc bnc" id="L94" title="All 2 branches missed.">			if (rt.pm()) {</span>
<span class="nc" id="L95">				pm++;</span>
			}
		}
<span class="nc" id="L98">		System.out.println(pm/(double)n);</span>
		
		//laz
<span class="nc" id="L101">		pm=0;</span>
<span class="nc bnc" id="L102" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++) {</span>
<span class="nc" id="L103">			RRTtpND_H rt = new RRTtpND_H(min, max, 0.75, failurePattern, new Random(i * 3));</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">			if (rt.pm()) {</span>
<span class="nc" id="L105">				pm++;</span>
			}
		}
<span class="nc" id="L108">		System.out.println(pm/(double)n);</span>
<span class="nc" id="L109">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>