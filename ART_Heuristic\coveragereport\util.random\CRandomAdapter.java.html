<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>CRandomAdapter.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util.random</a> &gt; <span class="el_source">CRandomAdapter.java</span></div><h1>CRandomAdapter.java</h1><pre class="source lang-java linenums">package util.random;

import java.util.Random;

import util.CRandomNumber;

<span class="nc" id="L7">public class CRandomAdapter extends Random{</span>

	@Override
	public synchronized void setSeed(long seed) {
<span class="nc" id="L11">		CRandomNumber.initSeed(seed);</span>
<span class="nc" id="L12">	}</span>

	@Override
	public double nextDouble() {
<span class="nc" id="L16">		return CRandomNumber.randome0e1();</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>