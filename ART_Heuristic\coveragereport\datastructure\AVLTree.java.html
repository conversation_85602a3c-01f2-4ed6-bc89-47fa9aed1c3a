<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>AVLTree.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure</a> &gt; <span class="el_source">AVLTree.java</span></div><h1>AVLTree.java</h1><pre class="source lang-java linenums">package datastructure;

/**
 * Implementation of an AVL Tree, along with code to test insertions on the
 * tree.
 * 
 * Based on code written by Mark Allen Weiss in his book Data Structures and
 * Algorithm Analysis in Java.
 *
 * Code for remove is based upon postings at:
 * http://www.dreamincode.net/forums/topic/214510-working-example-of-avl-tree-
 * remove-method/
 *
 * <AUTHOR> Ethier
 */
class AvlTree&lt;T extends Comparable&lt;? super T&gt;&gt; {
	/**
	 * AvlNode is a container class that is used to store each element (node) of an
	 * AVL tree.
	 * 
	 * <AUTHOR> Ethier
	 */
	protected static class AvlNode&lt;T&gt; {

		/**
		 * Node data
		 */
		protected T element;

		/**
		 * Left child
		 */
		protected AvlNode&lt;T&gt; left;

		/**
		 * Right child
		 */
		protected AvlNode&lt;T&gt; right;

		/**
		 * Height of node
		 */
		protected int height;

		/**
		 * Constructor; creates a node without any children
		 * 
		 * @param theElement
		 *            The element contained in this node
		 */
		public AvlNode(T theElement) {
<span class="nc" id="L52">			this(theElement, null, null);</span>
<span class="nc" id="L53">		}</span>

		/**
		 * Constructor; creates a node with children
		 * 
		 * @param theElement
		 *            The element contained in this node
		 * @param lt
		 *            Left child
		 * @param rt
		 *            Right child
		 */
<span class="nc" id="L65">		public AvlNode(T theElement, AvlNode&lt;T&gt; lt, AvlNode&lt;T&gt; rt) {</span>
<span class="nc" id="L66">			element = theElement;</span>
<span class="nc" id="L67">			left = lt;</span>
<span class="nc" id="L68">			right = rt;</span>
<span class="nc" id="L69">		}</span>
	}

	public AvlNode&lt;T&gt; root;

	// TODO: make these optional based on some sort of 'debug' flag?
	// at the very least, make them read-only properties
	public int countInsertions;
	public int countSingleRotations;
	public int countDoubleRotations;

	/**
	 * Avl Tree Constructor.
	 * 
	 * Creates an empty tree
	 */
<span class="nc" id="L85">	public AvlTree() {</span>
<span class="nc" id="L86">		root = null;</span>

<span class="nc" id="L88">		countInsertions = 0;</span>
<span class="nc" id="L89">		countSingleRotations = 0;</span>
<span class="nc" id="L90">		countDoubleRotations = 0;</span>
<span class="nc" id="L91">	}</span>

	/***********************************************************************/
	// Diagnostic functions for the tree
	public boolean checkBalanceOfTree(AvlTree.AvlNode&lt;Integer&gt; current) {

<span class="nc" id="L97">		boolean balancedRight = true, balancedLeft = true;</span>
<span class="nc" id="L98">		int leftHeight = 0, rightHeight = 0;</span>

<span class="nc bnc" id="L100" title="All 2 branches missed.">		if (current.right != null) {</span>
<span class="nc" id="L101">			balancedRight = checkBalanceOfTree(current.right);</span>
<span class="nc" id="L102">			rightHeight = getDepth(current.right);</span>
		}

<span class="nc bnc" id="L105" title="All 2 branches missed.">		if (current.left != null) {</span>
<span class="nc" id="L106">			balancedLeft = checkBalanceOfTree(current.left);</span>
<span class="nc" id="L107">			leftHeight = getDepth(current.left);</span>
		}

<span class="nc bnc" id="L110" title="All 6 branches missed.">		return balancedLeft &amp;&amp; balancedRight &amp;&amp; Math.abs(leftHeight - rightHeight) &lt; 2;</span>
	}

	public boolean checkOrderingOfTree(AvlTree.AvlNode&lt;Integer&gt; current) {
<span class="nc bnc" id="L114" title="All 2 branches missed.">		if (current.left != null) {</span>
<span class="nc bnc" id="L115" title="All 2 branches missed.">			if (current.left.element.compareTo(current.element) &gt; 0)</span>
<span class="nc" id="L116">				return false;</span>
			else
<span class="nc" id="L118">				return checkOrderingOfTree(current.left);</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">		} else if (current.right != null) {</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">			if (current.right.element.compareTo(current.element) &lt; 0)</span>
<span class="nc" id="L121">				return false;</span>
			else
<span class="nc" id="L123">				return checkOrderingOfTree(current.right);</span>
<span class="nc bnc" id="L124" title="All 4 branches missed.">		} else if (current.left == null &amp;&amp; current.right == null)</span>
<span class="nc" id="L125">			return true;</span>

<span class="nc" id="L127">		return true;</span>
	}

	/**
	 * Search for an element within the tree.
	 *
	 * @param x
	 *            Element to find
	 * @param t
	 *            Root of the tree
	 * @return True if the element is found, false otherwise
	 */
	public boolean contains(T x) {
<span class="nc" id="L140">		return contains(x, root);</span>
	}

	/**
	 * Internal find method; search for an element starting at the given node.
	 *
	 * @param x
	 *            Element to find
	 * @param t
	 *            Root of the tree
	 * @return True if the element is found, false otherwise
	 */
	protected boolean contains(T x, AvlNode&lt;T&gt; t) {
<span class="nc bnc" id="L153" title="All 2 branches missed.">		if (t == null) {</span>
<span class="nc" id="L154">			return false; // The node was not found</span>

<span class="nc bnc" id="L156" title="All 2 branches missed.">		} else if (x.compareTo(t.element) &lt; 0) {</span>
<span class="nc" id="L157">			return contains(x, t.left);</span>
<span class="nc bnc" id="L158" title="All 2 branches missed.">		} else if (x.compareTo(t.element) &gt; 0) {</span>
<span class="nc" id="L159">			return contains(x, t.right);</span>
		}

<span class="nc" id="L162">		return true; // Can only reach here if node was found</span>
	}

	/**
	 * Double rotate binary tree node: first left child with its right child; then
	 * node k3 with new left child. For AVL trees, this is a double rotation for
	 * case 2. Update heights, then return new root.
	 * 
	 * @param k3
	 *            Root of tree we are rotating
	 * @return New root
	 */
	protected AvlNode&lt;T&gt; doubleWithLeftChild(AvlNode&lt;T&gt; k3) {
<span class="nc" id="L175">		k3.left = rotateWithRightChild(k3.left);</span>
<span class="nc" id="L176">		return rotateWithLeftChild(k3);</span>
	}

	/**
	 * Double rotate binary tree node: first right child with its left child; then
	 * node k1 with new right child. For AVL trees, this is a double rotation for
	 * case 3. Update heights, then return new root.
	 * 
	 * @param k1
	 *            Root of tree we are rotating
	 * @return New root
	 */
	protected AvlNode&lt;T&gt; doubleWithRightChild(AvlNode&lt;T&gt; k1) {
<span class="nc" id="L189">		k1.right = rotateWithLeftChild(k1.right);</span>
<span class="nc" id="L190">		return rotateWithRightChild(k1);</span>
	}

	/**
	 * Find the largest item in the tree.
	 * 
	 * @return the largest item of null if empty.
	 */
	public T findMax() {
<span class="nc bnc" id="L199" title="All 2 branches missed.">		if (isEmpty())</span>
<span class="nc" id="L200">			return null;</span>
<span class="nc" id="L201">		return findMax(root).element;</span>
	}

	/**
	 * Internal method to find the largest item in a subtree.
	 * 
	 * @param t
	 *            the node that roots the tree.
	 * @return node containing the largest item.
	 */
	private AvlNode&lt;T&gt; findMax(AvlNode&lt;T&gt; t) {
<span class="nc bnc" id="L212" title="All 2 branches missed.">		if (t == null)</span>
<span class="nc" id="L213">			return t;</span>

<span class="nc bnc" id="L215" title="All 2 branches missed.">		while (t.right != null)</span>
<span class="nc" id="L216">			t = t.right;</span>
<span class="nc" id="L217">		return t;</span>
	}

	/**
	 * Find the smallest item in the tree.
	 * 
	 * @return smallest item or null if empty.
	 */
	public T findMin() {
<span class="nc bnc" id="L226" title="All 2 branches missed.">		if (isEmpty())</span>
<span class="nc" id="L227">			return null;</span>

<span class="nc" id="L229">		return findMin(root).element;</span>
	}

	/**
	 * Internal method to find the smallest item in a subtree.
	 * 
	 * @param t
	 *            the node that roots the tree.
	 * @return node containing the smallest item.
	 */
	private AvlNode&lt;T&gt; findMin(AvlNode&lt;T&gt; t) {
<span class="nc bnc" id="L240" title="All 2 branches missed.">		if (t == null)</span>
<span class="nc" id="L241">			return t;</span>

<span class="nc bnc" id="L243" title="All 2 branches missed.">		while (t.left != null)</span>
<span class="nc" id="L244">			t = t.left;</span>
<span class="nc" id="L245">		return t;</span>
	}

	public int getDepth(AvlTree.AvlNode&lt;Integer&gt; n) {
<span class="nc" id="L249">		int leftHeight = 0, rightHeight = 0;</span>

<span class="nc bnc" id="L251" title="All 2 branches missed.">		if (n.right != null)</span>
<span class="nc" id="L252">			rightHeight = getDepth(n.right);</span>
<span class="nc bnc" id="L253" title="All 2 branches missed.">		if (n.left != null)</span>
<span class="nc" id="L254">			leftHeight = getDepth(n.left);</span>

<span class="nc" id="L256">		return Math.max(rightHeight, leftHeight) + 1;</span>
	}

	/**
	 * Determine the height of the given node.
	 * 
	 * @param t
	 *            Node
	 * @return Height of the given node.
	 */
	public int height(AvlNode&lt;T&gt; t) {
<span class="nc bnc" id="L267" title="All 2 branches missed.">		return t == null ? -1 : t.height;</span>
	}

	/**
	 * Insert an element into the tree.
	 * 
	 * @param x
	 *            Element to insert into the tree
	 * @return True - Success, the Element was added. False - Error, the element was
	 *         a duplicate.
	 */
	public boolean insert(T x) {
		try {
<span class="nc" id="L280">			root = insert(x, root);</span>

<span class="nc" id="L282">			countInsertions++;</span>
<span class="nc" id="L283">			return true;</span>
<span class="nc" id="L284">		} catch (Exception e) { // TODO: catch a DuplicateValueException</span>
								// instead!
<span class="nc" id="L286">			return false;</span>
		}
	}

	/**
	 * Internal method to perform an actual insertion.
	 * 
	 * @param x
	 *            Element to add
	 * @param t
	 *            Root of the tree
	 * @return New root of the tree
	 * @throws Exception
	 */
	protected AvlNode&lt;T&gt; insert(T x, AvlNode&lt;T&gt; t) throws Exception {
<span class="nc bnc" id="L301" title="All 2 branches missed.">		if (t == null)</span>
<span class="nc" id="L302">			t = new AvlNode&lt;T&gt;(x);</span>
<span class="nc bnc" id="L303" title="All 2 branches missed.">		else if (x.compareTo(t.element) &lt; 0) {</span>
<span class="nc" id="L304">			t.left = insert(x, t.left);</span>

<span class="nc bnc" id="L306" title="All 2 branches missed.">			if (height(t.left) - height(t.right) == 2) {</span>
<span class="nc bnc" id="L307" title="All 2 branches missed.">				if (x.compareTo(t.left.element) &lt; 0) {</span>
<span class="nc" id="L308">					t = rotateWithLeftChild(t);</span>
<span class="nc" id="L309">					countSingleRotations++;</span>
<span class="nc" id="L310">				} else {</span>
<span class="nc" id="L311">					t = doubleWithLeftChild(t);</span>
<span class="nc" id="L312">					countDoubleRotations++;</span>
				}
			}
<span class="nc bnc" id="L315" title="All 2 branches missed.">		} else if (x.compareTo(t.element) &gt; 0) {</span>
<span class="nc" id="L316">			t.right = insert(x, t.right);</span>

<span class="nc bnc" id="L318" title="All 2 branches missed.">			if (height(t.right) - height(t.left) == 2)</span>
<span class="nc bnc" id="L319" title="All 2 branches missed.">				if (x.compareTo(t.right.element) &gt; 0) {</span>
<span class="nc" id="L320">					t = rotateWithRightChild(t);</span>
<span class="nc" id="L321">					countSingleRotations++;</span>
<span class="nc" id="L322">				} else {</span>
<span class="nc" id="L323">					t = doubleWithRightChild(t);</span>
<span class="nc" id="L324">					countDoubleRotations++;</span>
				}
<span class="nc" id="L326">		} else {</span>
<span class="nc" id="L327">			throw new Exception(&quot;Attempting to insert duplicate value&quot;);</span>
		}

<span class="nc" id="L330">		t.height = max(height(t.left), height(t.right)) + 1;</span>
<span class="nc" id="L331">		return t;</span>
	}

	/**
	 * Determine if the tree is empty.
	 * 
	 * @return True if the tree is empty
	 */
	public boolean isEmpty() {
<span class="nc bnc" id="L340" title="All 2 branches missed.">		return (root == null);</span>
	}

	/**
	 * Deletes all nodes from the tree.
	 *
	 */
	public void makeEmpty() {
<span class="nc" id="L348">		root = null;</span>
<span class="nc" id="L349">	}</span>

	/**
	 * Find the maximum value among the given numbers.
	 * 
	 * @param a
	 *            First number
	 * @param b
	 *            Second number
	 * @return Maximum value
	 */
	public int max(int a, int b) {
<span class="nc bnc" id="L361" title="All 2 branches missed.">		if (a &gt; b)</span>
<span class="nc" id="L362">			return a;</span>
<span class="nc" id="L363">		return b;</span>
	}

	/**
	 * Remove from the tree. Nothing is done if x is not found.
	 * 
	 * @param x
	 *            the item to remove.
	 */
	public void remove(T x) {
<span class="nc" id="L373">		root = remove(x, root);</span>
<span class="nc" id="L374">	}</span>

	// A version of remove from
	// http://www.dreamincode.net/forums/topic/214510-working-example-of-avl-tree-remove-method/
	// but it needs some attention and does not appear to be 100% correct

	public AvlNode&lt;T&gt; remove(T x, AvlNode&lt;T&gt; t) {
<span class="nc bnc" id="L381" title="All 2 branches missed.">		if (t == null) {</span>
<span class="nc" id="L382">			System.out.println(&quot;Sorry but you're mistaken, &quot; + t + &quot; doesn't exist in this tree :)\n&quot;);</span>
<span class="nc" id="L383">			return null;</span>
		}
<span class="nc" id="L385">		System.out.println(&quot;Remove starts... &quot; + t.element + &quot; and &quot; + x);</span>

<span class="nc bnc" id="L387" title="All 2 branches missed.">		if (x.compareTo(t.element) &lt; 0) {</span>
<span class="nc" id="L388">			t.left = remove(x, t.left);</span>
<span class="nc bnc" id="L389" title="All 2 branches missed.">			int l = t.left != null ? t.left.height : 0;</span>

<span class="nc bnc" id="L391" title="All 4 branches missed.">			if ((t.right != null) &amp;&amp; (t.right.height - l &gt;= 2)) {</span>
<span class="nc bnc" id="L392" title="All 2 branches missed.">				int rightHeight = t.right.right != null ? t.right.right.height : 0;</span>
<span class="nc bnc" id="L393" title="All 2 branches missed.">				int leftHeight = t.right.left != null ? t.right.left.height : 0;</span>

<span class="nc bnc" id="L395" title="All 2 branches missed.">				if (rightHeight &gt;= leftHeight)</span>
<span class="nc" id="L396">					t = rotateWithLeftChild(t);</span>
				else
<span class="nc" id="L398">					t = doubleWithRightChild(t);</span>
			}
<span class="nc bnc" id="L400" title="All 2 branches missed.">		} else if (x.compareTo(t.element) &gt; 0) {</span>
<span class="nc" id="L401">			t.right = remove(x, t.right);</span>
<span class="nc bnc" id="L402" title="All 2 branches missed.">			int r = t.right != null ? t.right.height : 0;</span>
<span class="nc bnc" id="L403" title="All 4 branches missed.">			if ((t.left != null) &amp;&amp; (t.left.height - r &gt;= 2)) {</span>
<span class="nc bnc" id="L404" title="All 2 branches missed.">				int leftHeight = t.left.left != null ? t.left.left.height : 0;</span>
<span class="nc bnc" id="L405" title="All 2 branches missed.">				int rightHeight = t.left.right != null ? t.left.right.height : 0;</span>
<span class="nc bnc" id="L406" title="All 2 branches missed.">				if (leftHeight &gt;= rightHeight)</span>
<span class="nc" id="L407">					t = rotateWithRightChild(t);</span>
				else
<span class="nc" id="L409">					t = doubleWithLeftChild(t);</span>
			}
<span class="nc" id="L411">		}</span>
		/*
		 * Here, we have ended up when we are node which shall be removed. Check if
		 * there is a left-hand node, if so pick out the largest element out, and move
		 * down to the root.
		 */
<span class="nc bnc" id="L417" title="All 2 branches missed.">		else if (t.left != null) {</span>
<span class="nc" id="L418">			t.element = findMax(t.left).element;</span>
<span class="nc" id="L419">			remove(t.element, t.left);</span>

<span class="nc bnc" id="L421" title="All 4 branches missed.">			if ((t.right != null) &amp;&amp; (t.right.height - t.left.height &gt;= 2)) {</span>
<span class="nc bnc" id="L422" title="All 2 branches missed.">				int rightHeight = t.right.right != null ? t.right.right.height : 0;</span>
<span class="nc bnc" id="L423" title="All 2 branches missed.">				int leftHeight = t.right.left != null ? t.right.left.height : 0;</span>

<span class="nc bnc" id="L425" title="All 2 branches missed.">				if (rightHeight &gt;= leftHeight)</span>
<span class="nc" id="L426">					t = rotateWithLeftChild(t);</span>
				else
<span class="nc" id="L428">					t = doubleWithRightChild(t);</span>
			}
<span class="nc" id="L430">		}</span>

		else
<span class="nc bnc" id="L433" title="All 2 branches missed.">			t = (t.left != null) ? t.left : t.right;</span>

<span class="nc bnc" id="L435" title="All 2 branches missed.">		if (t != null) {</span>
<span class="nc bnc" id="L436" title="All 2 branches missed.">			int leftHeight = t.left != null ? t.left.height : 0;</span>
<span class="nc bnc" id="L437" title="All 2 branches missed.">			int rightHeight = t.right != null ? t.right.height : 0;</span>
<span class="nc" id="L438">			t.height = Math.max(leftHeight, rightHeight) + 1;</span>
		}
<span class="nc" id="L440">		return t;</span>
	} // End of remove...

	/**
	 * Rotate binary tree node with left child. For AVL trees, this is a single
	 * rotation for case 1. Update heights, then return new root.
	 * 
	 * @param k2
	 *            Root of tree we are rotating
	 * @return New root
	 */
	protected AvlNode&lt;T&gt; rotateWithLeftChild(AvlNode&lt;T&gt; k2) {
<span class="nc" id="L452">		AvlNode&lt;T&gt; k1 = k2.left;</span>

<span class="nc" id="L454">		k2.left = k1.right;</span>
<span class="nc" id="L455">		k1.right = k2;</span>

<span class="nc" id="L457">		k2.height = max(height(k2.left), height(k2.right)) + 1;</span>
<span class="nc" id="L458">		k1.height = max(height(k1.left), k2.height) + 1;</span>

<span class="nc" id="L460">		return (k1);</span>
	}

	/**
	 * Rotate binary tree node with right child. For AVL trees, this is a single
	 * rotation for case 4. Update heights, then return new root.
	 * 
	 * @param k1
	 *            Root of tree we are rotating.
	 * @return New root
	 */
	protected AvlNode&lt;T&gt; rotateWithRightChild(AvlNode&lt;T&gt; k1) {
<span class="nc" id="L472">		AvlNode&lt;T&gt; k2 = k1.right;</span>

<span class="nc" id="L474">		k1.right = k2.left;</span>
<span class="nc" id="L475">		k2.left = k1;</span>

<span class="nc" id="L477">		k1.height = max(height(k1.left), height(k1.right)) + 1;</span>
<span class="nc" id="L478">		k2.height = max(height(k2.right), k1.height) + 1;</span>

<span class="nc" id="L480">		return (k2);</span>
	}

	/**
	 * Serialize the tree to a string using an infix traversal.
	 * 
	 * In other words, the tree items will be serialized in numeric order.
	 * 
	 * @return String representation of the tree
	 */
	public String serializeInfix() {
<span class="nc" id="L491">		StringBuilder str = new StringBuilder();</span>
<span class="nc" id="L492">		serializeInfix(root, str, &quot; &quot;);</span>
<span class="nc" id="L493">		return str.toString();</span>
	}

	/**
	 * Internal method to infix-serialize a tree.
	 * 
	 * @param t
	 *            Tree node to traverse
	 * @param str
	 *            Accumulator; string to keep appending items to.
	 */
	protected void serializeInfix(AvlNode&lt;T&gt; t, StringBuilder str, String sep) {
<span class="nc bnc" id="L505" title="All 2 branches missed.">		if (t != null) {</span>
<span class="nc" id="L506">			serializeInfix(t.left, str, sep);</span>
<span class="nc" id="L507">			str.append(t.element.toString());</span>
<span class="nc" id="L508">			str.append(sep);</span>
<span class="nc" id="L509">			serializeInfix(t.right, str, sep);</span>
		}
<span class="nc" id="L511">	}</span>

	/**
	 * Serialize the tree to a string using a prefix traversal.
	 * 
	 * In other words, the tree items will be serialized in the order that they are
	 * stored within the tree.
	 * 
	 * @return String representation of the tree
	 */
	public String serializePrefix() {
<span class="nc" id="L522">		StringBuilder str = new StringBuilder();</span>
<span class="nc" id="L523">		serializePrefix(root, str, &quot; &quot;);</span>
<span class="nc" id="L524">		return str.toString();</span>
	}

	/**
	 * Internal method to prefix-serialize a tree.
	 * 
	 * @param t
	 *            Tree node to traverse
	 * @param str
	 *            Accumulator; string to keep appending items to.
	 */
	private void serializePrefix(AvlNode&lt;T&gt; t, StringBuilder str, String sep) {
<span class="nc bnc" id="L536" title="All 2 branches missed.">		if (t != null) {</span>
<span class="nc" id="L537">			str.append(t.element.toString());</span>
<span class="nc" id="L538">			str.append(sep);</span>
<span class="nc" id="L539">			serializePrefix(t.left, str, sep);</span>
<span class="nc" id="L540">			serializePrefix(t.right, str, sep);</span>
		}
<span class="nc" id="L542">	}</span>

	/**
	 * Main entry point; contains test code for the tree.
	 *
	 * public static void main () { //String []args){ AvlTree&lt;Integer&gt; t = new
	 * AvlTree&lt;Integer&gt;();
	 * 
	 * t.insert (new Integer(2)); t.insert (new Integer(1)); t.insert (new
	 * Integer(4)); t.insert (new Integer(5)); t.insert (new Integer(9)); t.insert
	 * (new Integer(3)); t.insert (new Integer(6)); t.insert (new Integer(7));
	 * 
	 * System.out.println (&quot;Infix Traversal:&quot;);
	 * System.out.println(t.serializeInfix());
	 * 
	 * System.out.println (&quot;Prefix Traversal:&quot;);
	 * System.out.println(t.serializePrefix()); }
	 */
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>