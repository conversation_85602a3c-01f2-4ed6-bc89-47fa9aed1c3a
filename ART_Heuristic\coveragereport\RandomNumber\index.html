<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RandomNumber</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <span class="el_package">RandomNumber</span></div><h1>RandomNumber</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">641 of 641</td><td class="ctr2">0%</td><td class="bar">36 of 36</td><td class="ctr2">0%</td><td class="ctr1">37</td><td class="ctr2">37</td><td class="ctr1">143</td><td class="ctr2">143</td><td class="ctr1">16</td><td class="ctr2">16</td><td class="ctr1">6</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a0"><a href="RandomNumberCompare.html" class="el_class">RandomNumberCompare</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="250" alt="250"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="18" alt="18"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">17</td><td class="ctr2" id="g0">17</td><td class="ctr1" id="h0">63</td><td class="ctr2" id="i0">63</td><td class="ctr1" id="j0">5</td><td class="ctr2" id="k0">5</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a5"><a href="RNTwoRectangle.html" class="el_class">RNTwoRectangle</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="53" height="10" title="111" alt="111"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h1">21</td><td class="ctr2" id="i1">21</td><td class="ctr1" id="j2">2</td><td class="ctr2" id="k2">2</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a2"><a href="RandomNumberInTriangle.html" class="el_class">RandomNumberInTriangle</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="48" height="10" title="100" alt="100"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="66" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h2">21</td><td class="ctr2" id="i2">21</td><td class="ctr1" id="j1">3</td><td class="ctr2" id="k1">3</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a1"><a href="RandomNumberInCircle.html" class="el_class">RandomNumberInCircle</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="43" height="10" title="90" alt="90"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h4">15</td><td class="ctr2" id="i4">15</td><td class="ctr1" id="j3">2</td><td class="ctr2" id="k3">2</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a3"><a href="RandomNumberOutCircle.html" class="el_class">RandomNumberOutCircle</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="37" height="10" title="78" alt="78"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h3">17</td><td class="ctr2" id="i3">17</td><td class="ctr1" id="j4">2</td><td class="ctr2" id="k4">2</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a4"><a href="Rectangle.html" class="el_class">Rectangle</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="5" height="10" title="12" alt="12"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h5">6</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j5">2</td><td class="ctr2" id="k5">2</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>