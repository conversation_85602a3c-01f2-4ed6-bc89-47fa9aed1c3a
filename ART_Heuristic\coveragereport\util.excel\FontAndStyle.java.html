<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>FontAndStyle.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util.excel</a> &gt; <span class="el_source">FontAndStyle.java</span></div><h1>FontAndStyle.java</h1><pre class="source lang-java linenums">package util.excel;

import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFFont;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.poi.hssf.util.HSSFColor;

/**
 * Created by xijiaxiang on 2017/6/10.
 */
<span class="nc" id="L11">public class FontAndStyle {</span>
     class CellStyle{
         HSSFCellStyle style;
<span class="nc" id="L14">         public CellStyle(HSSFWorkbook wb,int colorNum){</span>
<span class="nc" id="L15">              style = wb.createCellStyle();</span>
<span class="nc" id="L16">             style.setAlignment(HSSFCellStyle.ALIGN_CENTER);</span>
<span class="nc" id="L17">             style.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);</span>
             // styleblue.setFillBackgroundColor(HSSFColor.BLUE.index);
<span class="nc" id="L19">             style.setFillForegroundColor(HSSFColor.BLUE.index);</span>
<span class="nc" id="L20">             style.setFillForegroundColor((short) colorNum);</span>
<span class="nc" id="L21">             style.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);</span>
<span class="nc" id="L22">        }</span>

         public HSSFCellStyle getStyle() {
<span class="nc" id="L25">             return style;</span>
         }
     }
    class Font{
        HSSFFont font;
<span class="nc" id="L30">         public Font(HSSFWorkbook wb,String fontName){</span>
<span class="nc" id="L31">             font= wb.createFont();</span>
<span class="nc" id="L32">             font.setFontName(fontName);</span>
<span class="nc" id="L33">             font.setBoldweight((short) 100);</span>
<span class="nc" id="L34">             font.setFontHeight((short) 300);</span>
<span class="nc" id="L35">             font.setColor(HSSFColor.BLACK.index);</span>
<span class="nc" id="L36">         }</span>

        public HSSFFont getFont() {
<span class="nc" id="L39">            return font;</span>
        }
    }

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>