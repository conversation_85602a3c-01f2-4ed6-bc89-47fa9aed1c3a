<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MersenneTwisterFast</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">util.random</a> &gt; <span class="el_class">MersenneTwisterFast</span></div><h1>MersenneTwisterFast</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">4,936 of 4,936</td><td class="ctr2">0%</td><td class="bar">326 of 326</td><td class="ctr2">0%</td><td class="ctr1">190</td><td class="ctr2">190</td><td class="ctr1">667</td><td class="ctr2">667</td><td class="ctr1">27</td><td class="ctr2">27</td></tr></tfoot><tbody><tr><td id="a2"><a href="MersenneTwisterFast.java.html#L1279" class="el_method">main(String[])</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="724" alt="724"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="96" alt="96"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">49</td><td class="ctr2" id="g0">49</td><td class="ctr1" id="h0">144</td><td class="ctr2" id="i0">144</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a16"><a href="MersenneTwisterFast.java.html#L975" class="el_method">nextGaussian()</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="117" height="10" title="707" alt="707"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="37" height="10" title="30" alt="30"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">16</td><td class="ctr2" id="g1">16</td><td class="ctr1" id="h1">79</td><td class="ctr2" id="i1">79</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a18"><a href="MersenneTwisterFast.java.html#L1195" class="el_method">nextInt(int)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="59" height="10" title="360" alt="360"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="22" height="10" title="18" alt="18"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">10</td><td class="ctr2" id="g3">10</td><td class="ctr1" id="h2">43</td><td class="ctr2" id="i2">43</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a7"><a href="MersenneTwisterFast.java.html#L592" class="el_method">nextBoolean(double)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="59" height="10" title="358" alt="358"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="27" height="10" title="22" alt="22"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">12</td><td class="ctr2" id="g2">12</td><td class="ctr1" id="h3">41</td><td class="ctr2" id="i3">41</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a20"><a href="MersenneTwisterFast.java.html#L789" class="el_method">nextLong(long)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="58" height="10" title="353" alt="353"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="20" height="10" title="16" alt="16"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">9</td><td class="ctr2" id="g4">9</td><td class="ctr1" id="h4">41</td><td class="ctr2" id="i4">41</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a12"><a href="MersenneTwisterFast.java.html#L863" class="el_method">nextDouble()</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="54" height="10" title="327" alt="327"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d8"><img src="../.resources/redbar.gif" width="15" height="10" title="12" alt="12"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f8">7</td><td class="ctr2" id="g8">7</td><td class="ctr1" id="h5">35</td><td class="ctr2" id="i5">35</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a19"><a href="MersenneTwisterFast.java.html#L729" class="el_method">nextLong()</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="53" height="10" title="320" alt="320"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d9"><img src="../.resources/redbar.gif" width="15" height="10" title="12" alt="12"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f9">7</td><td class="ctr2" id="g9">7</td><td class="ctr1" id="h6">35</td><td class="ctr2" id="i6">35</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a8"><a href="MersenneTwisterFast.java.html#L547" class="el_method">nextBoolean(float)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="32" height="10" title="194" alt="194"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="20" height="10" title="16" alt="16"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f5">9</td><td class="ctr2" id="g5">9</td><td class="ctr1" id="h8">24</td><td class="ctr2" id="i8">24</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a10"><a href="MersenneTwisterFast.java.html#L689" class="el_method">nextBytes(byte[])</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="28" height="10" title="172" alt="172"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d12"><img src="../.resources/redbar.gif" width="10" height="10" title="8" alt="8"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f12">5</td><td class="ctr2" id="g12">5</td><td class="ctr1" id="h9">20</td><td class="ctr2" id="i9">20</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a6"><a href="MersenneTwisterFast.java.html#L507" class="el_method">nextBoolean()</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="27" height="10" title="164" alt="164"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d13"><img src="../.resources/redbar.gif" width="10" height="10" title="8" alt="8"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f13">5</td><td class="ctr2" id="g13">5</td><td class="ctr1" id="h10">18</td><td class="ctr2" id="i10">18</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a14"><a href="MersenneTwisterFast.java.html#L1116" class="el_method">nextFloat()</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="27" height="10" title="163" alt="163"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d14"><img src="../.resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f14">4</td><td class="ctr2" id="g14">4</td><td class="ctr1" id="h11">18</td><td class="ctr2" id="i11">18</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a21"><a href="MersenneTwisterFast.java.html#L443" class="el_method">nextShort()</a></td><td class="bar" id="b11"><img src="../.resources/redbar.gif" width="26" height="10" title="161" alt="161"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d15"><img src="../.resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g15">4</td><td class="ctr1" id="h12">18</td><td class="ctr2" id="i12">18</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a11"><a href="MersenneTwisterFast.java.html#L475" class="el_method">nextChar()</a></td><td class="bar" id="b12"><img src="../.resources/redbar.gif" width="26" height="10" title="161" alt="161"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d16"><img src="../.resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f16">4</td><td class="ctr2" id="g16">4</td><td class="ctr1" id="h13">18</td><td class="ctr2" id="i13">18</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a9"><a href="MersenneTwisterFast.java.html#L657" class="el_method">nextByte()</a></td><td class="bar" id="b13"><img src="../.resources/redbar.gif" width="26" height="10" title="161" alt="161"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d17"><img src="../.resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f17">4</td><td class="ctr2" id="g17">4</td><td class="ctr1" id="h14">18</td><td class="ctr2" id="i14">18</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a17"><a href="MersenneTwisterFast.java.html#L411" class="el_method">nextInt()</a></td><td class="bar" id="b14"><img src="../.resources/redbar.gif" width="26" height="10" title="158" alt="158"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d18"><img src="../.resources/redbar.gif" width="7" height="10" title="6" alt="6"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f18">4</td><td class="ctr2" id="g18">4</td><td class="ctr1" id="h15">18</td><td class="ctr2" id="i15">18</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a23"><a href="MersenneTwisterFast.java.html#L375" class="el_method">setSeed(int[])</a></td><td class="bar" id="b15"><img src="../.resources/redbar.gif" width="22" height="10" title="135" alt="135"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="17" height="10" title="14" alt="14"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f6">8</td><td class="ctr2" id="g6">8</td><td class="ctr1" id="h7">25</td><td class="ctr2" id="i7">25</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a24"><a href="MersenneTwisterFast.java.html#L347" class="el_method">setSeed(long)</a></td><td class="bar" id="b16"><img src="../.resources/redbar.gif" width="11" height="10" title="71" alt="71"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d21"><img src="../.resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h19">9</td><td class="ctr2" id="i19">9</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a25"><a href="MersenneTwisterFast.java.html#L266" class="el_method">stateEquals(MersenneTwisterFast)</a></td><td class="bar" id="b17"><img src="../.resources/redbar.gif" width="9" height="10" title="58" alt="58"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="17" height="10" title="14" alt="14"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f7">8</td><td class="ctr2" id="g7">8</td><td class="ctr1" id="h16">13</td><td class="ctr2" id="i16">13</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a22"><a href="MersenneTwisterFast.java.html#L284" class="el_method">readState(DataInputStream)</a></td><td class="bar" id="b18"><img src="../.resources/redbar.gif" width="7" height="10" title="47" alt="47"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d19"><img src="../.resources/redbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f19">3</td><td class="ctr2" id="g19">3</td><td class="ctr1" id="h17">10</td><td class="ctr2" id="i17">10</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a26"><a href="MersenneTwisterFast.java.html#L299" class="el_method">writeState(DataOutputStream)</a></td><td class="bar" id="b19"><img src="../.resources/redbar.gif" width="7" height="10" title="47" alt="47"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d20"><img src="../.resources/redbar.gif" width="5" height="10" title="4" alt="4"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f20">3</td><td class="ctr2" id="g20">3</td><td class="ctr1" id="h18">10</td><td class="ctr2" id="i18">10</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a13"><a href="MersenneTwisterFast.java.html#L951" class="el_method">nextDouble(boolean, boolean)</a></td><td class="bar" id="b20"><img src="../.resources/redbar.gif" width="4" height="10" title="26" alt="26"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d10"><img src="../.resources/redbar.gif" width="12" height="10" title="10" alt="10"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f10">6</td><td class="ctr2" id="g10">6</td><td class="ctr1" id="h20">7</td><td class="ctr2" id="i20">7</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a15"><a href="MersenneTwisterFast.java.html#L1177" class="el_method">nextFloat(boolean, boolean)</a></td><td class="bar" id="b21"><img src="../.resources/redbar.gif" width="4" height="10" title="26" alt="26"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d11"><img src="../.resources/redbar.gif" width="12" height="10" title="10" alt="10"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f11">6</td><td class="ctr2" id="g11">6</td><td class="ctr1" id="h21">7</td><td class="ctr2" id="i21">7</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a1"><a href="MersenneTwisterFast.java.html#L245" class="el_method">clone()</a></td><td class="bar" id="b22"><img src="../.resources/redbar.gif" width="3" height="10" title="23" alt="23"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h22">6</td><td class="ctr2" id="i22">6</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a5"><a href="MersenneTwisterFast.java.html#L324" class="el_method">MersenneTwisterFast(long)</a></td><td class="bar" id="b23"/><td class="ctr2" id="c23">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h23">3</td><td class="ctr2" id="i23">3</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a4"><a href="MersenneTwisterFast.java.html#L334" class="el_method">MersenneTwisterFast(int[])</a></td><td class="bar" id="b24"/><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h24">3</td><td class="ctr2" id="i24">3</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a3"><a href="MersenneTwisterFast.java.html#L316" class="el_method">MersenneTwisterFast()</a></td><td class="bar" id="b25"/><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h25">2</td><td class="ctr2" id="i25">2</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a0"><a href="MersenneTwisterFast.java.html#L971" class="el_method">clearGaussian()</a></td><td class="bar" id="b26"/><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"/><td class="ctr2" id="e26">n/a</td><td class="ctr1" id="f26">1</td><td class="ctr2" id="g26">1</td><td class="ctr1" id="h26">2</td><td class="ctr2" id="i26">2</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>