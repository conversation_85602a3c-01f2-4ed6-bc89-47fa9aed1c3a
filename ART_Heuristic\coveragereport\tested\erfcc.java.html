<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>erfcc.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">erfcc.java</span></div><h1>erfcc.java</h1><pre class="source lang-java linenums">package tested;

import util.TestProgram;

/*
 * Input Domain:(-30000,30000)
 * failure rate:0.000574 avf=1/0.000574=1742.1602787456445993031358885017
 *errors: 5 errors(2 AOR, 1 ROR, 1SVR ,1CR)
 * */
<span class="nc" id="L10">public class erfcc {</span>
<span class="nc" id="L11">	public  static double[] min = { -30000 };</span>
<span class="nc" id="L12">	public  static double[] max = { 30000 };</span>
<span class="nc" id="L13">	public  static double failureRate = 0.000574;</span>
<span class="nc" id="L14">	public  static int Dimension = 1;</span>

	public boolean isCorrect(double x) {
<span class="nc" id="L17">		return TestProgram.test_erfcc(x);</span>
	}
	/*
	 * public double correct(double x) { double t,z,ans; z=Math.abs(x);
	 * t=1.0/(1.0+0.5*z);
	 * ans=t*Math.exp(-z*z-1.26551223+t*(1.00002368+t*(0.37409196+t*(0.09678418+
	 * t*(-0.18628806+t*(0.27886807+t*(-1.13520398+t*(1.48851587+
	 * t*(-0.82215223+t*0.17087277))))))))); return x &gt;= 0.0 ? ans : 2.0-ans; }
	 * public double wrong(double x) { double t,z,ans;
	 * 
	 * z=Math.abs(x); t=1.0/(1.0+0.5*z);
	 * ans=t*Math.exp(-z*z-1.26551223+t*(1.00002368+t*(0.37409196+t*(0.09678418+
	 * ERROR t*(-0.18628806+t*(0.27886807+t*(-1.13520398+t*(1.48851587+
	 * t*(-0.18628806+t*(0.27886807+z*(-1.13520398+t*(1.48851587+ ERROR
	 * t*(-0.82215223+t*0.17087277))))))))); t*(-0.82215223-t*0.17087277)))))))));
	 * ERROR return x &gt;= 0.0 ? ans : 2.0-ans; return x &gt; 0.1 ? ans : 2.0-ans; }
	 */

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>