<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>Bi_Test.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.bi_t</a> &gt; <span class="el_source">Bi_Test.java</span></div><h1>Bi_Test.java</h1><pre class="source lang-java linenums">package test.simulations.bi_t;

import java.util.Random;

public class Bi_Test {
	public static void main(String[] args) {
<span class="nc" id="L7">		int cishu = 3000;</span>
<span class="nc" id="L8">		long sumOfF = 0;</span>
<span class="nc" id="L9">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L10" title="All 2 branches missed.">		for (int i = 0; i &lt; cishu; i++) {</span>
<span class="nc" id="L11">			Bi_Test bi_t = new Bi_Test((i * 3));</span>

<span class="nc" id="L13">			int f_measure = bi_t.run();</span>
<span class="nc" id="L14">			sumOfF += f_measure;</span>
		}
<span class="nc" id="L16">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L17">		System.out.println(&quot;Fm: &quot; + sumOfF / (double) cishu);</span>
<span class="nc" id="L18">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) cishu);</span>
<span class="nc" id="L19">	}</span>
	double fail_start;
<span class="nc" id="L21">	double fail_rate = 0.0005;</span>

	int randomseed;
	// ArrayList&lt;Double&gt; al = new ArrayList&lt;&gt;();

<span class="nc" id="L26">	public Bi_Test(int seed) {</span>
<span class="nc" id="L27">		randomseed = seed;</span>
<span class="nc" id="L28">	}</span>

	public int run() {
<span class="nc" id="L31">		Random random = new Random(randomseed);</span>
<span class="nc" id="L32">		int count = 0;</span>
<span class="nc" id="L33">		fail_start = random.nextDouble() * (1 - fail_rate);</span>
<span class="nc" id="L34">		double p = 0.5;</span>
<span class="nc" id="L35">		int i = 1, m = 0;</span>
<span class="nc bnc" id="L36" title="All 2 branches missed.">		while (test(p)) {</span>
<span class="nc" id="L37">			count++;</span>
<span class="nc" id="L38">			m = (int) (count + 1 - Math.pow(2, i));</span>
<span class="nc" id="L39">			p = (Math.pow(2, -(i + 1))) * (2 * m + 1);</span>
<span class="nc bnc" id="L40" title="All 2 branches missed.">			if (2 * m + 1 == (Math.pow(2, i + 1)) - 1) {</span>
<span class="nc" id="L41">				i++;</span>
			}

		}
<span class="nc" id="L45">		return count;</span>
	}

	public boolean test(double p) {
		boolean flag;
<span class="nc bnc" id="L50" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L51">			flag = false;</span>
<span class="nc" id="L52">		} else {</span>
<span class="nc" id="L53">			flag = true;</span>
		}
<span class="nc" id="L55">		return flag;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>