<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ThreadedTree.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure</a> &gt; <span class="el_source">ThreadedTree.java</span></div><h1>ThreadedTree.java</h1><pre class="source lang-java linenums">package datastructure;

public class ThreadedTree {
<span class="nc" id="L4">	private class ThreadedNode {</span>
		double value;
		ThreadedNode right;
		ThreadedNode left;
		boolean leftThread, rightThread;
	}

	public static void main(String[] args) {
<span class="nc" id="L12">		ThreadedTree tree = new ThreadedTree();</span>
<span class="nc" id="L13">		tree.insert(1.2);</span>
<span class="nc" id="L14">		tree.insert(0.5);</span>
<span class="nc" id="L15">		tree.insert(1.5);</span>
<span class="nc" id="L16">		tree.insert(0.00001);</span>
<span class="nc" id="L17">		tree.search(0.00001);</span>
<span class="nc" id="L18">		System.out.println();</span>
<span class="nc" id="L19">	}</span>

	private ThreadedNode root;

<span class="nc" id="L23">	public ThreadedTree() {</span>
<span class="nc" id="L24">		root = null;</span>
<span class="nc" id="L25">	}</span>

	public void delete(double value) {
<span class="nc bnc" id="L28" title="All 2 branches missed.">		if (root == null)</span>
<span class="nc" id="L29">			return;</span>
<span class="nc" id="L30">		ThreadedNode parent = root;</span>
<span class="nc" id="L31">		ThreadedNode curr = root;</span>
<span class="nc bnc" id="L32" title="All 2 branches missed.">		while (curr != null) {</span>
<span class="nc bnc" id="L33" title="All 2 branches missed.">			if (value &lt; curr.value) {</span>
<span class="nc" id="L34">				parent = curr;</span>
<span class="nc bnc" id="L35" title="All 2 branches missed.">				if (curr.leftThread)</span>
<span class="nc" id="L36">					break;</span>
<span class="nc" id="L37">				curr = curr.left;</span>
<span class="nc bnc" id="L38" title="All 2 branches missed.">			} else if (value &gt; curr.value) {</span>
<span class="nc" id="L39">				parent = curr;</span>
<span class="nc bnc" id="L40" title="All 2 branches missed.">				if (curr.rightThread)</span>
<span class="nc" id="L41">					break;</span>
<span class="nc" id="L42">				curr = curr.right;</span>
			} else {
				break;
			}
		}
<span class="nc bnc" id="L47" title="All 2 branches missed.">		if (curr == null)</span>
<span class="nc" id="L48">			return;</span>
<span class="nc" id="L49">		int side = 0;</span>
<span class="nc bnc" id="L50" title="All 2 branches missed.">		if (value &lt; parent.value) {</span>
<span class="nc" id="L51">			side = 0;</span>
<span class="nc" id="L52">		} else {</span>
<span class="nc" id="L53">			side = 1;</span>
		}
<span class="nc bnc" id="L55" title="All 8 branches missed.">		if ((curr.leftThread || curr.left == null) &amp;&amp; (curr.rightThread || curr.right == null)) {</span>
<span class="nc bnc" id="L56" title="All 2 branches missed.">			if (side == 0) {</span>
<span class="nc" id="L57">				parent.left = curr.left;</span>
<span class="nc" id="L58">				parent.leftThread = true;</span>
<span class="nc" id="L59">			} else {</span>
<span class="nc" id="L60">				parent.right = curr.right;</span>
<span class="nc" id="L61">				parent.rightThread = true;</span>
			}
<span class="nc bnc" id="L63" title="All 4 branches missed.">		} else if (curr.rightThread || curr.right == null) {</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">			if (side == 0) {</span>
<span class="nc" id="L65">				parent.left = curr.left;</span>
<span class="nc" id="L66">			} else {</span>
<span class="nc" id="L67">				parent.right = curr.left;</span>
			}
<span class="nc" id="L69">			curr.left.right = curr.right;</span>
<span class="nc" id="L70">			curr.left.rightThread = true;</span>
<span class="nc bnc" id="L71" title="All 4 branches missed.">		} else if (curr.leftThread || curr.left == null) {</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">			if (side == 0) {</span>
<span class="nc" id="L73">				parent.left = curr.right;</span>
<span class="nc" id="L74">			} else {</span>
<span class="nc" id="L75">				parent.right = curr.right;</span>
			}
<span class="nc" id="L77">			curr.right.left = curr.left;</span>
<span class="nc" id="L78">			curr.right.leftThread = true;</span>
<span class="nc" id="L79">		} else {</span>
<span class="nc" id="L80">			ThreadedNode leftMost = curr.right;</span>
<span class="nc" id="L81">			ThreadedNode leftMostParent = curr.right;</span>
<span class="nc bnc" id="L82" title="All 4 branches missed.">			while (leftMost.left != null &amp;&amp; !leftMost.leftThread) {</span>
<span class="nc" id="L83">				leftMostParent = leftMost;</span>
<span class="nc" id="L84">				leftMost = leftMost.left;</span>
			}

<span class="nc" id="L87">			curr.value = leftMost.value;</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">			if (leftMost.rightThread)</span>
<span class="nc" id="L89">				curr.right = leftMost.right;</span>
<span class="nc bnc" id="L90" title="All 4 branches missed.">			if (leftMost.right != null &amp;&amp; !leftMost.rightThread) {</span>
<span class="nc" id="L91">				leftMost.right.left = curr;</span>
<span class="nc" id="L92">				leftMost.right.leftThread = true;</span>
<span class="nc bnc" id="L93" title="All 2 branches missed.">				if (leftMost != leftMostParent) {</span>
<span class="nc bnc" id="L94" title="All 2 branches missed.">					if (leftMost.right.value != leftMostParent.value) {</span>
<span class="nc" id="L95">						leftMostParent.left = leftMost.right;</span>
					}
				}
			}

		}
<span class="nc" id="L101">	}</span>

	public void insert(double value) {

<span class="nc bnc" id="L105" title="All 2 branches missed.">		if (root == null) {</span>
<span class="nc" id="L106">			root = new ThreadedNode();</span>
<span class="nc" id="L107">			root.value = value;</span>
<span class="nc" id="L108">			root.leftThread = false;</span>
<span class="nc" id="L109">			root.rightThread = false;</span>
<span class="nc" id="L110">		} else {</span>
<span class="nc" id="L111">			ThreadedNode parent = null;</span>
<span class="nc" id="L112">			ThreadedNode curr = root;</span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">			while (curr != null) {</span>
<span class="nc bnc" id="L114" title="All 2 branches missed.">				if (value &lt; curr.value) {</span>
<span class="nc" id="L115">					parent = curr;</span>
<span class="nc bnc" id="L116" title="All 2 branches missed.">					if (curr.leftThread)</span>
<span class="nc" id="L117">						break;</span>
<span class="nc" id="L118">					curr = curr.left;</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">				} else if (value &gt; curr.value) {</span>
<span class="nc" id="L120">					parent = curr;</span>
<span class="nc bnc" id="L121" title="All 2 branches missed.">					if (curr.rightThread)</span>
<span class="nc" id="L122">						break;</span>
<span class="nc" id="L123">					curr = curr.right;</span>
<span class="nc" id="L124">				} else {</span>
<span class="nc" id="L125">					return;</span>
				}
			}
<span class="nc bnc" id="L128" title="All 2 branches missed.">			if (value &lt; parent.value) {</span>
<span class="nc bnc" id="L129" title="All 2 branches missed.">				if (parent.leftThread) {</span>
<span class="nc" id="L130">					curr = new ThreadedNode();</span>
<span class="nc" id="L131">					curr.value = value;</span>
<span class="nc" id="L132">					curr.left = parent.left;</span>
<span class="nc" id="L133">					curr.leftThread = true;</span>
<span class="nc" id="L134">					curr.right = parent;</span>
<span class="nc" id="L135">					curr.rightThread = true;</span>

<span class="nc" id="L137">					parent.leftThread = false;</span>
<span class="nc" id="L138">					parent.left = curr;</span>
<span class="nc" id="L139">				} else {</span>
<span class="nc" id="L140">					parent.left = new ThreadedNode();</span>
<span class="nc" id="L141">					curr = parent.left;</span>

<span class="nc" id="L143">					curr.value = value;</span>
<span class="nc" id="L144">					curr.rightThread = true;</span>
<span class="nc" id="L145">					curr.right = parent;</span>
				}
<span class="nc" id="L147">			} else {</span>
<span class="nc bnc" id="L148" title="All 2 branches missed.">				if (parent.rightThread) {</span>
<span class="nc" id="L149">					curr = new ThreadedNode();</span>
<span class="nc" id="L150">					curr.value = value;</span>
<span class="nc" id="L151">					curr.right = parent.right;</span>
<span class="nc" id="L152">					curr.rightThread = true;</span>
<span class="nc" id="L153">					curr.left = parent;</span>
<span class="nc" id="L154">					curr.leftThread = true;</span>

<span class="nc" id="L156">					parent.rightThread = false;</span>
<span class="nc" id="L157">					parent.right = curr;</span>
<span class="nc" id="L158">				} else {</span>
<span class="nc" id="L159">					parent.right = new ThreadedNode();</span>
<span class="nc" id="L160">					curr = parent.right;</span>

<span class="nc" id="L162">					curr.value = value;</span>
<span class="nc" id="L163">					curr.leftThread = true;</span>
<span class="nc" id="L164">					curr.left = parent;</span>
				}
			}
		}
<span class="nc" id="L168">	}</span>

	public boolean search(double value) {
<span class="nc bnc" id="L171" title="All 2 branches missed.">		if (root == null)</span>
<span class="nc" id="L172">			return false;</span>

<span class="nc" id="L174">		ThreadedNode curr = root;</span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">		while (curr != null) {</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">			if (value &lt; curr.value) {</span>
<span class="nc bnc" id="L177" title="All 2 branches missed.">				if (curr.leftThread)</span>
<span class="nc" id="L178">					break;</span>
<span class="nc" id="L179">				curr = curr.left;</span>
<span class="nc bnc" id="L180" title="All 2 branches missed.">			} else if (value &gt; curr.value) {</span>
<span class="nc bnc" id="L181" title="All 2 branches missed.">				if (curr.rightThread)</span>
<span class="nc" id="L182">					break;</span>
<span class="nc" id="L183">				curr = curr.right;</span>
<span class="nc" id="L184">			} else {</span>
<span class="nc" id="L185">				System.out.println(&quot;find:&quot; + curr.value);</span>
<span class="nc" id="L186">				return true;</span>
			}
		}
<span class="nc" id="L189">		return false;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>