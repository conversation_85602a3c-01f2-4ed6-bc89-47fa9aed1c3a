<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>PRT_MR_OD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.prt</a> &gt; <span class="el_source">PRT_MR_OD.java</span></div><h1>PRT_MR_OD.java</h1><pre class="source lang-java linenums">package test.simulations.prt;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

/***
 *
 * <AUTHOR> PRT_MR_OD 最大区域，maxRegion
 */
public class PRT_MR_OD {
	public static void main(String[] args) {
<span class="nc" id="L14">		double exp = 3;</span>
<span class="nc" id="L15">		double fail_rate = 0.005;</span>
<span class="nc" id="L16">		int times = 5000;</span>
<span class="nc" id="L17">		long sums = 0;</span>
		// long startTime=System.nanoTime();
<span class="nc" id="L19">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L20" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L21">			PRT_MR_OD prt = new PRT_MR_OD((i + 3) * 15, fail_rate, exp);</span>
<span class="nc" id="L22">			int f_measure = prt.run();</span>
<span class="nc" id="L23">			sums += f_measure;</span>
		}
<span class="nc" id="L25">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L26">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L27">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L28">	}</span>
	double fail_start;
	double fail_rate;
	int seedOfRandom;
	double exp;// 分布函数的指数

<span class="nc" id="L34">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L36">	public PRT_MR_OD(int seed, double fail_rate, double exp) {</span>
<span class="nc" id="L37">		this.seedOfRandom = seed;</span>
<span class="nc" id="L38">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L39">		this.exp = exp;</span>
<span class="nc" id="L40">	}</span>

	// check is failure
	public boolean isCorrect(double p) {
<span class="nc bnc" id="L44" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L45">			return false;</span>
		} else {
<span class="nc" id="L47">			return true;</span>
		}
	}

	public int run() {
<span class="nc" id="L52">		Random random = new Random(seedOfRandom);</span>
<span class="nc" id="L53">		fail_start = random.nextDouble() * (1 - fail_rate);</span>
		// System.out.println(&quot;fail:&quot;+fail_start);
<span class="nc" id="L55">		int count = 0;</span>
<span class="nc" id="L56">		double value = random.nextDouble();</span>
<span class="nc" id="L57">		TestCase p = new TestCase();</span>
<span class="nc" id="L58">		p.p = value;</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L60">			count++;</span>
			/* sort tests */
<span class="nc bnc" id="L62" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L63">				tests.add(p);</span>
<span class="nc" id="L64">			} else {</span>
<span class="nc" id="L65">				sortTestCases(p);</span>
			}
			/* end sort */
			// init subRegion range
			// every subRegion low and high
<span class="nc" id="L70">			double low = 0.0, high = 1.0;</span>
<span class="nc" id="L71">			double Max = 0.0;</span>
			// Max subRegion low and high and index
<span class="nc" id="L73">			double Mhigh = 1.0, Mlow = 0.0;</span>
<span class="nc" id="L74">			int indexOfMnode = 0;</span>
<span class="nc bnc" id="L75" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size(); i++) {</span>
<span class="nc bnc" id="L76" title="All 2 branches missed.">				if (i == 0) {</span>
<span class="nc" id="L77">					low = 0.0;</span>
<span class="nc" id="L78">				} else {</span>
<span class="nc" id="L79">					low = (tests.get(i).p + tests.get(i - 1).p) / 2.0;</span>
				}
<span class="nc bnc" id="L81" title="All 2 branches missed.">				if (i == (tests.size() - 1)) {</span>
<span class="nc" id="L82">					high = 1.0;</span>
<span class="nc" id="L83">				} else {</span>
<span class="nc" id="L84">					high = (tests.get(i).p + tests.get(i + 1).p) / 2.0;</span>
				}
<span class="nc bnc" id="L86" title="All 2 branches missed.">				if (Max &lt; (high - low)) {</span>
<span class="nc" id="L87">					Max = high - low;</span>
<span class="nc" id="L88">					Mhigh = high;</span>
<span class="nc" id="L89">					Mlow = low;</span>
<span class="nc" id="L90">					indexOfMnode = i;</span>
				}
			}
			// System.out.println(&quot;max:(&quot;+Mlow+&quot;,&quot;+Mhigh+&quot;) &quot;+indexOfMnode);
			// 求出概率分布函数的系数
<span class="nc" id="L95">			double cMaxNode = tests.get(indexOfMnode).p;</span>
<span class="nc" id="L96">			double Co = (exp + 1.0)</span>
<span class="nc" id="L97">					/ (Math.pow((cMaxNode - Mlow), (exp + 1.0)) + Math.pow(Mhigh - cMaxNode, (exp + 1.0)));</span>
			// 概率学生成下一个测试用例
<span class="nc" id="L99">			double T = random.nextDouble();</span>
			//// 根据概率分布算出一个随机的值
			// 第一段积分值为intgral(t-r)^3 (low,t)
<span class="nc" id="L102">			double FirstIntgral = Co * (Math.pow(cMaxNode - Mlow, (exp + 1.0)) / (exp + 1.0));</span>
			// double SecondIntgral=Co*(Math.pow(Mhigh-cMaxNode,(exp+1.0))/(exp+1.0));
<span class="nc" id="L104">			p = new TestCase();</span>
<span class="nc bnc" id="L105" title="All 2 branches missed.">			if (T &lt;= FirstIntgral) {</span>
<span class="nc" id="L106">				p.p = cMaxNode - Math.pow((exp + 1.0) * (FirstIntgral - T) / Co, 1.0 / (exp + 1.0));</span>
<span class="nc" id="L107">			} else {</span>
<span class="nc" id="L108">				p.p = cMaxNode + Math.pow((exp + 1.0) * (T - FirstIntgral) / Co, 1.0 / (exp + 1.0));</span>
			}
		}
		// System.out.println(&quot;last p:&quot;+p);
<span class="nc" id="L112">		return count;</span>
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L116">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L118">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L120">				low = mid + 1;</span>
<span class="nc" id="L121">			} else {</span>
<span class="nc" id="L122">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L125" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L126">			mid = mid - 1;</span>
		}
<span class="nc" id="L128">		tests.add(mid + 1, p);</span>
<span class="nc" id="L129">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>