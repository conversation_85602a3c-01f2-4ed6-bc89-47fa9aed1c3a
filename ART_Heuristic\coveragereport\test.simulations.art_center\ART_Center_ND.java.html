<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_Center_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_center</a> &gt; <span class="el_source">ART_Center_ND.java</span></div><h1>ART_Center_ND.java</h1><pre class="source lang-java linenums">package test.simulations.art_center;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import util.PaiLie;
import util.data.ZeroOneCreator;

public class ART_Center_ND extends ART {

//	public int allParts = 1000;
//	public int startParts = 425;
//	public int endParts = 575;

<span class="nc" id="L21">	public double howManyParts=0.220;</span>
	
<span class="nc" id="L23">	public ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L24">	public ArrayList&lt;NRectRegion&gt; regions = new ArrayList&lt;&gt;();</span>

	public ART_Center_ND(double[] min, double[] max, Random random, FailurePattern failurePattern) {
<span class="nc" id="L27">		super(min, max, random, failurePattern);</span>
<span class="nc" id="L28">	}</span>

	@Override
	public int run() {
<span class="nc" id="L32">		int count = 0;</span>
<span class="nc" id="L33">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc" id="L34">		NRectRegion maxRegion = new NRectRegion(new NPoint(min), new NPoint(max));</span>
<span class="nc" id="L35">		this.regions.add(maxRegion);</span>
<span class="nc" id="L36">		int index = 0;</span>

<span class="nc bnc" id="L38" title="All 2 branches missed.">		while (this.failPattern.isCorrect(p)) {</span>
			// count++;
<span class="nc" id="L40">			count++;</span>

			// add tests
<span class="nc" id="L43">			this.tests.add(p);</span>

			// split region
<span class="nc" id="L46">			splitRegion(index, p);</span>
			// max region
<span class="nc" id="L48">			index = maxRegion();</span>
			//make the region min
<span class="nc" id="L50">			NRectRegion smallerRegion=makeMaxRegionSmall(this.regions.get(index));</span>
			// another point
<span class="nc" id="L52">			p = randomCreator.randomPoint(smallerRegion);</span>
		}
<span class="nc" id="L54">		return count;</span>
	}

	// 只生成测试用例的方法
	public NPoint generateTC() {
<span class="nc" id="L59">		NPoint p = null;</span>

<span class="nc bnc" id="L61" title="All 2 branches missed.">		if (this.tests.size() == 0) {</span>
<span class="nc" id="L62">			p = randomCreator.randomPoint();</span>
<span class="nc" id="L63">			this.regions.add(new NRectRegion(new NPoint(min), new NPoint(max)));</span>
<span class="nc" id="L64">			splitRegion(0, p);</span>
<span class="nc" id="L65">		} else {</span>
<span class="nc" id="L66">			int index = maxRegion();</span>
<span class="nc" id="L67">			p = randomCreator.randomPoint(makeMaxRegionSmall(this.regions.get(index)));</span>
<span class="nc" id="L68">			splitRegion(index, p);</span>
		}
<span class="nc" id="L70">		this.tests.add(p);</span>

<span class="nc" id="L72">		return p;</span>
	}

	public NRectRegion makeMaxRegionSmall(NRectRegion maxregion){
<span class="nc" id="L76">		double[] start=maxregion.getStart().getXn();</span>
<span class="nc" id="L77">		double[] end=maxregion.getEnd().getXn();</span>
<span class="nc" id="L78">		double[] s1=new double[start.length];</span>
<span class="nc" id="L79">		double[] e1=new double[end.length];</span>
		
		//double eachtemplength = 1.0 / (double)allParts;
<span class="nc bnc" id="L82" title="All 2 branches missed.">		for(int i=0;i&lt;start.length;i++){</span>
<span class="nc" id="L83">			double length=(end[i] - start[i]);</span>
			
<span class="nc" id="L85">			s1[i]=0.5*(end[i]+start[i])-howManyParts*length*0.5;</span>
<span class="nc" id="L86">			e1[i]=0.5*(end[i]+start[i])+howManyParts*length*0.5;</span>
			
		}
		
<span class="nc" id="L90">		return new NRectRegion(new NPoint(s1),new NPoint(e1));</span>
		
	}
	
	public void splitRegion(int index, NPoint p) {
<span class="nc bnc" id="L95" title="All 4 branches missed.">		if (index &lt; 0 || index &gt;= this.regions.size()) {</span>
<span class="nc" id="L96">			System.out.println(&quot;split region error! index not correct!&quot;);</span>
<span class="nc" id="L97">			return;</span>
		}
		// first remove it
<span class="nc" id="L100">		NRectRegion region = this.regions.remove(index);</span>
		try {
			// add regions;
<span class="nc" id="L103">			addRegionsInND(region, p);</span>
<span class="nc" id="L104">		} catch (Exception e) {</span>
<span class="nc" id="L105">			System.out.println(&quot;split region error in split region rec&quot;);</span>
		}
<span class="nc" id="L107">	}</span>

	public void addRegionsInND(NRectRegion region, NPoint p) throws Exception {
<span class="nc" id="L110">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L111">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L112">		double[] pxn = p.getXn();</span>
<span class="nc" id="L113">		List&lt;List&lt;Double&gt;&gt; result1 = splitRegions(start, pxn);</span>
<span class="nc" id="L114">		List&lt;List&lt;Double&gt;&gt; result2 = splitRegions(pxn, end);</span>
		// System.out.println(result1.size());
<span class="nc bnc" id="L116" title="All 2 branches missed.">		if (result1.size() != result2.size()) {</span>
<span class="nc" id="L117">			throw new Exception(&quot;result1's size!=result2's size ,split region wrong&quot;);</span>
		}
<span class="nc bnc" id="L119" title="All 2 branches missed.">		for (int i = 0; i &lt; result1.size(); i++) {</span>
<span class="nc" id="L120">			List&lt;Double&gt; temp1 = result1.get(i);</span>
<span class="nc" id="L121">			List&lt;Double&gt; temp2 = result2.get(i);</span>
<span class="nc" id="L122">			double[] newStart = new double[temp1.size()];</span>
<span class="nc" id="L123">			double[] newEnd = new double[temp2.size()];</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">			for (int j = 0; j &lt; temp1.size(); j++) {</span>
<span class="nc" id="L125">				newStart[j] = temp1.get(j);</span>
<span class="nc" id="L126">				newEnd[j] = temp2.get(j);</span>
			}

<span class="nc" id="L129">			NRectRegion tempRegion = new NRectRegion(new NPoint(newStart), new NPoint(newEnd));</span>
<span class="nc" id="L130">			this.regions.add(tempRegion);</span>
		}
<span class="nc" id="L132">	}</span>

	public List&lt;List&lt;Double&gt;&gt; splitRegions(double[] start, double[] end) {
<span class="nc" id="L135">		ArrayList&lt;double[]&gt; values = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L137">			double[] temp = new double[2];</span>

<span class="nc" id="L139">			temp[0] = start[i];</span>
<span class="nc" id="L140">			temp[1] = end[i];</span>
<span class="nc" id="L141">			values.add(temp);</span>
		}

<span class="nc" id="L144">		ArrayList&lt;List&lt;Double&gt;&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L145">		PaiLie.per(values, 0, new ArrayList&lt;&gt;(), result);</span>
<span class="nc" id="L146">		return result;</span>
	}

	public int maxRegion() {
<span class="nc" id="L150">		int index = 0;</span>
<span class="nc" id="L151">		double maxRegionSize = 0.0;</span>
<span class="nc bnc" id="L152" title="All 2 branches missed.">		for (int i = 0; i &lt; this.regions.size(); i++) {</span>
<span class="nc" id="L153">			double tempRegionSize = this.regions.get(i).size();</span>
<span class="nc bnc" id="L154" title="All 2 branches missed.">			if (tempRegionSize &gt; maxRegionSize) {</span>
<span class="nc" id="L155">				maxRegionSize = tempRegionSize;</span>
<span class="nc" id="L156">				index = i;</span>
			}
		}
<span class="nc" id="L159">		return index;</span>
	}

	public static void main(String[] args) {
		
<span class="nc bnc" id="L164" title="All 2 branches missed.">		for(int j=0;j&lt;1000;j++){</span>
<span class="nc" id="L165">		double howmany=0.10;	</span>
		
<span class="nc" id="L167">		int fm=0;</span>
<span class="nc" id="L168">		int times = 3000;</span>

<span class="nc" id="L170">		int d=2;</span>
<span class="nc" id="L171">		double failure_rate=0.001;</span>
		
<span class="nc" id="L173">		ZeroOneCreator dataCreator=new ZeroOneCreator();</span>
<span class="nc" id="L174">		double[] min=dataCreator.minCreator(d);</span>
<span class="nc" id="L175">		double[] max=dataCreator.maxCreator(d);</span>
		
<span class="nc bnc" id="L177" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L178">			FailurePattern pattern=new BlockPattern();</span>
<span class="nc" id="L179">			pattern.fail_rate=failure_rate;</span>
<span class="nc" id="L180">			ART_Center_ND center = new ART_Center_ND(min, max, new Random(i * 3), pattern);</span>
<span class="nc" id="L181">			center.howManyParts=howmany+j/(1000.0);</span>
<span class="nc" id="L182">			int temp=center.run();</span>
<span class="nc" id="L183">			fm+=temp;</span>
		}
<span class="nc" id="L185">		System.out.println(fm/(double)times);</span>
		}
<span class="nc" id="L187">	}</span>

	@Override
	public int em() {
		// TODO Auto-generated method stub
<span class="nc" id="L192">		return 0;</span>
	}

	@Override
	public NPoint generateNextTC() {
		// TODO Auto-generated method stub
<span class="nc" id="L198">		return null;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>