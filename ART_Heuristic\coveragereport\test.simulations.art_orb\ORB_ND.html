<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ORB_ND</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.art_orb</a> &gt; <span class="el_class">ORB_ND</span></div><h1>ORB_ND</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">600 of 600</td><td class="ctr2">0%</td><td class="bar">34 of 34</td><td class="ctr2">0%</td><td class="ctr1">25</td><td class="ctr2">25</td><td class="ctr1">116</td><td class="ctr2">116</td><td class="ctr1">8</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a7"><a href="ORB_ND.java.html#L190" class="el_method">splitRegion(NPoint, NPoint, ComplexRegion)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="190" alt="190"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h0">36</td><td class="ctr2" id="i0">36</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="ORB_ND.java.html#L138" class="el_method">run()</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="61" height="10" title="97" alt="97"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h1">22</td><td class="ctr2" id="i1">22</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a2"><a href="ORB_ND.java.html#L15" class="el_method">main(String[])</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="48" height="10" title="77" alt="77"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h3">15</td><td class="ctr2" id="i3">15</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a3"><a href="ORB_ND.java.html#L44" class="el_method">ORB_ND(double[], double[], FailurePattern, long)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="43" height="10" title="69" alt="69"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i2">16</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="ORB_ND.java.html#L125" class="el_method">randomTC(NRectRegion)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="30" height="10" title="49" alt="49"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h4">8</td><td class="ctr2" id="i4">8</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="ORB_ND.java.html#L98" class="el_method">randomTC()</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="29" height="10" title="46" alt="46"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a1"><a href="ORB_ND.java.html#L87" class="el_method">isPointInRegion(NRectRegion, NPoint)</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="23" height="10" title="37" alt="37"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h7">5</td><td class="ctr2" id="i7">5</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a0"><a href="ORB_ND.java.html#L75" class="el_method">findMaxRegion()</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="22" height="10" title="35" alt="35"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>