<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>HilbertCurve2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util</a> &gt; <span class="el_source">HilbertCurve2.java</span></div><h1>HilbertCurve2.java</h1><pre class="source lang-java linenums">package util;

import java.math.BigDecimal;
import java.util.ArrayList;

/*
 * 将之前c#中的uint（32位无符号数）转成long
 * 
 * */
<span class="nc" id="L10">public class HilbertCurve2 {</span>

	// public final double[] oneD_2_twoD(double value_10jinzhi){
	// String bin_str=Double2Bin(value_10jinzhi);
	// RefObject&lt;Double&gt; x=new RefObject&lt;Double&gt;((double)-1);
	// RefObject&lt;Double&gt; y=new RefObject&lt;&gt;((double)-1);
	// int numOfbits=bin_str.length()-2;
	// String hiber_key_valid=null;
	// if(numOfbits&gt;32){numOfbits=32;}
	//
	// }
	// test only
	public static void main(String[] args) throws Exception {
<span class="nc" id="L23">		HilbertCurve2 hilbertCurve = new HilbertCurve2();</span>
		// double a=0.00001111111111;
		// String a=&quot;0.000111111111111111111111111111111111111&quot;;
		// //BigDecimal b=new
		// BigDecimal(&quot;0.11111111111111111111111111111111111&quot;);
		// //System.out.println(b.toString());
		// //double result1[]=hilbertCurve.oneD_2_nD(a, 10);
		// double result2[]=hilbertCurve.oneD_2_nD3(a, 10);
		// //System.out.println(Arrays.toString(result1));
		// System.out.println(Arrays.toString(result2));

		// double results[] = hilbertCurve.oneD_2_nD(0.8886545227094472, 4);
		// for (int i = 0; i &lt; results.length; i++) {
		// System.out.println(results[i]);
		// }
		// long start = System.nanoTime();
		// System.out.println(hilbertCurve.Double2Bin(0.8125));
		// long end = System.nanoTime();
		// System.out.println((end - start) / Math.pow(10, 3.0));

		// start = System.nanoTime();
		// System.out.println(hilbertCurve.decimal2Bin(0.8125, 64));
		// end = System.nanoTime();
		// System.out.println((end - start) / Math.pow(10, 3.0));
		// System.out.println(hilbertCurve.decimal2Binary(0.8125));

		// 比较时间
<span class="nc" id="L50">		int[] dimension = { 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24, 25,</span>
<span class="nc" id="L51">				26, 27, 28, 29, 30, 31, 32 };</span>
<span class="nc" id="L52">		double testValue = 0.333333333333;</span>
<span class="nc" id="L53">		System.out.println(testValue);</span>
<span class="nc bnc" id="L54" title="All 2 branches missed.">		for (int i = 0; i &lt; dimension.length; i++) {</span>
<span class="nc" id="L55">			long start = System.nanoTime();</span>
<span class="nc bnc" id="L56" title="All 2 branches missed.">			for (int j = 0; j &lt; 100; j++)</span>
<span class="nc" id="L57">				hilbertCurve.oneD_2_nD3(testValue+&quot;&quot;, dimension[i]);</span>
<span class="nc" id="L58">			long end = System.nanoTime();</span>
<span class="nc" id="L59">			System.out.println(end - start);</span>
		}

<span class="nc" id="L62">	}</span>

	public final void ADD(int dst, int u, int v, double[][] abcd, double[][] tmp) {
<span class="nc" id="L65">		abcd[(dst)][0] = (tmp[(u)][0] + tmp[v][0]) / 2.0;</span>
<span class="nc" id="L66">		abcd[(dst)][1] = (tmp[(u)][1] + tmp[(v)][1]) / 2.0;</span>
<span class="nc" id="L67">	}</span>

	// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no directv
	// equivalent in Java:
	// ORIGINAL LINE: public void ADD(int dst,int u, int v,uint[,] abcd,uint[,]
	// tmp)
	public final void ADD(int dst, int u, int v, long[][] abcd, long[][] tmp) {
<span class="nc" id="L74">		abcd[(dst)][0] = tmp[(u)][0] + tmp[v][0];</span>
<span class="nc" id="L75">		abcd[(dst)][1] = tmp[(u)][1] + tmp[(v)][1];</span>
<span class="nc" id="L76">	}</span>

	public final double Bin2Double(String bin_str) {
<span class="nc" id="L79">		double sum = 0;</span>
<span class="nc" id="L80">		double binbase = 1;</span>
<span class="nc" id="L81">		int len = bin_str.length();</span>
<span class="nc bnc" id="L82" title="All 2 branches missed.">		for (int i = 0; i &lt; len; i++) {</span>
<span class="nc" id="L83">			binbase = binbase * 0.5;</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">			if (bin_str.charAt(i) == '1') {</span>
<span class="nc" id="L85">				sum += binbase;</span>
			}

		}
<span class="nc" id="L89">		return sum;</span>

	}

	/**
	 * 0.[00001000]numbits为8，key为8
	 * 
	 * @param key
	 * @param numbits
	 * @param xx
	 * @param yy
	 */
	// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
	// equivalent in Java:
	// ORIGINAL LINE: public void convert_hilbert_key(uint key, int numbits, ref
	// uint xx,ref uint yy)
	public final void convert_hilbert_key(long key, int numbits, RefObject&lt;Long&gt; xx, RefObject&lt;Long&gt; yy) {

		// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
		// equivalent in Java:
		// ORIGINAL LINE: uint[,] abcd = new uint[4, 2] { { 0, 0 }, { 0, 1 }, {
		// 1, 1 }, { 1, 0 } };
<span class="nc" id="L111">		long[][] abcd = new long[][] { { 0, 0 }, { 0, 1 }, { 1, 1 }, { 1, 0 } };</span>
		// ({0, 0}, {0,1}, {1, 1}, {1, 0});
		// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
		// equivalent in Java:
		// ORIGINAL LINE: uint[,] tmp = new uint[4, 2];
<span class="nc" id="L116">		long[][] tmp = new long[4][2];</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">		while (key &gt; 1) // should be &gt; 0, but this is safer for (invalid) odd</span>
						// numbers
		{
			// uint[,] tmp=new uint[4,2]; /* save abcd here */
			byte subcell; // unsigned char subcell;
<span class="nc bnc" id="L122" title="All 2 branches missed.">			for (int j = 0; j &lt; 1; j++) {</span>
<span class="nc bnc" id="L123" title="All 2 branches missed.">				for (int k = 0; k &lt; 4; k++) {</span>
<span class="nc" id="L124">					tmp[k][j] = abcd[k][j];</span>
				}
			}
			// memcpy(tmp, abcd, sizeof tmp); /* save our 4 points with the new
			// ones */
<span class="nc" id="L129">			numbits -= 2; // each subdivision decision takes 2 bits</span>
			// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no
			// direct equivalent in Java:
			// ORIGINAL LINE: uint u_subcell=(key &gt;&gt; numbits) &amp; 3;
<span class="nc" id="L133">			long u_subcell = (key &gt;&gt; numbits) &amp; 3;</span>
<span class="nc" id="L134">			subcell = Byte.parseByte((new Long(u_subcell)).toString());</span>
			// namely these two (taken from the top)
<span class="nc" id="L136">			key &amp;= (int) ((1 &lt;&lt; numbits) - 1);</span>
			// update key by removing the bits we used
			// * Add the two points with indices u and v (in tmp) and store the
			// result at
			// * index dst in abcd (for both x(0) and y(1)).
			// *
			/// #define ADD(dst, u, v) (abcd[(dst)][0] = tmp[(u)][0] +
			// tmp[(v)][0],
			// abcd[(dst)][1] = tmp[(u)][1] + tmp[(v)][1])

<span class="nc bnc" id="L146" title="All 5 branches missed.">			switch (subcell) {</span>
			// divide into subcells
			case 0:
				// h(key, numbits, a &lt;&lt; 1, a + d, a + c, a + b);
<span class="nc" id="L150">				ADD(0, 0, 0, abcd, tmp);</span>
<span class="nc" id="L151">				ADD(1, 0, 3, abcd, tmp);</span>
<span class="nc" id="L152">				ADD(2, 0, 2, abcd, tmp);</span>
<span class="nc" id="L153">				ADD(3, 0, 1, abcd, tmp);</span>
<span class="nc" id="L154">				break;</span>
			case 1:
				// h(key, numbits, b + a, b &lt;&lt; 1, b + c, b + d);
<span class="nc" id="L157">				ADD(0, 1, 0, abcd, tmp);</span>
<span class="nc" id="L158">				ADD(1, 1, 1, abcd, tmp);</span>
<span class="nc" id="L159">				ADD(2, 1, 2, abcd, tmp);</span>
<span class="nc" id="L160">				ADD(3, 1, 3, abcd, tmp);</span>
<span class="nc" id="L161">				break;</span>
			case 2:
				// h(key, numbits, c + a, c + b, c &lt;&lt; 1, c + d);
<span class="nc" id="L164">				ADD(0, 2, 0, abcd, tmp);</span>
<span class="nc" id="L165">				ADD(1, 2, 1, abcd, tmp);</span>
<span class="nc" id="L166">				ADD(2, 2, 2, abcd, tmp);</span>
<span class="nc" id="L167">				ADD(3, 2, 3, abcd, tmp);</span>
<span class="nc" id="L168">				break;</span>
			case 3:
				// h(key, numbits, d + c, d + b, d + a, d &lt;&lt; 1);
<span class="nc" id="L171">				ADD(0, 3, 2, abcd, tmp);</span>
<span class="nc" id="L172">				ADD(1, 3, 1, abcd, tmp);</span>
<span class="nc" id="L173">				ADD(2, 3, 0, abcd, tmp);</span>
<span class="nc" id="L174">				ADD(3, 3, 3, abcd, tmp);</span>
				break;
			}

			/// #undef ADD
		}
		// final result is the midpoint of the cell, i.e. (a + b + c + d) / 4
<span class="nc" id="L181">		xx.argvalue = (abcd[0][0] + abcd[1][0] + abcd[2][0] + abcd[3][0] + 1) &gt;&gt; 2;</span>
<span class="nc" id="L182">		yy.argvalue = (abcd[0][1] + abcd[1][1] + abcd[2][1] + abcd[3][1] + 1) &gt;&gt; 2;</span>
<span class="nc" id="L183">		System.out.println(&quot;二维点为x=&quot; + (new Long(xx.argvalue)).toString() + &quot;;y=&quot; + (new Long(yy.argvalue)).toString());</span>
		// printf(&quot;x: %u y: %un&quot;, *xx, *yy);

<span class="nc" id="L186">	}</span>

	/**
	 * 0.[00001000]numbits为8，key为8
	 * 
	 * @param key
	 * @param numbits
	 * @param xx
	 * @param yy
	 */
	// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
	// equivalent in Java:
	// ORIGINAL LINE: public ArrayList convert_hilbert_key_V1(uint key, int
	// numbits, ref double xx,ref double yy)
	public final ArrayList convert_hilbert_key_V1(long key, int numbits, RefObject&lt;Double&gt; xx, RefObject&lt;Double&gt; yy) {

<span class="nc" id="L202">		double[][] abcd = new double[][] { { 0, 0 }, { 0, 1 }, { 1, 1 }, { 1, 0 } };</span>
		// ({0,0}, {0, 1}, {1, 1}, {1, 0});
		// unit square
<span class="nc" id="L205">		double[][] tmp = new double[4][2];</span>
<span class="nc" id="L206">		java.util.ArrayList nds = new java.util.ArrayList();</span>
<span class="nc bnc" id="L207" title="All 2 branches missed.">		while (numbits &gt; 1) // should be &gt; 0, but this is safer for (invalid)</span>
							// odd numbers
		{
			// uint[,] tmp=new uint[4,2]; /* save abcd here */
			byte subcell; // unsigned char subcell;
<span class="nc bnc" id="L212" title="All 2 branches missed.">			for (int j = 0; j &lt; 2; j++) {</span>
<span class="nc bnc" id="L213" title="All 2 branches missed.">				for (int k = 0; k &lt; 4; k++) {</span>
<span class="nc" id="L214">					tmp[k][j] = abcd[k][j];</span>
				}
			}

			// memcpy(tmp, abcd, sizeof tmp); /* save our 4 points with the new
			// ones */

<span class="nc" id="L221">			numbits -= 2; // each subdivision decision takes 2 bits</span>
			// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no
			// direct equivalent in Java:
			// ORIGINAL LINE: uint u_subcell=(key &gt;&gt; numbits) &amp; 3;
<span class="nc" id="L225">			long u_subcell = (key &gt;&gt; numbits) &amp; 3;</span>
<span class="nc" id="L226">			subcell = Byte.parseByte((new Long(u_subcell)).toString());</span>
			// namely these two (taken from the top)
			// key &amp;= (uint)((1&lt;&lt;numbits) - 1); /* update key by removing the
			// bits we used */

			// * Add the two points with indices u and v (in tmp) and store the
			// result at
			// * index dst in abcd (for both x(0) and y(1)).
			// *
			/// #define ADD(dst, u, v) (abcd[(dst)][0] = tmp[(u)][0] +
			// tmp[(v)][0],
			// abcd[(dst)][1] = tmp[(u)][1] + tmp[(v)][1])

<span class="nc bnc" id="L239" title="All 5 branches missed.">			switch (subcell) { // divide into subcells</span>
			case 0:
				// h(key, numbits, a &lt;&lt; 1, a + d, a + c, a + b);
<span class="nc" id="L242">				ADD(0, 0, 0, abcd, tmp);</span>
<span class="nc" id="L243">				ADD(1, 0, 3, abcd, tmp);</span>
<span class="nc" id="L244">				ADD(2, 0, 2, abcd, tmp);</span>
<span class="nc" id="L245">				ADD(3, 0, 1, abcd, tmp);</span>
<span class="nc" id="L246">				break;</span>
			case 1:
				// h(key, numbits, b + a, b &lt;&lt; 1, b + c, b + d);
<span class="nc" id="L249">				ADD(0, 1, 0, abcd, tmp);</span>
<span class="nc" id="L250">				ADD(1, 1, 1, abcd, tmp);</span>
<span class="nc" id="L251">				ADD(2, 1, 2, abcd, tmp);</span>
<span class="nc" id="L252">				ADD(3, 1, 3, abcd, tmp);</span>
<span class="nc" id="L253">				break;</span>
			case 2:
				// h(key, numbits, c + a, c + b, c &lt;&lt; 1, c + d);
<span class="nc" id="L256">				ADD(0, 2, 0, abcd, tmp);</span>
<span class="nc" id="L257">				ADD(1, 2, 1, abcd, tmp);</span>
<span class="nc" id="L258">				ADD(2, 2, 2, abcd, tmp);</span>
<span class="nc" id="L259">				ADD(3, 2, 3, abcd, tmp);</span>
<span class="nc" id="L260">				break;</span>
			case 3:
				// h(key, numbits, d + c, d + b, d + a, d &lt;&lt; 1);
<span class="nc" id="L263">				ADD(0, 3, 2, abcd, tmp);</span>
<span class="nc" id="L264">				ADD(1, 3, 1, abcd, tmp);</span>
<span class="nc" id="L265">				ADD(2, 3, 0, abcd, tmp);</span>
<span class="nc" id="L266">				ADD(3, 3, 3, abcd, tmp);</span>
				break;
			}

			/// #undef ADD
		}
		// final result is the midpoint of the cell, i.e. (a + b + c + d) / 4
<span class="nc" id="L273">		xx.argvalue = (abcd[0][0] + abcd[1][0] + abcd[2][0] + abcd[3][0]) / 4.0;</span>
<span class="nc" id="L274">		yy.argvalue = (abcd[0][1] + abcd[1][1] + abcd[2][1] + abcd[3][1]) / 4.0;</span>
<span class="nc" id="L275">		nds.add(xx.argvalue);</span>
<span class="nc" id="L276">		nds.add(yy.argvalue);</span>
<span class="nc" id="L277">		return nds;</span>
		// Console.WriteLine(&quot;二维点为x=&quot;+xx.ToString()+&quot;;y=&quot;+yy.ToString());
		// printf(&quot;x: %u y: %un&quot;, *xx, *yy);

	}

	public String decimal2Bin(double value, int k) {
<span class="nc" id="L284">		StringBuilder binary = new StringBuilder();</span>
		// double fractional = value;
<span class="nc" id="L286">		binary.append('0');</span>
<span class="nc" id="L287">		binary.append('.');</span>
<span class="nc bnc" id="L288" title="All 2 branches missed.">		while (k-- &gt; 0) {</span>
<span class="nc" id="L289">			value *= 2;</span>
<span class="nc" id="L290">			int fract_bit = (int) value;</span>
<span class="nc bnc" id="L291" title="All 2 branches missed.">			if (fract_bit == 1) {</span>
<span class="nc" id="L292">				value -= fract_bit;</span>
<span class="nc" id="L293">				binary.append('1');</span>
<span class="nc" id="L294">			} else</span>
<span class="nc" id="L295">				binary.append('0');</span>
		}
<span class="nc" id="L297">		return binary.toString();</span>
	}

	public String decimal2Bin(String value, int k) {
<span class="nc" id="L301">		StringBuilder binary = new StringBuilder();</span>
		// double fractional = value;
<span class="nc" id="L303">		binary.append('0');</span>
<span class="nc" id="L304">		binary.append('.');</span>
<span class="nc" id="L305">		BigDecimal temp = new BigDecimal(value);</span>
<span class="nc bnc" id="L306" title="All 2 branches missed.">		while (k-- &gt; 0) {</span>
<span class="nc" id="L307">			temp = temp.multiply(new BigDecimal(2));</span>
<span class="nc" id="L308">			int fract_bit = temp.intValue();</span>
<span class="nc bnc" id="L309" title="All 2 branches missed.">			if (fract_bit == 1) {</span>
<span class="nc" id="L310">				temp = temp.subtract(new BigDecimal(1));</span>
<span class="nc" id="L311">				binary.append('1');</span>
<span class="nc" id="L312">			} else</span>
<span class="nc" id="L313">				binary.append('0');</span>
		}
<span class="nc" id="L315">		return binary.toString();</span>
	}

	/**
	 * 小于1的小数转换为二进制
	 * 
	 * @param val_double
	 * @return
	 */
	public final String Double2Bin(double val_double) {
<span class="nc" id="L325">		double ud = 2 * val_double;</span>
<span class="nc" id="L326">		StringBuilder bin_sb = new StringBuilder();</span>
<span class="nc" id="L327">		double converVal = 0;</span>
		// equivalent in Java: ORIGINAL LINE: uint i=1;
<span class="nc" id="L329">		long i = 1;</span>
<span class="nc" id="L330">		double eachbit = 0;</span>
<span class="nc" id="L331">		double resolution = 1e-22;</span>
<span class="nc bnc" id="L332" title="All 2 branches missed.">		if ((ud - 1) == 0) {</span>
<span class="nc" id="L333">			bin_sb.append(&quot;1&quot;);</span>
		}
<span class="nc bnc" id="L335" title="All 2 branches missed.">		while ((ud - 1) != 0) {</span>
<span class="nc bnc" id="L336" title="All 2 branches missed.">			if (ud &gt; 1) {</span>
<span class="nc" id="L337">				eachbit = 1;</span>
<span class="nc" id="L338">				bin_sb.append(&quot;1&quot;);</span>
<span class="nc bnc" id="L339" title="All 2 branches missed.">			} else if (ud &lt; 1) {</span>
<span class="nc" id="L340">				eachbit = 0;</span>
<span class="nc" id="L341">				bin_sb.append(&quot;0&quot;);</span>
			}
			// eachbit = Convert.ToDouble(ud.ToString().Substring(0, 1));
<span class="nc" id="L344">			converVal += eachbit * (1 / Math.pow(2, i));</span>
<span class="nc" id="L345">			i = i + 1;</span>
<span class="nc bnc" id="L346" title="All 2 branches missed.">			if (ud &gt; 1) {</span>
<span class="nc" id="L347">				ud = (ud - 1) * 2;</span>
<span class="nc bnc" id="L348" title="All 2 branches missed.">			} else if (ud &lt; 1) {</span>
<span class="nc" id="L349">				ud = ud * 2;</span>
			}
<span class="nc bnc" id="L351" title="All 2 branches missed.">			if ((ud - 1) == 0) {</span>
<span class="nc" id="L352">				bin_sb.append(&quot;1&quot;);</span>
<span class="nc" id="L353">				break;</span>
			}
<span class="nc bnc" id="L355" title="All 2 branches missed.">			if (Math.abs(converVal - val_double) &lt; resolution) {</span>
				// 达到一定的精度
<span class="nc" id="L357">				break;</span>
			}
		}
<span class="nc" id="L360">		return &quot;0.&quot; + bin_sb.toString();</span>
	}

	/**
	 * 将一维转换为多维
	 * 
	 * @param hilbercode
	 * @param dim
	 * @return
	 */
	// C# TO JAVA CONVERTER WARNING: Unsigned integer types have no direct
	// equivalent in Java:
	// ORIGINAL LINE: public string[] HilbertCode2Coordinates(string
	// hilbercode,uint dim)
	public final String[] HilbertCode2Coordinates(String hilbercode, long dim) {
<span class="nc" id="L375">		final int Wordbits = 32;</span>
<span class="nc" id="L376">		final int OrderOfHilbert = 32;</span>
<span class="nc" id="L377">		int ndimbits = hilbercode.length();</span>
		// int dim_int = Integer.parseInt((new Long(dim)).toString());
<span class="nc" id="L379">		int dim_int = (int) dim;</span>
		// 填充补0使得hilbertcode为dim的整数倍位
<span class="nc" id="L381">		int paddings = -1;</span>
<span class="nc" id="L382">		int intNdim = ndimbits / dim_int;</span>
<span class="nc bnc" id="L383" title="All 2 branches missed.">		if ((ndimbits % dim_int) != 0) {</span>
<span class="nc" id="L384">			ndimbits = intNdim * dim_int + dim_int;</span>
<span class="nc" id="L385">			paddings = ndimbits - hilbercode.length();</span>
<span class="nc bnc" id="L386" title="All 2 branches missed.">			for (int kindex = 0; kindex &lt; paddings; kindex++) {</span>
<span class="nc" id="L387">				hilbercode += &quot;0&quot;;</span>
			}
		}
<span class="nc" id="L390">		String[] point = new String[(int) dim];</span>
		// equivalent in Java: ORIGINAL LINE: uint mask = (uint)1 &lt;&lt; Wordbits -
		// 1;
<span class="nc" id="L393">		long mask = (long) 1 &lt;&lt; Wordbits - 1; // 31个1</span>
		// equivalent in Java: ORIGINAL LINE: uint element, temp1, temp2, A, W =
		// 0, S, tS, T, tT, J,
		// P = 0, xJ;
<span class="nc" id="L397">		long element, temp1, temp2, A, W = 0, S, tS, T, tT, J, P = 0, xJ;</span>

<span class="nc" id="L399">		int i = 0, j;</span>
<span class="nc bnc" id="L400" title="All 2 branches missed.">		for (int kindex = 0; kindex &lt; dim; kindex++) {</span>
<span class="nc" id="L401">			point[kindex] = &quot;&quot;;</span>
		}

		// --- P ---
<span class="nc" id="L405">		String p_str = hilbercode.substring(i, i + dim_int);</span>
<span class="nc" id="L406">		P = Integer.parseInt(p_str, 2);</span>
		// --- xJ ---
<span class="nc" id="L408">		J = dim;</span>
<span class="nc bnc" id="L409" title="All 2 branches missed.">		for (j = 1; j &lt; dim_int; j++) {</span>
<span class="nc bnc" id="L410" title="All 2 branches missed.">			if ((P &gt;&gt; j &amp; 1) == (P &amp; 1)) {</span>
				continue;
			} else {
				break;
			}
		}
<span class="nc bnc" id="L416" title="All 2 branches missed.">		if (j != dim_int) {</span>
			// J = J - Integer.parseInt((new Integer(j)).toString());
<span class="nc" id="L418">			J = J - j;</span>
		}
<span class="nc" id="L420">		xJ = J - 1;</span>

		// --- S, tS, A ---
<span class="nc" id="L423">		A = S = tS = P ^ (P / 2); // 异或运算</span>

		// --- T ---
<span class="nc bnc" id="L426" title="All 2 branches missed.">		if (P &lt; 3) {</span>
<span class="nc" id="L427">			T = 0;</span>
<span class="nc" id="L428">		} else {</span>
<span class="nc bnc" id="L429" title="All 2 branches missed.">			if (P % 2 != 0) {</span>
<span class="nc" id="L430">				T = (P - 1) ^ (P - 1) / 2;</span>
<span class="nc" id="L431">			} else {</span>
<span class="nc" id="L432">				T = (P - 2) ^ (P - 2) / 2;</span>
			}
		}

		// --- tT ---
<span class="nc" id="L437">		tT = T;</span>

		// --- distrib bits to coords ---
		// for (j = DIM - 1; P &gt; 0; P &gt;&gt;=1, j--)
		// if (P &amp; 1)
		// pt.hcode[j] |= mask;
<span class="nc bnc" id="L443" title="All 2 branches missed.">		for (j = dim_int - 1; j &gt;= 0; A &gt;&gt;= 1, j--) {</span>
<span class="nc bnc" id="L444" title="All 2 branches missed.">			if ((A &amp; 1) != 0) {</span>
<span class="nc" id="L445">				point[j] = point[j] + &quot;1&quot;;</span>
<span class="nc" id="L446">			} else {</span>
<span class="nc" id="L447">				point[j] = point[j] + &quot;0&quot;;</span>
			}
		}

<span class="nc" id="L451">		int noOfshiftbits = 0;</span>
<span class="nc bnc" id="L452" title="All 2 branches missed.">		for (i = dim_int, mask &gt;&gt;= 1; i &lt; ndimbits; i = i + dim_int, mask &gt;&gt;= 1) {</span>
			// --- P ---
<span class="nc" id="L454">			p_str = hilbercode.substring(i, i + dim_int);</span>
<span class="nc" id="L455">			P = Integer.parseInt(p_str, 2);</span>

			// --- S ---
<span class="nc" id="L458">			S = P ^ (P / 2);</span>

			// --- tS ---
<span class="nc" id="L461">			noOfshiftbits = (int) (xJ % dim);</span>
<span class="nc bnc" id="L462" title="All 2 branches missed.">			if (xJ % dim != 0) {</span>
<span class="nc" id="L463">				temp1 = S &gt;&gt; (noOfshiftbits);</span>
<span class="nc" id="L464">				temp2 = S &lt;&lt; (dim_int - noOfshiftbits);</span>
<span class="nc" id="L465">				tS = temp1 | temp2;</span>
<span class="nc" id="L466">				tS &amp;= ((int) 1 &lt;&lt; dim_int) - 1;</span>
<span class="nc" id="L467">			} else {</span>
<span class="nc" id="L468">				tS = S;</span>
			}

			// --- W ---
<span class="nc" id="L472">			W ^= tT;</span>

			// --- A ---
<span class="nc" id="L475">			A = W ^ tS;</span>

			// --- distrib bits to coords ---
<span class="nc bnc" id="L478" title="All 2 branches missed.">			for (j = dim_int - 1; j &gt;= 0; A &gt;&gt;= 1, j--) {</span>
				// if ((A &amp; 1)!=0)
				// point[j] |= mask;
<span class="nc bnc" id="L481" title="All 2 branches missed.">				if ((A &amp; 1) != 0) {</span>
<span class="nc" id="L482">					point[j] = point[j] + &quot;1&quot;;</span>
<span class="nc" id="L483">				} else {</span>
<span class="nc" id="L484">					point[j] = point[j] + &quot;0&quot;;</span>
				}
			}

			// --- T ---
<span class="nc bnc" id="L489" title="All 2 branches missed.">			if (P &lt; 3) {</span>
<span class="nc" id="L490">				T = 0;</span>
<span class="nc" id="L491">			} else {</span>
<span class="nc bnc" id="L492" title="All 2 branches missed.">				if (P % 2 != 0) {</span>
<span class="nc" id="L493">					T = (P - 1) ^ (P - 1) / 2;</span>
<span class="nc" id="L494">				} else {</span>
<span class="nc" id="L495">					T = (P - 2) ^ (P - 2) / 2;</span>
				}
			}

			// --- tT ---
<span class="nc" id="L500">			noOfshiftbits = (int) (xJ % dim);</span>
<span class="nc bnc" id="L501" title="All 2 branches missed.">			if (xJ % dim != 0) {</span>
<span class="nc" id="L502">				temp1 = T &gt;&gt; noOfshiftbits;</span>
<span class="nc" id="L503">				temp2 = T &lt;&lt; (dim_int - noOfshiftbits);</span>
<span class="nc" id="L504">				tT = temp1 | temp2;</span>
<span class="nc" id="L505">				tT &amp;= ((int) 1 &lt;&lt; dim_int) - 1;</span>
<span class="nc" id="L506">			} else {</span>
<span class="nc" id="L507">				tT = T;</span>
			}

			// --- xJ ---
<span class="nc" id="L511">			J = dim;</span>
<span class="nc bnc" id="L512" title="All 2 branches missed.">			for (j = 1; j &lt; dim; j++) {</span>
<span class="nc bnc" id="L513" title="All 2 branches missed.">				if ((P &gt;&gt; j &amp; 1) == (P &amp; 1)) {</span>
					continue;
				} else {
					break;
				}
			}
<span class="nc bnc" id="L519" title="All 2 branches missed.">			if (j != dim) {</span>
<span class="nc" id="L520">				J -= (int) j;</span>
			}
<span class="nc" id="L522">			xJ += J - 1;</span>
		}
<span class="nc" id="L524">		return point;</span>
	}

	public final String[] HilbertCode2Coordinates2(String hilbercode, long dim) {
<span class="nc" id="L528">		final int Wordbits = 64;</span>
<span class="nc" id="L529">		final int OrderOfHilbert = 32;</span>
<span class="nc" id="L530">		int ndimbits = hilbercode.length();</span>
		// int dim_int = Integer.parseInt((new Long(dim)).toString());
<span class="nc" id="L532">		int dim_int = (int) dim;</span>
		// 填充补0使得hilbertcode为dim的整数倍位
<span class="nc" id="L534">		int paddings = -1;</span>
<span class="nc" id="L535">		int intNdim = ndimbits / dim_int;</span>
<span class="nc bnc" id="L536" title="All 2 branches missed.">		if ((ndimbits % dim_int) != 0) {</span>
<span class="nc" id="L537">			ndimbits = intNdim * dim_int + dim_int;</span>
<span class="nc" id="L538">			paddings = ndimbits - hilbercode.length();</span>
<span class="nc bnc" id="L539" title="All 2 branches missed.">			for (int kindex = 0; kindex &lt; paddings; kindex++) {</span>
<span class="nc" id="L540">				hilbercode += &quot;0&quot;;</span>
			}
		}
<span class="nc" id="L543">		String[] point = new String[(int) dim];</span>
		// equivalent in Java: ORIGINAL LINE: uint mask = (uint)1 &lt;&lt; Wordbits -
		// 1;
<span class="nc" id="L546">		long mask = (long) 1 &lt;&lt; Wordbits - 1; // 31个1</span>
		// equivalent in Java: ORIGINAL LINE: uint element, temp1, temp2, A, W =
		// 0, S, tS, T, tT, J,
		// P = 0, xJ;
<span class="nc" id="L550">		long element, temp1, temp2, A, W = 0, S, tS, T, tT, J, P = 0, xJ;</span>

<span class="nc" id="L552">		int i = 0, j;</span>
<span class="nc bnc" id="L553" title="All 2 branches missed.">		for (int kindex = 0; kindex &lt; dim; kindex++) {</span>
<span class="nc" id="L554">			point[kindex] = &quot;&quot;;</span>
		}

		// --- P ---
<span class="nc" id="L558">		String p_str = hilbercode.substring(i, i + dim_int);</span>
<span class="nc" id="L559">		P = Integer.parseInt(p_str, 2);</span>
		// --- xJ ---
<span class="nc" id="L561">		J = dim;</span>
<span class="nc bnc" id="L562" title="All 2 branches missed.">		for (j = 1; j &lt; dim_int; j++) {</span>
<span class="nc bnc" id="L563" title="All 2 branches missed.">			if ((P &gt;&gt; j &amp; 1) == (P &amp; 1)) {</span>
				continue;
			} else {
				break;
			}
		}
<span class="nc bnc" id="L569" title="All 2 branches missed.">		if (j != dim_int) {</span>
			// J = J - Integer.parseInt((new Integer(j)).toString());
<span class="nc" id="L571">			J = J - j;</span>
		}
<span class="nc" id="L573">		xJ = J - 1;</span>

		// --- S, tS, A ---
<span class="nc" id="L576">		A = S = tS = P ^ (P / 2); // 异或运算</span>

		// --- T ---
<span class="nc bnc" id="L579" title="All 2 branches missed.">		if (P &lt; 3) {</span>
<span class="nc" id="L580">			T = 0;</span>
<span class="nc" id="L581">		} else {</span>
<span class="nc bnc" id="L582" title="All 2 branches missed.">			if (P % 2 != 0) {</span>
<span class="nc" id="L583">				T = (P - 1) ^ (P - 1) / 2;</span>
<span class="nc" id="L584">			} else {</span>
<span class="nc" id="L585">				T = (P - 2) ^ (P - 2) / 2;</span>
			}
		}

		// --- tT ---
<span class="nc" id="L590">		tT = T;</span>

		// --- distrib bits to coords ---
		// for (j = DIM - 1; P &gt; 0; P &gt;&gt;=1, j--)
		// if (P &amp; 1)
		// pt.hcode[j] |= mask;
<span class="nc bnc" id="L596" title="All 2 branches missed.">		for (j = dim_int - 1; j &gt;= 0; A &gt;&gt;= 1, j--) {</span>
<span class="nc bnc" id="L597" title="All 2 branches missed.">			if ((A &amp; 1) != 0) {</span>
<span class="nc" id="L598">				point[j] = point[j] + &quot;1&quot;;</span>
<span class="nc" id="L599">			} else {</span>
<span class="nc" id="L600">				point[j] = point[j] + &quot;0&quot;;</span>
			}
		}

<span class="nc" id="L604">		int noOfshiftbits = 0;</span>
<span class="nc bnc" id="L605" title="All 2 branches missed.">		for (i = dim_int, mask &gt;&gt;= 1; i &lt; ndimbits; i = i + dim_int, mask &gt;&gt;= 1) {</span>
			// --- P ---
<span class="nc" id="L607">			p_str = hilbercode.substring(i, i + dim_int);</span>
<span class="nc" id="L608">			P = Integer.parseInt(p_str, 2);</span>

			// --- S ---
<span class="nc" id="L611">			S = P ^ (P / 2);</span>

			// --- tS ---
<span class="nc" id="L614">			noOfshiftbits = (int) (xJ % dim);</span>
<span class="nc bnc" id="L615" title="All 2 branches missed.">			if (xJ % dim != 0) {</span>
<span class="nc" id="L616">				temp1 = S &gt;&gt; (noOfshiftbits);</span>
<span class="nc" id="L617">				temp2 = S &lt;&lt; (dim_int - noOfshiftbits);</span>
<span class="nc" id="L618">				tS = temp1 | temp2;</span>
<span class="nc" id="L619">				tS &amp;= ((int) 1 &lt;&lt; dim_int) - 1;</span>
<span class="nc" id="L620">			} else {</span>
<span class="nc" id="L621">				tS = S;</span>
			}

			// --- W ---
<span class="nc" id="L625">			W ^= tT;</span>

			// --- A ---
<span class="nc" id="L628">			A = W ^ tS;</span>

			// --- distrib bits to coords ---
<span class="nc bnc" id="L631" title="All 2 branches missed.">			for (j = dim_int - 1; j &gt;= 0; A &gt;&gt;= 1, j--) {</span>
				// if ((A &amp; 1)!=0)
				// point[j] |= mask;
<span class="nc bnc" id="L634" title="All 2 branches missed.">				if ((A &amp; 1) != 0) {</span>
<span class="nc" id="L635">					point[j] = point[j] + &quot;1&quot;;</span>
<span class="nc" id="L636">				} else {</span>
<span class="nc" id="L637">					point[j] = point[j] + &quot;0&quot;;</span>
				}
			}

			// --- T ---
<span class="nc bnc" id="L642" title="All 2 branches missed.">			if (P &lt; 3) {</span>
<span class="nc" id="L643">				T = 0;</span>
<span class="nc" id="L644">			} else {</span>
<span class="nc bnc" id="L645" title="All 2 branches missed.">				if (P % 2 != 0) {</span>
<span class="nc" id="L646">					T = (P - 1) ^ (P - 1) / 2;</span>
<span class="nc" id="L647">				} else {</span>
<span class="nc" id="L648">					T = (P - 2) ^ (P - 2) / 2;</span>
				}
			}

			// --- tT ---
<span class="nc" id="L653">			noOfshiftbits = (int) (xJ % dim);</span>
<span class="nc bnc" id="L654" title="All 2 branches missed.">			if (xJ % dim != 0) {</span>
<span class="nc" id="L655">				temp1 = T &gt;&gt; noOfshiftbits;</span>
<span class="nc" id="L656">				temp2 = T &lt;&lt; (dim_int - noOfshiftbits);</span>
<span class="nc" id="L657">				tT = temp1 | temp2;</span>
<span class="nc" id="L658">				tT &amp;= ((int) 1 &lt;&lt; dim_int) - 1;</span>
<span class="nc" id="L659">			} else {</span>
<span class="nc" id="L660">				tT = T;</span>
			}

			// --- xJ ---
<span class="nc" id="L664">			J = dim;</span>
<span class="nc bnc" id="L665" title="All 2 branches missed.">			for (j = 1; j &lt; dim; j++) {</span>
<span class="nc bnc" id="L666" title="All 2 branches missed.">				if ((P &gt;&gt; j &amp; 1) == (P &amp; 1)) {</span>
					continue;
				} else {
					break;
				}
			}
<span class="nc bnc" id="L672" title="All 2 branches missed.">			if (j != dim) {</span>
<span class="nc" id="L673">				J -= (int) j;</span>
			}
<span class="nc" id="L675">			xJ += J - 1;</span>
		}
<span class="nc" id="L677">		return point;</span>
	}

	//// 下面这个方法将求解一维到多维进行封装 by XJX
	public final double[] oneD_2_nD(double value_10jinzhi, int dim) {
<span class="nc" id="L682">		double[] curvals = new double[dim];</span>
		// System.out.println(&quot;---------------------&quot;);
		// long starttime = System.currentTimeMillis();
<span class="nc" id="L685">		String bin_str = decimal2Bin(value_10jinzhi, 32);</span>
		// long endtime = System.currentTimeMillis();
		// System.out.println(endtime - starttime);
<span class="nc" id="L688">		int numOfbits = bin_str.length() - 2;</span>
<span class="nc bnc" id="L689" title="All 2 branches missed.">		if (numOfbits &gt; 32) {</span>
<span class="nc" id="L690">			numOfbits = 32;</span>
		}
		// starttime = System.currentTimeMillis();
<span class="nc" id="L693">		String[] pointOfstrs = HilbertCode2Coordinates(bin_str.substring(2, 2 + numOfbits), (int) dim);</span>
		// endtime = System.currentTimeMillis();
		// System.out.println(endtime - starttime);
		// starttime = System.currentTimeMillis();
<span class="nc bnc" id="L697" title="All 2 branches missed.">		for (int i = 0; i &lt; dim; i++) {</span>
<span class="nc" id="L698">			curvals[i] = Bin2Double(pointOfstrs[i]);</span>
		}
		// endtime = System.currentTimeMillis();
		// System.out.println(endtime - starttime);
<span class="nc" id="L702">		return curvals;</span>
	}

	public final double[] oneD_2_nD2(double value_10jinzhi, int dim, long[] time) {
<span class="nc" id="L706">		double[] curvals = new double[dim];</span>
		// System.out.println(&quot;---------------------&quot;);
		// long starttime = System.nanoTime();
<span class="nc" id="L709">		String bin_str = decimal2Bin(value_10jinzhi, 64);</span>
		// String bin_str=Double2Bin(value_10jinzhi);
		// long endtime = System.nanoTime();
		// time[0] = endtime - starttime;
		// System.out.println(endtime-starttime);
<span class="nc" id="L714">		int numOfbits = bin_str.length() - 2;</span>
<span class="nc bnc" id="L715" title="All 2 branches missed.">		if (numOfbits &gt; 32) {</span>
<span class="nc" id="L716">			numOfbits = 32;</span>
		}
		// starttime = System.currentTimeMillis();
<span class="nc" id="L719">		String[] pointOfstrs = HilbertCode2Coordinates(bin_str.substring(2, 2 + numOfbits), (int) dim);</span>
		// endtime = System.currentTimeMillis();
		// time[1] = endtime - starttime;
		// starttime = System.currentTimeMillis();
<span class="nc bnc" id="L723" title="All 2 branches missed.">		for (int i = 0; i &lt; dim; i++) {</span>
<span class="nc" id="L724">			curvals[i] = Bin2Double(pointOfstrs[i]);</span>
		}
		// endtime = System.currentTimeMillis();
		// time[2] = endtime - starttime;
		// System.out.println(endtime-starttime);
<span class="nc" id="L729">		return curvals;</span>
	}

	public final double[] oneD_2_nD3(String value_10jinzhi, int dim) {
<span class="nc" id="L733">		double[] curvals = new double[dim];</span>
		// System.out.println(&quot;---------------------&quot;);
		// long starttime = System.nanoTime();
<span class="nc" id="L736">		String bin_str = decimal2Bin(value_10jinzhi, 64);</span>
		// String bin_str=Double2Bin(value_10jinzhi);
		// long endtime = System.nanoTime();
		// time[0] = endtime - starttime;
		// System.out.println(endtime-starttime);
<span class="nc" id="L741">		int numOfbits = bin_str.length() - 2;</span>
		// if (numOfbits &gt; 32) {
		// numOfbits = 32;
		// }
		// starttime = System.currentTimeMillis();
<span class="nc" id="L746">		String[] pointOfstrs = HilbertCode2Coordinates2(bin_str.substring(2, 2 + numOfbits), (int) dim);</span>
		// endtime = System.currentTimeMillis();
		// time[1] = endtime - starttime;
		// starttime = System.currentTimeMillis();
<span class="nc bnc" id="L750" title="All 2 branches missed.">		for (int i = 0; i &lt; dim; i++) {</span>
<span class="nc" id="L751">			curvals[i] = Bin2Double(pointOfstrs[i]);</span>
		}
		// endtime = System.currentTimeMillis();
		// time[2] = endtime - starttime;
		// System.out.println(endtime-starttime);
<span class="nc" id="L756">		return curvals;</span>
	}
}

// ----------------------------------------------------------------------------------------
// Copyright © 2006 - 2010 Tangible Software Solutions Inc.
// This class can be used by anyone provided that the copyright notice remains
// intact.
//
// This class is used to simulate the ability to pass arguments by reference in
// Java.
// ----------------------------------------------------------------------------------------
// final class RefObject&lt;T&gt; {
// public T argvalue;
//
// public RefObject(T refarg) {
// argvalue = refarg;
// }
// }
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>