/* DO NOT EDIT THIS FILE - it is machine generated */
#include <jni.h>
/* Header for class util_TestProgramEm */

#ifndef _Included_util_TestProgramEm
#define _Included_util_TestProgramEm
#ifdef __cplusplus
extern "C" {
#endif
/*
 * Class:     util_TestProgramEm
 * Method:    test_airy
 * Signature: (D)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1airy
  (JNIEnv *, jclass, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_bessj
 * Signature: (DD)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1bessj
  (JNIEnv *, jclass, jdouble, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_bessj0
 * Signature: (D)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1bessj0
  (JNIEnv *, jclass, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_cel
 * Signature: (DDDD)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1cel
  (JNIEnv *, jclass, jdouble, jdouble, jdouble, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_el2
 * Signature: (DDDD)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1el2
  (JNIEnv *, jclass, jdouble, jdouble, jdouble, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_erfcc
 * Signature: (D)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1erfcc
  (JNIEnv *, jclass, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_gammq
 * Signature: (DD)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1gammq
  (JNIEnv *, jclass, jdouble, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_golden
 * Signature: (DDD)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1golden
  (JNIEnv *, jclass, jdouble, jdouble, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_plgndr
 * Signature: (DDD)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1plgndr
  (JNIEnv *, jclass, jdouble, jdouble, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_probks
 * Signature: (D)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1probks
  (JNIEnv *, jclass, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_sncndn
 * Signature: (DD)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1sncndn
  (JNIEnv *, jclass, jdouble, jdouble);

/*
 * Class:     util_TestProgramEm
 * Method:    test_tanh
 * Signature: (D)[I
 */
JNIEXPORT jintArray JNICALL Java_util_TestProgramEm_test_1tanh
  (JNIEnv *, jclass, jdouble);

#ifdef __cplusplus
}
#endif
#endif
