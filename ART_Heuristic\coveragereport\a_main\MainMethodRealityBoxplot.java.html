<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MainMethodRealityBoxplot.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">a_main</a> &gt; <span class="el_source">MainMethodRealityBoxplot.java</span></div><h1>MainMethodRealityBoxplot.java</h1><pre class="source lang-java linenums">package a_main;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.Random;

import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tp._ND.ART_TP_ND;
import test.simulations.art_tpp.ART_TPP;
import test.simulations.fscs.FSCS_ND;
import test.simulations.rrt.RRT_ND;
import test.simulations.rrttp.hilbert.RRTtpND_H;
import test.simulations.rt.RT_ND;
import util.data.RealityClasses;
import util.file.FileUtils;

<span class="nc" id="L22">public class MainMethodRealityBoxplot {</span>
	// TODO 完成参数化
	public static void main(String[] args) throws Exception, IllegalAccessException, NoSuchFieldException, SecurityException, InstantiationException {
<span class="nc" id="L25">		int times = 100;</span>
<span class="nc" id="L26">		Class[] classes=RealityClasses.get();</span>
<span class="nc" id="L27">		String path=&quot;C:\\Users\\<USER>\\Desktop\\研究方向和内容\\小论文\\补充数据\\boxplot\\&quot;;</span>
		//File tempfile=new File(path+&quot;&quot;+tempClass.getName());
		//tempfile.mkdir();
		//System.out.println(&quot;now class:&quot;+tempClass.getName());
<span class="nc" id="L31">		File rtf=FileUtils.createNewFile(path, &quot;LAZRealityAll.txt&quot;);</span>
<span class="nc" id="L32">		BufferedWriter writer=get(rtf);</span>
<span class="nc bnc" id="L33" title="All 2 branches missed.">		for (int j =0; j &lt; classes.length; j++) {</span>
			
			//=SUM(A2:GS2)/((ROW(GT2)-1)*500)
			
<span class="nc" id="L37">			Class tempClass=classes[j];</span>
			
			
<span class="nc" id="L40">			double failureRate=tempClass.getDeclaredField(&quot;failureRate&quot;).getDouble(null);</span>
<span class="nc" id="L41">			double[] min=(double[] )tempClass.getDeclaredField(&quot;min&quot;).get(null);</span>
<span class="nc" id="L42">			double[] max=(double[]) tempClass.getDeclaredField(&quot;max&quot;).get(null);</span>
<span class="nc" id="L43">			int Dimension=(int)tempClass.getDeclaredField(&quot;Dimension&quot;).get(null);</span>
			
<span class="nc" id="L45">			FailurePattern failurePattern = new RealityFailPattern(tempClass.newInstance().getClass().getSimpleName());</span>
<span class="nc" id="L46">			failurePattern.fail_rate = failureRate;</span>
<span class="nc" id="L47">			failurePattern.min = min;</span>
<span class="nc" id="L48">			failurePattern.max = max;</span>
<span class="nc" id="L49">			failurePattern.dimension = Dimension;</span>

			//rt
/*			int fm = 0;
			long startTime = System.currentTimeMillis();
			writer.write(&quot;rt=[&quot;);
			for (int i = 0; i &lt; times; i++) {
				RT_ND rt = new RT_ND(min, max, new Random(i * 3), failurePattern);
				int temp = rt.run();
				writer.write(temp+&quot;&quot;);
				writer.newLine();
				writer.flush();
				fm += temp;
			}
			long endTime = System.currentTimeMillis();
			writer.write(&quot;];&quot;);
			writer.newLine();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
		
			//rrt
			double r=1.5;
			if(Dimension==1) {
				r=0.75;
			}else {
				r=1.5;
			}
			fm=0;
			startTime = System.currentTimeMillis();
			writer.write(&quot;rrt=[&quot;);
			for (int i = 0; i &lt; times; i++) {
				RRT_ND rt = new RRT_ND(min, max,  failurePattern,new Random(i * 3),r);
				int temp = rt.run();
				fm += temp;
				writer.write(temp+&quot;&quot;);
				writer.newLine();
				writer.flush();
			}
			writer.write(&quot;];&quot;);
			writer.newLine();
			endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
			
			//fscs
			fm=0;
			int s=10;
			startTime = System.currentTimeMillis();
			writer.write(&quot;fscs=[&quot;);
			for (int i = 0; i &lt; times; i++) {
				FSCS_ND rt = new FSCS_ND(min, max, s, failurePattern,new Random(i * 3));
				int temp = rt.run();
				writer.write(temp+&quot;&quot;);
				writer.newLine();
				writer.flush();
				fm += temp;
			}
			writer.write(&quot;];&quot;);
			writer.newLine();
			endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
			
			
			//art_b
			fm=0;
			startTime = System.currentTimeMillis();
			writer.write(&quot;artb=[&quot;);
			for (int i = 0; i &lt; times; i++) {
				ART_B_ND rt = new ART_B_ND(min, max, new Random(i * 3),failurePattern);
				int temp = rt.run();
				writer.write(temp+&quot;&quot;);
				writer.newLine();
				writer.flush();
				fm += temp;
			}
			writer.write(&quot;];&quot;);
			writer.newLine();
			endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
			
			//art_rp
			fm=0;
			startTime = System.currentTimeMillis();
			writer.write(&quot;artrp=[&quot;);
			for (int i = 0; i &lt; times; i++) {
				ART_RP_ND rt = new ART_RP_ND(min, max, new Random(i * 3),failurePattern);
				int temp = rt.run();
				writer.write(temp+&quot;&quot;);
				writer.newLine();
				writer.flush();
				fm += temp;
			}
			writer.write(&quot;];&quot;);
			writer.newLine();
			endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
			
			
			//art_tpp
			fm=0;
			int k=10;
			writer.write(&quot;arttpp=[&quot;);
			startTime = System.currentTimeMillis();
			for (int i = 0; i &lt; times; i++) {
				ART_TPP rt = new ART_TPP(min, max, new Random(i * 3),failurePattern,k);
				int temp = rt.run();
				writer.write(temp+&quot;&quot;);
				writer.newLine();
				writer.flush();
				fm += temp;
			}
			writer.write(&quot;];&quot;);
			writer.newLine();
			endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
			
			//art_tp
			 fm=0;
			 writer.write(&quot;arttp=[&quot;);
			 startTime = System.currentTimeMillis();
			for (int i = 0; i &lt; times; i++) {
				ART_TP_ND rt = new ART_TP_ND(min, max, failurePattern,new Random(i * 3));
				int temp = rt.run();
				writer.write(temp+&quot;&quot;);
				writer.newLine();
				writer.flush();
				fm += temp;
			}
			writer.write(&quot;];&quot;);
			writer.newLine();
			 endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
			
		*/
			//my method
			//1dimension
<span class="nc" id="L183">			int fm=0;</span>
<span class="nc" id="L184">			long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L185">			writer.write(tempClass.getSimpleName());</span>
<span class="nc" id="L186">			writer.newLine();</span>
<span class="nc" id="L187">			writer.write(&quot;laz=[&quot;);</span>
<span class="nc" id="L188">			writer.newLine();</span>
<span class="nc bnc" id="L189" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L190">				RRTtpND_H rt = new RRTtpND_H(min, max,0.75,failurePattern, new Random(i * 3));</span>
<span class="nc" id="L191">				int temp = rt.run();</span>
<span class="nc" id="L192">				writer.write(temp);</span>
<span class="nc" id="L193">				writer.newLine();</span>
<span class="nc" id="L194">				writer.flush();</span>
<span class="nc" id="L195">				fm += temp;</span>
			}
<span class="nc" id="L197">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L198">			writer.write(&quot;];&quot;);</span>
<span class="nc" id="L199">			writer.newLine();</span>
<span class="nc" id="L200">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			
		}
<span class="nc" id="L204">		writer.close();</span>
<span class="nc" id="L205">	}</span>
	public static BufferedWriter get(File f) throws Exception{
<span class="nc" id="L207">		return new BufferedWriter(new OutputStreamWriter(new FileOutputStream(f),&quot;UTF-8&quot;));</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>