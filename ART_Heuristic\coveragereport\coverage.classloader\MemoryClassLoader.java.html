<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MemoryClassLoader.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">coverage.classloader</a> &gt; <span class="el_source">MemoryClassLoader.java</span></div><h1>MemoryClassLoader.java</h1><pre class="source lang-java linenums">package coverage.classloader;

/**
 * Created by xijiaxiang on 2016/3/12.
 */

import java.util.HashMap;
import java.util.Map;

/**
 * A class loader that loads classes from in-memory data.
 */
<span class="nc" id="L13">public class MemoryClassLoader extends ClassLoader {</span>

<span class="nc" id="L15">	private final Map&lt;String, byte[]&gt; definitions = new HashMap&lt;String, byte[]&gt;();</span>

	/**
	 * Add a in-memory representation of a class.
	 *
	 * @param name
	 *            name of the class
	 * @param bytes
	 *            class definition
	 */
	public void addDefinition(final String name, final byte[] bytes) {
<span class="nc" id="L26">		definitions.put(name, bytes);</span>
<span class="nc" id="L27">	}</span>

	@Override
	protected Class&lt;?&gt; loadClass(final String name, final boolean resolve) throws ClassNotFoundException {
<span class="nc" id="L31">		final byte[] bytes = definitions.get(name);</span>
<span class="nc bnc" id="L32" title="All 2 branches missed.">		if (bytes != null) {</span>
<span class="nc" id="L33">			return defineClass(name, bytes, 0, bytes.length);</span>
		}
<span class="nc" id="L35">		return super.loadClass(name, resolve);</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>