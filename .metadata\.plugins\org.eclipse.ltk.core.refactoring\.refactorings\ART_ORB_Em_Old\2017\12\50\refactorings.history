<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_dc/RRT_DC.java&apos; to &apos;RRT_DC_Em.java&apos;" description="Rename resource &apos;RRT_DC.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_dc/RRT_DC.java" name="RRT_DC_Em.java" stamp="1513052412665" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_b/ART_B_ND.java&apos; to &apos;ART_B_ND_Em.java&apos;" description="Rename resource &apos;ART_B_ND.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_b/ART_B_ND.java" name="ART_B_ND_Em.java" stamp="1513052838012" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_tpp/ART_TPP.java&apos; to &apos;ART_TPP_Em.java&apos;" description="Rename resource &apos;ART_TPP.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_tpp/ART_TPP.java" name="ART_TPP_Em.java" stamp="1513072739478" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_rp/ART_RP_ND.java&apos; to &apos;ART_RP_Em.java&apos;" description="Rename resource &apos;ART_RP_ND.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_rp/ART_RP_ND.java" name="ART_RP_Em.java" stamp="1513074932819" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_rp/ART_RP_Em.java&apos; to &apos;ART_RP_ND_Em.java&apos;" description="Rename resource &apos;ART_RP_Em.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_rp/ART_RP_Em.java" name="ART_RP_ND_Em.java" stamp="1513075153079" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_orb/ORB_RRT_ND.java&apos; to &apos;ORB_RRT_ND1.java&apos;" description="Rename resource &apos;ORB_RRT_ND.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_orb/ORB_RRT_ND.java" name="ORB_RRT_ND1.java" stamp="1513077250118" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_orb/ORB_RRT_ND.java&apos; to &apos;ORB_RRT_ND2.java&apos;" description="Rename resource &apos;ORB_RRT_ND.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_orb/ORB_RRT_ND.java" name="ORB_RRT_ND2.java" stamp="1513077278558" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_orb/ORB_RRT_ND1.java&apos; to &apos;ORB_RRT_ND.java&apos;" description="Rename resource &apos;ORB_RRT_ND1.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_orb/ORB_RRT_ND1.java" name="ORB_RRT_ND.java" stamp="1513077288408" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_orb/ORB_RRT_ND2.java&apos; to &apos;ORB_RRT_ND_Em.java&apos;" description="Rename resource &apos;ORB_RRT_ND2.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_orb/ORB_RRT_ND2.java" name="ORB_RRT_ND_Em.java" stamp="1513077368684" updateReferences="true"/>
</session>