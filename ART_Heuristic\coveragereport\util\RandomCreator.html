<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RandomCreator</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">util</a> &gt; <span class="el_class">RandomCreator</span></div><h1>RandomCreator</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">200 of 261</td><td class="ctr2">23%</td><td class="bar">14 of 16</td><td class="ctr2">12%</td><td class="ctr1">11</td><td class="ctr2">14</td><td class="ctr1">41</td><td class="ctr2">54</td><td class="ctr1">4</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a2"><a href="RandomCreator.java.html#L69" class="el_method">randomPoint(ArrayList)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="67" alt="67"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">4</td><td class="ctr1" id="h0">15</td><td class="ctr2" id="i0">15</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="RandomCreator.java.html#L48" class="el_method">randomPoint(NRectRegion[])</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="114" height="10" title="64" alt="64"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h1">15</td><td class="ctr2" id="i1">15</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a4"><a href="RandomCreator.java.html#L35" class="el_method">randomPoint(NRectRegion)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="84" height="10" title="47" alt="47"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">8</td><td class="ctr2" id="i2">8</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a3"><a href="RandomCreator.java.html#L92" class="el_method">randomPoint(double, double)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="39" height="10" title="22" alt="22"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h3">3</td><td class="ctr2" id="i5">3</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a1"><a href="RandomCreator.java.html#L24" class="el_method">randomPoint()</a></td><td class="bar" id="b4"><img src="../.resources/greenbar.gif" width="82" height="10" title="46" alt="46"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d3"><img src="../.resources/greenbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f4">0</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h4">0</td><td class="ctr2" id="i3">7</td><td class="ctr1" id="j4">0</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="RandomCreator.java.html#L15" class="el_method">RandomCreator(Random, int, double[], double[])</a></td><td class="bar" id="b5"><img src="../.resources/greenbar.gif" width="26" height="10" title="15" alt="15"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">0</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">0</td><td class="ctr2" id="i4">6</td><td class="ctr1" id="j5">0</td><td class="ctr2" id="k5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>