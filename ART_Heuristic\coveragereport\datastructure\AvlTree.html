<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>AvlTree</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">datastructure</a> &gt; <span class="el_class">AvlTree</span></div><h1>AvlTree</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">840 of 840</td><td class="ctr2">0%</td><td class="bar">118 of 118</td><td class="ctr2">0%</td><td class="ctr1">85</td><td class="ctr2">85</td><td class="ctr1">167</td><td class="ctr2">167</td><td class="ctr1">26</td><td class="ctr2">26</td></tr></tfoot><tbody><tr><td id="a19"><a href="AVLTree.java.html#L381" class="el_method">remove(Comparable, AvlTree.AvlNode)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="265" alt="265"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="50" alt="50"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">26</td><td class="ctr2" id="g0">26</td><td class="ctr1" id="h0">39</td><td class="ctr2" id="i0">39</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a14"><a href="AVLTree.java.html#L301" class="el_method">insert(Comparable, AvlTree.AvlNode)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="59" height="10" title="131" alt="131"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="33" height="10" title="14" alt="14"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">8</td><td class="ctr2" id="g1">8</td><td class="ctr1" id="h1">24</td><td class="ctr2" id="i1">24</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a1"><a href="AVLTree.java.html#L97" class="el_method">checkBalanceOfTree(AvlTree.AvlNode)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="21" height="10" title="48" alt="48"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="24" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h3">9</td><td class="ctr2" id="i3">9</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a2"><a href="AVLTree.java.html#L114" class="el_method">checkOrderingOfTree(AvlTree.AvlNode)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="21" height="10" title="48" alt="48"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="28" height="10" title="12" alt="12"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g2">7</td><td class="ctr1" id="h2">11</td><td class="ctr2" id="i2">11</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a20"><a href="AVLTree.java.html#L452" class="el_method">rotateWithLeftChild(AvlTree.AvlNode)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="17" height="10" title="38" alt="38"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h5">6</td><td class="ctr2" id="i5">6</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a21"><a href="AVLTree.java.html#L472" class="el_method">rotateWithRightChild(AvlTree.AvlNode)</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="17" height="10" title="38" alt="38"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a4"><a href="AVLTree.java.html#L153" class="el_method">contains(Comparable, AvlTree.AvlNode)</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="12" height="10" title="28" alt="28"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="14" height="10" title="6" alt="6"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h4">7</td><td class="ctr2" id="i4">7</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a11"><a href="AVLTree.java.html#L249" class="el_method">getDepth(AvlTree.AvlNode)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="11" height="10" title="26" alt="26"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h7">6</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a23"><a href="AVLTree.java.html#L505" class="el_method">serializeInfix(AvlTree.AvlNode, StringBuilder, String)</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="11" height="10" title="26" alt="26"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"><img src="../.resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h8">6</td><td class="ctr2" id="i8">6</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a25"><a href="AVLTree.java.html#L536" class="el_method">serializePrefix(AvlTree.AvlNode, StringBuilder, String)</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="11" height="10" title="26" alt="26"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"><img src="../.resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h9">6</td><td class="ctr2" id="i9">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a13"><a href="AVLTree.java.html#L280" class="el_method">insert(Comparable)</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="8" height="10" title="18" alt="18"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h11">5</td><td class="ctr2" id="i11">5</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a0"><a href="AVLTree.java.html#L85" class="el_method">AvlTree()</a></td><td class="bar" id="b11"><img src="../.resources/redbar.gif" width="6" height="10" title="15" alt="15"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h10">6</td><td class="ctr2" id="i10">6</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a22"><a href="AVLTree.java.html#L491" class="el_method">serializeInfix()</a></td><td class="bar" id="b12"><img src="../.resources/redbar.gif" width="5" height="10" title="13" alt="13"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d19"/><td class="ctr2" id="e19">n/a</td><td class="ctr1" id="f19">1</td><td class="ctr2" id="g19">1</td><td class="ctr1" id="h14">3</td><td class="ctr2" id="i14">3</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a24"><a href="AVLTree.java.html#L522" class="el_method">serializePrefix()</a></td><td class="bar" id="b13"><img src="../.resources/redbar.gif" width="5" height="10" title="13" alt="13"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d20"/><td class="ctr2" id="e20">n/a</td><td class="ctr1" id="f20">1</td><td class="ctr2" id="g20">1</td><td class="ctr1" id="h15">3</td><td class="ctr2" id="i15">3</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a7"><a href="AVLTree.java.html#L199" class="el_method">findMax()</a></td><td class="bar" id="b14"><img src="../.resources/redbar.gif" width="5" height="10" title="12" alt="12"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d10"><img src="../.resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h16">3</td><td class="ctr2" id="i16">3</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a8"><a href="AVLTree.java.html#L212" class="el_method">findMax(AvlTree.AvlNode)</a></td><td class="bar" id="b15"><img src="../.resources/redbar.gif" width="5" height="10" title="12" alt="12"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h12">5</td><td class="ctr2" id="i12">5</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a9"><a href="AVLTree.java.html#L226" class="el_method">findMin()</a></td><td class="bar" id="b16"><img src="../.resources/redbar.gif" width="5" height="10" title="12" alt="12"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d11"><img src="../.resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h17">3</td><td class="ctr2" id="i17">3</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a10"><a href="AVLTree.java.html#L240" class="el_method">findMin(AvlTree.AvlNode)</a></td><td class="bar" id="b17"><img src="../.resources/redbar.gif" width="5" height="10" title="12" alt="12"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="9" height="10" title="4" alt="4"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h13">5</td><td class="ctr2" id="i13">5</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a5"><a href="AVLTree.java.html#L175" class="el_method">doubleWithLeftChild(AvlTree.AvlNode)</a></td><td class="bar" id="b18"><img src="../.resources/redbar.gif" width="4" height="10" title="10" alt="10"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d21"/><td class="ctr2" id="e21">n/a</td><td class="ctr1" id="f21">1</td><td class="ctr2" id="g21">1</td><td class="ctr1" id="h19">2</td><td class="ctr2" id="i19">2</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a6"><a href="AVLTree.java.html#L189" class="el_method">doubleWithRightChild(AvlTree.AvlNode)</a></td><td class="bar" id="b19"><img src="../.resources/redbar.gif" width="4" height="10" title="10" alt="10"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d22"/><td class="ctr2" id="e22">n/a</td><td class="ctr1" id="f22">1</td><td class="ctr2" id="g22">1</td><td class="ctr1" id="h20">2</td><td class="ctr2" id="i20">2</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a18"><a href="AVLTree.java.html#L373" class="el_method">remove(Comparable)</a></td><td class="bar" id="b20"><img src="../.resources/redbar.gif" width="3" height="10" title="8" alt="8"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d23"/><td class="ctr2" id="e23">n/a</td><td class="ctr1" id="f23">1</td><td class="ctr2" id="g23">1</td><td class="ctr1" id="h21">2</td><td class="ctr2" id="i21">2</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a12"><a href="AVLTree.java.html#L267" class="el_method">height(AvlTree.AvlNode)</a></td><td class="bar" id="b21"><img src="../.resources/redbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d12"><img src="../.resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h23">1</td><td class="ctr2" id="i23">1</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a15"><a href="AVLTree.java.html#L340" class="el_method">isEmpty()</a></td><td class="bar" id="b22"><img src="../.resources/redbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d13"><img src="../.resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f13">2</td><td class="ctr2" id="g13">2</td><td class="ctr1" id="h24">1</td><td class="ctr2" id="i24">1</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a17"><a href="AVLTree.java.html#L361" class="el_method">max(int, int)</a></td><td class="bar" id="b23"><img src="../.resources/redbar.gif" width="3" height="10" title="7" alt="7"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d14"><img src="../.resources/redbar.gif" width="4" height="10" title="2" alt="2"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f14">2</td><td class="ctr2" id="g14">2</td><td class="ctr1" id="h18">3</td><td class="ctr2" id="i18">3</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a3"><a href="AVLTree.java.html#L140" class="el_method">contains(Comparable)</a></td><td class="bar" id="b24"><img src="../.resources/redbar.gif" width="2" height="10" title="6" alt="6"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d24"/><td class="ctr2" id="e24">n/a</td><td class="ctr1" id="f24">1</td><td class="ctr2" id="g24">1</td><td class="ctr1" id="h25">1</td><td class="ctr2" id="i25">1</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a16"><a href="AVLTree.java.html#L348" class="el_method">makeEmpty()</a></td><td class="bar" id="b25"><img src="../.resources/redbar.gif" width="1" height="10" title="4" alt="4"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"/><td class="ctr2" id="e25">n/a</td><td class="ctr1" id="f25">1</td><td class="ctr2" id="g25">1</td><td class="ctr1" id="h22">2</td><td class="ctr2" id="i22">2</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>