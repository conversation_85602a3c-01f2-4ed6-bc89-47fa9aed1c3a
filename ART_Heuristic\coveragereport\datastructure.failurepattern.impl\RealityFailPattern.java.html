<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RealityFailPattern.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.failurepattern.impl</a> &gt; <span class="el_source">RealityFailPattern.java</span></div><h1>RealityFailPattern.java</h1><pre class="source lang-java linenums">package datastructure.failurepattern.impl;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import util.TestProgram;

public class RealityFailPattern extends FailurePattern {

	String projectName;

	public RealityFailPattern(String projectName) {
<span class="nc" id="L12">		super();</span>
<span class="nc" id="L13">		this.projectName = projectName;</span>
<span class="nc" id="L14">	}</span>

	@Override
	public void genFailurePattern() {
		// we don't need to generate failure pattern in reality projects
<span class="nc" id="L19">	}</span>

	@Override
	public boolean isCorrect(NPoint p) {
<span class="nc" id="L23">		double[] pxn = p.getXn();</span>
<span class="nc" id="L24">		boolean result = false;</span>
<span class="nc" id="L25">		String message = &quot;the test case p's dimension is not accordingly&quot;;</span>
<span class="nc bnc" id="L26" title="All 37 branches missed.">		switch (projectName) {</span>
		case &quot;airy&quot;:
<span class="nc bnc" id="L28" title="All 2 branches missed.">			if (pxn.length == 1) {</span>
<span class="nc" id="L29">				result = TestProgram.test_airy(pxn[0]);</span>
<span class="nc" id="L30">			} else {</span>
<span class="nc" id="L31">				System.out.println(message);</span>
			}
<span class="nc" id="L33">			break;</span>
		case &quot;bessj&quot;:
			// 2 dimension
<span class="nc bnc" id="L36" title="All 2 branches missed.">			if (pxn.length == 2) {</span>
<span class="nc" id="L37">				result = TestProgram.test_bessj(pxn[0], pxn[1]);</span>
<span class="nc" id="L38">			} else {</span>
<span class="nc" id="L39">				System.out.println(message);</span>
			}
<span class="nc" id="L41">			break;</span>
		case &quot;bessj0&quot;:
			// 1 dimension
<span class="nc bnc" id="L44" title="All 2 branches missed.">			if (pxn.length == 1) {</span>
<span class="nc" id="L45">				result = TestProgram.test_bessj0(pxn[0]);</span>
<span class="nc" id="L46">			} else {</span>
<span class="nc" id="L47">				System.out.println(message);</span>
			}
<span class="nc" id="L49">			break;</span>
		case &quot;cel&quot;:
			// 4 dimension
<span class="nc bnc" id="L52" title="All 2 branches missed.">			if (pxn.length == 4) {</span>
<span class="nc" id="L53">				double a = pxn[0];</span>
<span class="nc" id="L54">				double b = pxn[1];</span>
<span class="nc" id="L55">				double c = pxn[2];</span>
<span class="nc" id="L56">				double d = pxn[3];</span>
<span class="nc" id="L57">				result = TestProgram.test_cel(a, b, c, d);</span>
<span class="nc" id="L58">			} else {</span>
<span class="nc" id="L59">				System.out.println(message);</span>
			}
<span class="nc" id="L61">			break;</span>
		case &quot;el2&quot;:
			// 4
<span class="nc bnc" id="L64" title="All 2 branches missed.">			if (pxn.length == 4) {</span>
<span class="nc" id="L65">				double a = pxn[0];</span>
<span class="nc" id="L66">				double b = pxn[1];</span>
<span class="nc" id="L67">				double c = pxn[2];</span>
<span class="nc" id="L68">				double d = pxn[3];</span>
<span class="nc" id="L69">				result = TestProgram.test_el2(a, b, c, d);</span>
			}
<span class="nc" id="L71">			break;</span>
		case &quot;erfcc&quot;:
			// 1
<span class="nc bnc" id="L74" title="All 2 branches missed.">			if (pxn.length == 1) {</span>
<span class="nc" id="L75">				result = TestProgram.test_erfcc(pxn[0]);</span>
<span class="nc" id="L76">			} else {</span>
<span class="nc" id="L77">				System.out.println(message);</span>
			}
<span class="nc" id="L79">			break;</span>
		case &quot;gammq&quot;:
			// 2
<span class="nc bnc" id="L82" title="All 2 branches missed.">			if (pxn.length == 2) {</span>
<span class="nc" id="L83">				result = TestProgram.test_gammq(pxn[0], pxn[1]);</span>
<span class="nc" id="L84">			} else {</span>
<span class="nc" id="L85">				System.out.println(message);</span>
			}
<span class="nc" id="L87">			break;</span>
		case &quot;golden&quot;:
			// 3
<span class="nc bnc" id="L90" title="All 2 branches missed.">			if (pxn.length == 3) {</span>
<span class="nc" id="L91">				result = TestProgram.test_golden(pxn[0], pxn[1], pxn[2]);</span>
<span class="nc" id="L92">			} else {</span>
<span class="nc" id="L93">				System.out.println(message);</span>
			}
<span class="nc" id="L95">			break;</span>
		case &quot;plgndr&quot;:
			// 3
<span class="nc bnc" id="L98" title="All 2 branches missed.">			if (pxn.length == 3) {</span>
<span class="nc" id="L99">				result = TestProgram.test_plgndr(pxn[0], pxn[1], pxn[2]);</span>
<span class="nc" id="L100">			} else {</span>
<span class="nc" id="L101">				System.out.println(message);</span>
			}
<span class="nc" id="L103">			break;</span>
		case &quot;probks&quot;:
			// 1
<span class="nc bnc" id="L106" title="All 2 branches missed.">			if (pxn.length == 1) {</span>
<span class="nc" id="L107">				result = TestProgram.test_probks(pxn[0]);</span>
<span class="nc" id="L108">			} else {</span>
<span class="nc" id="L109">				System.out.println(message);</span>
			}
<span class="nc" id="L111">			break;</span>
		case &quot;sncndn&quot;:
			// 2
<span class="nc bnc" id="L114" title="All 2 branches missed.">			if (pxn.length == 2) {</span>
<span class="nc" id="L115">				result = TestProgram.test_sncndn(pxn[0], pxn[1]);</span>
<span class="nc" id="L116">			} else {</span>
<span class="nc" id="L117">				System.out.println(message);</span>
			}
<span class="nc" id="L119">			break;</span>
		case &quot;tanh&quot;:
<span class="nc bnc" id="L121" title="All 2 branches missed.">			if (pxn.length == 1) {</span>
<span class="nc" id="L122">				result = TestProgram.test_tanh(pxn[0]);</span>
<span class="nc" id="L123">			} else {</span>
<span class="nc" id="L124">				System.out.println(message);</span>
			}
<span class="nc" id="L126">			break;</span>
		default:
<span class="nc" id="L128">			System.out.println(&quot;error program name!&quot;);</span>
			break;
		}
<span class="nc" id="L131">		return result;</span>
	}

	@Override
	public void showFailurePattern() {
		// TODO Auto-generated method stub
		
<span class="nc" id="L138">	}</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>