<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_RP_RRT2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rprrt</a> &gt; <span class="el_source">ART_RP_RRT2.java</span></div><h1>ART_RP_RRT2.java</h1><pre class="source lang-java linenums">package test.simulations.rprrt;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import util.PaiLie;
import util.data.ZeroOneCreator;
import util.triangle.TriangleUtil2D;

/**
 * <AUTHOR>
 * @date   2017/12/13
 * 先采用直线， 后采用弧形 
 */
public class ART_RP_RRT2 extends ART {

<span class="nc" id="L23">	public ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L24">	public ArrayList&lt;NRectRegion&gt; regions = new ArrayList&lt;&gt;();</span>

	double R;

	public ART_RP_RRT2(double[] min, double[] max, Random random, FailurePattern failurePattern, double R) {
<span class="nc" id="L29">		super(min, max, random, failurePattern);</span>
<span class="nc" id="L30">		this.R = R;</span>
<span class="nc" id="L31">	}</span>

	@Override
	public int run() {
<span class="nc" id="L35">		int count = 0;</span>
<span class="nc" id="L36">		NPoint p = randomCreator.randomPoint();</span>
		//System.out.println(&quot;p0:&quot;+p);
<span class="nc" id="L38">		NRectRegion maxRegion = new NRectRegion(new NPoint(min), new NPoint(max));</span>
<span class="nc" id="L39">		this.regions.add(maxRegion);</span>
<span class="nc" id="L40">		int index = 0;</span>

<span class="nc bnc" id="L42" title="All 2 branches missed.">		while (this.failPattern.isCorrect(p)) {</span>
			// count++;
<span class="nc" id="L44">			count++;</span>

			// add tests
<span class="nc" id="L47">			this.tests.add(p);</span>

			// split region
<span class="nc" id="L50">			splitRegion(index, p);</span>
			// max region
<span class="nc" id="L52">			index = maxRegion();</span>
			//System.out.println(&quot;maxregion:&quot;+this.regions.get(index));
			// make the region min
			///NRectRegion[] smallerRegions = makeMaxRegionSmall(this.regions.get(index));
			// another point
<span class="nc" id="L57">			p = randomPoint(this.regions.get(index));</span>
		}
<span class="nc" id="L59">		return count;</span>
	}

	// 只生成测试用例的方法
	public NPoint generateTC() {
<span class="nc" id="L64">		NPoint p = null;</span>

<span class="nc bnc" id="L66" title="All 2 branches missed.">		if (this.tests.size() == 0) {</span>
<span class="nc" id="L67">			p = randomCreator.randomPoint();</span>
<span class="nc" id="L68">			this.regions.add(new NRectRegion(new NPoint(min), new NPoint(max)));</span>
<span class="nc" id="L69">			splitRegion(0, p);</span>
<span class="nc" id="L70">		} else {</span>
<span class="nc" id="L71">			int index = maxRegion();</span>
<span class="nc" id="L72">			p = randomCreator.randomPoint((this.regions.get(index)));</span>
<span class="nc" id="L73">			splitRegion(index, p);</span>
		}
<span class="nc" id="L75">		this.tests.add(p);</span>

<span class="nc" id="L77">		return p;</span>
	}

	public NPoint randomPoint(NRectRegion maxregion){
<span class="nc" id="L81">		NPoint p=super.randomCreator.randomPoint(maxregion);</span>
		//System.out.println(&quot;temp0:&quot;+p);
<span class="nc bnc" id="L83" title="All 2 branches missed.">		while(!isOutArc(p, maxregion)){</span>
			//System.out.println(&quot;temp:&quot;+p);
<span class="nc" id="L85">			p=randomPoint(maxregion);</span>
		}
<span class="nc" id="L87">		return p;</span>
	}

	public boolean isOutArc(NPoint p,NRectRegion region){
<span class="nc" id="L91">		boolean result=false;</span>
		
<span class="nc" id="L93">		double px1=p.getXn()[0];</span>
<span class="nc" id="L94">		double px2=p.getXn()[1];</span>
<span class="nc" id="L95">		double xmin=region.getStart().getXn()[0];</span>
<span class="nc" id="L96">		double ymin=region.getStart().getXn()[1];</span>
<span class="nc" id="L97">		double xmax=region.getEnd().getXn()[0];</span>
<span class="nc" id="L98">		double ymax=region.getEnd().getXn()[1];</span>
<span class="nc" id="L99">		double midx=0.5*(xmax+xmin);</span>
<span class="nc" id="L100">		double midy=0.5*(ymax+ymin);</span>
<span class="nc bnc" id="L101" title="All 4 branches missed.">		if(px1&lt;midx&amp;&amp;px2&lt;midy){</span>
			//zuoxia
<span class="nc" id="L103">			result=isOutArc(xmin, xmax, midx, midy, p,0);</span>
		}
<span class="nc bnc" id="L105" title="All 4 branches missed.">		if(px1&gt;midx&amp;&amp;px2&lt;midy){</span>
			//youxia
<span class="nc" id="L107">			result=isOutArc(midx, xmax, ymin, midy, p,1);</span>
		}
<span class="nc bnc" id="L109" title="All 4 branches missed.">		if(px1&gt;midx&amp;&amp;px2&gt;midy){</span>
			//youshang
<span class="nc" id="L111">			result=isOutArc(midx, xmax, midy, ymax, p,2);</span>
		}
<span class="nc bnc" id="L113" title="All 4 branches missed.">		if(px1&lt;midx&amp;&amp;px2&gt;midy){</span>
<span class="nc" id="L114">			result=isOutArc(xmin, midx, midy, ymax, p,3);</span>
		}
<span class="nc" id="L116">		return result;</span>
	}
	public boolean isOutArc(double xmin,double xmax,double ymin,double ymax,NPoint p,int order){
<span class="nc" id="L119">		double midx=R*(xmin+xmax);</span>
<span class="nc" id="L120">		double midy=R*(ymin+ymax);</span>
		
<span class="nc bnc" id="L122" title="All 2 branches missed.">		if(order==0){</span>
<span class="nc" id="L123">			boolean flag1=TriangleUtil2D.IsPointInTriangle(new NPoint(new double[]{xmin,ymin}), new NPoint(new double[]{midx,ymax}), new NPoint(new double[]{xmin,ymax}), p);</span>
<span class="nc" id="L124">			boolean flag2=TriangleUtil2D.IsPointInTriangle(new NPoint(new double[]{xmin,ymin}), new NPoint(new double[]{xmax,ymin}), new NPoint(new double[]{xmax,midy}), p);</span>
<span class="nc bnc" id="L125" title="All 4 branches missed.">			return !(flag1||flag2);</span>
		}
<span class="nc bnc" id="L127" title="All 2 branches missed.">		if(order==1){</span>
<span class="nc" id="L128">			boolean flag1=TriangleUtil2D.IsPointInTriangle(new NPoint(new double[]{xmin,ymin}), new NPoint(new double[]{xmin,midy}), new NPoint(new double[]{xmax,ymin}), p);</span>
<span class="nc" id="L129">			boolean flag2=TriangleUtil2D.IsPointInTriangle(new NPoint(new double[]{xmax,ymin}), new NPoint(new double[]{midx,ymax}), new NPoint(new double[]{xmax,ymax}), p);</span>
<span class="nc bnc" id="L130" title="All 4 branches missed.">			return !(flag1||flag2);</span>
		}
<span class="nc bnc" id="L132" title="All 2 branches missed.">		if(order==2){</span>
<span class="nc" id="L133">			boolean flag1=TriangleUtil2D.IsPointInTriangle(new NPoint(new double[]{midx,ymin}), new NPoint(new double[]{xmax,ymin}), new NPoint(new double[]{xmax,ymax}), p);</span>
<span class="nc" id="L134">			boolean flag2=TriangleUtil2D.IsPointInTriangle(new NPoint(new double[]{xmin,midy}), new NPoint(new double[]{xmin,ymax}), new NPoint(new double[]{xmax,ymax}), p);</span>
<span class="nc bnc" id="L135" title="All 4 branches missed.">			return !(flag1||flag2);</span>
		}
<span class="nc bnc" id="L137" title="All 2 branches missed.">		if(order==3){</span>
<span class="nc" id="L138">			boolean flag1=TriangleUtil2D.IsPointInTriangle(new NPoint(new double[]{xmin,ymin}), new NPoint(new double[]{midx,ymin}), new NPoint(new double[]{xmin,ymax}), p);</span>
<span class="nc" id="L139">			boolean flag2=TriangleUtil2D.IsPointInTriangle(new NPoint(new double[]{xmin,ymax}), new NPoint(new double[]{xmax,midy}), new NPoint(new double[]{xmax,ymax}), p);</span>
<span class="nc bnc" id="L140" title="All 4 branches missed.">			return !(flag1||flag2);</span>
		}
<span class="nc" id="L142">		return false;</span>
	}
	
	public void splitRegion(int index, NPoint p) {
<span class="nc bnc" id="L146" title="All 4 branches missed.">		if (index &lt; 0 || index &gt;= this.regions.size()) {</span>
<span class="nc" id="L147">			System.out.println(&quot;split region error! index not correct!&quot;);</span>
<span class="nc" id="L148">			return;</span>
		}
		// first remove it
<span class="nc" id="L151">		NRectRegion region = this.regions.remove(index);</span>
		try {
			// add regions;
<span class="nc" id="L154">			addRegionsInND(region, p);</span>
<span class="nc" id="L155">		} catch (Exception e) {</span>
<span class="nc" id="L156">			System.out.println(&quot;split region error in split region rec&quot;);</span>
		}
<span class="nc" id="L158">	}</span>

	public void addRegionsInND(NRectRegion region, NPoint p) throws Exception {
<span class="nc" id="L161">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L162">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L163">		double[] pxn = p.getXn();</span>
<span class="nc" id="L164">		List&lt;List&lt;Double&gt;&gt; result1 = splitRegions(start, pxn);</span>
<span class="nc" id="L165">		List&lt;List&lt;Double&gt;&gt; result2 = splitRegions(pxn, end);</span>
		// System.out.println(result1.size());
<span class="nc bnc" id="L167" title="All 2 branches missed.">		if (result1.size() != result2.size()) {</span>
<span class="nc" id="L168">			throw new Exception(&quot;result1's size!=result2's size ,split region wrong&quot;);</span>
		}
<span class="nc bnc" id="L170" title="All 2 branches missed.">		for (int i = 0; i &lt; result1.size(); i++) {</span>
<span class="nc" id="L171">			List&lt;Double&gt; temp1 = result1.get(i);</span>
<span class="nc" id="L172">			List&lt;Double&gt; temp2 = result2.get(i);</span>
<span class="nc" id="L173">			double[] newStart = new double[temp1.size()];</span>
<span class="nc" id="L174">			double[] newEnd = new double[temp2.size()];</span>
<span class="nc bnc" id="L175" title="All 2 branches missed.">			for (int j = 0; j &lt; temp1.size(); j++) {</span>
<span class="nc" id="L176">				newStart[j] = temp1.get(j);</span>
<span class="nc" id="L177">				newEnd[j] = temp2.get(j);</span>
			}

<span class="nc" id="L180">			NRectRegion tempRegion = new NRectRegion(new NPoint(newStart), new NPoint(newEnd));</span>
<span class="nc" id="L181">			this.regions.add(tempRegion);</span>
		}
<span class="nc" id="L183">	}</span>

	public List&lt;List&lt;Double&gt;&gt; splitRegions(double[] start, double[] end) {
<span class="nc" id="L186">		ArrayList&lt;double[]&gt; values = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L188">			double[] temp = new double[2];</span>

<span class="nc" id="L190">			temp[0] = start[i];</span>
<span class="nc" id="L191">			temp[1] = end[i];</span>
<span class="nc" id="L192">			values.add(temp);</span>
		}

<span class="nc" id="L195">		ArrayList&lt;List&lt;Double&gt;&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L196">		PaiLie.per(values, 0, new ArrayList&lt;&gt;(), result);</span>
<span class="nc" id="L197">		return result;</span>
	}

	public int maxRegion() {
<span class="nc" id="L201">		int index = 0;</span>
<span class="nc" id="L202">		double maxRegionSize = 0.0;</span>
<span class="nc bnc" id="L203" title="All 2 branches missed.">		for (int i = 0; i &lt; this.regions.size(); i++) {</span>
<span class="nc" id="L204">			double tempRegionSize = this.regions.get(i).size();</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">			if (tempRegionSize &gt; maxRegionSize) {</span>
<span class="nc" id="L206">				maxRegionSize = tempRegionSize;</span>
<span class="nc" id="L207">				index = i;</span>
			}
		}
<span class="nc" id="L210">		return index;</span>
	}

	public static void main(String[] args) {
<span class="nc" id="L214">		int d=2;//only in 2dimension</span>
<span class="nc" id="L215">		int times=3000;</span>
<span class="nc" id="L216">		double failure_rate=0.001;</span>
<span class="nc" id="L217">		double R=0.6;</span>
<span class="nc" id="L218">		int fm=0;</span>
		
<span class="nc" id="L220">		ZeroOneCreator dataCreator=new ZeroOneCreator();</span>
<span class="nc" id="L221">		double[] min=dataCreator.minCreator(d);</span>
<span class="nc" id="L222">		double[] max=dataCreator.maxCreator(d);</span>
		
<span class="nc bnc" id="L224" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L225">			FailurePattern pattern=new BlockPattern();</span>
<span class="nc" id="L226">			pattern.fail_rate=failure_rate;</span>
<span class="nc" id="L227">			ART_RP_RRT2 center = new ART_RP_RRT2(min, max, new Random(i * 3), pattern, R);</span>
<span class="nc" id="L228">			int temp=center.run();</span>
<span class="nc" id="L229">			fm+=temp;</span>
		}
<span class="nc" id="L231">		System.out.println(fm/(double)times);</span>
<span class="nc" id="L232">	}</span>
	//效果不行，跟不均匀有关

	@Override
	public int em() {
		// TODO Auto-generated method stub
<span class="nc" id="L238">		return 0;</span>
	}

	@Override
	public NPoint generateNextTC() {
		// TODO Auto-generated method stub
<span class="nc" id="L244">		return null;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>