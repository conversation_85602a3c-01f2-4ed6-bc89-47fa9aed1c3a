<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_OD</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.art_tp._1D</a> &gt; <span class="el_class">ART_TP_OD</span></div><h1>ART_TP_OD</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,133 of 1,133</td><td class="ctr2">0%</td><td class="bar">62 of 62</td><td class="ctr2">0%</td><td class="ctr1">39</td><td class="ctr2">39</td><td class="ctr1">165</td><td class="ctr2">165</td><td class="ctr1">8</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a2"><a href="ART_TP_OD.java.html#L50" class="el_method">genNext(ArrayList, double, TestCase)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="357" alt="357"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="28" alt="28"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">15</td><td class="ctr2" id="g0">15</td><td class="ctr1" id="h0">49</td><td class="ctr2" id="i0">49</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="ART_TP_OD.java.html#L125" class="el_method">run()</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="103" height="10" title="308" alt="308"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="51" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h1">40</td><td class="ctr2" id="i1">40</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a6"><a href="ART_TP_OD.java.html#L182" class="el_method">shengjinFormula(double, double, double, double)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="91" height="10" title="272" alt="272"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="42" height="10" title="10" alt="10"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h2">38</td><td class="ctr2" id="i2">38</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="ART_TP_OD.java.html#L10" class="el_method">main(String[])</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="21" height="10" title="64" alt="64"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="8" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h3">12</td><td class="ctr2" id="i3">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="ART_TP_OD.java.html#L229" class="el_method">sortTestCases(TestCase)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="19" height="10" title="58" alt="58"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="25" height="10" title="6" alt="6"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h4">11</td><td class="ctr2" id="i4">11</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="ART_TP_OD.java.html#L29" class="el_method">ART_TP_OD(double, double, double, int)</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="10" height="10" title="31" alt="31"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h5">9</td><td class="ctr2" id="i5">9</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a3"><a href="ART_TP_OD.java.html#L117" class="el_method">isCorrect(double)</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="7" height="10" title="23" alt="23"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="17" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h6">3</td><td class="ctr2" id="i6">3</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a1"><a href="ART_TP_OD.java.html#L45" class="el_method">genFail_start()</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="6" height="10" title="20" alt="20"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>