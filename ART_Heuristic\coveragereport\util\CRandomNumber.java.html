<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>CRandomNumber.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util</a> &gt; <span class="el_source">CRandomNumber.java</span></div><h1>CRandomNumber.java</h1><pre class="source lang-java linenums">package util;


import util.draw.StdDraw;

<span class="nc" id="L6">public class CRandomNumber{</span>
	static {
<span class="nc" id="L8">		System.loadLibrary(&quot;libRNG2&quot;);</span>
<span class="nc" id="L9">	}</span>

	public static native void initSeed(long seed);



	public static native double randome0e1();

	public static native double randomi0e1();

	public static native double randomi0i1();
	
	public static void main(String[] args) {
<span class="nc" id="L22">		CRandomNumber.initSeed(3);</span>
<span class="nc bnc" id="L23" title="All 2 branches missed.">		for (int i = 0; i &lt; 10000; i++) {</span>
<span class="nc" id="L24">			StdDraw.point(CRandomNumber.randomi0i1(), CRandomNumber.randomi0i1());</span>
		}
<span class="nc" id="L26">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>