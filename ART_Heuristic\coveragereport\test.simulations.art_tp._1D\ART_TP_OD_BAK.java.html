<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_OD_BAK.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._1D</a> &gt; <span class="el_source">ART_TP_OD_BAK.java</span></div><h1>ART_TP_OD_BAK.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._1D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class ART_TP_OD_BAK {
	public static void main(String[] args) throws Exception {
<span class="nc" id="L10">		double fail_rate = 0.01;</span>
<span class="nc" id="L11">		int times = 300;</span>
<span class="nc" id="L12">		long sums = 0;</span>
<span class="nc" id="L13">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L14" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
			// 暂不支持测试其他类，没有考虑清楚
<span class="nc" id="L16">			ART_TP_OD_BAK art_tp_od = new ART_TP_OD_BAK(0, 1, fail_rate, i * 3);</span>
<span class="nc" id="L17">			int fm = art_tp_od.run();</span>
<span class="nc" id="L18">			sums += fm;</span>
		}
<span class="nc" id="L20">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L21">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L22">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L23">	}</span>
	int seedOfRandom;
	double min;
	double max;
	double fail_rate;
	double fail_start;
<span class="nc" id="L29">	Random random = null;</span>
	double An;
	double Bn;

<span class="nc" id="L33">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	// 构造函数
<span class="nc" id="L36">	public ART_TP_OD_BAK(double min, double max, double fail_rate, int seedOfRandom) {</span>
<span class="nc" id="L37">		this.seedOfRandom = seedOfRandom;</span>
<span class="nc" id="L38">		this.min = min;</span>
<span class="nc" id="L39">		this.max = max;</span>
<span class="nc" id="L40">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L41">		random = new Random(this.seedOfRandom);</span>
<span class="nc" id="L42">	}</span>

	public double genFail_start() {
<span class="nc" id="L45">		double size = (max - min) * fail_rate;</span>
<span class="nc" id="L46">		return random.nextDouble() * (max - size);</span>
	}

	public TestCase genNext(ArrayList&lt;Double&gt; integrals, double Co, TestCase p, long sed) {
<span class="nc" id="L50">		random.setSeed(seedOfRandom);</span>
<span class="nc" id="L51">		double e1 = tests.get(0).p;// first node</span>
<span class="nc" id="L52">		double en = tests.get(tests.size() - 1).p;// last node</span>
		// 随机生成一个0-1的数
<span class="nc" id="L54">		double T = random.nextDouble();</span>
		// System.out.println(&quot;An:&quot;+An+&quot;,Bn:&quot;+Bn+&quot;,Co:&quot;+Co+&quot;,T:&quot;+T);
		// 看T落在哪个区间
<span class="nc" id="L57">		double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L58">		double PreIntegral = 0.0;</span>
<span class="nc" id="L59">		int temp = 0;</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">		for (int i = 0; i &lt; integrals.size(); i++) {</span>
<span class="nc bnc" id="L61" title="All 2 branches missed.">			if (SumIntegral &lt; T) {</span>
<span class="nc" id="L62">				PreIntegral = SumIntegral;</span>
<span class="nc" id="L63">				temp = i;</span>
			}
<span class="nc" id="L65">			SumIntegral += integrals.get(i) * Co;</span>
		}
		//
		double A, B, C, D;
<span class="nc bnc" id="L69" title="All 2 branches missed.">		if (temp == 0) {</span>
<span class="nc" id="L70">			A = -Co / 3.0;</span>
<span class="nc" id="L71">			B = (Co / 2.0) * (e1 + An);</span>
<span class="nc" id="L72">			C = -Co * e1 * An;</span>
<span class="nc" id="L73">			D = -T;</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">		} else if (temp == integrals.size() - 1) {</span>
<span class="nc" id="L75">			A = -Co / 3.0;</span>
<span class="nc" id="L76">			B = (Co / 2.0) * (en + Bn);</span>
<span class="nc" id="L77">			C = -Co * (Bn) * (en);</span>
<span class="nc" id="L78">			D = PreIntegral - T - Co * ((1.0 / 6.0) * (Math.pow(en, 3.0)))</span>
<span class="nc" id="L79">					+ Co * ((1.0 / 2.0) * Bn * Math.pow(en, 2.0));</span>
<span class="nc" id="L80">		} else {</span>
<span class="nc" id="L81">			A = -Co / 3.0;</span>
<span class="nc" id="L82">			B = (Co / 2.0) * (tests.get(temp - 1).p + tests.get(temp).p);</span>
<span class="nc" id="L83">			C = -Co * tests.get(temp - 1).p * tests.get(temp).p;</span>
<span class="nc" id="L84">			D = -T + PreIntegral - Co * ((1.0 / 6.0) * (Math.pow(tests.get(temp - 1).p, 3.0))</span>
<span class="nc" id="L85">					- (1.0 / 2.0) * (tests.get(temp).p) * (Math.pow(tests.get(temp - 1).p, 2.0)));</span>
		}
<span class="nc" id="L87">		double[] roots = shengjinFormula(A, B, C, D);</span>
<span class="nc" id="L88">		boolean haveAanswer = false;</span>
<span class="nc bnc" id="L89" title="All 2 branches missed.">		for (double root : roots) {</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">			if (temp == 0) {</span>
<span class="nc bnc" id="L91" title="All 4 branches missed.">				if (root &gt;= 0.0 &amp;&amp; root &lt;= e1) {</span>
<span class="nc" id="L92">					p = new TestCase();</span>
<span class="nc" id="L93">					p.p = root;</span>
<span class="nc" id="L94">					haveAanswer = true;</span>
				}
<span class="nc bnc" id="L96" title="All 2 branches missed.">			} else if (temp == integrals.size() - 1) {</span>
<span class="nc bnc" id="L97" title="All 4 branches missed.">				if (root &gt;= en &amp;&amp; root &lt;= 1.0) {</span>
<span class="nc" id="L98">					p = new TestCase();</span>
<span class="nc" id="L99">					p.p = root;</span>
<span class="nc" id="L100">					haveAanswer = true;</span>
				}
<span class="nc" id="L102">			} else {</span>
<span class="nc bnc" id="L103" title="All 4 branches missed.">				if (root &gt;= tests.get(temp - 1).p &amp;&amp; root &lt;= tests.get(temp).p) {</span>
<span class="nc" id="L104">					p = new TestCase();</span>
<span class="nc" id="L105">					p.p = root;</span>
<span class="nc" id="L106">					haveAanswer = true;</span>
				}
			}
		}
<span class="nc bnc" id="L110" title="All 2 branches missed.">		if (!haveAanswer) {</span>
<span class="nc" id="L111">			return null;</span>
		} else {
<span class="nc" id="L113">			return p;</span>
		}
	}

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L118" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L119">			return false;</span>
		} else {
<span class="nc" id="L121">			return true;</span>
		}
	}

	public int run() {
<span class="nc" id="L126">		fail_start = genFail_start();</span>
<span class="nc" id="L127">		TestCase p = new TestCase();</span>
<span class="nc" id="L128">		p.p = random.nextDouble() * (max - min) + min;</span>
<span class="nc" id="L129">		int count = 0;</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L131">			count++;</span>
<span class="nc bnc" id="L132" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L133">				tests.add(p);</span>
<span class="nc" id="L134">			} else {</span>
<span class="nc" id="L135">				sortTestCases(p);</span>
			}
			/////////////////////
			// 生成概率分布函数
			// An-&gt;(-e1,0) Bn-&gt;(1,2-en)
<span class="nc" id="L140">			double e1 = tests.get(0).p;// first node</span>
<span class="nc" id="L141">			double en = tests.get(tests.size() - 1).p;// last node</span>
			// calculate An and Bn
			// An (min-(e1-min)) to min |||| Bn max to max+max-en
<span class="nc" id="L144">			An = random.nextDouble() * (e1 - min) + (2 * min - e1);</span>
<span class="nc" id="L145">			Bn = random.nextDouble() * (max - en) + max;</span>
			// 计算系数
<span class="nc" id="L147">			double Co = 0.0;</span>
<span class="nc" id="L148">			ArrayList&lt;Double&gt; integrals = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L149" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size() + 1; i++) {</span>
<span class="nc bnc" id="L150" title="All 2 branches missed.">				if (i == 0) {</span>
					// 1/6*e1^3-1/2*an*e1^2
<span class="nc" id="L152">					double temp = ((1.0 / 6.0) * (Math.pow(e1, 3))) - ((1.0 / 2.0) * An * (Math.pow(e1, 2)));</span>
<span class="nc" id="L153">					Co += temp;</span>
<span class="nc" id="L154">					integrals.add(temp);</span>
<span class="nc bnc" id="L155" title="All 2 branches missed.">				} else if (i == tests.size()) {</span>
					// (-1/6)en^3+(1/2)*bn*en^2-en*bn+(1/2)*(bn+en)-(1/3)
<span class="nc" id="L157">					double temp = (-1.0 / 6.0) * Math.pow(en, 3.0) + (1.0 / 2.0) * (Bn) * Math.pow(en, 2.0) - en * Bn</span>
<span class="nc" id="L158">							+ (1.0 / 2.0) * (Bn + en) - (1.0 / 3.0);</span>
<span class="nc" id="L159">					Co += temp;</span>
<span class="nc" id="L160">					integrals.add(temp);</span>
<span class="nc" id="L161">				} else {</span>
<span class="nc" id="L162">					double ei_1 = tests.get(i - 1).p;</span>
<span class="nc" id="L163">					double ei = tests.get(i).p;</span>
					// (1/6)*(ei1^3-ei^3)+(1/2)*(ei*ei1)*(ei-ei1)
<span class="nc" id="L165">					double temp = (1.0 / 6.0) * (Math.pow(ei, 3.0) - Math.pow(ei_1, 3))</span>
<span class="nc" id="L166">							+ (1.0 / 2.0) * (ei_1 * ei) * (ei_1 - ei);</span>
<span class="nc" id="L167">					Co += temp;</span>
<span class="nc" id="L168">					integrals.add(temp);</span>
				}
			}
<span class="nc" id="L171">			Co = 1.0 / Co;</span>
<span class="nc" id="L172">			p = genNext(integrals, Co, p, seedOfRandom);</span>
<span class="nc" id="L173">			int countErr = 0;</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">			while (p == null) {</span>
<span class="nc" id="L175">				p = genNext(integrals, Co, p, System.currentTimeMillis());</span>
				// System.out.println(&quot;error:&quot;+countErr++);
			}
		}
<span class="nc" id="L179">		return count;</span>
	}

	public double[] shengjinFormula(double acof, double bcof, double cof, double dof) {
<span class="nc" id="L183">		double A = bcof * bcof - 3.0 * acof * cof;// A=b^2-3ac</span>
<span class="nc" id="L184">		double B = bcof * cof - 9.0 * acof * dof;// B=bc-9ad</span>
<span class="nc" id="L185">		double C = cof * cof - 3.0 * bcof * dof;// C=c^2-3bd</span>
<span class="nc" id="L186">		double delta = B * B - 4.0 * A * C;</span>
<span class="nc" id="L187">		double root = 0.0;</span>
<span class="nc" id="L188">		double r1 = 0.0;</span>
<span class="nc" id="L189">		double r2 = 0.0;</span>
<span class="nc" id="L190">		double[] roots = new double[3];</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">		if (delta &gt; 0) {</span>
<span class="nc" id="L192">			double Y1 = A * bcof + 3.0 * acof * (-B + Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
<span class="nc" id="L193">			double Y2 = A * bcof + 3.0 * acof * (-B - Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
			double powY1;
			double powY2;
<span class="nc bnc" id="L196" title="All 2 branches missed.">			if (Y1 &lt; 0) {</span>
<span class="nc" id="L197">				powY1 = -Math.pow(-Y1, 1.0 / 3.0);</span>
<span class="nc" id="L198">			} else {</span>
<span class="nc" id="L199">				powY1 = Math.pow(Y1, 1.0 / 3.0);</span>
			}
<span class="nc bnc" id="L201" title="All 2 branches missed.">			if (Y2 &lt; 0) {</span>
<span class="nc" id="L202">				powY2 = -Math.pow(-Y2, 1.0 / 3.0);</span>
<span class="nc" id="L203">			} else {</span>
<span class="nc" id="L204">				powY2 = Math.pow(Y2, 1.0 / 3.0);</span>
			}
<span class="nc" id="L206">			root = (-bcof - powY1 - powY2) / (3.0 * acof);</span>
<span class="nc" id="L207">			r1 = root;</span>
<span class="nc" id="L208">			r2 = root;</span>
<span class="nc bnc" id="L209" title="All 2 branches missed.">		} else if (delta == 0) {</span>
<span class="nc" id="L210">			root = -bcof / acof + B / A;</span>
<span class="nc" id="L211">			r1 = -B / (2.0 * A);</span>
<span class="nc" id="L212">			r2 = r1;</span>

<span class="nc bnc" id="L214" title="All 2 branches missed.">		} else if (delta &lt; 0) {</span>
<span class="nc" id="L215">			double T = (2.0 * A * bcof - 3.0 * acof * B) / (2.0 * Math.pow(A, 3.0 / 2.0));</span>
<span class="nc" id="L216">			double theta = Math.acos(T);</span>
<span class="nc" id="L217">			root = (-bcof - 2.0 * Math.sqrt(A) * Math.cos(theta / 3.0)) / (3.0 * acof);</span>
<span class="nc" id="L218">			r1 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) + Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L219">					/ (3.0 * acof);</span>
<span class="nc" id="L220">			r2 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) - Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L221">					/ (3.0 * acof);</span>
		}
<span class="nc" id="L223">		roots[0] = root;</span>
<span class="nc" id="L224">		roots[1] = r1;</span>
<span class="nc" id="L225">		roots[2] = r2;</span>
<span class="nc" id="L226">		return roots;</span>
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L230">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L231" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L232">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L233" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L234">				low = mid + 1;</span>
<span class="nc" id="L235">			} else {</span>
<span class="nc" id="L236">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L239" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L240">			mid = mid - 1;</span>
		}
<span class="nc" id="L242">		tests.add(mid + 1, p);</span>
<span class="nc" id="L243">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>