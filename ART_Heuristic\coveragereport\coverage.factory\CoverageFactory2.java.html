<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>CoverageFactory2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">coverage.factory</a> &gt; <span class="el_source">CoverageFactory2.java</span></div><h1>CoverageFactory2.java</h1><pre class="source lang-java linenums">package coverage.factory;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;

import org.apache.poi.util.SystemOutLogger;
import org.jacoco.core.analysis.Analyzer;
import org.jacoco.core.analysis.CoverageBuilder;
import org.jacoco.core.analysis.IClassCoverage;
import org.jacoco.core.analysis.ICounter;
import org.jacoco.core.data.ExecutionDataStore;
import org.jacoco.core.data.SessionInfoStore;
import org.jacoco.core.instr.Instrumenter;
import org.jacoco.core.runtime.IRuntime;
import org.jacoco.core.runtime.LoggerRuntime;
import org.jacoco.core.runtime.RuntimeData;

import coverage.classloader.MemoryClassLoader;

public class CoverageFactory2 {
<span class="nc" id="L22">	public static void main(String[] args) {</span>
<span class="nc" id="L23">		CoverageFactory2 factory = new CoverageFactory2();</span>
<span class="nc" id="L24">		factory.setClassName(&quot;tested.coverage.TestForCoverage&quot;);</span>
		System.out.println(factory.execute(12));
<span class="nc" id="L26"></span>
	}

	private String ClassName;
<span class="nc" id="L30"></span>
	private String[] methodsArr = null;
<span class="nc" id="L32"></span>
<span class="nc" id="L33">	public CoverageFactory2() {</span>
	}
<span class="nc" id="L35"></span>
<span class="nc" id="L36">	public CoverageFactory2(String ClassName, String[] methodsArr) {</span>
<span class="nc" id="L37">		this.ClassName = ClassName;</span>
<span class="nc" id="L38">		this.methodsArr = methodsArr;</span>
	}

<span class="nc" id="L41">	public double execute(double a) {</span>
		final String targetName = ClassName;
<span class="nc" id="L43">		</span>
<span class="nc" id="L44">		final IRuntime runtime = new LoggerRuntime();</span>
<span class="nc" id="L45">		final Instrumenter instr = new Instrumenter(runtime);</span>
		byte[] instrumented = null;
<span class="nc" id="L47">		try {</span>
<span class="nc" id="L48">			instrumented = instr.instrument(getTargetClass(targetName), targetName);</span>
<span class="nc" id="L49">		} catch (IOException e) {</span>
			e.printStackTrace();
<span class="nc" id="L51">		}</span>
		final RuntimeData data = new RuntimeData();
<span class="nc" id="L53">		try {</span>
<span class="nc" id="L54">			runtime.startup(data);</span>
<span class="nc" id="L55">		} catch (Exception e) {</span>
			e.printStackTrace();
		}
		// In this tutorial we use a special class loader to directly load the
<span class="nc" id="L59">		// instrumented class definition from a byte[] instances.</span>
<span class="nc" id="L60">		final MemoryClassLoader memoryClassLoader = new MemoryClassLoader();</span>
<span class="nc" id="L61">		memoryClassLoader.addDefinition(targetName, instrumented);</span>
		Class&lt;?&gt; targetClass = null;
<span class="nc" id="L63">		try {</span>
<span class="nc" id="L64">			targetClass = memoryClassLoader.loadClass(targetName);</span>
<span class="nc" id="L65">		} catch (ClassNotFoundException e) {</span>
			e.printStackTrace();
		}

		// Here we execute our test target class through its Runnable interface:
		// final Runnable targetInstance = (Runnable) targetClass.newInstance();
<span class="nc" id="L71">		// targetInstance.run();</span>
		Object targetInstance = null;

<span class="nc" id="L74">		try {</span>
<span class="nc" id="L75">			targetInstance = targetClass.newInstance();</span>
<span class="nc" id="L76">		} catch (InstantiationException | IllegalAccessException e) {</span>
			e.printStackTrace();
		}
		try {
			// 改进
			//targetClass.getMethod(&quot;correct&quot;, double.class).invoke(targetInstance, a);
			String[] b=new String[]{&quot;&quot;,&quot;&quot;};
			targetClass.getMethod(&quot;main&quot;,String[].class).invoke(targetInstance,(Object)b);
		} catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException | NoSuchMethodException
				| SecurityException e) {
<span class="nc" id="L86">			e.printStackTrace();</span>
<span class="nc" id="L87">		}</span>
<span class="nc" id="L88">		final ExecutionDataStore executionData = new ExecutionDataStore();</span>
<span class="nc" id="L89">		final SessionInfoStore sessionInfos = new SessionInfoStore();</span>
<span class="nc" id="L90">		data.collect(executionData, sessionInfos, false);</span>
<span class="nc" id="L91">		runtime.shutdown();</span>
		final CoverageBuilder coverageBuilder = new CoverageBuilder();
<span class="nc" id="L93">		final Analyzer analyzer = new Analyzer(executionData, coverageBuilder);</span>
<span class="nc" id="L94">		try {</span>
<span class="nc" id="L95">			analyzer.analyzeClass(getTargetClass(targetName), targetName);</span>
		} catch (IOException e) {
			e.printStackTrace();
<span class="nc" id="L98">		}</span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">		// Let's dump some metrics and line coverage information:</span>
		double codeCoverage = 0;
		for (final IClassCoverage cc : coverageBuilder.getClasses()) {
			// System.out.printf(&quot;Coverage of class %s%n&quot;, cc.getName());
<span class="nc" id="L103">			// printCounter(&quot;instructions&quot;, cc.getInstructionCounter());</span>
			// printCounter(&quot;branches:&quot;, cc.getBranchCounter());
			codeCoverage = printCounter(&quot;lines&quot;, cc.getLineCounter());
			// printCounter(&quot;methods&quot;, cc.getMethodCounter());
<span class="nc" id="L107">			// printCounter(&quot;complexity&quot;, cc.getComplexityCounter());</span>
		}
		return codeCoverage;
	}
<span class="nc" id="L111"></span>
<span class="nc" id="L112">	private InputStream getTargetClass(final String name) {</span>
		final String resource = '/' + name.replace('.', '/') + &quot;.class&quot;;
		return getClass().getResourceAsStream(resource);
	}
<span class="nc" id="L116"></span>
<span class="nc" id="L117">	private double printCounter(final String unit, final ICounter counter) {</span>
		final Integer missed = Integer.valueOf(counter.getMissedCount());
		final Integer total = Integer.valueOf(counter.getTotalCount());
<span class="nc" id="L120">		// System.out.printf(&quot;%s of %s %s missed%n&quot;, missed, total, unit);</span>
		// System.out.println((total-missed)/(double)total);
		//System.out.println(total+&quot; miss:&quot;+missed);
		return (total - missed) / (double) total;
<span class="nc" id="L124">	}</span>
<span class="nc" id="L125"></span>
	public void setClassName(String name) {
		this.ClassName = name;
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>