<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRT_DC.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_dc</a> &gt; <span class="el_source">RRT_DC.java</span></div><h1>RRT_DC.java</h1><pre class="source lang-java linenums">package test.simulations.art_dc;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.simulations.rrt.RRT_ND;
import util.PaiLie;
import util.data.ZeroOneCreator;

/**
 * <AUTHOR>
 * @date 2017/11/25 二维DivideAndConquer,使用RRT方法产生测试用例
 */
public class RRT_DC extends RRT_ND {

	public static void main(String[] args) {
<span class="nc" id="L22">		int n = 5;</span>
<span class="nc" id="L23">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L24">		double[] min = dataCreator.minCreator(n);</span>
<span class="nc" id="L25">		double[] max = dataCreator.maxCreator(n);</span>

<span class="nc" id="L27">		int lamda = 100;</span>

<span class="nc" id="L29">		int times = 3000;</span>

<span class="nc" id="L31">		int fm = 0;</span>
<span class="nc bnc" id="L32" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L33">			FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L34">			failurePattern.fail_rate = 0.005;</span>
<span class="nc" id="L35">			RRT_DC dc = new RRT_DC(min, max, failurePattern, new Random(i * 3), 1.5, lamda);</span>
<span class="nc" id="L36">			int temp = dc.run();</span>
<span class="nc" id="L37">			fm += temp;</span>
		}
<span class="nc" id="L39">		System.out.println(fm / (double) times);</span>
<span class="nc" id="L40">	}</span>
	private int lamda;

<span class="nc" id="L43">	ArrayList&lt;NRectRegion&gt; d_queue = new ArrayList&lt;&gt;();</span>

	public RRT_DC(double[] min, double[] max, FailurePattern pattern, Random random, double r, int lamda) {
<span class="nc" id="L46">		super(min, max, pattern, random, r);</span>
<span class="nc" id="L47">		this.lamda = lamda;</span>
<span class="nc" id="L48">	}</span>

	public void addNewRegions(ArrayList&lt;NRectRegion&gt; d_queue, NRectRegion region) {
		// only for two dimension
		// addRegionsIn2D(region);

		// for n dimesnion
<span class="nc" id="L55">		NPoint midPoint = midPoint(region.getStart(), region.getEnd());</span>
		try {
<span class="nc" id="L57">			addRegionsInND(region, midPoint);</span>
<span class="nc" id="L58">		} catch (Exception e) {</span>
<span class="nc" id="L59">			e.printStackTrace();</span>
		}
<span class="nc" id="L61">	}</span>

	public void addRegionsIn2D(NRectRegion region) {
<span class="nc" id="L64">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L65">		double[] end = region.getEnd().getXn();</span>

<span class="nc" id="L67">		double xmin = start[0];</span>
<span class="nc" id="L68">		double ymin = start[1];</span>
<span class="nc" id="L69">		double xmax = end[0];</span>
<span class="nc" id="L70">		double ymax = end[1];</span>
<span class="nc" id="L71">		double pp = 0.5 * (end[0] - start[0]);</span>
<span class="nc" id="L72">		double qq = 0.5 * (end[1] - start[1]);</span>

<span class="nc" id="L74">		NRectRegion first = new NRectRegion(new NPoint(new double[] { xmin, ymin }),</span>
<span class="nc" id="L75">				new NPoint(new double[] { pp, qq }));</span>
<span class="nc" id="L76">		NRectRegion second = new NRectRegion(new NPoint(new double[] { pp, ymin }),</span>
<span class="nc" id="L77">				new NPoint(new double[] { xmax, qq }));</span>
<span class="nc" id="L78">		NRectRegion third = new NRectRegion(new NPoint(new double[] { pp, qq }),</span>
<span class="nc" id="L79">				new NPoint(new double[] { xmax, ymax }));</span>
<span class="nc" id="L80">		NRectRegion fourth = new NRectRegion(new NPoint(new double[] { xmin, qq }),</span>
<span class="nc" id="L81">				new NPoint(new double[] { pp, ymax }));</span>

<span class="nc" id="L83">		d_queue.add(first);</span>
<span class="nc" id="L84">		d_queue.add(second);</span>
<span class="nc" id="L85">		d_queue.add(third);</span>
<span class="nc" id="L86">		d_queue.add(fourth);</span>
<span class="nc" id="L87">	}</span>

	public void addRegionsInND(NRectRegion region, NPoint p) throws Exception {
		// int regions=(int) Math.pow(2, this.dimension);
<span class="nc" id="L91">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L92">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L93">		double[] pxn = p.getXn();</span>
<span class="nc" id="L94">		List&lt;List&lt;Double&gt;&gt; result1 = splitRegions(start, pxn);</span>
<span class="nc" id="L95">		List&lt;List&lt;Double&gt;&gt; result2 = splitRegions(pxn, end);</span>
		// System.out.println(result1.size());
<span class="nc bnc" id="L97" title="All 2 branches missed.">		if (result1.size() != result2.size()) {</span>
<span class="nc" id="L98">			throw new Exception(&quot;result1's size!=result2's size ,split region wrong&quot;);</span>
		}
<span class="nc bnc" id="L100" title="All 2 branches missed.">		for (int i = 0; i &lt; result1.size(); i++) {</span>
<span class="nc" id="L101">			List&lt;Double&gt; temp1 = result1.get(i);</span>
<span class="nc" id="L102">			List&lt;Double&gt; temp2 = result2.get(i);</span>
<span class="nc" id="L103">			double[] newStart = new double[temp1.size()];</span>
<span class="nc" id="L104">			double[] newEnd = new double[temp2.size()];</span>
<span class="nc bnc" id="L105" title="All 2 branches missed.">			for (int j = 0; j &lt; temp1.size(); j++) {</span>
<span class="nc" id="L106">				newStart[j] = temp1.get(j);</span>
<span class="nc" id="L107">				newEnd[j] = temp2.get(j);</span>
			}

<span class="nc" id="L110">			NRectRegion tempRegion = new NRectRegion(new NPoint(newStart), new NPoint(newEnd));</span>
<span class="nc" id="L111">			this.d_queue.add(tempRegion);</span>
		}
<span class="nc" id="L113">	}</span>

	public double calculateRadius(NRectRegion region, int count) {
<span class="nc bnc" id="L116" title="All 2 branches missed.">		if (this.dimension % 2 == 0) {</span>
<span class="nc" id="L117">			int k = this.dimension / 2;</span>
<span class="nc" id="L118">			double kjie = 1;</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">			for (int i = k; i &gt; 0; i--) {</span>
<span class="nc" id="L120">				kjie *= i;</span>
			}
<span class="nc" id="L122">			double temp = (this.R * region.size() * kjie) / (count * Math.pow(Math.PI, k));</span>

<span class="nc" id="L124">			return Math.pow(temp, 1 / (double) this.dimension);</span>
		} else {
<span class="nc" id="L126">			int k = this.dimension / 2;</span>
<span class="nc" id="L127">			double kjie = 1;</span>
<span class="nc" id="L128">			double k2jie = 1;</span>
<span class="nc bnc" id="L129" title="All 2 branches missed.">			for (int i = k; i &gt; 0; i--) {</span>
<span class="nc" id="L130">				kjie *= i;</span>
			}
<span class="nc bnc" id="L132" title="All 2 branches missed.">			for (int i = (2 * k + 1); i &gt; 0; i--) {</span>
<span class="nc" id="L133">				k2jie *= i;</span>
			}
<span class="nc" id="L135">			double temp = (this.R * region.size() * k2jie)</span>
<span class="nc" id="L136">					/ (kjie * Math.pow(2, 2 * k + 1) * Math.pow(Math.PI, k) * count);</span>
			// System.out.println(&quot;return R&quot;);
<span class="nc" id="L138">			return Math.pow(temp, 1 / (double) this.dimension);</span>
		}
	}

	public int findLeastTestCaseInD(ArrayList&lt;NPoint&gt; pointsInDi) {
		// NRectRegion region=null;
<span class="nc" id="L144">		int index = -1;</span>
<span class="nc" id="L145">		int count = Integer.MAX_VALUE;</span>
<span class="nc bnc" id="L146" title="All 2 branches missed.">		for (int i = 0; i &lt; d_queue.size(); i++) {</span>
<span class="nc" id="L147">			int temp = 0;</span>
<span class="nc bnc" id="L148" title="All 2 branches missed.">			for (int j = 0; j &lt; this.tests.size(); j++) {</span>
<span class="nc bnc" id="L149" title="All 2 branches missed.">				if (isTCInD(d_queue.get(i), this.tests.get(j))) {</span>
<span class="nc" id="L150">					temp++;</span>
<span class="nc" id="L151">					pointsInDi.add(this.tests.get(j));</span>
				}
			}
<span class="nc bnc" id="L154" title="All 2 branches missed.">			if (temp &lt; count) {</span>
<span class="nc" id="L155">				count = temp;</span>
<span class="nc" id="L156">				index = i;</span>
				// region=d_queue.get(i);
			}
		}
<span class="nc" id="L160">		return index;</span>
	}

	public boolean isTCInD(NRectRegion region, NPoint point) {
<span class="nc" id="L164">		boolean result = true;</span>
<span class="nc" id="L165">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L166">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L167">		double[] pxn = point.getXn();</span>
<span class="nc bnc" id="L168" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc bnc" id="L169" title="All 4 branches missed.">			if (pxn[i] &lt; start[i] || pxn[i] &gt; end[i]) {</span>
<span class="nc" id="L170">				result = false;</span>
<span class="nc" id="L171">				break;</span>
			}
		}
<span class="nc" id="L174">		return result;</span>
	}

	public NPoint midPoint(NPoint p1, NPoint p2) {
<span class="nc" id="L178">		double[] p1xn = p1.getXn();</span>
<span class="nc" id="L179">		double[] p2xn = p2.getXn();</span>
<span class="nc" id="L180">		double[] mid = new double[this.dimension];</span>
<span class="nc bnc" id="L181" title="All 2 branches missed.">		for (int i = 0; i &lt; mid.length; i++) {</span>
<span class="nc" id="L182">			mid[i] = 0.5 * (p1xn[i] + p2xn[i]);</span>
		}
<span class="nc" id="L184">		return new NPoint(mid);</span>
	}

	public NPoint randomTCByRRT(NRectRegion region, ArrayList&lt;NPoint&gt; tests) {
<span class="nc" id="L188">		NPoint point = null;</span>
<span class="nc" id="L189">		double radius = calculateRadius(region, tests.size());</span>

<span class="nc" id="L191">		boolean flag = true;</span>
<span class="nc bnc" id="L192" title="All 2 branches missed.">		while (flag) {</span>
<span class="nc" id="L193">			flag = false;</span>
<span class="nc" id="L194">			point = randomCreator.randomPoint(region);</span>
<span class="nc bnc" id="L195" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size(); i++) {</span>
				// 排除区域是圆
				// 计算距离
<span class="nc" id="L198">				double[] tested = tests.get(i).getXn();</span>
<span class="nc" id="L199">				double distance = 0;</span>
<span class="nc" id="L200">				double[] untested = point.getXn();</span>
<span class="nc bnc" id="L201" title="All 2 branches missed.">				for (int j = 0; j &lt; this.dimension; j++) {</span>
<span class="nc" id="L202">					distance += Math.pow((tests.get(i).getXn()[j] - untested[j]), 2);</span>
				}
<span class="nc" id="L204">				distance = Math.sqrt(distance);</span>
<span class="nc bnc" id="L205" title="All 2 branches missed.">				if (distance &lt; radius) {</span>
<span class="nc" id="L206">					flag = true;</span>
					// break;
				}
				/*
				 * //排除区域是正方形 if(Math.abs(p.p-tests.get(i).p)&lt;radius){
				 * if(Math.abs(p.q-tests.get(i).q)&lt;radius){ flag=true; } }
				 */
			}

		}
<span class="nc" id="L216">		return point;</span>
	}

	public NPoint randomTCByRT(NRectRegion region, ArrayList&lt;NPoint&gt; tests) {
<span class="nc" id="L220">		NPoint point = new NPoint();</span>
<span class="nc" id="L221">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L222">		double[] end = region.getEnd().getXn();</span>
		// NPoint point = new NPoint();
<span class="nc" id="L224">		point.dimension = this.dimension;</span>
<span class="nc" id="L225">		double[] xn = new double[this.dimension];</span>
<span class="nc bnc" id="L226" title="All 2 branches missed.">		for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="nc" id="L227">			xn[i] = random.nextDouble() * (end[i] - start[i]) + start[i];</span>
		}
<span class="nc" id="L229">		point.setXn(xn);</span>
<span class="nc" id="L230">		return point;</span>
	}

	@Override
	public int run() {
<span class="nc" id="L235">		int count = 0;</span>
<span class="nc" id="L236">		int depth = 0;</span>

<span class="nc" id="L238">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc" id="L239">		d_queue.add(new NRectRegion(new NPoint(this.min), new NPoint(this.max)));</span>

<span class="nc" id="L241">		ArrayList&lt;NRectRegion&gt; temp_d_queue = new ArrayList&lt;&gt;();</span>
		// t_queue.add(new )
<span class="nc bnc" id="L243" title="All 2 branches missed.">		while (this.failPattern.isCorrect(p)) {</span>
			// select the least populated sub-domain Di from d_queue;
			/* that is, find the least number of test cases in all RectRegion */
<span class="nc" id="L246">			this.tests.add(p);</span>

<span class="nc" id="L248">			count++;</span>

<span class="nc" id="L250">			ArrayList&lt;NPoint&gt; pointsInDi = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L251">			int indexOfLeastTCRegion = findLeastTestCaseInD(pointsInDi);</span>
<span class="nc" id="L252">			NRectRegion leastTCRegion = d_queue.get(indexOfLeastTCRegion);</span>

<span class="nc" id="L254">			p = randomTCByRRT(leastTCRegion, pointsInDi);</span>
			// p=randomTCByRT(leastTCRegion,pointsInDi);

<span class="nc bnc" id="L257" title="All 2 branches missed.">			if (pointsInDi.size() == lamda) {</span>
				// remove this region and add it into temp
<span class="nc" id="L259">				this.d_queue.remove(indexOfLeastTCRegion);</span>
				// add new regions into temp
				// temp_d_queue.add()
<span class="nc" id="L262">				addNewRegions(temp_d_queue, leastTCRegion);</span>
				// leastTCRegion.getStart();
			}
<span class="nc bnc" id="L265" title="All 2 branches missed.">			if (d_queue.size() == 0) {</span>
<span class="nc" id="L266">				depth++;</span>
<span class="nc" id="L267">				d_queue = temp_d_queue;</span>
<span class="nc" id="L268">				temp_d_queue = new ArrayList&lt;&gt;();</span>
			}
		}

<span class="nc" id="L272">		return count;</span>
	}

	public List&lt;List&lt;Double&gt;&gt; splitRegions(double[] start, double[] end) {
<span class="nc" id="L276">		ArrayList&lt;double[]&gt; values = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L277" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L278">			double[] temp = new double[2];</span>

<span class="nc" id="L280">			temp[0] = start[i];</span>
<span class="nc" id="L281">			temp[1] = end[i];</span>
<span class="nc" id="L282">			values.add(temp);</span>
		}

<span class="nc" id="L285">		ArrayList&lt;List&lt;Double&gt;&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L286">		PaiLie.per(values, 0, new ArrayList&lt;&gt;(), result);</span>
<span class="nc" id="L287">		return result;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>