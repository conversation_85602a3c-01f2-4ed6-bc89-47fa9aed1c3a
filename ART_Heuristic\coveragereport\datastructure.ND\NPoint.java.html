<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>NPoint.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.ND</a> &gt; <span class="el_source">NPoint.java</span></div><h1>NPoint.java</h1><pre class="source lang-java linenums">package datastructure.ND;

public class NPoint {
	public static void main(String[] args) {
		// NPoint npoint=new NPoint(null);
		// System.out.println(npoint.toString());
		// npoint.setXn(new double[]{2.2,3,4.6,7,8});
		// System.out.println(npoint.toString());

<span class="nc" id="L10">	}</span>
	double xn[];

	public int dimension;

<span class="fc" id="L15">	public NPoint() {</span>
		
<span class="fc" id="L17">	}</span>

<span class="nc" id="L19">	public NPoint(double xn[]) {</span>
<span class="nc" id="L20">		this.xn = xn;</span>
<span class="nc bnc" id="L21" title="All 2 branches missed.">		if (xn != null) {</span>
<span class="nc" id="L22">			this.dimension = xn.length;</span>
<span class="nc" id="L23">		} else {</span>
<span class="nc" id="L24">			this.dimension = -1;</span>
		}
<span class="nc" id="L26">	}</span>

	public int getDimension() {
<span class="nc" id="L29">		return dimension;</span>
	}

	public double[] getXn() {
<span class="fc" id="L33">		return xn;</span>
	}

	public void setDimension(int dimension) {
<span class="fc" id="L37">		this.dimension = dimension;</span>
<span class="fc" id="L38">	}</span>

	public void setXn(double[] xn) {
<span class="fc" id="L41">		this.xn = xn;</span>
<span class="fc" id="L42">	}</span>

	public String show() {
<span class="nc" id="L45">		StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L46">		sb.append(&quot;(&quot;);</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">		if (xn != null) {</span>
<span class="nc bnc" id="L48" title="All 2 branches missed.">			for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="nc bnc" id="L49" title="All 2 branches missed.">				if (i == xn.length - 1)</span>
<span class="nc" id="L50">					sb.append(xn[i] + &quot;)&quot;);</span>
				else
<span class="nc" id="L52">					sb.append(xn[i] + &quot;,&quot;);</span>
			}
<span class="nc" id="L54">		} else {</span>
<span class="nc" id="L55">			sb.append(&quot;null)&quot;);</span>
		}
<span class="nc" id="L57">		return sb.toString();</span>
	}

	public String toString() {
<span class="nc" id="L61">		StringBuilder sb = new StringBuilder();</span>
<span class="nc" id="L62">		sb.append(&quot;Point[&quot;);</span>
<span class="nc bnc" id="L63" title="All 2 branches missed.">		if (xn != null) {</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">			for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="nc bnc" id="L65" title="All 2 branches missed.">				if (i == xn.length - 1)</span>
<span class="nc" id="L66">					sb.append(&quot;x&quot; + i + &quot;=&quot; + xn[i] + &quot;]&quot;);</span>
				else
<span class="nc" id="L68">					sb.append(&quot;x&quot; + i + &quot;=&quot; + xn[i] + &quot;,&quot;);</span>
			}
<span class="nc" id="L70">		} else {</span>
<span class="nc" id="L71">			sb.append(&quot;null]&quot;);</span>
		}
<span class="nc" id="L73">		return sb.toString();</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>