<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRTtpMax2D</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.rrttp.partion</a> &gt; <span class="el_class">RRTtpMax2D</span></div><h1>RRTtpMax2D</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">579 of 579</td><td class="ctr2">0%</td><td class="bar">16 of 16</td><td class="ctr2">0%</td><td class="ctr1">19</td><td class="ctr2">19</td><td class="ctr1">101</td><td class="ctr2">101</td><td class="ctr1">11</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a0"><a href="RRTtpMax2D.java.html#L61" class="el_method">addRegionsIn2D(int, NPoint)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="233" alt="233"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h0">32</td><td class="ctr2" id="i0">32</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a1"><a href="RRTtpMax2D.java.html#L142" class="el_method">calculateRadius(int)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="60" height="10" title="117" alt="117"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h1">16</td><td class="ctr2" id="i1">16</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a10"><a href="RRTtpMax2D.java.html#L192" class="el_method">testReality()</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="40" height="10" title="78" alt="78"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h2">15</td><td class="ctr2" id="i2">15</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a4"><a href="RRTtpMax2D.java.html#L113" class="el_method">genNextFromAvailableRegion()</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="31" height="10" title="61" alt="61"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h3">14</td><td class="ctr2" id="i3">14</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a8"><a href="RRTtpMax2D.java.html#L37" class="el_method">run()</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="30" height="10" title="60" alt="60"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h4">12</td><td class="ctr2" id="i4">12</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="RRTtpMax2D.java.html#L28" class="el_method">RRTtpMax2D(double[], double[], Random, FailurePattern, double)</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="7" height="10" title="15" alt="15"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">4</td><td class="ctr2" id="i5">4</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a9"><a href="RRTtpMax2D.java.html#L57" class="el_method">splitRegions(int, NPoint)</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">2</td><td class="ctr2" id="i6">2</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="RRTtpMax2D.java.html#L137" class="el_method">inAvailRegion(RRTtpRegion, NPoint)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="RRTtpMax2D.java.html#L187" class="el_method">main(String[])</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="RRTtpMax2D.java.html#L217" class="el_method">em()</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="RRTtpMax2D.java.html#L223" class="el_method">generateNextTC()</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>