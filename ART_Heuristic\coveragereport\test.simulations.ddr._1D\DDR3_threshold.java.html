<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>DDR3_threshold.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.ddr._1D</a> &gt; <span class="el_source">DDR3_threshold.java</span></div><h1>DDR3_threshold.java</h1><pre class="source lang-java linenums">package test.simulations.ddr._1D;
/* (Simulation) This code resets the radius anytime the all the candidate have empty exclusion regions. 
That is when the radius becomes too small due to multi-dimentional inputs.
*/

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class DDR3_threshold {
	public static void main(String[] args) {
<span class="nc" id="L13">		int times = 5000;</span>
<span class="nc" id="L14">		long sums = 0;</span>
<span class="nc" id="L15">		int temp = 0;</span>
<span class="nc" id="L16">		int s = 10;</span>
<span class="nc" id="L17">		int t = 10;//</span>
		//////////////
		///////////////
<span class="nc" id="L20">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L21" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L22">			DDR3_threshold rrt_od = new DDR3_threshold(0, 1, 0.2, s, t, 0.01, i * 3);// 0.002 refers to failure rate</span>
<span class="nc" id="L23">			temp = rrt_od.run();</span>
<span class="nc" id="L24">			sums += temp;</span>
		}
<span class="nc" id="L26">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L27">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L28">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L29">	}</span>
	double min;
	double max;
	double fail_start;
	double fail_rate;
	double R;
	int s;
	int t;
	int randomseed;

<span class="nc" id="L39">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	public DDR3_threshold(double min, double max, double r, int s, int t, double fail_rate, int randomseed) {
<span class="nc" id="L42">		super();</span>
<span class="nc" id="L43">		this.min = min;</span>
<span class="nc" id="L44">		this.max = max;</span>
<span class="nc" id="L45">		R = r;</span>
<span class="nc" id="L46">		this.s = s;</span>
<span class="nc" id="L47">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L48">		this.t = t;</span>
<span class="nc" id="L49">		this.randomseed = randomseed;</span>
<span class="nc" id="L50">	}</span>

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L53" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L54">			return false;</span>
		} else {
<span class="nc" id="L56">			return true;</span>
		}
	}

	public TestCase randomTC(Random random) {
<span class="nc" id="L61">		TestCase temp = new TestCase();</span>
<span class="nc" id="L62">		double temp_value = random.nextDouble() * (max - min) + min;</span>
<span class="nc" id="L63">		temp.p = temp_value;</span>
<span class="nc" id="L64">		return temp;</span>
	}

	public int run() {
<span class="nc" id="L68">		Random random = new Random(randomseed);</span>
<span class="nc" id="L69">		fail_start = random.nextDouble() * (1 - fail_rate);</span>
<span class="nc" id="L70">		int count = 0;// 记录测试用例数量</span>
<span class="nc" id="L71">		int rrtcount = 0;</span>
<span class="nc" id="L72">		int fscscount = 0;</span>
<span class="nc" id="L73">		int _10CandidateCount = 0;// 每十个一次的数量</span>
<span class="nc" id="L74">		int ttemp = 0;</span>
		double radius;
<span class="nc" id="L76">		TestCase p = randomTC(random);// 第一个测试用例</span>
		// System.out.println(&quot;p0:&quot; + p.p);
<span class="nc bnc" id="L78" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">			if (ttemp == t) {</span>
<span class="nc" id="L80">				_10CandidateCount = 0;</span>
<span class="nc" id="L81">				System.out.println(&quot;Candidate reset&quot;);</span>
			}
<span class="nc" id="L83">			radius = R / (2 * (s + _10CandidateCount));</span>
<span class="nc" id="L84">			ttemp = 0;</span>
			// 生成s个候选测试用例,从s个候选测试用例中挑选符合要求的测试用例
<span class="nc" id="L86">			boolean all_s_has_E_flag = true;</span>
<span class="nc" id="L87">			double TS2C[] = new double[s];</span>
<span class="nc" id="L88">			double Cvalue[] = new double[s];</span>
<span class="nc bnc" id="L89" title="All 2 branches missed.">			for (int k = 0; k &lt; s; k++) {</span>
				// 生成一个候选测试用例
				// System.out.println(&quot;k&quot;+k);
<span class="nc" id="L92">				TestCase ck = randomTC(random);</span>
<span class="nc" id="L93">				boolean this_ck_has_E_flag = false;</span>
<span class="nc" id="L94">				double min = Double.MAX_VALUE;</span>
<span class="nc bnc" id="L95" title="All 2 branches missed.">				for (int i = 0; i &lt; tests.size(); i++) {</span>
<span class="nc bnc" id="L96" title="All 4 branches missed.">					if ((ck.p &gt; (tests.get(i).p - radius) &amp;&amp; (ck.p &lt; (tests.get(i).p + radius)))) {</span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">						if (min &gt; Math.abs(ck.p - tests.get(i).p)) {</span>
<span class="nc" id="L98">							min = Math.abs(ck.p - tests.get(i).p);</span>
<span class="nc" id="L99">							TS2C[k] = min;</span>
<span class="nc" id="L100">							Cvalue[k] = ck.p;</span>
						}
<span class="nc" id="L102">						this_ck_has_E_flag = true;</span>
					}
				}
<span class="nc bnc" id="L105" title="All 2 branches missed.">				if (!this_ck_has_E_flag) {</span>
<span class="nc" id="L106">					all_s_has_E_flag = false;</span>
<span class="nc" id="L107">					p = new TestCase();</span>
<span class="nc" id="L108">					p.p = ck.p;</span>
<span class="nc bnc" id="L109" title="All 2 branches missed.">					if (!isCorrect(p.p)) {</span>
<span class="nc" id="L110">						System.out.println(count + &quot;,&quot; + rrtcount + &quot;,&quot; + fscscount);</span>
<span class="nc" id="L111">						return count;</span>
					} else {
<span class="nc" id="L113">						count++;</span>
<span class="nc" id="L114">						ttemp++;</span>
						// System.out.println(&quot;t=&quot;+t);
<span class="nc" id="L116">						rrtcount++;</span>
<span class="nc" id="L117">						tests.add(p);</span>
					}
				}
			}
<span class="nc bnc" id="L121" title="All 2 branches missed.">			if (all_s_has_E_flag) {</span>
<span class="nc" id="L122">				double max = 0;</span>
<span class="nc" id="L123">				int index = 0;</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">				for (int i = 0; i &lt; TS2C.length; i++) {</span>
<span class="nc bnc" id="L125" title="All 2 branches missed.">					if (max &lt; TS2C[i]) {</span>
<span class="nc" id="L126">						max = TS2C[i];</span>
<span class="nc" id="L127">						index = i;</span>
					}
				}
<span class="nc" id="L130">				p = new TestCase();</span>
<span class="nc" id="L131">				p.p = Cvalue[index];</span>
				// System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.p + &quot; fscs&quot;);
<span class="nc bnc" id="L133" title="All 2 branches missed.">				if (!isCorrect(p.p)) {</span>
<span class="nc" id="L134">					System.out.println(count + &quot;,&quot; + rrtcount + &quot;,&quot; + fscscount);</span>
<span class="nc" id="L135">					return count;</span>
				} else {
<span class="nc" id="L137">					count++;</span>
<span class="nc" id="L138">					fscscount++;</span>
<span class="nc" id="L139">					tests.add(p);</span>
				}
			}
<span class="nc" id="L142">			_10CandidateCount++;</span>
		}
<span class="nc" id="L144">		System.out.println(count + &quot;,&quot; + rrtcount + &quot;,&quot; + fscscount);</span>
<span class="nc" id="L145">		return count;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>