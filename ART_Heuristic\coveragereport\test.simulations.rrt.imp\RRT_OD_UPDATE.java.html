<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRT_OD_UPDATE.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rrt.imp</a> &gt; <span class="el_source">RRT_OD_UPDATE.java</span></div><h1>RRT_OD_UPDATE.java</h1><pre class="source lang-java linenums">package test.simulations.rrt.imp;

import java.util.ArrayList;
import java.util.Random;

class BeginEnd {
	public double start;
	public double end;
	public BeginEnd next;

<span class="nc" id="L11">	public BeginEnd(double start, double end) {</span>
<span class="nc" id="L12">		this.start = start;</span>
<span class="nc" id="L13">		this.end = end;</span>
<span class="nc" id="L14">	}</span>
}

class Node {
	public double value;
	public Node next;

<span class="nc" id="L21">	public Node(double value) {</span>
<span class="nc" id="L22">		this.value = value;</span>
<span class="nc" id="L23">	}</span>
}

/**
 * 此算法用来改善RRT的时间问题
 */
public class RRT_OD_UPDATE {
	public static void main(String[] args) {

		// for (int i = 0; i &lt; 3; i++) {
		// double a=Math.random();
		// System.out.println(a);
		// rrt_OD_UPDATE.addNewNode(a);
		// BeginEnd beroot=null;
		// System.out.println(rrt_OD_UPDATE.getDatumAndBE(beroot));
		// }
<span class="nc" id="L39">		int times = 8000;</span>
<span class="nc" id="L40">		long sums = 0;</span>
		//////////////
		///////////////
<span class="nc" id="L43">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L45">			RRT_OD_UPDATE rrt_OD_UPDATE = new RRT_OD_UPDATE(0, 1, 0.75, 0.0005, i * 3);</span>
<span class="nc" id="L46">			int fm = rrt_OD_UPDATE.run();</span>
<span class="nc" id="L47">			sums += fm;</span>
		}
<span class="nc" id="L49">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L50">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L51">		System.out.println(&quot;Time: &quot; + (endTime - startTime));</span>
<span class="nc" id="L52">	}</span>
	double min;
	double max;
	double fail_start;
	double fail_rate;
	double R;
	int randomseed;
	public Node root;

<span class="nc" id="L61">	public int size = 0;</span>

	// ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();
	public RRT_OD_UPDATE(double min, double max, double r, double fail_rate, int randomseed) {
<span class="nc" id="L65">		super();</span>
<span class="nc" id="L66">		this.min = min;</span>
<span class="nc" id="L67">		this.max = max;</span>
<span class="nc" id="L68">		this.R = r;</span>
<span class="nc" id="L69">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L70">		this.randomseed = randomseed;</span>
<span class="nc" id="L71">	}</span>

	public void addNewNode(double value) {
<span class="nc bnc" id="L74" title="All 2 branches missed.">		if (root == null) {</span>
<span class="nc" id="L75">			root = new Node(value);</span>
<span class="nc" id="L76">			size++;</span>
<span class="nc" id="L77">		} else {</span>
<span class="nc" id="L78">			Node current = root;</span>
<span class="nc" id="L79">			Node pre = null;</span>
<span class="nc bnc" id="L80" title="All 4 branches missed.">			while (current != null &amp;&amp; current.value &lt; value) {</span>
<span class="nc" id="L81">				pre = current;</span>
<span class="nc" id="L82">				current = current.next;</span>
			}
<span class="nc" id="L84">			Node newNode = new Node(value);</span>
<span class="nc" id="L85">			newNode.next = current;</span>
<span class="nc bnc" id="L86" title="All 2 branches missed.">			if (pre == null)</span>
<span class="nc" id="L87">				root = newNode;</span>
			else
<span class="nc" id="L89">				pre.next = newNode;</span>
<span class="nc" id="L90">			size++;</span>
		}
<span class="nc" id="L92">	}</span>

	public ArrayList getDatumAndBE() {
<span class="nc" id="L95">		double sum = 0;</span>
<span class="nc" id="L96">		BeginEnd beroot = null;</span>
<span class="nc" id="L97">		double radius = R / (2 * size);</span>
<span class="nc" id="L98">		Node current = root;</span>
<span class="nc" id="L99">		BeginEnd becurrent = null;</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">		if (root.value - radius - min &gt; 0) {</span>
<span class="nc" id="L101">			sum += root.value - radius - min;</span>
<span class="nc bnc" id="L102" title="All 2 branches missed.">			if (beroot == null) {</span>
<span class="nc" id="L103">				beroot = new BeginEnd(min, root.value - radius);</span>
<span class="nc" id="L104">				becurrent = beroot;</span>
			}
		}
<span class="nc bnc" id="L107" title="All 2 branches missed.">		while (current.next != null) {</span>
<span class="nc bnc" id="L108" title="All 2 branches missed.">			if (current.next.value - current.value &gt; (2 * radius)) {</span>
<span class="nc" id="L109">				sum += current.next.value - current.value - (2 * radius);</span>
<span class="nc bnc" id="L110" title="All 2 branches missed.">				if (beroot == null) {</span>
<span class="nc" id="L111">					beroot = new BeginEnd(current.value + radius, current.next.value - radius);</span>
<span class="nc" id="L112">					becurrent = beroot;</span>
<span class="nc" id="L113">				} else {</span>
<span class="nc" id="L114">					becurrent.next = new BeginEnd(current.value + radius, current.next.value - radius);</span>
<span class="nc" id="L115">					becurrent = becurrent.next;</span>
				}
			}
<span class="nc" id="L118">			current = current.next;</span>
		}
<span class="nc bnc" id="L120" title="All 2 branches missed.">		if (current.value + radius &lt; max) {</span>
<span class="nc" id="L121">			sum += max - (current.value + radius);</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">			if (beroot == null) {</span>
<span class="nc" id="L123">				beroot = new BeginEnd(current.value + radius, max);</span>
<span class="nc" id="L124">				becurrent = beroot;</span>
<span class="nc" id="L125">			} else {</span>
<span class="nc" id="L126">				becurrent.next = new BeginEnd(current.value + radius, max);</span>
<span class="nc" id="L127">				becurrent = becurrent.next;</span>
			}
		}
<span class="nc" id="L130">		ArrayList result = new ArrayList();</span>
<span class="nc" id="L131">		result.add(1.0 / sum);</span>
<span class="nc" id="L132">		result.add(beroot);</span>
<span class="nc" id="L133">		return result;</span>
	}

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L137" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L138">			return false;</span>
		} else {
<span class="nc" id="L140">			return true;</span>
		}
	}

	public void print() {
<span class="nc" id="L145">		Node current = root;</span>
<span class="nc bnc" id="L146" title="All 2 branches missed.">		while (current != null) {</span>
<span class="nc" id="L147">			System.out.println(current.value);</span>
<span class="nc" id="L148">			current = current.next;</span>
		}
<span class="nc" id="L150">	}</span>

	public int run() {
<span class="nc" id="L153">		int count = 0;</span>
<span class="nc" id="L154">		Random random = new Random(randomseed);</span>
<span class="nc" id="L155">		fail_start = random.nextDouble() * (max - min - fail_rate) + min;</span>
<span class="nc" id="L156">		double value = random.nextDouble() * (max - min) + min;</span>

<span class="nc bnc" id="L158" title="All 2 branches missed.">		while (isCorrect(value)) {</span>
<span class="nc" id="L159">			count++;</span>
<span class="nc" id="L160">			addNewNode(value);</span>
			// 先求基准线o(n)并求得开始和结束
<span class="nc" id="L162">			ArrayList datumandbe = getDatumAndBE();</span>
<span class="nc" id="L163">			double datum = (double) datumandbe.get(0);</span>
<span class="nc" id="L164">			BeginEnd beroot = (BeginEnd) datumandbe.get(1);</span>
			// T(0-1)
<span class="nc" id="L166">			double T = random.nextDouble();</span>
			//
<span class="nc" id="L168">			BeginEnd becurrent = beroot;</span>
<span class="nc" id="L169">			BeginEnd bepre = null;</span>
<span class="nc" id="L170">			double PreInt = 0;</span>
<span class="nc" id="L171">			double SumInt = 0;</span>
<span class="nc bnc" id="L172" title="All 4 branches missed.">			while (becurrent != null &amp;&amp; (SumInt &lt; T)) {</span>
<span class="nc" id="L173">				PreInt = SumInt;</span>
<span class="nc" id="L174">				SumInt += (becurrent.end - becurrent.start) * datum;</span>
<span class="nc" id="L175">				bepre = becurrent;</span>
<span class="nc" id="L176">				becurrent = becurrent.next;</span>
			}
			// 产生下一个测试用例
			// PreInt+int(bepre.start-&gt;x)=T;
<span class="nc" id="L180">			value = ((T - PreInt) / datum) + bepre.start;</span>
		}
<span class="nc" id="L182">		return count;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>