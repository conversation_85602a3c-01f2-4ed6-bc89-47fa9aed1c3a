<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>TestCase.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.TD</a> &gt; <span class="el_source">TestCase.java</span></div><h1>TestCase.java</h1><pre class="source lang-java linenums">package datastructure.TD;

/*
 * 一/二维的测试用例类，包含测试的值
 * */
@Deprecated
public class TestCase {
	public double p;// x
	public double q;// y
	public double m;// z
	public double n;//
	public double z;//
	public double coverage;

	public TestCase() {
<span class="nc" id="L16">		super();</span>
<span class="nc" id="L17">	}</span>

	public TestCase(double p) {
<span class="nc" id="L20">		super();</span>
<span class="nc" id="L21">		this.p = p;</span>
<span class="nc" id="L22">	}</span>

	public TestCase(double p, double q) {
<span class="nc" id="L25">		super();</span>
<span class="nc" id="L26">		this.p = p;</span>
<span class="nc" id="L27">		this.q = q;</span>
<span class="nc" id="L28">	}</span>

	@Override
	public String toString() {
<span class="nc" id="L32">		return &quot;TestCase [p=&quot; + p + &quot;, q=&quot; + q + &quot;, m=&quot; + m + &quot;, n=&quot; + n + &quot;, z=&quot; + z + &quot;, coverage=&quot; + coverage + &quot;]&quot;;</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>