<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRTtpND_H.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rrttp.hilbert</a> &gt; <span class="el_source">RRTtpND_H.java</span></div><h1>RRTtpND_H.java</h1><pre class="source lang-java linenums">package test.simulations.rrttp.hilbert;

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import test.simulations.rrt.RRT_ND;
import util.HilbertCurve2;
import util.data.ZeroOneCreator;

/**
 * RRT_TP的n维实现（利用希尔伯特曲线）
 * -Djava.library.path=&quot;${workspace_loc}/ART/Resource;${env_var:PATH}&quot;
 */
public class RRTtpND_H extends ART {
	public static void main(String[] args) throws Exception {
<span class="nc" id="L20">		 testTCTime(2,10000);</span>
		//testFm();
		//testEm(2, 0.01);
<span class="nc" id="L23">	}</span>


	double R;
	HilbertCurve2 hiblert;

<span class="nc" id="L29">	ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>

	public RRTtpND_H(double[] min, double[] max, double r, FailurePattern pattern, Random random) {
<span class="nc" id="L32">		super(min, max, random, pattern);</span>
<span class="nc" id="L33">		this.min = min;</span>
<span class="nc" id="L34">		this.max = max;</span>
<span class="nc" id="L35">		R = r;</span>
<span class="nc" id="L36">		hiblert = new HilbertCurve2();</span>
<span class="nc" id="L37">	}</span>

	public NPoint mapOne2N(NPoint p) {
<span class="nc" id="L40">		double[] nDPoints = hiblert.oneD_2_nD3(p.getXn()[0] + &quot;&quot;, this.dimension);</span>
		// 根据min，max来调整
<span class="nc bnc" id="L42" title="All 2 branches missed.">		for (int i = 0; i &lt; nDPoints.length; i++) {</span>
<span class="nc" id="L43">			double length = max[i] - min[i];</span>
<span class="nc" id="L44">			double value = nDPoints[i] * length + min[i];</span>
<span class="nc" id="L45">			nDPoints[i] = value;</span>
		}
		//
<span class="nc" id="L48">		return new NPoint(nDPoints);</span>
	}
	public void sortTestCases(NPoint p) {
<span class="nc" id="L51">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc" id="L52">		double value = p.getXn()[0];</span>
<span class="nc bnc" id="L53" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L54">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L55" title="All 2 branches missed.">			if (value &gt; tests.get(mid).getXn()[0]) {</span>
<span class="nc" id="L56">				low = mid + 1;</span>
<span class="nc" id="L57">			} else {</span>
<span class="nc" id="L58">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L61" title="All 2 branches missed.">		if (value &lt; tests.get(mid).getXn()[0]) {</span>
<span class="nc" id="L62">			mid = mid - 1;</span>
		}
<span class="nc" id="L64">		tests.add(mid + 1, p);</span>
<span class="nc" id="L65">	}</span>

	@Override
	public NPoint generateNextTC() {
<span class="nc" id="L69">		NPoint pp=null;</span>
		
<span class="nc bnc" id="L71" title="All 2 branches missed.">		if(tests.size()==0){</span>
<span class="nc" id="L72">			NPoint p = randomCreator.randomPoint(0, 1);// 一维的测试用例</span>
<span class="nc" id="L73">			pp = mapOne2N(p);</span>
<span class="nc" id="L74">			tests.add(p);</span>
<span class="nc" id="L75">		}else{</span>
<span class="nc" id="L76">			long size = tests.size();</span>
<span class="nc" id="L77">			double radius = R / (2 * size);</span>
<span class="nc" id="L78">			double max = -1;</span>
<span class="nc" id="L79">			double start = 0.0;</span>
<span class="nc" id="L80">			double end = 0.0;</span>
			
<span class="nc" id="L82">			double length = 0;</span>
<span class="nc" id="L83">			double tempstart = 0.0;</span>
<span class="nc" id="L84">			double tempend = 0.0;</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">			for (int i = 0; i &lt;= size; i++) {</span>

<span class="nc" id="L87">				boolean flag = true;</span>

<span class="nc bnc" id="L89" title="All 2 branches missed.">				if (i == 0) {</span>
<span class="nc" id="L90">					double temp = tests.get(0).getXn()[0];</span>
<span class="nc bnc" id="L91" title="All 2 branches missed.">					if (temp - radius &gt; this.min[0]) {</span>
<span class="nc" id="L92">						length = temp - radius - min[0];</span>
<span class="nc" id="L93">						tempstart = min[0];</span>
<span class="nc" id="L94">						tempend = temp - radius;</span>
<span class="nc" id="L95">					} else {</span>
<span class="nc" id="L96">						flag = false;</span>
					}
<span class="nc bnc" id="L98" title="All 2 branches missed.">				} else if (i == size) {</span>
<span class="nc" id="L99">					double temp = tests.get(i - 1).getXn()[0];</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">					if (temp + radius &lt;= this.max[0]) {</span>
<span class="nc" id="L101">						length = this.max[0] - (temp + radius);</span>
<span class="nc" id="L102">						tempstart = temp + radius;</span>
<span class="nc" id="L103">						tempend = this.max[0];</span>
<span class="nc" id="L104">					} else {</span>
<span class="nc" id="L105">						flag = false;</span>
					}
<span class="nc" id="L107">				} else {</span>
<span class="nc" id="L108">					double temp1 = tests.get(i).getXn()[0];</span>
<span class="nc" id="L109">					double temp2 = tests.get(i - 1).getXn()[0];</span>
<span class="nc bnc" id="L110" title="All 2 branches missed.">					if (temp1 - temp2 &gt; 2 * radius) {</span>
<span class="nc" id="L111">						length = temp1 - radius - (temp2 + radius);</span>
<span class="nc" id="L112">						tempstart = temp2 + radius;</span>
<span class="nc" id="L113">						tempend = temp1 - radius;</span>
<span class="nc" id="L114">					} else {</span>
<span class="nc" id="L115">						flag = false;</span>
					}
				}
<span class="nc bnc" id="L118" title="All 2 branches missed.">				if (flag) {</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">					if (max &lt; length) {</span>
<span class="nc" id="L120">						max = length;</span>
<span class="nc" id="L121">						start = tempstart;</span>
<span class="nc" id="L122">						end = tempend;</span>
					}
				} else {
					continue;
				}
			}
			// System.out.println(&quot;start:&quot; + start);
			// System.out.println(&quot;end:&quot; + end);
			// 选取下一个测试用例
<span class="nc" id="L131">			NPoint p = new NPoint();</span>
<span class="nc" id="L132">			p = randomCreator.randomPoint(start, end);</span>
			
<span class="nc" id="L134">			sortTestCases(p);</span>
			
<span class="nc" id="L136">			pp = mapOne2N(p);</span>
			
		}
<span class="nc" id="L139">		return pp;</span>
	}
	

	public void time() {
<span class="nc" id="L144">		int count = 0;</span>
<span class="nc" id="L145">		NPoint p = randomCreator.randomPoint(0, 1);// 一维的测试用例</span>
		// map function
<span class="nc" id="L147">		NPoint pp = mapOne2N(p);</span>
<span class="nc bnc" id="L148" title="All 2 branches missed.">		while (count &lt; tcCount) {</span>
<span class="nc" id="L149">			count++;</span>
<span class="nc bnc" id="L150" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L151">				tests.add(p);</span>
<span class="nc" id="L152">			} else {</span>
<span class="nc" id="L153">				sortTestCases(p);</span>
			}
<span class="nc" id="L155">			double radius = R / (2 * tests.size());</span>
<span class="nc" id="L156">			double max = -1;</span>
<span class="nc" id="L157">			double start = 0.0;</span>
<span class="nc" id="L158">			double end = 0.0;</span>
<span class="nc" id="L159">			long size = tests.size();</span>
<span class="nc" id="L160">			double length = 0;</span>
<span class="nc" id="L161">			double tempstart = 0.0;</span>
<span class="nc" id="L162">			double tempend = 0.0;</span>
<span class="nc bnc" id="L163" title="All 2 branches missed.">			for (int i = 0; i &lt;= size; i++) {</span>

<span class="nc" id="L165">				boolean flag = true;</span>

<span class="nc bnc" id="L167" title="All 2 branches missed.">				if (i == 0) {</span>
<span class="nc" id="L168">					double temp = tests.get(0).getXn()[0];</span>
<span class="nc bnc" id="L169" title="All 2 branches missed.">					if (temp - radius &gt; this.min[0]) {</span>
<span class="nc" id="L170">						length = temp - radius - min[0];</span>
<span class="nc" id="L171">						tempstart = min[0];</span>
<span class="nc" id="L172">						tempend = temp - radius;</span>
<span class="nc" id="L173">					} else {</span>
<span class="nc" id="L174">						flag = false;</span>
					}
<span class="nc bnc" id="L176" title="All 2 branches missed.">				} else if (i == size) {</span>
<span class="nc" id="L177">					double temp = tests.get(i - 1).getXn()[0];</span>
<span class="nc bnc" id="L178" title="All 2 branches missed.">					if (temp + radius &lt;= this.max[0]) {</span>
<span class="nc" id="L179">						length = this.max[0] - (temp + radius);</span>
<span class="nc" id="L180">						tempstart = temp + radius;</span>
<span class="nc" id="L181">						tempend = this.max[0];</span>
<span class="nc" id="L182">					} else {</span>
<span class="nc" id="L183">						flag = false;</span>
					}
<span class="nc" id="L185">				} else {</span>
<span class="nc" id="L186">					double temp1 = tests.get(i).getXn()[0];</span>
<span class="nc" id="L187">					double temp2 = tests.get(i - 1).getXn()[0];</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">					if (temp1 - temp2 &gt; 2 * radius) {</span>
<span class="nc" id="L189">						length = temp1 - radius - (temp2 + radius);</span>
<span class="nc" id="L190">						tempstart = temp2 + radius;</span>
<span class="nc" id="L191">						tempend = temp1 - radius;</span>
<span class="nc" id="L192">					} else {</span>
<span class="nc" id="L193">						flag = false;</span>
					}
				}
<span class="nc bnc" id="L196" title="All 2 branches missed.">				if (flag) {</span>
<span class="nc bnc" id="L197" title="All 2 branches missed.">					if (max &lt; length) {</span>
<span class="nc" id="L198">						max = length;</span>
<span class="nc" id="L199">						start = tempstart;</span>
<span class="nc" id="L200">						end = tempend;</span>
					}
				} else {
					continue;
				}
			}
			// System.out.println(&quot;start:&quot; + start);
			// System.out.println(&quot;end:&quot; + end);
			// 选取下一个测试用例
<span class="nc" id="L209">			p = new NPoint();</span>
<span class="nc" id="L210">			p = randomCreator.randomPoint(start, end);</span>

<span class="nc" id="L212">			pp = mapOne2N(p);</span>
		}
<span class="nc" id="L214">	}</span>

	public static void testFm() {
<span class="nc" id="L217">		double[] theta = { 0.005 };// , 0.002, 0.0015, 0.001, 0.0005, 0.0001</span>
<span class="nc" id="L218">		int[] d = { 2 };</span>
<span class="nc" id="L219">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc bnc" id="L220" title="All 2 branches missed.">		for (int v = 0; v &lt; d.length; v++) {</span>

<span class="nc" id="L222">			double min[] = dataCreator.minCreator(d[v]);</span>
<span class="nc" id="L223">			double max[] = dataCreator.maxCreator(d[v]);</span>
<span class="nc bnc" id="L224" title="All 2 branches missed.">			for (int k = 0; k &lt; theta.length; k++) {</span>

<span class="nc" id="L226">				int times = 2000;</span>
<span class="nc" id="L227">				long sums = 0;</span>
<span class="nc" id="L228">				int temp = 0;</span>
<span class="nc" id="L229">				FailurePattern pattern = new BlockPattern();</span>
<span class="nc" id="L230">				pattern.fail_rate = theta[k];</span>

<span class="nc" id="L232">				long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L233" title="All 2 branches missed.">				for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L234">					RRTtpND_H rrt = new RRTtpND_H(min, max, 0.75, pattern, new Random(i * 3 + v * 5));</span>
<span class="nc" id="L235">					temp = rrt.run();</span>
<span class="nc" id="L236">					sums += temp;</span>
				}
<span class="nc" id="L238">				long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L239">				double fm = (sums / (double) times);</span>
<span class="nc" id="L240">				System.out.println(&quot;theta:&quot; + theta[k] + &quot; d:&quot; + d[v] + &quot; Fm:&quot; + fm + &quot; time:&quot;</span>
<span class="nc" id="L241">						+ ((endTime - startTime) / (double) times));</span>

			}
		}
<span class="nc" id="L245">	}</span>

	public static double testTCTime(int d, int tcCount) {
<span class="nc" id="L248">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L249">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L250">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L252">		int times = 1;</span>

<span class="nc" id="L254">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L255">		failurePattern.fail_rate = 0.001;</span>
<span class="nc" id="L256">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L257" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L258">			RRTtpND_H nd = new RRTtpND_H(min, max, 0.75, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L259">			nd.tcCount = tcCount;</span>
<span class="nc" id="L260">			nd.time2();</span>
		}
<span class="nc" id="L262">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L263">		System.out.println((endTime - startTime) / (double) times);</span>
<span class="nc" id="L264">		return ((endTime - startTime) / (double) times);</span>
	}

	public static double[] testEm(int dimension, double failrate) {
<span class="nc" id="L268">		int d = dimension;</span>
		// double R = j == 1 ? 1 : 2;
<span class="nc" id="L270">		int emTime = 6;</span>
<span class="nc" id="L271">		double[] result = new double[emTime];</span>

<span class="nc" id="L273">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L274">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L275">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L277">		int times = 2000;</span>

<span class="nc" id="L279">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L280">		failurePattern.fail_rate = failrate;</span>
<span class="nc" id="L281">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L282" title="All 2 branches missed.">		for (int k = 0; k &lt; emTime; k++) {</span>
<span class="nc" id="L283">			long sums = 0;</span>
<span class="nc" id="L284">			int temp = 0;</span>
<span class="nc bnc" id="L285" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L286">				RRTtpND_H nd = new RRTtpND_H(min, max, 0.75, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L287">				nd.emCount = (k + 1) * 500;</span>
<span class="nc" id="L288">				temp = nd.em();</span>
<span class="nc" id="L289">				sums += temp;</span>
			}
<span class="nc" id="L291">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L292">			double em = sums / (double) times;</span>
<span class="nc" id="L293">			result[k] = em;</span>
<span class="nc" id="L294">			System.out.println(&quot;em:&quot; + em + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
		}
<span class="nc" id="L296">		return result;</span>
	}
	
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>