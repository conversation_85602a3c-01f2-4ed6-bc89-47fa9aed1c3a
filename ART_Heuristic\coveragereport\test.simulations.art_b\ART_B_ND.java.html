<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_B_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_b</a> &gt; <span class="el_source">ART_B_ND.java</span></div><h1>ART_B_ND.java</h1><pre class="source lang-java linenums">package test.simulations.art_b;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import test.simulations.fscs.FSCS_ND;
import util.data.ZeroOneCreator;

public class ART_B_ND extends ART {
	public static void main(String[] args) {

		//testEm(1, 0.01);
<span class="nc" id="L19">		testTCTime(2,10000);</span>
		
		//testFm();
<span class="nc" id="L22">	}</span>

<span class="nc" id="L24">	ArrayList&lt;NRectRegion&gt; untestedRegions = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L25">	ArrayList&lt;NRectRegion&gt; testedRegions = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L27">	ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>

	public ART_B_ND(double[] min, double[] max, Random random, FailurePattern failurePattern) {
<span class="nc" id="L30">		super(min, max, random, failurePattern);</span>
<span class="nc" id="L31">		untestedRegions.add(new NRectRegion(new NPoint(min), new NPoint(max)));</span>
<span class="nc" id="L32">	}</span>

	public NRectRegion findRandomRegionAndDelete(ArrayList&lt;NRectRegion&gt; regions) {
<span class="nc" id="L35">		int T = random.nextInt(regions.size());</span>
<span class="nc" id="L36">		return regions.remove(T);</span>
	}

	public boolean hasPointInRegion(NRectRegion region) {
<span class="nc" id="L40">		boolean result = false;</span>
<span class="nc" id="L41">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L42">		double[] end = region.getEnd().getXn();</span>

<span class="nc bnc" id="L44" title="All 2 branches missed.">		for (int i = 0; i &lt; tests.size(); i++) {</span>
<span class="nc" id="L45">			double[] p = tests.get(i).getXn();</span>
<span class="nc" id="L46">			boolean isPIn = true;</span>
<span class="nc bnc" id="L47" title="All 2 branches missed.">			for (int j = 0; j &lt; p.length; j++) {</span>
<span class="nc bnc" id="L48" title="All 4 branches missed.">				if (p[j] &lt; start[j] || p[j] &gt; end[j]) {</span>
<span class="nc" id="L49">					isPIn = false;</span>
				}
			}
<span class="nc bnc" id="L52" title="All 2 branches missed.">			if (isPIn) {</span>
<span class="nc" id="L53">				result = true;</span>
<span class="nc" id="L54">				break;</span>
			}
		}
<span class="nc" id="L57">		return result;</span>
	}

	@Override
	public NPoint generateNextTC() {
<span class="nc" id="L62">		NPoint p = null;</span>
<span class="nc bnc" id="L63" title="All 2 branches missed.">		if (untestedRegions.size() != 0) {</span>
			// 直接生成一个点
<span class="nc" id="L65">			NRectRegion randomRegion = findRandomRegionAndDelete(untestedRegions);</span>
<span class="nc" id="L66">			p = randomCreator.randomPoint(randomRegion);</span>
<span class="nc" id="L67">			tests.add(p);</span>
<span class="nc" id="L68">			testedRegions.add(randomRegion);</span>
<span class="nc" id="L69">		} else {</span>
<span class="nc" id="L70">			ArrayList&lt;NRectRegion&gt; temp = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">			for (int i = 0; i &lt; testedRegions.size(); i++) {</span>
				// 找最长边
<span class="nc" id="L73">				NRectRegion tempRegion = testedRegions.get(i);</span>
<span class="nc" id="L74">				double maxBian = 0.0;</span>
<span class="nc" id="L75">				int maxIndex = 0;</span>
<span class="nc bnc" id="L76" title="All 2 branches missed.">				for (int j = 0; j &lt; tempRegion.getStart().getXn().length; j++) {</span>
<span class="nc bnc" id="L77" title="All 2 branches missed.">					if (tempRegion.getEnd().getXn()[j] - tempRegion.getStart().getXn()[j] &gt; maxBian) {</span>
<span class="nc" id="L78">						maxBian = tempRegion.getEnd().getXn()[j] - tempRegion.getStart().getXn()[j];</span>
<span class="nc" id="L79">						maxIndex = j;</span>
					}
				}
<span class="nc" id="L82">				NRectRegion region1 = new NRectRegion();</span>
<span class="nc" id="L83">				NRectRegion region2 = new NRectRegion();</span>
<span class="nc" id="L84">				region1.setStart(tempRegion.getStart());</span>
<span class="nc" id="L85">				double[] end = Arrays.copyOf(tempRegion.getEnd().getXn(), tempRegion.getEnd().getXn().length);</span>
<span class="nc" id="L86">				double midValue1 = 0.5</span>
<span class="nc" id="L87">						* (tempRegion.getEnd().getXn()[maxIndex] + tempRegion.getStart().getXn()[maxIndex]);</span>
<span class="nc" id="L88">				end[maxIndex] = midValue1;</span>
<span class="nc" id="L89">				region1.setEnd(new NPoint(end));</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">				if (hasPointInRegion(region1)) {</span>
<span class="nc" id="L91">					temp.add(region1);</span>
<span class="nc" id="L92">				} else {</span>
<span class="nc" id="L93">					untestedRegions.add(region1);</span>
				}

<span class="nc" id="L96">				double[] start = Arrays.copyOf(tempRegion.getStart().getXn(), tempRegion.getStart().getXn().length);</span>
<span class="nc" id="L97">				start[maxIndex] = midValue1;</span>
<span class="nc" id="L98">				region2.setStart(new NPoint(start));</span>
<span class="nc" id="L99">				region2.setEnd(tempRegion.getEnd());</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">				if (hasPointInRegion(region2)) {</span>
<span class="nc" id="L101">					temp.add(region2);</span>
<span class="nc" id="L102">				} else {</span>
<span class="nc" id="L103">					untestedRegions.add(region2);</span>
				}
			}
<span class="nc" id="L106">			testedRegions = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L107">			testedRegions = temp;</span>

			// 分完区域重新生成一个点
<span class="nc" id="L110">			NRectRegion randomRegion = findRandomRegionAndDelete(untestedRegions);</span>
<span class="nc" id="L111">			p = randomCreator.randomPoint(randomRegion);</span>
<span class="nc" id="L112">			tests.add(p);</span>
<span class="nc" id="L113">			testedRegions.add(randomRegion);</span>
		}
<span class="nc" id="L115">		return p;</span>
	}

	
	public void time() {
<span class="nc" id="L120">		int count = 0;</span>
<span class="nc bnc" id="L121" title="All 2 branches missed.">		while (count &lt; tcCount) {</span>
<span class="nc" id="L122">			NPoint p = null;</span>
<span class="nc bnc" id="L123" title="All 4 branches missed.">			while (untestedRegions.size() != 0 &amp;&amp; count &lt; tcCount) {</span>
<span class="nc" id="L124">				NRectRegion randomRegion = findRandomRegionAndDelete(untestedRegions);</span>
				// System.out.println(&quot;randomRegion:&quot;+randomRegion);
<span class="nc" id="L126">				p = randomCreator.randomPoint(randomRegion);</span>
				// System.out.println(&quot;point:&quot;+p);
<span class="nc" id="L128">				count++;</span>
<span class="nc" id="L129">				tests.add(p);</span>

<span class="nc" id="L131">				testedRegions.add(randomRegion);</span>

			}
<span class="nc" id="L134">			ArrayList&lt;NRectRegion&gt; temp = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L135" title="All 2 branches missed.">			for (int i = 0; i &lt; testedRegions.size(); i++) {</span>
				// 找最长边
<span class="nc" id="L137">				NRectRegion tempRegion = testedRegions.get(i);</span>
<span class="nc" id="L138">				double maxBian = 0.0;</span>
<span class="nc" id="L139">				int maxIndex = 0;</span>
<span class="nc bnc" id="L140" title="All 2 branches missed.">				for (int j = 0; j &lt; tempRegion.getStart().getXn().length; j++) {</span>
<span class="nc bnc" id="L141" title="All 2 branches missed.">					if (tempRegion.getEnd().getXn()[j] - tempRegion.getStart().getXn()[j] &gt; maxBian) {</span>
<span class="nc" id="L142">						maxBian = tempRegion.getEnd().getXn()[j] - tempRegion.getStart().getXn()[j];</span>
<span class="nc" id="L143">						maxIndex = j;</span>
					}
				}
				//
<span class="nc" id="L147">				NRectRegion region1 = new NRectRegion();</span>
<span class="nc" id="L148">				NRectRegion region2 = new NRectRegion();</span>

<span class="nc" id="L150">				region1.setStart(tempRegion.getStart());</span>
<span class="nc" id="L151">				double[] end = Arrays.copyOf(tempRegion.getEnd().getXn(), tempRegion.getEnd().getXn().length);</span>
<span class="nc" id="L152">				double midValue1 = 0.5</span>
<span class="nc" id="L153">						* (tempRegion.getEnd().getXn()[maxIndex] + tempRegion.getStart().getXn()[maxIndex]);</span>
<span class="nc" id="L154">				end[maxIndex] = midValue1;</span>
<span class="nc" id="L155">				region1.setEnd(new NPoint(end));</span>
<span class="nc bnc" id="L156" title="All 2 branches missed.">				if (hasPointInRegion(region1)) {</span>
<span class="nc" id="L157">					temp.add(region1);</span>
<span class="nc" id="L158">				} else {</span>
<span class="nc" id="L159">					untestedRegions.add(region1);</span>
				}

<span class="nc" id="L162">				double[] start = Arrays.copyOf(tempRegion.getStart().getXn(), tempRegion.getStart().getXn().length);</span>
<span class="nc" id="L163">				start[maxIndex] = midValue1;</span>
<span class="nc" id="L164">				region2.setStart(new NPoint(start));</span>
<span class="nc" id="L165">				region2.setEnd(tempRegion.getEnd());</span>
<span class="nc bnc" id="L166" title="All 2 branches missed.">				if (hasPointInRegion(region2)) {</span>
<span class="nc" id="L167">					temp.add(region2);</span>
<span class="nc" id="L168">				} else {</span>
<span class="nc" id="L169">					untestedRegions.add(region2);</span>
				}
			}
<span class="nc" id="L172">			testedRegions = temp;</span>
		}
<span class="nc" id="L174">	}</span>

	
	public static double testFm() {
<span class="nc" id="L178">		int d = 2;</span>
<span class="nc" id="L179">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L180">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L181">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L183">		int times = 2000;</span>

<span class="nc" id="L185">		int temp = 0;</span>
<span class="nc" id="L186">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L187">		failurePattern.fail_rate = 0.005;</span>
<span class="nc" id="L188">		long sums = 0;</span>
<span class="nc" id="L189">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L191">			ART_B_ND rt = new ART_B_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L192">			temp = rt.run();</span>
<span class="nc" id="L193">			sums += temp;</span>
		}
<span class="nc" id="L195">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L196">		double fm = sums / (double) times;</span>
<span class="nc" id="L197">		System.out.println(&quot;fm:&quot; + fm + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
<span class="nc" id="L198">		return fm;</span>
	}

	public static void testEm(int dimension, double failrate) {
<span class="nc" id="L202">		int d = dimension;</span>
<span class="nc" id="L203">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L204">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L205">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L207">		int times = 1000;</span>

<span class="nc" id="L209">		int temp = 0;</span>
<span class="nc" id="L210">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L211">		failurePattern.fail_rate = failrate;</span>
<span class="nc bnc" id="L212" title="All 2 branches missed.">		for (int k = 0; k &lt; 6; k++) {</span>
<span class="nc" id="L213">			long sums = 0;</span>
<span class="nc" id="L214">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L215" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L216">				ART_B_ND rt = new ART_B_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L217">				rt.emCount = (k + 1) * 500;</span>
<span class="nc" id="L218">				temp = rt.em();</span>
<span class="nc" id="L219">				sums += temp;</span>
			}
<span class="nc" id="L221">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L222">			double em = sums / (double) times;</span>
<span class="nc" id="L223">			System.out.println(&quot;em:&quot; + em + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
		}
<span class="nc" id="L225">		System.out.println();</span>
<span class="nc" id="L226">	}</span>

	public static double testTCTime(int d, int tcCount) {
<span class="nc" id="L229">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L230">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L231">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L233">		int times = 1;</span>

<span class="nc" id="L235">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L236">		failurePattern.fail_rate = 0.001;</span>
<span class="nc" id="L237">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L238" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L239">			ART_B_ND rt = new ART_B_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L240">			rt.tcCount = tcCount;</span>
<span class="nc" id="L241">			rt.time2();</span>
		}
<span class="nc" id="L243">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L244">		System.out.println((endTime - startTime) / (double) times);</span>
<span class="nc" id="L245">		return ((endTime - startTime) / (double) times);</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>