<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>Solution</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.art_tp._2D</a> &gt; <span class="el_class">Solution</span></div><h1>Solution</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">813 of 813</td><td class="ctr2">0%</td><td class="bar">42 of 42</td><td class="ctr2">0%</td><td class="ctr1">30</td><td class="ctr2">30</td><td class="ctr1">119</td><td class="ctr2">119</td><td class="ctr1">9</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a2"><span class="el_method">calEachRegion(ArrayList)</span></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="226" alt="226"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h0">30</td><td class="ctr2" id="i0">30</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><span class="el_method">addTDRegions(double[], double[], NPoint)</span></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="89" height="10" title="169" alt="169"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h3">17</td><td class="ctr2" id="i3">17</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a7"><span class="el_method">Solution(double[], double[], double, long)</span></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="58" height="10" title="110" alt="110"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h2">18</td><td class="ctr2" id="i2">18</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><span class="el_method">run()</span></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="48" height="10" title="92" alt="92"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h1">21</td><td class="ctr2" id="i1">21</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a8"><span class="el_method">updateRegions(NPoint)</span></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="29" height="10" title="55" alt="55"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h4">11</td><td class="ctr2" id="i4">11</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a5"><span class="el_method">randomTC()</span></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="24" height="10" title="46" alt="46"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="24" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a4"><span class="el_method">isInRegion(NRectRegion, NPoint)</span></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="21" height="10" title="41" alt="41"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h5">8</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a3"><span class="el_method">isCorrect(NPoint)</span></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="20" height="10" title="38" alt="38"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h7">6</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><span class="el_method">calEachIntEC(double, double, double, double)</span></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="19" height="10" title="36" alt="36"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">1</td><td class="ctr2" id="i8">1</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>