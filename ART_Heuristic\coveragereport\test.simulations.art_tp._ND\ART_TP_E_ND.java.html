<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_E_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._ND</a> &gt; <span class="el_source">ART_TP_E_ND.java</span></div><h1>ART_TP_E_ND.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._ND;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import test.ART;

//TODO 完成ART_TP_E
public class ART_TP_E_ND extends ART{

	double R;
	double Co;
<span class="nc" id="L16">	List&lt;NPoint&gt; tests=new ArrayList&lt;&gt;();</span>
	public ART_TP_E_ND(double[] min, double[] max, Random random, FailurePattern failurePattern,double r) {
<span class="nc" id="L18">		super(min, max, random, failurePattern);</span>
<span class="nc" id="L19">		this.R=r;</span>
		
<span class="nc" id="L21">	}</span>

	@Override
	public int run() {
<span class="nc" id="L25">		int count=0;</span>
<span class="nc" id="L26">		NPoint p=randomCreator.randomPoint();</span>
<span class="nc bnc" id="L27" title="All 2 branches missed.">		while(this.failPattern.isCorrect(p)){</span>
			//
<span class="nc" id="L29">			count++;</span>
<span class="nc" id="L30">			this.tests.add(p);</span>
			
<span class="nc" id="L32">			Co=calCDF();</span>
<span class="nc" id="L33">			Co=1.0/Co;</span>
			//next 
<span class="nc" id="L35">			p=genNextTC();</span>
			
		}
		
<span class="nc" id="L39">		return count;</span>
	}
	private double calCDF(){
<span class="nc" id="L42">		double temp=0.0;</span>
		//
<span class="nc" id="L44">		return temp;</span>
	}
	private NPoint genNextTC(){
<span class="nc" id="L47">		NPoint p=null;</span>
<span class="nc" id="L48">		return p;</span>
	}
	public static void main(String[] args) {
		
<span class="nc" id="L52">	}</span>

	@Override
	public int em() {
		// TODO Auto-generated method stub
<span class="nc" id="L57">		return 0;</span>
	}

	@Override
	public NPoint generateNextTC() {
		// TODO Auto-generated method stub
<span class="nc" id="L63">		return null;</span>
	}
	
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>