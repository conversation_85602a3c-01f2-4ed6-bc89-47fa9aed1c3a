<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRT_ND2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rrt</a> &gt; <span class="el_source">RRT_ND2.java</span></div><h1>RRT_ND2.java</h1><pre class="source lang-java linenums">package test.simulations.rrt;

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import util.data.ZeroOneCreator;

/*
 * n维实现，包含了1,2,3,4维，以及其他维度
 * 根据长度不同来定排除范围的比例
 * */
public class RRT_ND2 extends ART {
	public static void main(String[] args) {

<span class="nc" id="L19">		int d = 2;</span>
<span class="nc" id="L20">		double R=1.0;</span>
<span class="nc" id="L21">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
		// bessj beTested = new bessj();
<span class="nc" id="L23">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L24">		double max[] = dataCreator.maxCreator(d);</span>
		// double[] theta = { 0.005 };

<span class="nc" id="L27">		int times = 2000;</span>
<span class="nc" id="L28">		long sums = 0;</span>
<span class="nc" id="L29">		int temp = 0;</span>

		

<span class="nc" id="L33">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L34" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L35">			FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L36">			failurePattern.fail_rate = 0.01;</span>
<span class="nc" id="L37">			RRT_ND2 nd = new RRT_ND2(min, max, failurePattern, new Random(i * 5), R);</span>
<span class="nc" id="L38">			temp = nd.run();</span>
<span class="nc" id="L39">			sums += temp;</span>
		}
<span class="nc" id="L41">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L42">		double fm = sums / (double) times;</span>
		// System.out.println(fm);
		// System.out.println(
		// timewaste + &quot;,&quot; + timewaste1 + &quot;,&quot; + timewaste2 + &quot; &quot; +
		// (timewaste + timewaste1 + timewaste2));

<span class="nc" id="L48">		System.out.println(&quot; Fm:&quot; + fm + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>

<span class="nc" id="L50">	}</span>
	public double R;

<span class="nc" id="L53">	public ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>

	public RRT_ND2(double[] min, double[] max, FailurePattern pattern, Random random, double r) {
<span class="nc" id="L56">		super(min, max, random, pattern);</span>
<span class="nc" id="L57">		this.R = r;</span>
<span class="nc" id="L58">	}</span>

	public double calculateRadius(int count) {
<span class="nc bnc" id="L61" title="All 2 branches missed.">		if (this.dimension % 2 == 0) {</span>
<span class="nc" id="L62">			int k = this.dimension / 2;</span>
<span class="nc" id="L63">			double kjie = 1;</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">			for (int i = k; i &gt; 0; i--) {</span>
<span class="nc" id="L65">				kjie *= i;</span>
			}
<span class="nc" id="L67">			double temp = (this.R * totalArea * kjie) / (count * Math.pow(Math.PI, k));</span>

<span class="nc" id="L69">			return Math.pow(temp, 1 / (double) this.dimension);</span>
		} else {
<span class="nc" id="L71">			int k = this.dimension / 2;</span>
<span class="nc" id="L72">			double kjie = 1;</span>
<span class="nc" id="L73">			double k2jie = 1;</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">			for (int i = k; i &gt; 0; i--) {</span>
<span class="nc" id="L75">				kjie *= i;</span>
			}
<span class="nc bnc" id="L77" title="All 2 branches missed.">			for (int i = (2 * k + 1); i &gt; 0; i--) {</span>
<span class="nc" id="L78">				k2jie *= i;</span>
			}
<span class="nc" id="L80">			double temp = (this.R * totalArea * k2jie) / (kjie * Math.pow(2, 2 * k + 1) * Math.pow(Math.PI, k) * count);</span>
			// System.out.println(&quot;return R&quot;);
<span class="nc" id="L82">			return Math.pow(temp, 1 / (double) this.dimension);</span>
		}
	}

	public NPoint randomTC() {
<span class="nc" id="L87">		NPoint point = new NPoint();</span>
<span class="nc" id="L88">		point.dimension = this.dimension;</span>
<span class="nc" id="L89">		double[] xn = new double[this.dimension];</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">		for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="nc" id="L91">			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];</span>
		}
<span class="nc" id="L93">		point.setXn(xn);</span>
<span class="nc" id="L94">		return point;</span>
	}

	
	@Override
	public int run() {
<span class="nc" id="L100">		int count = 0;</span>
<span class="nc" id="L101">		NPoint p = randomTC();</span>
		// while (this.failPattern.isCorrect(p)) {
<span class="nc bnc" id="L103" title="All 2 branches missed.">		while (this.failPattern.isCorrect(p)) {</span>
			// StdDraw.filledCircle(p.getXn()[0], p.getXn()[1], 0.01);
<span class="nc" id="L105">			count++;</span>
<span class="nc" id="L106">			tests.add(p);</span>

<span class="nc" id="L108">			double eachExclusionArea=(this.totalArea*R)/(double)tests.size();</span>
			//double radius = calculateRadius(tests.size());
			//System.out.println(radius);
<span class="nc" id="L111">			double minValue=Math.pow((eachExclusionArea/this.totalArea),1.0/(double)this.dimension);</span>
<span class="nc" id="L112">			boolean flag = true;</span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">			while (flag) {</span>
<span class="nc" id="L114">				flag = false;</span>
<span class="nc" id="L115">				p = randomTC();</span>
<span class="nc bnc" id="L116" title="All 2 branches missed.">				for (int i = 0; i &lt; tests.size(); i++) {</span>
					// 排除区域是圆
					// 计算距离
					/*double[] tested = tests.get(i).getXn();
					double distance = 0;
					double[] untested = p.getXn();
					for (int j = 0; j &lt; this.dimension; j++) {
						distance += Math.pow((tests.get(i).getXn()[j] - untested[j]), 2);
					}
					distance = Math.sqrt(distance);
					if (distance &lt; radius) {
						flag = true;
						break;
					}*/
					
					  //排除区域是正方形 if(Math.abs(p.p-tests.get(i).p)&lt;radius){
<span class="nc" id="L132">					boolean isInArea=true;</span>
<span class="nc bnc" id="L133" title="All 2 branches missed.">					for(int j=0;j&lt;p.getXn().length;j++){</span>
<span class="nc" id="L134">						double testxn[]=tests.get(i).getXn();</span>
<span class="nc" id="L135">						double[] pxn=p.getXn();</span>
<span class="nc" id="L136">						 double length=(this.max[j]-this.min[j]);</span>
<span class="nc bnc" id="L137" title="All 4 branches missed.">						if((pxn[j]&gt;testxn[j]+0.5*minValue*length)||(pxn[j]&lt;testxn[j]-0.5*minValue*length)){</span>
<span class="nc" id="L138">							isInArea=false;</span>
						}
					}
<span class="nc bnc" id="L141" title="All 2 branches missed.">					 if(isInArea){</span>
<span class="nc" id="L142">						 flag=true;</span>
					 }
				}

			}

		}
<span class="nc" id="L149">		return count;</span>
	}

	@Override
	public int em() {
		// TODO Auto-generated method stub
<span class="nc" id="L155">		return 0;</span>
	}

	@Override
	public NPoint generateNextTC() {
		// TODO Auto-generated method stub
<span class="nc" id="L161">		return null;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>