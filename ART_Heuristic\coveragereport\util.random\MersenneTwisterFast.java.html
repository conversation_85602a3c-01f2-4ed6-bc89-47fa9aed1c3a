<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MersenneTwisterFast.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util.random</a> &gt; <span class="el_source">MersenneTwisterFast.java</span></div><h1>MersenneTwisterFast.java</h1><pre class="source lang-java linenums">package util.random;

import java.io.*;
import java.util.*;

/**
 * &lt;h3&gt;MersenneTwister and MersenneTwisterFast&lt;/h3&gt;
 * &lt;p&gt;
 * &lt;b&gt;Version 22&lt;/b&gt;, based on version MT199937(99/10/29) of the Mersenne
 * Twister algorithm found at
 * &lt;a href=&quot;http://www.math.keio.ac.jp/matumoto/emt.html&quot;&gt; The Mersenne Twister
 * Home Page&lt;/a&gt;, with the initialization improved using the new 2002/1/26
 * initialization algorithm By Sean Luke, October 2004.
 * 
 * &lt;p&gt;
 * &lt;b&gt;MersenneTwister&lt;/b&gt; is a drop-in subclass replacement for
 * java.util.Random. It is properly synchronized and can be used in a
 * multithreaded environment. On modern VMs such as HotSpot, it is approximately
 * 1/3 slower than java.util.Random.
 *
 * &lt;p&gt;
 * &lt;b&gt;MersenneTwisterFast&lt;/b&gt; is not a subclass of java.util.Random. It has the
 * same public methods as Random does, however, and it is algorithmically
 * identical to MersenneTwister. MersenneTwisterFast has hard-code inlined all
 * of its methods directly, and made all of them final (well, the ones of
 * consequence anyway). Further, these methods are &lt;i&gt;not&lt;/i&gt; synchronized, so
 * the same MersenneTwisterFast instance cannot be shared by multiple threads.
 * But all this helps MersenneTwisterFast achieve well over twice the speed of
 * MersenneTwister. java.util.Random is about 1/3 slower than
 * MersenneTwisterFast.
 *
 * &lt;h3&gt;About the Mersenne Twister&lt;/h3&gt;
 * &lt;p&gt;
 * This is a Java version of the C-program for MT19937: Integer version. The
 * MT19937 algorithm was created by Makoto Matsumoto and Takuji Nishimura, who
 * ask: &quot;When you use this, send an email to: <EMAIL> with an
 * appropriate reference to your work&quot;. Indicate that this is a translation of
 * their algorithm into Java.
 *
 * &lt;p&gt;
 * &lt;b&gt;Reference. &lt;/b&gt; Makato Matsumoto and Takuji Nishimura, &quot;Mersenne Twister:
 * A 623-Dimensionally Equidistributed Uniform Pseudo-Random Number Generator&quot;,
 * &lt;i&gt;ACM Transactions on Modeling and. Computer Simulation,&lt;/i&gt; Vol. 8, No. 1,
 * January 1998, pp 3--30.
 *
 * &lt;h3&gt;About this Version&lt;/h3&gt;
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes since V21:&lt;/b&gt; Minor documentation HTML fixes.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes since V20:&lt;/b&gt; Added clearGuassian(). Modified stateEquals() to be
 * synchronizd on both objects for MersenneTwister, and changed its
 * documentation. Added synchronization to both setSeed() methods, to
 * writeState(), and to readState() in MersenneTwister. Removed synchronization
 * from readObject() in MersenneTwister.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes since V19:&lt;/b&gt; nextFloat(boolean, boolean) now returns float, not
 * double.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes since V18:&lt;/b&gt; Removed old final declarations, which used to
 * potentially speed up the code, but no longer.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes since V17:&lt;/b&gt; Removed vestigial references to &amp;amp;= 0xffffffff
 * which stemmed from the original C code. The C code could not guarantee that
 * ints were 32 bit, hence the masks. The vestigial references in the Java code
 * were likely optimized out anyway.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes since V16:&lt;/b&gt; Added nextDouble(includeZero, includeOne) and
 * nextFloat(includeZero, includeOne) to allow for half-open, fully-closed, and
 * fully-open intervals.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V15:&lt;/b&gt; Added serialVersionUID to quiet compiler warnings
 * from Sun's overly verbose compilers as of JDK 1.5.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V14:&lt;/b&gt; made strictfp, with StrictMath.log and
 * StrictMath.sqrt in nextGaussian instead of Math.log and Math.sqrt. This is
 * largely just to be safe, as it presently makes no difference in the speed,
 * correctness, or results of the algorithm.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V13:&lt;/b&gt; clone() method CloneNotSupportedException removed.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V12:&lt;/b&gt; clone() method added.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V11:&lt;/b&gt; stateEquals(...) method added. MersenneTwisterFast
 * is equal to other MersenneTwisterFasts with identical state; likewise
 * MersenneTwister is equal to other MersenneTwister with identical state. This
 * isn't equals(...) because that requires a contract of immutability to compare
 * by value.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V10:&lt;/b&gt; A documentation error suggested that setSeed(int[])
 * required an int[] array 624 long. In fact, the array can be any non-zero
 * length. The new version also checks for this fact.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V9:&lt;/b&gt; readState(stream) and writeState(stream) provided.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V8:&lt;/b&gt; setSeed(int) was only using the first 28 bits of the
 * seed; it should have been 32 bits. For small-number seeds the behavior is
 * identical.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V7:&lt;/b&gt; A documentation error in MersenneTwisterFast (but
 * not MersenneTwister) stated that nextDouble selects uniformly from the
 * full-open interval [0,1]. It does not. nextDouble's contract is identical
 * across MersenneTwisterFast, MersenneTwister, and java.util.Random, namely,
 * selection in the half-open interval [0,1). That is, 1.0 should not be
 * returned. A similar contract exists in nextFloat.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V6:&lt;/b&gt; License has changed from LGPL to BSD. New timing
 * information to compare against java.util.Random. Recent versions of HotSpot
 * have helped Random increase in speed to the point where it is faster than
 * MersenneTwister but slower than MersenneTwisterFast (which should be the
 * case, as it's a less complex algorithm but is synchronized).
 * 
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V5:&lt;/b&gt; New empty constructor made to work the same as
 * java.util.Random -- namely, it seeds based on the current time in
 * milliseconds.
 *
 * &lt;p&gt;
 * &lt;b&gt;Changes Since V4:&lt;/b&gt; New initialization algorithms. See (see
 * &lt;a href=&quot;http://www.math.keio.ac.jp/matumoto/MT2002/emt19937ar.html&quot;&gt;
 * http://www.math.keio.ac.jp/matumoto/MT2002/emt19937ar.html&lt;/a&gt;)
 *
 * &lt;p&gt;
 * The MersenneTwister code is based on standard MT19937 C/C++ code by Takuji
 * Nishimura, with suggestions from Topher Cooper and Marc Rieffel, July 1997.
 * The code was originally translated into Java by Michael Lecuyer, January
 * 1999, and the original code is Copyright (c) 1999 by Michael Lecuyer.
 *
 * &lt;h3&gt;Java notes&lt;/h3&gt;
 * 
 * &lt;p&gt;
 * This implementation implements the bug fixes made in Java 1.2's version of
 * Random, which means it can be used with earlier versions of Java. See
 * &lt;a href=
 * &quot;http://www.javasoft.com/products/jdk/1.2/docs/api/java/util/Random.html&quot;&gt;
 * the JDK 1.2 java.util.Random documentation&lt;/a&gt; for further documentation on
 * the random-number generation contracts made. Additionally, there's an
 * undocumented bug in the JDK java.util.Random.nextBytes() method, which this
 * code fixes.
 *
 * &lt;p&gt;
 * Just like java.util.Random, this generator accepts a long seed but doesn't
 * use all of it. java.util.Random uses 48 bits. The Mersenne Twister instead
 * uses 32 bits (int size). So it's best if your seed does not exceed the int
 * range.
 *
 * &lt;p&gt;
 * MersenneTwister can be used reliably on JDK version 1.1.5 or above. Earlier
 * Java versions have serious bugs in java.util.Random; only MersenneTwisterFast
 * (and not MersenneTwister nor java.util.Random) should be used with them.
 *
 * &lt;h3&gt;License&lt;/h3&gt;
 *
 * Copyright (c) 2003 by Sean Luke. &lt;br&gt;
 * Portions copyright (c) 1993 by Michael Lecuyer. &lt;br&gt;
 * All rights reserved. &lt;br&gt;
 *
 * &lt;p&gt;
 * Redistribution and use in source and binary forms, with or without
 * modification, are permitted provided that the following conditions are met:
 * &lt;ul&gt;
 * &lt;li&gt;Redistributions of source code must retain the above copyright notice,
 * this list of conditions and the following disclaimer.
 * &lt;li&gt;Redistributions in binary form must reproduce the above copyright notice,
 * this list of conditions and the following disclaimer in the documentation
 * and/or other materials provided with the distribution.
 * &lt;li&gt;Neither the name of the copyright owners, their employers, nor the names
 * of its contributors may be used to endorse or promote products derived from
 * this software without specific prior written permission.
 * &lt;/ul&gt;
 * &lt;p&gt;
 * THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &quot;AS IS&quot;
 * AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE
 * IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE
 * ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNERS OR CONTRIBUTORS BE
 * LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR
 * CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF
 * SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS
 * INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF LIABILITY, WHETHER IN
 * CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE)
 * ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE
 * POSSIBILITY OF SUCH DAMAGE.
 *
 * @version 22
 */

// Note: this class is hard-inlined in all of its methods. This makes some of
// the methods well-nigh unreadable in their complexity. In fact, the Mersenne
// Twister is fairly easy code to understand: if you're trying to get a handle
// on the code, I strongly suggest looking at MersenneTwister.java first.
// -- Sean

public strictfp class MersenneTwisterFast extends Random implements Serializable, Cloneable {
	// Serialization
	private static final long serialVersionUID = -8219700664442619525L; // locked
																		// as of
																		// Version
																		// 15

	// Period parameters
	private static final int N = 624;
	private static final int M = 397;
	private static final int MATRIX_A = 0x9908b0df; // private static final *
													// constant vector a
	private static final int UPPER_MASK = 0x80000000; // most significant w-r
														// bits
	private static final int LOWER_MASK = 0x7fffffff; // least significant r
														// bits

	// Tempering parameters
	private static final int TEMPERING_MASK_B = 0x9d2c5680;
	private static final int TEMPERING_MASK_C = 0xefc60000;

	private int mt[]; // the array for the state vector
	private int mti; // mti==N+1 means mt[N] is not initialized
	private int mag01[];

	// a good initial seed (of int size, though stored in a long)
	// private static final long GOOD_SEED = 4357;

	private double __nextNextGaussian;
	private boolean __haveNextNextGaussian;

	/*
	 * We're overriding all internal data, to my knowledge, so this should be
	 * okay
	 */
	public Object clone() {
		try {
<span class="nc" id="L245">			MersenneTwisterFast f = (MersenneTwisterFast) (super.clone());</span>
<span class="nc" id="L246">			f.mt = (int[]) (mt.clone());</span>
<span class="nc" id="L247">			f.mag01 = (int[]) (mag01.clone());</span>
<span class="nc" id="L248">			return f;</span>
<span class="nc" id="L249">		} catch (CloneNotSupportedException e) {</span>
<span class="nc" id="L250">			throw new InternalError();</span>
		} // should never happen
	}

	/**
	 * Returns true if the MersenneTwisterFast's current internal state is equal
	 * to another MersenneTwisterFast. This is roughly the same as
	 * equals(other), except that it compares based on value but does not
	 * guarantee the contract of immutability (obviously random number
	 * generators are immutable). Note that this does NOT check to see if the
	 * internal gaussian storage is the same for both. You can guarantee that
	 * the internal gaussian storage is the same (and so the nextGaussian()
	 * methods will return the same values) by calling clearGaussian() on both
	 * objects.
	 */
	public boolean stateEquals(MersenneTwisterFast other) {
<span class="nc bnc" id="L266" title="All 2 branches missed.">		if (other == this)</span>
<span class="nc" id="L267">			return true;</span>
<span class="nc bnc" id="L268" title="All 2 branches missed.">		if (other == null)</span>
<span class="nc" id="L269">			return false;</span>

<span class="nc bnc" id="L271" title="All 2 branches missed.">		if (mti != other.mti)</span>
<span class="nc" id="L272">			return false;</span>
<span class="nc bnc" id="L273" title="All 2 branches missed.">		for (int x = 0; x &lt; mag01.length; x++)</span>
<span class="nc bnc" id="L274" title="All 2 branches missed.">			if (mag01[x] != other.mag01[x])</span>
<span class="nc" id="L275">				return false;</span>
<span class="nc bnc" id="L276" title="All 2 branches missed.">		for (int x = 0; x &lt; mt.length; x++)</span>
<span class="nc bnc" id="L277" title="All 2 branches missed.">			if (mt[x] != other.mt[x])</span>
<span class="nc" id="L278">				return false;</span>
<span class="nc" id="L279">		return true;</span>
	}

	/** Reads the entire state of the MersenneTwister RNG from the stream */
	public void readState(DataInputStream stream) throws IOException {
<span class="nc" id="L284">		int len = mt.length;</span>
<span class="nc bnc" id="L285" title="All 2 branches missed.">		for (int x = 0; x &lt; len; x++)</span>
<span class="nc" id="L286">			mt[x] = stream.readInt();</span>

<span class="nc" id="L288">		len = mag01.length;</span>
<span class="nc bnc" id="L289" title="All 2 branches missed.">		for (int x = 0; x &lt; len; x++)</span>
<span class="nc" id="L290">			mag01[x] = stream.readInt();</span>

<span class="nc" id="L292">		mti = stream.readInt();</span>
<span class="nc" id="L293">		__nextNextGaussian = stream.readDouble();</span>
<span class="nc" id="L294">		__haveNextNextGaussian = stream.readBoolean();</span>
<span class="nc" id="L295">	}</span>

	/** Writes the entire state of the MersenneTwister RNG to the stream */
	public void writeState(DataOutputStream stream) throws IOException {
<span class="nc" id="L299">		int len = mt.length;</span>
<span class="nc bnc" id="L300" title="All 2 branches missed.">		for (int x = 0; x &lt; len; x++)</span>
<span class="nc" id="L301">			stream.writeInt(mt[x]);</span>

<span class="nc" id="L303">		len = mag01.length;</span>
<span class="nc bnc" id="L304" title="All 2 branches missed.">		for (int x = 0; x &lt; len; x++)</span>
<span class="nc" id="L305">			stream.writeInt(mag01[x]);</span>

<span class="nc" id="L307">		stream.writeInt(mti);</span>
<span class="nc" id="L308">		stream.writeDouble(__nextNextGaussian);</span>
<span class="nc" id="L309">		stream.writeBoolean(__haveNextNextGaussian);</span>
<span class="nc" id="L310">	}</span>

	/**
	 * Constructor using the default seed.
	 */
	public MersenneTwisterFast() {
<span class="nc" id="L316">		this(System.currentTimeMillis());</span>
<span class="nc" id="L317">	}</span>

	/**
	 * Constructor using a given seed. Though you pass this seed in as a long,
	 * it's best to make sure it's actually an integer.
	 *
	 */
<span class="nc" id="L324">	public MersenneTwisterFast(long seed) {</span>
<span class="nc" id="L325">		setSeed(seed);</span>
<span class="nc" id="L326">	}</span>

	/**
	 * Constructor using an array of integers as seed. Your array must have a
	 * non-zero length. Only the first 624 integers in the array are used; if
	 * the array is shorter than this then integers are repeatedly used in a
	 * wrap-around fashion.
	 */
<span class="nc" id="L334">	public MersenneTwisterFast(int[] array) {</span>
<span class="nc" id="L335">		setSeed(array);</span>
<span class="nc" id="L336">	}</span>

	/**
	 * Initalize the pseudo random number generator. Don't pass in a long that's
	 * bigger than an int (Mersenne Twister only uses the first 32 bits for its
	 * seed).
	 */

	public void setSeed(long seed) {
		// Due to a bug in java.util.Random clear up to 1.2, we're
		// doing our own Gaussian variable.
<span class="nc" id="L347">		__haveNextNextGaussian = false;</span>

<span class="nc" id="L349">		mt = new int[N];</span>

<span class="nc" id="L351">		mag01 = new int[2];</span>
<span class="nc" id="L352">		mag01[0] = 0x0;</span>
<span class="nc" id="L353">		mag01[1] = MATRIX_A;</span>

<span class="nc" id="L355">		mt[0] = (int) (seed &amp; 0xffffffff);</span>
<span class="nc bnc" id="L356" title="All 2 branches missed.">		for (mti = 1; mti &lt; N; mti++) {</span>
<span class="nc" id="L357">			mt[mti] = (1812433253 * (mt[mti - 1] ^ (mt[mti - 1] &gt;&gt;&gt; 30)) + mti);</span>
			/* See Knuth TAOCP Vol2. 3rd Ed. P.106 for multiplier. */
			/* In the previous versions, MSBs of the seed affect */
			/* only MSBs of the array mt[]. */
			/* 2002/01/09 modified by Makoto Matsumoto */
			// mt[mti] &amp;= 0xffffffff;
			/* for &gt;32 bit machines */
		}
<span class="nc" id="L365">	}</span>

	/**
	 * Sets the seed of the MersenneTwister using an array of integers. Your
	 * array must have a non-zero length. Only the first 624 integers in the
	 * array are used; if the array is shorter than this then integers are
	 * repeatedly used in a wrap-around fashion.
	 */

	public void setSeed(int[] array) {
<span class="nc bnc" id="L375" title="All 2 branches missed.">		if (array.length == 0)</span>
<span class="nc" id="L376">			throw new IllegalArgumentException(&quot;Array length must be greater than zero&quot;);</span>
		int i, j, k;
<span class="nc" id="L378">		setSeed(19650218);</span>
<span class="nc" id="L379">		i = 1;</span>
<span class="nc" id="L380">		j = 0;</span>
<span class="nc bnc" id="L381" title="All 2 branches missed.">		k = (N &gt; array.length ? N : array.length);</span>
<span class="nc bnc" id="L382" title="All 2 branches missed.">		for (; k != 0; k--) {</span>
<span class="nc" id="L383">			mt[i] = (mt[i] ^ ((mt[i - 1] ^ (mt[i - 1] &gt;&gt;&gt; 30)) * 1664525)) + array[j]</span>
<span class="nc" id="L384">					+ j; /* non linear */</span>
			// mt[i] &amp;= 0xffffffff; /* for WORDSIZE &gt; 32 machines */
<span class="nc" id="L386">			i++;</span>
<span class="nc" id="L387">			j++;</span>
<span class="nc bnc" id="L388" title="All 2 branches missed.">			if (i &gt;= N) {</span>
<span class="nc" id="L389">				mt[0] = mt[N - 1];</span>
<span class="nc" id="L390">				i = 1;</span>
			}
<span class="nc bnc" id="L392" title="All 2 branches missed.">			if (j &gt;= array.length)</span>
<span class="nc" id="L393">				j = 0;</span>
		}
<span class="nc bnc" id="L395" title="All 2 branches missed.">		for (k = N - 1; k != 0; k--) {</span>
<span class="nc" id="L396">			mt[i] = (mt[i] ^ ((mt[i - 1] ^ (mt[i - 1] &gt;&gt;&gt; 30)) * 1566083941))</span>
<span class="nc" id="L397">					- i; /* non linear */</span>
			// mt[i] &amp;= 0xffffffff; /* for WORDSIZE &gt; 32 machines */
<span class="nc" id="L399">			i++;</span>
<span class="nc bnc" id="L400" title="All 2 branches missed.">			if (i &gt;= N) {</span>
<span class="nc" id="L401">				mt[0] = mt[N - 1];</span>
<span class="nc" id="L402">				i = 1;</span>
			}
		}
<span class="nc" id="L405">		mt[0] = 0x80000000; /* MSB is 1; assuring non-zero initial array */</span>
<span class="nc" id="L406">	}</span>

	public int nextInt() {
		int y;

<span class="nc bnc" id="L411" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L414">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L415">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L417" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L418">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L419">				mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc bnc" id="L421" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L422">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L423">				mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc" id="L425">			y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L426">			mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L428">			mti = 0;</span>
		}

<span class="nc" id="L431">		y = mt[mti++];</span>
<span class="nc" id="L432">		y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L433">		y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L434">		y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L435">		y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc" id="L437">		return y;</span>
	}

	public short nextShort() {
		int y;

<span class="nc bnc" id="L443" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L446">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L447">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L449" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L450">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L451">				mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc bnc" id="L453" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L454">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L455">				mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc" id="L457">			y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L458">			mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L460">			mti = 0;</span>
		}

<span class="nc" id="L463">		y = mt[mti++];</span>
<span class="nc" id="L464">		y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L465">		y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L466">		y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L467">		y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc" id="L469">		return (short) (y &gt;&gt;&gt; 16);</span>
	}

	public char nextChar() {
		int y;

<span class="nc bnc" id="L475" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L478">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L479">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L481" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L482">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L483">				mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc bnc" id="L485" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L486">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L487">				mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc" id="L489">			y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L490">			mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L492">			mti = 0;</span>
		}

<span class="nc" id="L495">		y = mt[mti++];</span>
<span class="nc" id="L496">		y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L497">		y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L498">		y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L499">		y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc" id="L501">		return (char) (y &gt;&gt;&gt; 16);</span>
	}

	public boolean nextBoolean() {
		int y;

<span class="nc bnc" id="L507" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L510">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L511">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L513" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L514">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L515">				mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc bnc" id="L517" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L518">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L519">				mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc" id="L521">			y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L522">			mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L524">			mti = 0;</span>
		}

<span class="nc" id="L527">		y = mt[mti++];</span>
<span class="nc" id="L528">		y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L529">		y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L530">		y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L531">		y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc bnc" id="L533" title="All 2 branches missed.">		return (boolean) ((y &gt;&gt;&gt; 31) != 0);</span>
	}

	/**
	 * This generates a coin flip with a probability &lt;tt&gt;probability&lt;/tt&gt; of
	 * returning true, else returning false. &lt;tt&gt;probability&lt;/tt&gt; must be
	 * between 0.0 and 1.0, inclusive. Not as precise a random real event as
	 * nextBoolean(double), but twice as fast. To explicitly use this, remember
	 * you may need to cast to float first.
	 */

	public boolean nextBoolean(float probability) {
		int y;

<span class="nc bnc" id="L547" title="All 4 branches missed.">		if (probability &lt; 0.0f || probability &gt; 1.0f)</span>
<span class="nc" id="L548">			throw new IllegalArgumentException(&quot;probability must be between 0.0 and 1.0 inclusive.&quot;);</span>
<span class="nc bnc" id="L549" title="All 2 branches missed.">		if (probability == 0.0f)</span>
<span class="nc" id="L550">			return false; // fix half-open issues</span>
<span class="nc bnc" id="L551" title="All 2 branches missed.">		else if (probability == 1.0f)</span>
<span class="nc" id="L552">			return true; // fix half-open issues</span>
<span class="nc bnc" id="L553" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L556">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L557">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L559" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L560">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L561">				mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc bnc" id="L563" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L564">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L565">				mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc" id="L567">			y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L568">			mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L570">			mti = 0;</span>
		}

<span class="nc" id="L573">		y = mt[mti++];</span>
<span class="nc" id="L574">		y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L575">		y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L576">		y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L577">		y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc bnc" id="L579" title="All 2 branches missed.">		return (y &gt;&gt;&gt; 8) / ((float) (1 &lt;&lt; 24)) &lt; probability;</span>
	}

	/**
	 * This generates a coin flip with a probability &lt;tt&gt;probability&lt;/tt&gt; of
	 * returning true, else returning false. &lt;tt&gt;probability&lt;/tt&gt; must be
	 * between 0.0 and 1.0, inclusive.
	 */

	public boolean nextBoolean(double probability) {
		int y;
		int z;

<span class="nc bnc" id="L592" title="All 4 branches missed.">		if (probability &lt; 0.0 || probability &gt; 1.0)</span>
<span class="nc" id="L593">			throw new IllegalArgumentException(&quot;probability must be between 0.0 and 1.0 inclusive.&quot;);</span>
<span class="nc bnc" id="L594" title="All 2 branches missed.">		if (probability == 0.0)</span>
<span class="nc" id="L595">			return false; // fix half-open issues</span>
<span class="nc bnc" id="L596" title="All 2 branches missed.">		else if (probability == 1.0)</span>
<span class="nc" id="L597">			return true; // fix half-open issues</span>
<span class="nc bnc" id="L598" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L601">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L602">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L604" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L605">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L606">				mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc bnc" id="L608" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L609">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L610">				mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc" id="L612">			y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L613">			mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L615">			mti = 0;</span>
		}

<span class="nc" id="L618">		y = mt[mti++];</span>
<span class="nc" id="L619">		y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L620">		y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L621">		y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L622">		y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc bnc" id="L624" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L627">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L628">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L630" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L631">				z = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L632">				mt[kk] = mt[kk + M] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>
			}
<span class="nc bnc" id="L634" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L635">				z = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L636">				mt[kk] = mt[kk + (M - N)] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>
			}
<span class="nc" id="L638">			z = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L639">			mt[N - 1] = mt[M - 1] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>

<span class="nc" id="L641">			mti = 0;</span>
		}

<span class="nc" id="L644">		z = mt[mti++];</span>
<span class="nc" id="L645">		z ^= z &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(z)</span>
<span class="nc" id="L646">		z ^= (z &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(z)</span>
<span class="nc" id="L647">		z ^= (z &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(z)</span>
<span class="nc" id="L648">		z ^= (z &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(z)</span>

		/* derived from nextDouble documentation in jdk 1.2 docs, see top */
<span class="nc bnc" id="L651" title="All 2 branches missed.">		return ((((long) (y &gt;&gt;&gt; 6)) &lt;&lt; 27) + (z &gt;&gt;&gt; 5)) / (double) (1L &lt;&lt; 53) &lt; probability;</span>
	}

	public byte nextByte() {
		int y;

<span class="nc bnc" id="L657" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L660">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L661">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L663" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L664">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L665">				mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc bnc" id="L667" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L668">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L669">				mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc" id="L671">			y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L672">			mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L674">			mti = 0;</span>
		}

<span class="nc" id="L677">		y = mt[mti++];</span>
<span class="nc" id="L678">		y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L679">		y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L680">		y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L681">		y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc" id="L683">		return (byte) (y &gt;&gt;&gt; 24);</span>
	}

	public void nextBytes(byte[] bytes) {
		int y;

<span class="nc bnc" id="L689" title="All 2 branches missed.">		for (int x = 0; x &lt; bytes.length; x++) {</span>
<span class="nc bnc" id="L690" title="All 2 branches missed.">			if (mti &gt;= N) // generate N words at one time</span>
			{
				int kk;
<span class="nc" id="L693">				final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L694">				final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L696" title="All 2 branches missed.">				for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L697">					y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L698">					mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
				}
<span class="nc bnc" id="L700" title="All 2 branches missed.">				for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L701">					y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L702">					mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
				}
<span class="nc" id="L704">				y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L705">				mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L707">				mti = 0;</span>
			}

<span class="nc" id="L710">			y = mt[mti++];</span>
<span class="nc" id="L711">			y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L712">			y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L713">			y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L714">			y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc" id="L716">			bytes[x] = (byte) (y &gt;&gt;&gt; 24);</span>
		}
<span class="nc" id="L718">	}</span>

	/**
	 * Returns a long drawn uniformly from 0 to n-1. Suffice it to say, n must
	 * be greater than 0, or an IllegalArgumentException is raised.
	 */

	public long nextLong() {
		int y;
		int z;

<span class="nc bnc" id="L729" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L732">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L733">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L735" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L736">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L737">				mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc bnc" id="L739" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L740">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L741">				mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc" id="L743">			y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L744">			mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L746">			mti = 0;</span>
		}

<span class="nc" id="L749">		y = mt[mti++];</span>
<span class="nc" id="L750">		y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L751">		y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L752">		y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L753">		y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc bnc" id="L755" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L758">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L759">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L761" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L762">				z = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L763">				mt[kk] = mt[kk + M] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>
			}
<span class="nc bnc" id="L765" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L766">				z = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L767">				mt[kk] = mt[kk + (M - N)] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>
			}
<span class="nc" id="L769">			z = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L770">			mt[N - 1] = mt[M - 1] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>

<span class="nc" id="L772">			mti = 0;</span>
		}

<span class="nc" id="L775">		z = mt[mti++];</span>
<span class="nc" id="L776">		z ^= z &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(z)</span>
<span class="nc" id="L777">		z ^= (z &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(z)</span>
<span class="nc" id="L778">		z ^= (z &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(z)</span>
<span class="nc" id="L779">		z ^= (z &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(z)</span>

<span class="nc" id="L781">		return (((long) y) &lt;&lt; 32) + (long) z;</span>
	}

	/**
	 * Returns a long drawn uniformly from 0 to n-1. Suffice it to say, n must
	 * be &amp;gt; 0, or an IllegalArgumentException is raised.
	 */
	public long nextLong(long n) {
<span class="nc bnc" id="L789" title="All 2 branches missed.">		if (n &lt;= 0)</span>
<span class="nc" id="L790">			throw new IllegalArgumentException(&quot;n must be positive, got: &quot; + n);</span>

		long bits, val;
<span class="nc bnc" id="L793" title="All 2 branches missed.">		do {</span>
			int y;
			int z;

<span class="nc bnc" id="L797" title="All 2 branches missed.">			if (mti &gt;= N) // generate N words at one time</span>
			{
				int kk;
<span class="nc" id="L800">				final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L801">				final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L803" title="All 2 branches missed.">				for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L804">					y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L805">					mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
				}
<span class="nc bnc" id="L807" title="All 2 branches missed.">				for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L808">					y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L809">					mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
				}
<span class="nc" id="L811">				y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L812">				mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L814">				mti = 0;</span>
			}

<span class="nc" id="L817">			y = mt[mti++];</span>
<span class="nc" id="L818">			y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L819">			y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L820">			y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L821">			y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc bnc" id="L823" title="All 2 branches missed.">			if (mti &gt;= N) // generate N words at one time</span>
			{
				int kk;
<span class="nc" id="L826">				final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L827">				final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L829" title="All 2 branches missed.">				for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L830">					z = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L831">					mt[kk] = mt[kk + M] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>
				}
<span class="nc bnc" id="L833" title="All 2 branches missed.">				for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L834">					z = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L835">					mt[kk] = mt[kk + (M - N)] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>
				}
<span class="nc" id="L837">				z = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L838">				mt[N - 1] = mt[M - 1] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>

<span class="nc" id="L840">				mti = 0;</span>
			}

<span class="nc" id="L843">			z = mt[mti++];</span>
<span class="nc" id="L844">			z ^= z &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(z)</span>
<span class="nc" id="L845">			z ^= (z &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(z)</span>
<span class="nc" id="L846">			z ^= (z &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(z)</span>
<span class="nc" id="L847">			z ^= (z &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(z)</span>

<span class="nc" id="L849">			bits = (((((long) y) &lt;&lt; 32) + (long) z) &gt;&gt;&gt; 1);</span>
<span class="nc" id="L850">			val = bits % n;</span>
<span class="nc" id="L851">		} while (bits - val + (n - 1) &lt; 0);</span>
<span class="nc" id="L852">		return val;</span>
	}

	/**
	 * Returns a random double in the half-open range from [0.0,1.0). Thus 0.0
	 * is a valid result but 1.0 is not.
	 */
	public double nextDouble() {
		int y;
		int z;

<span class="nc bnc" id="L863" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L866">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L867">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L869" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L870">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L871">				mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc bnc" id="L873" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L874">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L875">				mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc" id="L877">			y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L878">			mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L880">			mti = 0;</span>
		}

<span class="nc" id="L883">		y = mt[mti++];</span>
<span class="nc" id="L884">		y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L885">		y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L886">		y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L887">		y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc bnc" id="L889" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L892">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L893">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L895" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L896">				z = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L897">				mt[kk] = mt[kk + M] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>
			}
<span class="nc bnc" id="L899" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L900">				z = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L901">				mt[kk] = mt[kk + (M - N)] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>
			}
<span class="nc" id="L903">			z = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L904">			mt[N - 1] = mt[M - 1] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>

<span class="nc" id="L906">			mti = 0;</span>
		}

<span class="nc" id="L909">		z = mt[mti++];</span>
<span class="nc" id="L910">		z ^= z &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(z)</span>
<span class="nc" id="L911">		z ^= (z &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(z)</span>
<span class="nc" id="L912">		z ^= (z &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(z)</span>
<span class="nc" id="L913">		z ^= (z &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(z)</span>

		/* derived from nextDouble documentation in jdk 1.2 docs, see top */
<span class="nc" id="L916">		return ((((long) (y &gt;&gt;&gt; 6)) &lt;&lt; 27) + (z &gt;&gt;&gt; 5)) / (double) (1L &lt;&lt; 53);</span>
	}

	/**
	 * Returns a double in the range from 0.0 to 1.0, possibly inclusive of 0.0
	 * and 1.0 themselves. Thus:
	 * 
	 * &lt;table border=0&gt;
	 * &lt;tr&gt;
	 * &lt;th&gt;Expression&lt;/th&gt;
	 * &lt;th&gt;Interval&lt;/th&gt;
	 * &lt;/tr&gt;
	 * &lt;tr&gt;
	 * &lt;td&gt;nextDouble(false, false)&lt;/td&gt;
	 * &lt;td&gt;(0.0, 1.0)&lt;/td&gt;
	 * &lt;/tr&gt;
	 * &lt;tr&gt;
	 * &lt;td&gt;nextDouble(true, false)&lt;/td&gt;
	 * &lt;td&gt;[0.0, 1.0)&lt;/td&gt;
	 * &lt;/tr&gt;
	 * &lt;tr&gt;
	 * &lt;td&gt;nextDouble(false, true)&lt;/td&gt;
	 * &lt;td&gt;(0.0, 1.0]&lt;/td&gt;
	 * &lt;/tr&gt;
	 * &lt;tr&gt;
	 * &lt;td&gt;nextDouble(true, true)&lt;/td&gt;
	 * &lt;td&gt;[0.0, 1.0]&lt;/td&gt;
	 * &lt;/tr&gt;
	 * &lt;caption&gt;Table of intervals&lt;/caption&gt;
	 * &lt;/table&gt;
	 * 
	 * &lt;p&gt;
	 * This version preserves all possible random values in the double range.
	 */
	public double nextDouble(boolean includeZero, boolean includeOne) {
<span class="nc" id="L951">		double d = 0.0;</span>
		do {
<span class="nc" id="L953">			d = nextDouble(); // grab a value, initially from half-open [0.0,</span>
								// 1.0)
<span class="nc bnc" id="L955" title="All 4 branches missed.">			if (includeOne &amp;&amp; nextBoolean())</span>
<span class="nc" id="L956">				d += 1.0; // if includeOne, with 1/2 probability, push to [1.0,</span>
							// 2.0)
<span class="nc bnc" id="L958" title="All 2 branches missed.">		} while ((d &gt; 1.0) || // everything above 1.0 is always invalid</span>
<span class="nc bnc" id="L959" title="All 4 branches missed.">				(!includeZero &amp;&amp; d == 0.0)); // if we're not including zero, 0.0</span>
												// is invalid
<span class="nc" id="L961">		return d;</span>
	}

	/**
	 * Clears the internal gaussian variable from the RNG. You only need to do
	 * this in the rare case that you need to guarantee that two RNGs have
	 * identical internal state. Otherwise, disregard this method. See
	 * stateEquals(other).
	 */
	public void clearGaussian() {
<span class="nc" id="L971">		__haveNextNextGaussian = false;</span>
<span class="nc" id="L972">	}</span>

	public double nextGaussian() {
<span class="nc bnc" id="L975" title="All 2 branches missed.">		if (__haveNextNextGaussian) {</span>
<span class="nc" id="L976">			__haveNextNextGaussian = false;</span>
<span class="nc" id="L977">			return __nextNextGaussian;</span>
		} else {
			double v1, v2, s;
			do {
				int y;
				int z;
				int a;
				int b;

<span class="nc bnc" id="L986" title="All 2 branches missed.">				if (mti &gt;= N) // generate N words at one time</span>
				{
					int kk;
<span class="nc" id="L989">					final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L990">					final int[] mag01 = this.mag01; // locals are slightly</span>
													// faster

<span class="nc bnc" id="L993" title="All 2 branches missed.">					for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L994">						y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L995">						mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
					}
<span class="nc bnc" id="L997" title="All 2 branches missed.">					for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L998">						y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L999">						mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
					}
<span class="nc" id="L1001">					y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L1002">					mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L1004">					mti = 0;</span>
				}

<span class="nc" id="L1007">				y = mt[mti++];</span>
<span class="nc" id="L1008">				y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L1009">				y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L1010">				y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L1011">				y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc bnc" id="L1013" title="All 2 branches missed.">				if (mti &gt;= N) // generate N words at one time</span>
				{
					int kk;
<span class="nc" id="L1016">					final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L1017">					final int[] mag01 = this.mag01; // locals are slightly</span>
													// faster

<span class="nc bnc" id="L1020" title="All 2 branches missed.">					for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L1021">						z = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1022">						mt[kk] = mt[kk + M] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>
					}
<span class="nc bnc" id="L1024" title="All 2 branches missed.">					for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L1025">						z = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1026">						mt[kk] = mt[kk + (M - N)] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>
					}
<span class="nc" id="L1028">					z = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L1029">					mt[N - 1] = mt[M - 1] ^ (z &gt;&gt;&gt; 1) ^ mag01[z &amp; 0x1];</span>

<span class="nc" id="L1031">					mti = 0;</span>
				}

<span class="nc" id="L1034">				z = mt[mti++];</span>
<span class="nc" id="L1035">				z ^= z &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(z)</span>
<span class="nc" id="L1036">				z ^= (z &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(z)</span>
<span class="nc" id="L1037">				z ^= (z &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(z)</span>
<span class="nc" id="L1038">				z ^= (z &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(z)</span>

<span class="nc bnc" id="L1040" title="All 2 branches missed.">				if (mti &gt;= N) // generate N words at one time</span>
				{
					int kk;
<span class="nc" id="L1043">					final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L1044">					final int[] mag01 = this.mag01; // locals are slightly</span>
													// faster

<span class="nc bnc" id="L1047" title="All 2 branches missed.">					for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L1048">						a = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1049">						mt[kk] = mt[kk + M] ^ (a &gt;&gt;&gt; 1) ^ mag01[a &amp; 0x1];</span>
					}
<span class="nc bnc" id="L1051" title="All 2 branches missed.">					for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L1052">						a = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1053">						mt[kk] = mt[kk + (M - N)] ^ (a &gt;&gt;&gt; 1) ^ mag01[a &amp; 0x1];</span>
					}
<span class="nc" id="L1055">					a = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L1056">					mt[N - 1] = mt[M - 1] ^ (a &gt;&gt;&gt; 1) ^ mag01[a &amp; 0x1];</span>

<span class="nc" id="L1058">					mti = 0;</span>
				}

<span class="nc" id="L1061">				a = mt[mti++];</span>
<span class="nc" id="L1062">				a ^= a &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(a)</span>
<span class="nc" id="L1063">				a ^= (a &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(a)</span>
<span class="nc" id="L1064">				a ^= (a &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(a)</span>
<span class="nc" id="L1065">				a ^= (a &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(a)</span>

<span class="nc bnc" id="L1067" title="All 2 branches missed.">				if (mti &gt;= N) // generate N words at one time</span>
				{
					int kk;
<span class="nc" id="L1070">					final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L1071">					final int[] mag01 = this.mag01; // locals are slightly</span>
													// faster

<span class="nc bnc" id="L1074" title="All 2 branches missed.">					for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L1075">						b = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1076">						mt[kk] = mt[kk + M] ^ (b &gt;&gt;&gt; 1) ^ mag01[b &amp; 0x1];</span>
					}
<span class="nc bnc" id="L1078" title="All 2 branches missed.">					for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L1079">						b = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1080">						mt[kk] = mt[kk + (M - N)] ^ (b &gt;&gt;&gt; 1) ^ mag01[b &amp; 0x1];</span>
					}
<span class="nc" id="L1082">					b = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L1083">					mt[N - 1] = mt[M - 1] ^ (b &gt;&gt;&gt; 1) ^ mag01[b &amp; 0x1];</span>

<span class="nc" id="L1085">					mti = 0;</span>
				}

<span class="nc" id="L1088">				b = mt[mti++];</span>
<span class="nc" id="L1089">				b ^= b &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(b)</span>
<span class="nc" id="L1090">				b ^= (b &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(b)</span>
<span class="nc" id="L1091">				b ^= (b &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(b)</span>
<span class="nc" id="L1092">				b ^= (b &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(b)</span>

				/*
				 * derived from nextDouble documentation in jdk 1.2 docs, see
				 * top
				 */
<span class="nc" id="L1098">				v1 = 2 * (((((long) (y &gt;&gt;&gt; 6)) &lt;&lt; 27) + (z &gt;&gt;&gt; 5)) / (double) (1L &lt;&lt; 53)) - 1;</span>
<span class="nc" id="L1099">				v2 = 2 * (((((long) (a &gt;&gt;&gt; 6)) &lt;&lt; 27) + (b &gt;&gt;&gt; 5)) / (double) (1L &lt;&lt; 53)) - 1;</span>
<span class="nc" id="L1100">				s = v1 * v1 + v2 * v2;</span>
<span class="nc bnc" id="L1101" title="All 4 branches missed.">			} while (s &gt;= 1 || s == 0);</span>
<span class="nc" id="L1102">			double multiplier = StrictMath.sqrt(-2 * StrictMath.log(s) / s);</span>
<span class="nc" id="L1103">			__nextNextGaussian = v2 * multiplier;</span>
<span class="nc" id="L1104">			__haveNextNextGaussian = true;</span>
<span class="nc" id="L1105">			return v1 * multiplier;</span>
		}
	}

	/**
	 * Returns a random float in the half-open range from [0.0f,1.0f). Thus 0.0f
	 * is a valid result but 1.0f is not.
	 */
	public float nextFloat() {
		int y;

<span class="nc bnc" id="L1116" title="All 2 branches missed.">		if (mti &gt;= N) // generate N words at one time</span>
		{
			int kk;
<span class="nc" id="L1119">			final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L1120">			final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L1122" title="All 2 branches missed.">			for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L1123">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1124">				mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc bnc" id="L1126" title="All 2 branches missed.">			for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L1127">				y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1128">				mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
			}
<span class="nc" id="L1130">			y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L1131">			mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L1133">			mti = 0;</span>
		}

<span class="nc" id="L1136">		y = mt[mti++];</span>
<span class="nc" id="L1137">		y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L1138">		y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L1139">		y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L1140">		y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc" id="L1142">		return (y &gt;&gt;&gt; 8) / ((float) (1 &lt;&lt; 24));</span>
	}

	/**
	 * Returns a float in the range from 0.0f to 1.0f, possibly inclusive of
	 * 0.0f and 1.0f themselves. Thus:
	 * 
	 * &lt;table border=0&gt;
	 * &lt;tr&gt;
	 * &lt;th&gt;Expression&lt;/th&gt;
	 * &lt;th&gt;Interval&lt;/th&gt;
	 * &lt;/tr&gt;
	 * &lt;tr&gt;
	 * &lt;td&gt;nextFloat(false, false)&lt;/td&gt;
	 * &lt;td&gt;(0.0f, 1.0f)&lt;/td&gt;
	 * &lt;/tr&gt;
	 * &lt;tr&gt;
	 * &lt;td&gt;nextFloat(true, false)&lt;/td&gt;
	 * &lt;td&gt;[0.0f, 1.0f)&lt;/td&gt;
	 * &lt;/tr&gt;
	 * &lt;tr&gt;
	 * &lt;td&gt;nextFloat(false, true)&lt;/td&gt;
	 * &lt;td&gt;(0.0f, 1.0f]&lt;/td&gt;
	 * &lt;/tr&gt;
	 * &lt;tr&gt;
	 * &lt;td&gt;nextFloat(true, true)&lt;/td&gt;
	 * &lt;td&gt;[0.0f, 1.0f]&lt;/td&gt;
	 * &lt;/tr&gt;
	 * &lt;caption&gt;Table of intervals&lt;/caption&gt;
	 * &lt;/table&gt;
	 * 
	 * &lt;p&gt;
	 * This version preserves all possible random values in the float range.
	 */
	public float nextFloat(boolean includeZero, boolean includeOne) {
<span class="nc" id="L1177">		float d = 0.0f;</span>
		do {
<span class="nc" id="L1179">			d = nextFloat(); // grab a value, initially from half-open [0.0f,</span>
								// 1.0f)
<span class="nc bnc" id="L1181" title="All 4 branches missed.">			if (includeOne &amp;&amp; nextBoolean())</span>
<span class="nc" id="L1182">				d += 1.0f; // if includeOne, with 1/2 probability, push to</span>
							// [1.0f, 2.0f)
<span class="nc bnc" id="L1184" title="All 2 branches missed.">		} while ((d &gt; 1.0f) || // everything above 1.0f is always invalid</span>
<span class="nc bnc" id="L1185" title="All 4 branches missed.">				(!includeZero &amp;&amp; d == 0.0f)); // if we're not including zero,</span>
												// 0.0f is invalid
<span class="nc" id="L1187">		return d;</span>
	}

	/**
	 * Returns an integer drawn uniformly from 0 to n-1. Suffice it to say, n
	 * must be &amp;gt; 0, or an IllegalArgumentException is raised.
	 */
	public int nextInt(int n) {
<span class="nc bnc" id="L1195" title="All 2 branches missed.">		if (n &lt;= 0)</span>
<span class="nc" id="L1196">			throw new IllegalArgumentException(&quot;n must be positive, got: &quot; + n);</span>

<span class="nc bnc" id="L1198" title="All 2 branches missed.">		if ((n &amp; -n) == n) // i.e., n is a power of 2</span>
		{
			int y;

<span class="nc bnc" id="L1202" title="All 2 branches missed.">			if (mti &gt;= N) // generate N words at one time</span>
			{
				int kk;
<span class="nc" id="L1205">				final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L1206">				final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L1208" title="All 2 branches missed.">				for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L1209">					y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1210">					mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
				}
<span class="nc bnc" id="L1212" title="All 2 branches missed.">				for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L1213">					y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1214">					mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
				}
<span class="nc" id="L1216">				y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L1217">				mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L1219">				mti = 0;</span>
			}

<span class="nc" id="L1222">			y = mt[mti++];</span>
<span class="nc" id="L1223">			y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L1224">			y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L1225">			y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L1226">			y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc" id="L1228">			return (int) ((n * (long) (y &gt;&gt;&gt; 1)) &gt;&gt; 31);</span>
		}

		int bits, val;
<span class="nc bnc" id="L1232" title="All 2 branches missed.">		do {</span>
			int y;

<span class="nc bnc" id="L1235" title="All 2 branches missed.">			if (mti &gt;= N) // generate N words at one time</span>
			{
				int kk;
<span class="nc" id="L1238">				final int[] mt = this.mt; // locals are slightly faster</span>
<span class="nc" id="L1239">				final int[] mag01 = this.mag01; // locals are slightly faster</span>

<span class="nc bnc" id="L1241" title="All 2 branches missed.">				for (kk = 0; kk &lt; N - M; kk++) {</span>
<span class="nc" id="L1242">					y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1243">					mt[kk] = mt[kk + M] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
				}
<span class="nc bnc" id="L1245" title="All 2 branches missed.">				for (; kk &lt; N - 1; kk++) {</span>
<span class="nc" id="L1246">					y = (mt[kk] &amp; UPPER_MASK) | (mt[kk + 1] &amp; LOWER_MASK);</span>
<span class="nc" id="L1247">					mt[kk] = mt[kk + (M - N)] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>
				}
<span class="nc" id="L1249">				y = (mt[N - 1] &amp; UPPER_MASK) | (mt[0] &amp; LOWER_MASK);</span>
<span class="nc" id="L1250">				mt[N - 1] = mt[M - 1] ^ (y &gt;&gt;&gt; 1) ^ mag01[y &amp; 0x1];</span>

<span class="nc" id="L1252">				mti = 0;</span>
			}

<span class="nc" id="L1255">			y = mt[mti++];</span>
<span class="nc" id="L1256">			y ^= y &gt;&gt;&gt; 11; // TEMPERING_SHIFT_U(y)</span>
<span class="nc" id="L1257">			y ^= (y &lt;&lt; 7) &amp; TEMPERING_MASK_B; // TEMPERING_SHIFT_S(y)</span>
<span class="nc" id="L1258">			y ^= (y &lt;&lt; 15) &amp; TEMPERING_MASK_C; // TEMPERING_SHIFT_T(y)</span>
<span class="nc" id="L1259">			y ^= (y &gt;&gt;&gt; 18); // TEMPERING_SHIFT_L(y)</span>

<span class="nc" id="L1261">			bits = (y &gt;&gt;&gt; 1);</span>
<span class="nc" id="L1262">			val = bits % n;</span>
<span class="nc" id="L1263">		} while (bits - val + (n - 1) &lt; 0);</span>
<span class="nc" id="L1264">		return val;</span>
	}

	/**
	 * Tests the code.
	 */
	public static void main(String args[]) {
		int j;

		MersenneTwisterFast r;

		// CORRECTNESS TEST
		// COMPARE WITH
		// http://www.math.keio.ac.jp/matumoto/CODES/MT2002/mt19937ar.out

<span class="nc" id="L1279">		r = new MersenneTwisterFast(new int[] { 0x123, 0x234, 0x345, 0x456 });</span>
<span class="nc" id="L1280">		System.out.println(&quot;Output of MersenneTwisterFast with new (2002/1/26) seeding mechanism&quot;);</span>
<span class="nc bnc" id="L1281" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
			// first, convert the int from signed to &quot;unsigned&quot;
<span class="nc" id="L1283">			long l = (long) r.nextInt();</span>
<span class="nc bnc" id="L1284" title="All 2 branches missed.">			if (l &lt; 0)</span>
<span class="nc" id="L1285">				l += 4294967296L; // max int value</span>
<span class="nc" id="L1286">			String s = String.valueOf(l);</span>
<span class="nc bnc" id="L1287" title="All 2 branches missed.">			while (s.length() &lt; 10)</span>
<span class="nc" id="L1288">				s = &quot; &quot; + s; // buffer</span>
<span class="nc" id="L1289">			System.out.print(s + &quot; &quot;);</span>
<span class="nc bnc" id="L1290" title="All 2 branches missed.">			if (j % 5 == 4)</span>
<span class="nc" id="L1291">				System.out.println();</span>
		}

		// SPEED TEST

<span class="nc" id="L1296">		final long SEED = 4357;</span>

		int xx;
		long ms;
<span class="nc" id="L1300">		System.out.println(&quot;\nTime to test grabbing 100000000 ints&quot;);</span>

<span class="nc" id="L1302">		Random rr = new Random(SEED);</span>
<span class="nc" id="L1303">		xx = 0;</span>
<span class="nc" id="L1304">		ms = System.currentTimeMillis();</span>
<span class="nc bnc" id="L1305" title="All 2 branches missed.">		for (j = 0; j &lt; 100000000; j++)</span>
<span class="nc" id="L1306">			xx += rr.nextInt();</span>
<span class="nc" id="L1307">		System.out.println(&quot;java.util.Random: &quot; + (System.currentTimeMillis() - ms) + &quot;          Ignore this: &quot; + xx);</span>

<span class="nc" id="L1309">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc" id="L1310">		ms = System.currentTimeMillis();</span>
<span class="nc" id="L1311">		xx = 0;</span>
<span class="nc bnc" id="L1312" title="All 2 branches missed.">		for (j = 0; j &lt; 100000000; j++)</span>
<span class="nc" id="L1313">			xx += r.nextInt();</span>
<span class="nc" id="L1314">		System.out.println(</span>
<span class="nc" id="L1315">				&quot;Mersenne Twister Fast: &quot; + (System.currentTimeMillis() - ms) + &quot;          Ignore this: &quot; + xx);</span>

		// TEST TO COMPARE TYPE CONVERSION BETWEEN
		// MersenneTwisterFast.java AND MersenneTwister.java

<span class="nc" id="L1320">		System.out.println(&quot;\nGrab the first 1000 booleans&quot;);</span>
<span class="nc" id="L1321">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc bnc" id="L1322" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1323">			System.out.print(r.nextBoolean() + &quot; &quot;);</span>
<span class="nc bnc" id="L1324" title="All 2 branches missed.">			if (j % 8 == 7)</span>
<span class="nc" id="L1325">				System.out.println();</span>
		}
<span class="nc bnc" id="L1327" title="All 2 branches missed.">		if (!(j % 8 == 7))</span>
<span class="nc" id="L1328">			System.out.println();</span>

<span class="nc" id="L1330">		System.out.println(&quot;\nGrab 1000 booleans of increasing probability using nextBoolean(double)&quot;);</span>
<span class="nc" id="L1331">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc bnc" id="L1332" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1333">			System.out.print(r.nextBoolean((double) (j / 999.0)) + &quot; &quot;);</span>
<span class="nc bnc" id="L1334" title="All 2 branches missed.">			if (j % 8 == 7)</span>
<span class="nc" id="L1335">				System.out.println();</span>
		}
<span class="nc bnc" id="L1337" title="All 2 branches missed.">		if (!(j % 8 == 7))</span>
<span class="nc" id="L1338">			System.out.println();</span>

<span class="nc" id="L1340">		System.out.println(&quot;\nGrab 1000 booleans of increasing probability using nextBoolean(float)&quot;);</span>
<span class="nc" id="L1341">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc bnc" id="L1342" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1343">			System.out.print(r.nextBoolean((float) (j / 999.0f)) + &quot; &quot;);</span>
<span class="nc bnc" id="L1344" title="All 2 branches missed.">			if (j % 8 == 7)</span>
<span class="nc" id="L1345">				System.out.println();</span>
		}
<span class="nc bnc" id="L1347" title="All 2 branches missed.">		if (!(j % 8 == 7))</span>
<span class="nc" id="L1348">			System.out.println();</span>

<span class="nc" id="L1350">		byte[] bytes = new byte[1000];</span>
<span class="nc" id="L1351">		System.out.println(&quot;\nGrab the first 1000 bytes using nextBytes&quot;);</span>
<span class="nc" id="L1352">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc" id="L1353">		r.nextBytes(bytes);</span>
<span class="nc bnc" id="L1354" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1355">			System.out.print(bytes[j] + &quot; &quot;);</span>
<span class="nc bnc" id="L1356" title="All 2 branches missed.">			if (j % 16 == 15)</span>
<span class="nc" id="L1357">				System.out.println();</span>
		}
<span class="nc bnc" id="L1359" title="All 2 branches missed.">		if (!(j % 16 == 15))</span>
<span class="nc" id="L1360">			System.out.println();</span>

		byte b;
<span class="nc" id="L1363">		System.out.println(&quot;\nGrab the first 1000 bytes -- must be same as nextBytes&quot;);</span>
<span class="nc" id="L1364">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc bnc" id="L1365" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1366">			System.out.print((b = r.nextByte()) + &quot; &quot;);</span>
<span class="nc bnc" id="L1367" title="All 2 branches missed.">			if (b != bytes[j])</span>
<span class="nc" id="L1368">				System.out.print(&quot;BAD &quot;);</span>
<span class="nc bnc" id="L1369" title="All 2 branches missed.">			if (j % 16 == 15)</span>
<span class="nc" id="L1370">				System.out.println();</span>
		}
<span class="nc bnc" id="L1372" title="All 2 branches missed.">		if (!(j % 16 == 15))</span>
<span class="nc" id="L1373">			System.out.println();</span>

<span class="nc" id="L1375">		System.out.println(&quot;\nGrab the first 1000 shorts&quot;);</span>
<span class="nc" id="L1376">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc bnc" id="L1377" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1378">			System.out.print(r.nextShort() + &quot; &quot;);</span>
<span class="nc bnc" id="L1379" title="All 2 branches missed.">			if (j % 8 == 7)</span>
<span class="nc" id="L1380">				System.out.println();</span>
		}
<span class="nc bnc" id="L1382" title="All 2 branches missed.">		if (!(j % 8 == 7))</span>
<span class="nc" id="L1383">			System.out.println();</span>

<span class="nc" id="L1385">		System.out.println(&quot;\nGrab the first 1000 ints&quot;);</span>
<span class="nc" id="L1386">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc bnc" id="L1387" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1388">			System.out.print(r.nextInt() + &quot; &quot;);</span>
<span class="nc bnc" id="L1389" title="All 2 branches missed.">			if (j % 4 == 3)</span>
<span class="nc" id="L1390">				System.out.println();</span>
		}
<span class="nc bnc" id="L1392" title="All 2 branches missed.">		if (!(j % 4 == 3))</span>
<span class="nc" id="L1393">			System.out.println();</span>

<span class="nc" id="L1395">		System.out.println(&quot;\nGrab the first 1000 ints of different sizes&quot;);</span>
<span class="nc" id="L1396">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc" id="L1397">		int max = 1;</span>
<span class="nc bnc" id="L1398" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1399">			System.out.print(r.nextInt(max) + &quot; &quot;);</span>
<span class="nc" id="L1400">			max *= 2;</span>
<span class="nc bnc" id="L1401" title="All 2 branches missed.">			if (max &lt;= 0)</span>
<span class="nc" id="L1402">				max = 1;</span>
<span class="nc bnc" id="L1403" title="All 2 branches missed.">			if (j % 4 == 3)</span>
<span class="nc" id="L1404">				System.out.println();</span>
		}
<span class="nc bnc" id="L1406" title="All 2 branches missed.">		if (!(j % 4 == 3))</span>
<span class="nc" id="L1407">			System.out.println();</span>

<span class="nc" id="L1409">		System.out.println(&quot;\nGrab the first 1000 longs&quot;);</span>
<span class="nc" id="L1410">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc bnc" id="L1411" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1412">			System.out.print(r.nextLong() + &quot; &quot;);</span>
<span class="nc bnc" id="L1413" title="All 2 branches missed.">			if (j % 3 == 2)</span>
<span class="nc" id="L1414">				System.out.println();</span>
		}
<span class="nc bnc" id="L1416" title="All 2 branches missed.">		if (!(j % 3 == 2))</span>
<span class="nc" id="L1417">			System.out.println();</span>

<span class="nc" id="L1419">		System.out.println(&quot;\nGrab the first 1000 longs of different sizes&quot;);</span>
<span class="nc" id="L1420">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc" id="L1421">		long max2 = 1;</span>
<span class="nc bnc" id="L1422" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1423">			System.out.print(r.nextLong(max2) + &quot; &quot;);</span>
<span class="nc" id="L1424">			max2 *= 2;</span>
<span class="nc bnc" id="L1425" title="All 2 branches missed.">			if (max2 &lt;= 0)</span>
<span class="nc" id="L1426">				max2 = 1;</span>
<span class="nc bnc" id="L1427" title="All 2 branches missed.">			if (j % 4 == 3)</span>
<span class="nc" id="L1428">				System.out.println();</span>
		}
<span class="nc bnc" id="L1430" title="All 2 branches missed.">		if (!(j % 4 == 3))</span>
<span class="nc" id="L1431">			System.out.println();</span>

<span class="nc" id="L1433">		System.out.println(&quot;\nGrab the first 1000 floats&quot;);</span>
<span class="nc" id="L1434">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc bnc" id="L1435" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1436">			System.out.print(r.nextFloat() + &quot; &quot;);</span>
<span class="nc bnc" id="L1437" title="All 2 branches missed.">			if (j % 4 == 3)</span>
<span class="nc" id="L1438">				System.out.println();</span>
		}
<span class="nc bnc" id="L1440" title="All 2 branches missed.">		if (!(j % 4 == 3))</span>
<span class="nc" id="L1441">			System.out.println();</span>

<span class="nc" id="L1443">		System.out.println(&quot;\nGrab the first 1000 doubles&quot;);</span>
<span class="nc" id="L1444">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc bnc" id="L1445" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1446">			System.out.print(r.nextDouble() + &quot; &quot;);</span>
<span class="nc bnc" id="L1447" title="All 2 branches missed.">			if (j % 3 == 2)</span>
<span class="nc" id="L1448">				System.out.println();</span>
		}
<span class="nc bnc" id="L1450" title="All 2 branches missed.">		if (!(j % 3 == 2))</span>
<span class="nc" id="L1451">			System.out.println();</span>

<span class="nc" id="L1453">		System.out.println(&quot;\nGrab the first 1000 gaussian doubles&quot;);</span>
<span class="nc" id="L1454">		r = new MersenneTwisterFast(SEED);</span>
<span class="nc bnc" id="L1455" title="All 2 branches missed.">		for (j = 0; j &lt; 1000; j++) {</span>
<span class="nc" id="L1456">			System.out.print(r.nextGaussian() + &quot; &quot;);</span>
<span class="nc bnc" id="L1457" title="All 2 branches missed.">			if (j % 3 == 2)</span>
<span class="nc" id="L1458">				System.out.println();</span>
		}
<span class="nc bnc" id="L1460" title="All 2 branches missed.">		if (!(j % 3 == 2))</span>
<span class="nc" id="L1461">			System.out.println();</span>

<span class="nc" id="L1463">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>