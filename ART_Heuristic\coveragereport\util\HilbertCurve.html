<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>HilbertCurve</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">util</a> &gt; <span class="el_class">HilbertCurve</span></div><h1>HilbertCurve</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,293 of 1,293</td><td class="ctr2">0%</td><td class="bar">88 of 88</td><td class="ctr2">0%</td><td class="ctr1">57</td><td class="ctr2">57</td><td class="ctr1">207</td><td class="ctr2">207</td><td class="ctr1">10</td><td class="ctr2">10</td></tr></tfoot><tbody><tr><td id="a6"><a href="HilbertCurve.java.html#L309" class="el_method">HilbertCode2Coordinates(String, int)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="422" alt="422"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="40" alt="40"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">21</td><td class="ctr2" id="g0">21</td><td class="ctr1" id="h0">81</td><td class="ctr2" id="i0">81</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a3"><a href="HilbertCurve.java.html#L72" class="el_method">convert_hilbert_key(int, int, RefObject, RefObject)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="84" height="10" title="298" alt="298"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="33" height="10" title="11" alt="11"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">8</td><td class="ctr2" id="g2">8</td><td class="ctr1" id="h1">35</td><td class="ctr2" id="i1">35</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a4"><a href="HilbertCurve.java.html#L161" class="el_method">convert_hilbert_key_V1(int, int, RefObject, RefObject)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="82" height="10" title="290" alt="290"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="33" height="10" title="11" alt="11"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">8</td><td class="ctr2" id="g3">8</td><td class="ctr1" id="h2">35</td><td class="ctr2" id="i2">35</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="HilbertCurve.java.html#L248" class="el_method">Double2Bin(double)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="32" height="10" title="114" alt="114"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="48" height="10" title="16" alt="16"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f1">9</td><td class="ctr2" id="g1">9</td><td class="ctr1" id="h3">27</td><td class="ctr2" id="i3">27</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a9"><a href="HilbertCurve.java.html#L475" class="el_method">oneD_2_nD(double, int)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="12" height="10" title="44" alt="44"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h4">9</td><td class="ctr2" id="i4">9</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="HilbertCurve.java.html#L26" class="el_method">ADD(int, int, int, double[][], double[][])</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="10" height="10" title="37" alt="37"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a1"><a href="HilbertCurve.java.html#L35" class="el_method">ADD(int, int, int, int[][], int[][])</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="9" height="10" title="33" alt="33"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">3</td><td class="ctr2" id="i8">3</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a2"><a href="HilbertCurve.java.html#L40" class="el_method">Bin2Double(String)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="8" height="10" title="29" alt="29"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="12" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h5">8</td><td class="ctr2" id="i5">8</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a8"><a href="HilbertCurve.java.html#L18" class="el_method">main(String[])</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="6" height="10" title="23" alt="23"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="6" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h6">5</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a7"><a href="HilbertCurve.java.html#L5" class="el_method">HilbertCurve()</a></td><td class="bar" id="b9"/><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>