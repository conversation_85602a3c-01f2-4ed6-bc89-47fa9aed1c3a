<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test</a> &gt; <span class="el_source">ART.java</span></div><h1>ART.java</h1><pre class="source lang-java linenums">package test;

import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import util.RandomCreator;

public abstract class ART {
	public double[] min;
	public double[] max;
	public int dimension;
	public Random random;//随机数，可以选用CRandomNumber和MerseneTwisterFast(这个较好)
	public int pmCount;//算pm时设置的测试用例数量
	public int emCount;//算em时设置的测试用例数量
	public int tcCount;//算时间设置的测试用例数量
	public FailurePattern failPattern;//失效模式
	public double totalArea;
	public RandomCreator randomCreator;//随机生成器（特定区域，特定区域组中生成）

<span class="fc" id="L21">	public ART(double[] min, double[] max, Random random, FailurePattern failurePattern) {</span>
<span class="fc" id="L22">		this.min = min;</span>
<span class="fc" id="L23">		this.max = max;</span>

<span class="fc" id="L25">		this.dimension = min.length;</span>
<span class="fc" id="L26">		this.random = random;</span>

<span class="fc" id="L28">		failurePattern.min = min;</span>
<span class="fc" id="L29">		failurePattern.max = max;</span>
<span class="fc" id="L30">		failurePattern.dimension = dimension;</span>
<span class="fc" id="L31">		failurePattern.random = random;</span>
<span class="fc" id="L32">		failurePattern.genFailurePattern();</span>
<span class="fc" id="L33">		this.failPattern = failurePattern;</span>

<span class="fc" id="L35">		totalArea = 1.0;</span>
<span class="fc bfc" id="L36" title="All 2 branches covered.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="fc" id="L37">			totalArea *= (max[i] - min[i]);</span>
		}

<span class="fc" id="L40">		randomCreator = new RandomCreator(random, dimension, min, max);</span>
		
<span class="fc" id="L42">		this.pmCount=(int) Math.round(Math.log(0.5)/Math.log(1-failPattern.fail_rate)); //95% CI</span>
<span class="fc" id="L43">	}</span>

	public abstract NPoint generateNextTC();//核心方法，生成测试用例的方法
	
 
	public int run(){
<span class="nc" id="L49">		int count = 0;</span>
<span class="nc" id="L50">		NPoint p = generateNextTC();</span>
<span class="nc bnc" id="L51" title="All 2 branches missed.">		while (this.failPattern.isCorrect(p)) {</span>
<span class="nc" id="L52">			count++;</span>
<span class="nc" id="L53">			p = generateNextTC();</span>
		}
<span class="nc" id="L55">		return count;</span>
	}
	
	public int em() {
<span class="nc" id="L59">		int emTemp = 0;</span>
<span class="nc" id="L60">		NPoint p = generateNextTC();</span>
<span class="nc" id="L61">		int count = 0;</span>
<span class="nc bnc" id="L62" title="All 2 branches missed.">		while (count &lt; emCount) {</span>
<span class="nc bnc" id="L63" title="All 2 branches missed.">			if (!this.failPattern.isCorrect(p)) {</span>
<span class="nc" id="L64">				emTemp++;</span>
			}
<span class="nc" id="L66">			count++;</span>
<span class="nc" id="L67">			p = generateNextTC();</span>
		}
<span class="nc" id="L69">		return emTemp;</span>
	}
	
	public  void time2(){
		//生成测试用例所需时间
<span class="nc" id="L74">		int count = 0;</span>
<span class="nc" id="L75">		NPoint p = generateNextTC();</span>
<span class="nc bnc" id="L76" title="All 2 branches missed.">		while (count &lt; tcCount) {</span>
<span class="nc" id="L77">			count++;</span>
<span class="nc" id="L78">			p = generateNextTC();</span>
		}
<span class="nc" id="L80">	}</span>
	
	public  boolean pm(){
<span class="nc" id="L83">		boolean Detected = false;</span>
<span class="nc" id="L84">		int count = 0;</span>
<span class="nc" id="L85">		NPoint p = generateNextTC();</span>
<span class="nc bnc" id="L86" title="All 2 branches missed.">		while (count &lt; pmCount) {</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">			if (!this.failPattern.isCorrect(p)) {</span>
<span class="nc" id="L88">				Detected = true;</span>
<span class="nc" id="L89">				break;</span>
			}
<span class="nc" id="L91">			count++;</span>
<span class="nc" id="L92">			p = generateNextTC();</span>
		}
<span class="nc" id="L94">		return Detected;</span>
	}
	

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>