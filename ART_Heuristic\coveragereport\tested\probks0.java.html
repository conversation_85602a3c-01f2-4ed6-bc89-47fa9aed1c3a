<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>probks0.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">probks0.java</span></div><h1>probks0.java</h1><pre class="source lang-java linenums">package tested;

<span class="nc" id="L3">public class probks0 {</span>

<span class="nc" id="L5">	public  static double min = -50000;</span>
<span class="nc" id="L6">	public static  double max = 50000;</span>
<span class="nc" id="L7">	public  static double failureRate = 0.000387;</span>

	public double correct(double alam) {
<span class="nc" id="L10">		double EPS1 = 0.001, EPS2 = 1.0e-8;</span>
		double a2, fac, sum, term, termbf;

<span class="nc" id="L13">		a2 = -2.0 * alam * alam;</span>
<span class="nc" id="L14">		fac = 2.0;</span>
<span class="nc" id="L15">		sum = 0.0;</span>
<span class="nc" id="L16">		termbf = 0.0;</span>

<span class="nc bnc" id="L18" title="All 2 branches missed.">		for (int j = 1; j &lt;= 100; j++) {</span>
			// Original Pascal uses Pascal routine sqr(j) instead of j*j
<span class="nc" id="L20">			term = fac * Math.exp(a2 * (j * j));</span>
<span class="nc" id="L21">			sum += term;</span>

			// In the Numerical recipes reference, '&lt;=' is used instead of '&lt;'
<span class="nc bnc" id="L24" title="All 4 branches missed.">			if ((Math.abs(term) &lt; (EPS1 * termbf)) || (Math.abs(term) &lt; (EPS2 * sum)))</span>
<span class="nc" id="L25">				return sum;</span>
			else {
<span class="nc" id="L27">				fac = -fac;</span>
<span class="nc" id="L28">				termbf = Math.abs(term);</span>
			}
		}

<span class="nc" id="L32">		return 1.0;</span>

	}

	public boolean isCorrect(double x) {
		// System.out.println(&quot;correct:&quot;+correct(x));
		// System.out.println(&quot;wrong:&quot;+wrong(x));
<span class="nc bnc" id="L39" title="All 2 branches missed.">		return correct(x) == wrong(x);</span>
	}

	public double wrong(double alam) {
<span class="nc" id="L43">		double EPS1 = 0.001, EPS2 = 1.0e-8;</span>
		double a2, fac, sum, term, termbf;

		/* ERROR */
		/* a2 = -2.0 * alam * alam; */
<span class="nc" id="L48">		a2 = -2.1 * alam * alam;</span>
<span class="nc" id="L49">		fac = 2.0;</span>
<span class="nc" id="L50">		sum = 0.0;</span>
<span class="nc" id="L51">		termbf = 0.0;</span>

<span class="nc bnc" id="L53" title="All 2 branches missed.">		for (int j = 1; j &lt;= 100; j++) {</span>
			// Original Pascal uses Pascal routine sqr(j) instead of j*j
			/* ERROR */
			/* term = fac * exp(a2*(j*j)); */
<span class="nc" id="L57">			term = fac * Math.exp(a2 + (j * j));</span>
<span class="nc" id="L58">			sum += term;</span>

			/* ERROR */
			/*
			 * // In the Numerical recipes reference, '&lt;=' is used instead of '&lt;'
			 */
			/*
			 * if ( ( fabs(term) &lt; (EPS1*termbf) ) || ( fabs(term) &lt; (EPS2*sum) ) )
			 */
<span class="nc bnc" id="L67" title="All 4 branches missed.">			if ((Math.abs(term) &lt; (EPS1 * termbf)) || (Math.abs(term) &gt; (EPS2 * sum)))</span>
<span class="nc" id="L68">				return sum;</span>
			else {
				/* ERROR */
				/* fac = -fac; */
<span class="nc" id="L72">				fac = -term;</span>
<span class="nc" id="L73">				termbf = Math.abs(term);</span>
			}
		}

<span class="nc" id="L77">		return 1.0;</span>

	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>