<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MainMethodReality.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">a_main</a> &gt; <span class="el_source">MainMethodReality.java</span></div><h1>MainMethodReality.java</h1><pre class="source lang-java linenums">package a_main;

import java.util.Random;

import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tp._ND.ART_TP_ND;
import test.simulations.art_tpp.ART_TPP;
import test.simulations.fscs.FSCS_ND;
import test.simulations.rrt.RRT_ND;
import test.simulations.rrttp.hilbert.RRTtp1D;
import test.simulations.rt.RT_ND;
import util.data.RealityClasses;
import util.data.ZeroOneCreator;

<span class="nc" id="L19">public class MainMethodReality {</span>
	// TODO 完成参数化
	public static void main(String[] args) throws IllegalArgumentException, IllegalAccessException, NoSuchFieldException, SecurityException, InstantiationException {
<span class="nc" id="L22">		int times = 1000;</span>
<span class="nc" id="L23">		Class[] classes=RealityClasses.get();</span>
<span class="nc bnc" id="L24" title="All 2 branches missed.">		for (int j = 9; j &lt; classes.length; j++) {</span>
			
<span class="nc" id="L26">			Class tempClass=classes[j];</span>
<span class="nc" id="L27">			System.out.println(&quot;now class:&quot;+tempClass.getName());</span>
			
<span class="nc" id="L29">			double failureRate=tempClass.getDeclaredField(&quot;failureRate&quot;).getDouble(null);</span>
<span class="nc" id="L30">			double[] min=(double[] )tempClass.getDeclaredField(&quot;min&quot;).get(null);</span>
<span class="nc" id="L31">			double[] max=(double[]) tempClass.getDeclaredField(&quot;max&quot;).get(null);</span>
<span class="nc" id="L32">			int Dimension=(int)tempClass.getDeclaredField(&quot;Dimension&quot;).get(null);</span>
			
<span class="nc" id="L34">			FailurePattern failurePattern = new RealityFailPattern(tempClass.newInstance().getClass().getSimpleName());</span>
<span class="nc" id="L35">			failurePattern.fail_rate = failureRate;</span>
<span class="nc" id="L36">			failurePattern.min = min;</span>
<span class="nc" id="L37">			failurePattern.max = max;</span>
<span class="nc" id="L38">			failurePattern.dimension = Dimension;</span>

			//rt
			/*int fm = 0;
			long startTime = System.currentTimeMillis();
			for (int i = 0; i &lt; times; i++) {
				RT_ND rt = new RT_ND(min, max, new Random(i * 3), failurePattern);
				int temp = rt.run();
				fm += temp;
			}
			long endTime = System.currentTimeMillis();

			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
		
			//rrt
			double r=1.5;
			if(Dimension==1) {
				r=0.75;
			}else {
				r=1.5;
			}
			fm=0;
			startTime = System.currentTimeMillis();
			for (int i = 0; i &lt; times; i++) {
				RRT_ND rt = new RRT_ND(min, max,  failurePattern,new Random(i * 3),r);
				int temp = rt.run();
				fm += temp;
			}
			endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
			
			//fscs
			fm=0;
			int s=10;
			startTime = System.currentTimeMillis();
			for (int i = 0; i &lt; times; i++) {
				FSCS_ND rt = new FSCS_ND(min, max, s, failurePattern,new Random(i * 3));
				int temp = rt.run();
				fm += temp;
			}
			endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
			
			
			//art_b
			fm=0;
			startTime = System.currentTimeMillis();
			for (int i = 0; i &lt; times; i++) {
				ART_B_ND rt = new ART_B_ND(min, max, new Random(i * 3),failurePattern);
				int temp = rt.run();
				fm += temp;
			}
			endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
			
			//art_rp
			fm=0;
			startTime = System.currentTimeMillis();
			for (int i = 0; i &lt; times; i++) {
				ART_RP_ND rt = new ART_RP_ND(min, max, new Random(i * 3),failurePattern);
				int temp = rt.run();
				fm += temp;
			}
			endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
			
			
			//art_tpp
			fm=0;
			int k=10;
			startTime = System.currentTimeMillis();
			for (int i = 0; i &lt; times; i++) {
				ART_TPP rt = new ART_TPP(min, max, new Random(i * 3),failurePattern,k);
				int temp = rt.run();
				fm += temp;
			}
			endTime = System.currentTimeMillis();
			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
*/			
			//art_tp
<span class="nc" id="L118">			int fm=0;</span>
<span class="nc" id="L119">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L121">				ART_TP_ND rt = new ART_TP_ND(min, max, failurePattern,new Random(i * 3));</span>
<span class="nc" id="L122">				int temp = rt.run();</span>
<span class="nc" id="L123">				fm += temp;</span>
			}
<span class="nc" id="L125">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L126">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//my method
			//1dimension
//			fm=0;
//			startTime = System.currentTimeMillis();
//			for (int i = 0; i &lt; times; i++) {
//				RRTtp1D rt = new RRTtp1D(min, max, new Random(i * 3),failurePattern,k);
//				int temp = rt.run();
//				fm += temp;
//			}
//			endTime = System.currentTimeMillis();
//			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));
//			
		}
<span class="nc" id="L141">	}</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>