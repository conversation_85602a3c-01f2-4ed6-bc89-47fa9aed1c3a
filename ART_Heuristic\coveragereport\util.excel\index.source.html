<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>util.excel</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.html" class="el_class">Classes</a><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <span class="el_package">util.excel</span></div><h1>util.excel</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">295 of 295</td><td class="ctr2">0%</td><td class="bar">30 of 30</td><td class="ctr2">0%</td><td class="ctr1">32</td><td class="ctr2">32</td><td class="ctr1">69</td><td class="ctr2">69</td><td class="ctr1">17</td><td class="ctr2">17</td><td class="ctr1">6</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a1"><a href="Excel.java.html" class="el_source">Excel.java</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="128" alt="128"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="105" height="10" title="14" alt="14"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">13</td><td class="ctr2" id="g0">13</td><td class="ctr1" id="h0">31</td><td class="ctr2" id="i0">31</td><td class="ctr1" id="j0">6</td><td class="ctr2" id="k0">6</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a0"><a href="EmResultExcel.java.html" class="el_source">EmResultExcel.java</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="91" height="10" title="98" alt="98"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">13</td><td class="ctr2" id="g1">13</td><td class="ctr1" id="h1">19</td><td class="ctr2" id="i1">19</td><td class="ctr1" id="j1">5</td><td class="ctr2" id="k1">5</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a2"><a href="FontAndStyle.java.html" class="el_source">FontAndStyle.java</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="61" height="10" title="66" alt="66"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"/><td class="ctr2" id="e2">n/a</td><td class="ctr1" id="f2">5</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h2">18</td><td class="ctr2" id="i2">18</td><td class="ctr1" id="j2">5</td><td class="ctr2" id="k2">5</td><td class="ctr1" id="l0">3</td><td class="ctr2" id="m0">3</td></tr><tr><td id="a3"><a href="Paper1Excel.java.html" class="el_source">Paper1Excel.java</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">1</td><td class="ctr2" id="i3">1</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>