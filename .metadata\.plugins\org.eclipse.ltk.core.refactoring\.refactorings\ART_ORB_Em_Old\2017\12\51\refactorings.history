<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/simulations/art_rp/ART_RP_ND.java&apos; to &apos;ART_RP_ND_Em.java&apos;" description="Rename resource &apos;ART_RP_ND.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/art_rp/ART_RP_ND.java" name="ART_RP_ND_Em.java" stamp="1513584278606" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/simulations/art_rp/ART_RP_ND_Em.java&apos; to &apos;ART_RP_ND_SimEm.java&apos;" description="Rename resource &apos;ART_RP_ND_Em.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/art_rp/ART_RP_ND_Em.java" name="ART_RP_ND_SimEm.java" stamp="1513592164393" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/simulations/art_tpp/ART_TPP.java&apos; to &apos;ART_TPP_SimEm.java&apos;" description="Rename resource &apos;ART_TPP.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/art_tpp/ART_TPP.java" name="ART_TPP_SimEm.java" stamp="1513593024151" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/simulations/art_dc/RRT_DC.java&apos; to &apos;RRT_DC_SimEm.java&apos;" description="Rename resource &apos;RRT_DC.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/art_dc/RRT_DC.java" name="RRT_DC_SimEm.java" stamp="1513593890221" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/simulations/art_orb/ORB_RRT_ND.java&apos; to &apos;ORB_RRT_ND_SimEm.java&apos;" description="Rename resource &apos;ORB_RRT_ND.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/art_orb/ORB_RRT_ND.java" name="ORB_RRT_ND_SimEm.java" stamp="1513661032410" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/simulations/art_b/ART_B_ND.java&apos; to &apos;ART_B_ND_SimEm.java&apos;" description="Rename resource &apos;ART_B_ND.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/art_b/ART_B_ND.java" name="ART_B_ND_SimEm.java" stamp="1513665117699" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/rt/RT_ND.java&apos; to &apos;RT_ND_E_Time.java&apos;" description="Rename resource &apos;RT_ND.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/rt/RT_ND.java" name="RT_ND_E_Time.java" stamp="1513760457670" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/rt/RT_ND.java&apos; to &apos;RT_ND_Em.java&apos;" description="Rename resource &apos;RT_ND.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/rt/RT_ND.java" name="RT_ND_Em.java" stamp="1513769871261" updateReferences="true"/>
</session>