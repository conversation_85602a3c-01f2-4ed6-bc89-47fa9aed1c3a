<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>_2DRegion.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.TD</a> &gt; <span class="el_source">_2DRegion.java</span></div><h1>_2DRegion.java</h1><pre class="source lang-java linenums">package datastructure.TD;

/**
 * two dimension Region
 */
public class _2DRegion {
	public _2DPoint min;
	public _2DPoint max;

<span class="nc" id="L10">	public _2DRegion() {</span>

<span class="nc" id="L12">	}</span>

<span class="nc" id="L14">	public _2DRegion(_2DPoint min, _2DPoint max) {</span>
<span class="nc" id="L15">		this.min = min;</span>
<span class="nc" id="L16">		this.max = max;</span>
<span class="nc" id="L17">	}</span>

	public double size() {
<span class="nc" id="L20">		return Math.abs(max.y - min.y) * Math.abs(max.x - min.x);</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>