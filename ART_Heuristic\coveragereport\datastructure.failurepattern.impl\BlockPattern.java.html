<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>BlockPattern.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.failurepattern.impl</a> &gt; <span class="el_source">BlockPattern.java</span></div><h1>BlockPattern.java</h1><pre class="source lang-java linenums">package datastructure.failurepattern.impl;

import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;

<span class="fc" id="L8">public class BlockPattern extends FailurePattern {</span>
	private double eachFailLength;
	private double fail_regionS;
	private double[] fail_start;

	@Override
	public void genFailurePattern() {
<span class="fc" id="L15">		fail_start = new double[this.dimension];</span>
<span class="fc" id="L16">		double totalArea = 1.0;</span>
<span class="fc bfc" id="L17" title="All 2 branches covered.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="fc" id="L18">			totalArea *= (max[i] - min[i]);</span>
		}
<span class="fc" id="L20">		this.fail_regionS = this.fail_rate * totalArea;</span>
<span class="fc" id="L21">		this.eachFailLength = Math.pow(fail_regionS, 1 / (double) this.dimension);</span>
		// System.out.println(&quot;EachFailArea:&quot;+this.EachFailLength);
<span class="fc bfc" id="L23" title="All 2 branches covered.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="fc" id="L24">			fail_start[i] = random.nextDouble() * (max[i] - min[i] - eachFailLength) + min[i];</span>
		}
<span class="fc" id="L26">	}</span>

	@Override
	public boolean isCorrect(NPoint p) {
<span class="nc" id="L30">		double[] xn = p.getXn();</span>
<span class="nc" id="L31">		boolean lead2Fail = false;</span>
<span class="nc bnc" id="L32" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc bnc" id="L33" title="All 4 branches missed.">			if (xn[i] &lt; this.fail_start[i] || xn[i] &gt; (this.fail_start[i] + eachFailLength)) {</span>
<span class="nc" id="L34">				lead2Fail = true;</span>
			}
		}
		// System.out.println(Arrays.toString(nDPoints));
		// System.out.println(&quot;isFail:&quot;+lead2Fail);
		// lead2Fail=false,失效，=true不失效
<span class="nc" id="L40">		return lead2Fail;</span>
	}

	@Override
	public void showFailurePattern() {
		// print block pattern
<span class="nc" id="L46">		System.out.println(&quot;Pattern type:&quot; + getClass().getSimpleName() + &quot; Dimension:&quot; + this.dimension);</span>
<span class="nc" id="L47">		System.out.print(&quot;[&quot;);</span>
<span class="nc bnc" id="L48" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>

<span class="nc" id="L50">			System.out.print(&quot;(&quot; + fail_start[i] + &quot;,&quot; + (fail_start[i] + eachFailLength) + &quot;)&quot;);</span>

<span class="nc bnc" id="L52" title="All 2 branches missed.">			if (i != this.dimension - 1) {</span>
<span class="nc" id="L53">				System.out.print(&quot;,&quot;);</span>
			}
<span class="nc bnc" id="L55" title="All 2 branches missed.">			if ((i + 1) % 5 == 0) {</span>
<span class="nc" id="L56">				System.out.println();</span>
			}
		}
<span class="nc" id="L59">		System.out.print(&quot;]&quot;);</span>
<span class="nc" id="L60">		System.out.println();</span>
<span class="nc" id="L61">	}</span>

	public static void main(String[] args) {
<span class="nc" id="L64">		BlockPattern pattern=new BlockPattern();</span>
<span class="nc" id="L65">		pattern.min=new double[]{0,0,0};</span>
<span class="nc" id="L66">		pattern.max=new double[]{1,1,1};</span>
<span class="nc" id="L67">		pattern.dimension=3;</span>
		
<span class="nc" id="L69">		pattern.random=new Random(3);</span>
		
<span class="nc" id="L71">		pattern.fail_rate=0.005;</span>
		
<span class="nc" id="L73">		pattern.genFailurePattern();</span>
		
<span class="nc" id="L75">		pattern.showFailurePattern();	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>