<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRT_ND</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.rrt</a> &gt; <span class="el_class">RRT_ND</span></div><h1>RRT_ND</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">502 of 682</td><td class="ctr2">26%</td><td class="bar">34 of 48</td><td class="ctr2">29%</td><td class="ctr1">24</td><td class="ctr2">33</td><td class="ctr1">111</td><td class="ctr2">146</td><td class="ctr1">6</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a4"><a href="RRT_ND.java.html#L173" class="el_method">testEm(int, double)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="123" alt="123"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h0">29</td><td class="ctr2" id="i0">29</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="RRT_ND.java.html#L142" class="el_method">testFm()</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="95" height="10" title="98" alt="98"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h1">23</td><td class="ctr2" id="i1">23</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a8"><a href="RRT_ND.java.html#L105" class="el_method">time()</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="87" height="10" title="90" alt="90"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">6</td><td class="ctr2" id="g0">6</td><td class="ctr1" id="h2">20</td><td class="ctr2" id="i3">20</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a7"><a href="RRT_ND.java.html#L210" class="el_method">testTCTime(int, int)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="74" height="10" title="76" alt="76"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h3">15</td><td class="ctr2" id="i5">15</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a6"><a href="RRT_ND.java.html#L231" class="el_method">testPm(int, double)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="60" height="10" title="62" alt="62"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="72" height="10" title="6" alt="6"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h4">15</td><td class="ctr2" id="i6">15</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a0"><a href="RRT_ND.java.html#L38" class="el_method">calculateRadius(int)</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="46" height="10" title="48" alt="48"/><img src="../.resources/greenbar.gif" width="67" height="10" title="69" alt="69"/></td><td class="ctr2" id="c2">59%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="48" height="10" title="4" alt="4"/><img src="../.resources/greenbar.gif" width="48" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">50%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g2">5</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i4">16</td><td class="ctr1" id="j6">0</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a2"><a href="RRT_ND.java.html#L23" class="el_method">main(String[])</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a1"><a href="RRT_ND.java.html#L64" class="el_method">generateNextTC()</a></td><td class="bar" id="b7"><img src="../.resources/greenbar.gif" width="93" height="10" title="96" alt="96"/></td><td class="ctr2" id="c0">100%</td><td class="bar" id="d6"><img src="../.resources/greenbar.gif" width="120" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">100%</td><td class="ctr1" id="f7">0</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h7">0</td><td class="ctr2" id="i2">22</td><td class="ctr1" id="j7">0</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a3"><a href="RRT_ND.java.html#L30" class="el_method">RRT_ND(double[], double[], FailurePattern, Random, double)</a></td><td class="bar" id="b8"><img src="../.resources/greenbar.gif" width="14" height="10" title="15" alt="15"/></td><td class="ctr2" id="c1">100%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">0</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">0</td><td class="ctr2" id="i7">4</td><td class="ctr1" id="j8">0</td><td class="ctr2" id="k8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>