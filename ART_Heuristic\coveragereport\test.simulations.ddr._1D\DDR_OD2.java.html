<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>DDR_OD2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.ddr._1D</a> &gt; <span class="el_source">DDR_OD2.java</span></div><h1>DDR_OD2.java</h1><pre class="source lang-java linenums">package test.simulations.ddr._1D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class DDR_OD2 {
	public static void main(String[] args) {
<span class="nc" id="L10">		int times = 70;</span>
<span class="nc" id="L11">		long sums = 0;</span>
<span class="nc" id="L12">		int temp = 0;</span>
<span class="nc" id="L13">		int s = 10;</span>
		//////////////
		///////////////
<span class="nc" id="L16">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L17" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L18">			DDR_OD2 rrt_od = new DDR_OD2(0, 1, 0.75, s, 0.0001, i * 3);// 0.002 refers to failure rate</span>
<span class="nc" id="L19">			temp = rrt_od.run();</span>
<span class="nc" id="L20">			sums += temp;</span>
		}
<span class="nc" id="L22">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L23">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L24">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L25">	}</span>
	double min;
	double max;
	double fail_start;
	double fail_rate;
	double R;
	int s;
	int randomseed;

<span class="nc" id="L34">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	public DDR_OD2(double min, double max, double r, int s, double fail_rate, int randomseed) {
<span class="nc" id="L37">		super();</span>
<span class="nc" id="L38">		this.min = min;</span>
<span class="nc" id="L39">		this.max = max;</span>
<span class="nc" id="L40">		R = r;</span>
<span class="nc" id="L41">		this.s = s;</span>
<span class="nc" id="L42">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L43">		this.randomseed = randomseed;</span>
<span class="nc" id="L44">	}</span>

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L47" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L48">			return false;</span>
		} else {
<span class="nc" id="L50">			return true;</span>
		}
	}

	public TestCase randomTC(Random random) {
<span class="nc" id="L55">		TestCase temp = new TestCase();</span>
<span class="nc" id="L56">		double temp_value = random.nextDouble() * (max - min) + min;</span>
<span class="nc" id="L57">		temp.p = temp_value;</span>
<span class="nc" id="L58">		return temp;</span>
	}

	public int run() {
<span class="nc" id="L62">		Random random = new Random(randomseed);</span>
<span class="nc" id="L63">		fail_start = random.nextDouble() * (1 - fail_rate);</span>
<span class="nc" id="L64">		int count = 0;// 记录测试用例数量</span>
<span class="nc" id="L65">		int rrtcount = 0;</span>
<span class="nc" id="L66">		int fscscount = 0;</span>
<span class="nc" id="L67">		int _10CandidateCount = 0;// 每十个一次的数量</span>
<span class="nc" id="L68">		TestCase p = randomTC(random);// 第一个测试用例</span>
		// System.out.println(&quot;p0:&quot; + p.p);
<span class="nc bnc" id="L70" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
			// different radius//s*(_10CandidateCount+1
<span class="nc" id="L72">			double radius = R / (s + _10CandidateCount);</span>
			// 生成s个候选测试用例,从s个候选测试用例中挑选符合要求的测试用例
<span class="nc" id="L74">			boolean all_s_has_E_flag = true;</span>
<span class="nc" id="L75">			double TS2C[] = new double[s];</span>
<span class="nc" id="L76">			double Cvalue[] = new double[s];</span>
<span class="nc bnc" id="L77" title="All 2 branches missed.">			for (int k = 0; k &lt; s; k++) {</span>
				// 生成一个候选测试用例
				// System.out.println(&quot;k&quot;+k);
<span class="nc" id="L80">				TestCase ck = randomTC(random);</span>
<span class="nc" id="L81">				boolean this_ck_has_E_flag = false;</span>
<span class="nc" id="L82">				double min = Double.MAX_VALUE;</span>
<span class="nc bnc" id="L83" title="All 2 branches missed.">				for (int i = 0; i &lt; tests.size(); i++) {</span>
<span class="nc bnc" id="L84" title="All 4 branches missed.">					if ((ck.p &gt; (tests.get(i).p - radius) &amp;&amp; (ck.p &lt; (tests.get(i).p + radius)))) {</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">						if (min &gt; Math.abs(ck.p - tests.get(i).p)) {</span>
<span class="nc" id="L86">							min = Math.abs(ck.p - tests.get(i).p);</span>
<span class="nc" id="L87">							TS2C[k] = min;</span>
<span class="nc" id="L88">							Cvalue[k] = ck.p;</span>
						}
<span class="nc" id="L90">						this_ck_has_E_flag = true;</span>
					}
				}
<span class="nc bnc" id="L93" title="All 2 branches missed.">				if (!this_ck_has_E_flag) {</span>
<span class="nc" id="L94">					all_s_has_E_flag = false;</span>
<span class="nc" id="L95">					p = new TestCase();</span>
<span class="nc" id="L96">					p.p = ck.p;</span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">					if (!isCorrect(p.p)) {</span>
						// System.out.println(count+&quot;,&quot;+rrtcount+&quot;,&quot;+fscscount);
<span class="nc" id="L99">						return count;</span>
					} else {
<span class="nc" id="L101">						count++;</span>
<span class="nc" id="L102">						rrtcount++;</span>
<span class="nc" id="L103">						tests.add(p);</span>
					}
				}
			}
<span class="nc bnc" id="L107" title="All 2 branches missed.">			if (all_s_has_E_flag) {</span>
<span class="nc" id="L108">				double max = 0;</span>
<span class="nc" id="L109">				int index = 0;</span>
<span class="nc bnc" id="L110" title="All 2 branches missed.">				for (int i = 0; i &lt; TS2C.length; i++) {</span>
<span class="nc bnc" id="L111" title="All 2 branches missed.">					if (max &lt; TS2C[i]) {</span>
<span class="nc" id="L112">						max = TS2C[i];</span>
<span class="nc" id="L113">						index = i;</span>
					}
				}
<span class="nc" id="L116">				p = new TestCase();</span>
<span class="nc" id="L117">				p.p = Cvalue[index];</span>
				// System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.p + &quot; fscs&quot;);
<span class="nc bnc" id="L119" title="All 2 branches missed.">				if (!isCorrect(p.p)) {</span>
					// System.out.println(count+&quot;,&quot;+rrtcount+&quot;,&quot;+fscscount);
<span class="nc" id="L121">					return count;</span>
				} else {
<span class="nc" id="L123">					count++;</span>
<span class="nc" id="L124">					fscscount++;</span>
<span class="nc" id="L125">					tests.add(p);</span>
				}
			}
<span class="nc" id="L128">			_10CandidateCount++;</span>
		}
		// System.out.println(count+&quot;,&quot;+rrtcount+&quot;,&quot;+fscscount);
<span class="nc" id="L131">		return count;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>