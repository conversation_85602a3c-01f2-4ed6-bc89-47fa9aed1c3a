<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>Excel</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">util.excel</a> &gt; <span class="el_class">Excel</span></div><h1>Excel</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">128 of 128</td><td class="ctr2">0%</td><td class="bar">14 of 14</td><td class="ctr2">0%</td><td class="ctr1">13</td><td class="ctr2">13</td><td class="ctr1">31</td><td class="ctr2">31</td><td class="ctr1">6</td><td class="ctr2">6</td></tr></tfoot><tbody><tr><td id="a4"><a href="Excel.java.html#L26" class="el_method">writeCell2Sheet(HSSFSheet, String[][])</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="40" alt="40"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h1">8</td><td class="ctr2" id="i1">8</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a5"><a href="Excel.java.html#L41" class="el_method">writeCell2Sheet(HSSFSheet, String[][], int, int)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="114" height="10" title="38" alt="38"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h2">6</td><td class="ctr2" id="i2">6</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a3"><a href="Excel.java.html#L51" class="el_method">saveAsFile(String)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="108" height="10" title="36" alt="36"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">2</td><td class="ctr2" id="g2">2</td><td class="ctr1" id="h0">13</td><td class="ctr2" id="i0">13</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="Excel.java.html#L16" class="el_method">Excel()</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="24" height="10" title="8" alt="8"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"/><td class="ctr2" id="e3">n/a</td><td class="ctr1" id="f3">1</td><td class="ctr2" id="g3">1</td><td class="ctr1" id="h3">2</td><td class="ctr2" id="i3">2</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a0"><a href="Excel.java.html#L21" class="el_method">createSheet(String)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="15" height="10" title="5" alt="5"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"/><td class="ctr2" id="e4">n/a</td><td class="ctr1" id="f4">1</td><td class="ctr2" id="g4">1</td><td class="ctr1" id="h4">1</td><td class="ctr2" id="i4">1</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a2"><a href="Excel.java.html#L74" class="el_method">main(String[])</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="3" height="10" title="1" alt="1"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"/><td class="ctr2" id="e5">n/a</td><td class="ctr1" id="f5">1</td><td class="ctr2" id="g5">1</td><td class="ctr1" id="h5">1</td><td class="ctr2" id="i5">1</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>