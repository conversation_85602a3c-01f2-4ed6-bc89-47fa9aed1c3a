<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_Center_TD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_center</a> &gt; <span class="el_source">ART_Center_TD.java</span></div><h1>ART_Center_TD.java</h1><pre class="source lang-java linenums">package test.simulations.art_center;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import datastructure.TD._2DPoint;
import datastructure.TD._2DRegion;
import util.CRandomNumber;

/*
 * 在最大的划分区间（RP）中间生成测试用例
 * */
@Deprecated
public class ART_Center_TD {
	public static void main(String[] args) {
<span class="nc" id="L17">		int times = 3000;</span>
<span class="nc" id="L18">		long sums = 0;</span>
<span class="nc" id="L19">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L20" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L21">			ART_Center_TD art_RP_TD = new ART_Center_TD(i * 3, new _2DRegion(new _2DPoint(0, 0), new _2DPoint(1, 1)), 0.01);</span>
<span class="nc" id="L22">			int fm = art_RP_TD.run();</span>
<span class="nc" id="L23">			sums += fm;</span>
		}
<span class="nc" id="L25">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L26">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L27">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L28">	}</span>
	long seed;
	public _2DRegion initRegion;
	public double xfail_start;
	public double yfail_start;
	public double fail_rate;
<span class="nc" id="L34">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L36">	ArrayList&lt;_2DRegion&gt; regions = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L38">	public ART_Center_TD(long seed, _2DRegion initRegion, double fail_rate) {</span>
<span class="nc" id="L39">		this.seed = seed;</span>
<span class="nc" id="L40">		this.initRegion = initRegion;</span>
<span class="nc" id="L41">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L42">		this.seed = seed;</span>
<span class="nc" id="L43">		CRandomNumber.initSeed(seed);</span>
<span class="nc" id="L44">	}</span>

	public boolean isCorrect(TestCase p) {
<span class="nc" id="L47">		double x = p.p;</span>
<span class="nc" id="L48">		double y = p.q;</span>
<span class="nc" id="L49">		double bianchang = Math.pow(fail_rate, 0.5);</span>
<span class="nc bnc" id="L50" title="All 8 branches missed.">		if ((x &gt; xfail_start &amp;&amp; x &lt; xfail_start + bianchang) &amp;&amp; (y &gt; yfail_start &amp;&amp; y &lt; yfail_start + bianchang)) {</span>
<span class="nc" id="L51">			return false;</span>
		} else {
<span class="nc" id="L53">			return true;</span>
		}
	}

	public int run() {
<span class="nc" id="L58">		Random random = new Random(seed);</span>
<span class="nc" id="L59">		int count = 0;</span>
<span class="nc" id="L60">		xfail_start = random.nextDouble() * (1.0 - Math.sqrt(fail_rate));</span>
<span class="nc" id="L61">		yfail_start = random.nextDouble() * (1.0 - Math.sqrt(fail_rate));</span>
<span class="nc" id="L62">		_2DRegion region = initRegion;</span>
<span class="nc" id="L63">		TestCase p = new TestCase();</span>
<span class="nc" id="L64">		p.p = random.nextDouble() * (region.max.x - region.min.x) + region.min.x;</span>
<span class="nc" id="L65">		p.q = random.nextDouble() * (region.max.y - region.min.y) + region.min.y;</span>
<span class="nc" id="L66">		tests.add(p);</span>
		//System.out.println(p);
<span class="nc" id="L68">		regions.add(initRegion);</span>
<span class="nc bnc" id="L69" title="All 2 branches missed.">		while (isCorrect(p)) {</span>
<span class="nc" id="L70">			count++;</span>
			// 找出最大区域
<span class="nc" id="L72">			double maxsize = 0;</span>
<span class="nc" id="L73">			_2DRegion maxregion = null;</span>
<span class="nc" id="L74">			int maxregion_index = 0;</span>
<span class="nc bnc" id="L75" title="All 2 branches missed.">			for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc" id="L76">				_2DRegion temp = regions.get(i);</span>
<span class="nc bnc" id="L77" title="All 2 branches missed.">				if (temp.size() &gt; maxsize) {</span>
<span class="nc" id="L78">					maxsize = temp.size();</span>
<span class="nc" id="L79">					maxregion = temp;</span>
<span class="nc" id="L80">					maxregion_index = i;</span>
				}
			}
			//
<span class="nc" id="L84">			regions.remove(maxregion_index);</span>
			// generate next one test case
<span class="nc" id="L86">			double xmin = maxregion.min.x;</span>
<span class="nc" id="L87">			double xmax = maxregion.max.x;</span>
<span class="nc" id="L88">			double ymin = maxregion.min.y;</span>
<span class="nc" id="L89">			double ymax = maxregion.max.y;</span>
			// System.out.println(xmin + &quot;,&quot; + xmax + &quot; &quot; + ymin + &quot;,&quot; + ymax);
<span class="nc" id="L91">			p = new TestCase();</span>
<span class="nc" id="L92">			double eachtemplength = 1.0 / 8.0;</span>
<span class="nc" id="L93">			double fenshu1 = 2;</span>
<span class="nc" id="L94">			double fenshu2 = 6;</span>
<span class="nc" id="L95">			double newstart = xmin + eachtemplength * fenshu1 * (xmax - xmin);</span>
<span class="nc" id="L96">			double newend = xmin + eachtemplength * fenshu2 * (xmax - xmin);</span>
<span class="nc" id="L97">			p.p = random.nextDouble() * (newend - newstart) + newstart;</span>
<span class="nc" id="L98">			double newstart1 = newstart = ymin + eachtemplength * fenshu1 * (ymax - ymin);</span>
<span class="nc" id="L99">			double newend1 = ymin + eachtemplength * fenshu2 * (ymax - ymin);</span>
<span class="nc" id="L100">			p.q = random.nextDouble() * (newend1 - newstart1) + newstart1;</span>
			// 添加fscs
			// int s = 3;
			// double TS2C[] = new double[s];
			// for (int i = 0; i &lt; TS2C.length; i++) {
			// TS2C[i] = -1;
			// }
			// double Pvalue[] = new double[s];
			// double Qvalue[] = new double[s];
			// double maxMin = 0;
			// int index = 0;
			// for (int k = 0; k &lt; 3; k++) {
			// // 生成一个候选测试用例
			// TestCase ck = new TestCase();
			// ck.p = random.nextDouble() * (newend - newstart) + newstart;
			// ck.q = random.nextDouble() * (newend1 - newstart1) + newstart1;
			// // System.out.println(&quot;first:&quot; + ck.p + &quot; Second&quot; + ck.q);
			// double min = Double.MAX_VALUE;
			// for (int i = 0; i &lt; tests.size(); i++) {
			// double distance = Math
			// .sqrt(Math.pow(ck.p - tests.get(i).p, 2) + Math.pow(ck.q - tests.get(i).q,
			// 2));
			// if (min &gt; distance) {
			// min = distance;
			// TS2C[k] = min;
			// Pvalue[k] = ck.p;
			// Qvalue[k] = ck.q;
			// }
			//
			// }
			// if (maxMin &lt; TS2C[k]) {
			// maxMin = TS2C[k];
			// index = k;
			// }
			// }
			// p.p = Pvalue[index];
			// p.q = Qvalue[index];
			// p.p = random.nextDouble() * (xmax - xmin) + xmin;
			// p.q = random.nextDouble() * (ymax - ymin) + ymin;
<span class="nc" id="L139">			tests.add(p);</span>
			//System.out.println(p);
			// System.out.println(isCorrect(p));
			// add four regions
<span class="nc" id="L143">			_2DRegion first = new _2DRegion(new _2DPoint(xmin, ymin), new _2DPoint(p.p, p.q));</span>
<span class="nc" id="L144">			_2DRegion second = new _2DRegion(new _2DPoint(p.p, ymin), new _2DPoint(xmax, p.q));</span>
<span class="nc" id="L145">			_2DRegion third = new _2DRegion(new _2DPoint(p.p, p.q), new _2DPoint(xmax, ymax));</span>
<span class="nc" id="L146">			_2DRegion fourth = new _2DRegion(new _2DPoint(xmin, p.q), new _2DPoint(p.p, ymax));</span>
<span class="nc" id="L147">			regions.add(first);</span>
<span class="nc" id="L148">			regions.add(second);</span>
<span class="nc" id="L149">			regions.add(third);</span>
<span class="nc" id="L150">			regions.add(fourth);</span>
		}
<span class="nc" id="L152">		return count;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>