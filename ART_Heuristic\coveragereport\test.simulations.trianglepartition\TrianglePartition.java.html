<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>TrianglePartition.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.trianglepartition</a> &gt; <span class="el_source">TrianglePartition.java</span></div><h1>TrianglePartition.java</h1><pre class="source lang-java linenums">package test.simulations.trianglepartition;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import util.CRandomNumber;

//TODO 三角形分割方法，效果一般
public class TrianglePartition {
	public static void main(String[] args) {
<span class="nc" id="L12">		double[] min = { 0, 0 };</span>
<span class="nc" id="L13">		double[] max = { 1, 1 };</span>

		// 测试随机三角形点
		/*
		 * TrianglePartition test = new TrianglePartition(min, max, 3, 0.001);
		 * TriangleRegion testtri = new TriangleRegion(new TestCase(0, 0.5), new
		 * TestCase(0.5,1), new TestCase(1, 0)); StdDraw.line(0, 0.5, 0.5, 1.0);
		 * StdDraw.line(0.5, 1, 1, 0); StdDraw.line(0, 0.5, 1,0);
		 * System.out.println(testtri.getSquare()); for (int i = 0; i &lt; 100; i++) {
		 * TestCase testcase = test.randomTC3(new Random(i*3),testtri);
		 * StdDraw.point(testcase.p, testcase.q); System.out.println(testcase);
		 * System.out.println(&quot;=======================================&quot;); }
		 */
<span class="nc" id="L26">		int sum = 0;</span>
<span class="nc" id="L27">		int times = 3000;</span>
<span class="nc bnc" id="L28" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L29">			TrianglePartition test = new TrianglePartition(min, max, i, 0.01);</span>
<span class="nc" id="L30">			int count = test.run();</span>
<span class="nc" id="L31">			sum += count;</span>
<span class="nc" id="L32">			System.out.println(count);</span>
		}
<span class="nc" id="L34">		System.out.println(&quot;fm:&quot; + (sum / (double) times));</span>
<span class="nc" id="L35">	}</span>
	double[] min;
	double[] max;
	long seed;
	double[] fail_start;
	double fail_regionS;

	double fail_rate;

<span class="nc" id="L44">	public TrianglePartition(double[] min, double[] max, long seed, double fail_rate) {</span>
<span class="nc" id="L45">		this.min = min;</span>
<span class="nc" id="L46">		this.max = max;</span>
<span class="nc" id="L47">		this.seed = seed;</span>
<span class="nc" id="L48">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L49">		fail_start = new double[min.length];</span>
<span class="nc" id="L50">		this.fail_regionS = fail_rate * (max[1] - min[1]) * (max[0] - min[0]);</span>
<span class="nc" id="L51">		CRandomNumber.initSeed(this.seed);</span>
<span class="nc" id="L52">	}</span>

	public boolean isCorrect(TestCase p) {
<span class="nc" id="L55">		boolean isCorrect = true;</span>
<span class="nc bnc" id="L56" title="All 4 branches missed.">		if (p.p &gt; fail_start[0] &amp;&amp; p.p &lt; fail_start[0] + Math.sqrt(fail_regionS)) {</span>
<span class="nc bnc" id="L57" title="All 4 branches missed.">			if (p.q &gt; fail_start[1] &amp;&amp; p.q &lt; fail_start[1] + Math.sqrt(fail_regionS)) {</span>
<span class="nc" id="L58">				isCorrect = false;</span>
			}
		}
<span class="nc" id="L61">		return isCorrect;</span>
	}

	public TestCase randomTC(Random random) {
		// CRandomNumber

<span class="nc" id="L67">		TestCase temp = new TestCase();</span>
<span class="nc" id="L68">		double p = CRandomNumber.randomi0i1() * (max[0] - min[0]) + min[0];</span>
<span class="nc" id="L69">		double q = CRandomNumber.randomi0i1() * (max[1] - min[1]) + min[1];</span>
<span class="nc" id="L70">		temp.p = p;</span>
<span class="nc" id="L71">		temp.q = q;</span>
<span class="nc" id="L72">		return temp;</span>
	}
	// public Test

	public TestCase randomTC(Random random, TriangleRegion region) {
<span class="nc" id="L77">		double s1, s2, s3 = 0;</span>
<span class="nc" id="L78">		TestCase temp = null;</span>
<span class="nc" id="L79">		double sum = region.getSquare();</span>
<span class="nc" id="L80">		int count = 0;</span>
		do {
<span class="nc" id="L82">			temp = randomTC(random);</span>
<span class="nc" id="L83">			TriangleRegion region1 = new TriangleRegion();</span>
<span class="nc" id="L84">			region1.setPs(region.getP1(), region.getP2(), temp);</span>
<span class="nc" id="L85">			s1 = region1.getSquare();</span>
<span class="nc" id="L86">			TriangleRegion region2 = new TriangleRegion();</span>
<span class="nc" id="L87">			region2.setPs(region.getP2(), region.getP3(), temp);</span>
<span class="nc" id="L88">			s2 = region2.getSquare();</span>
<span class="nc" id="L89">			TriangleRegion region3 = new TriangleRegion();</span>
<span class="nc" id="L90">			region3.setPs(region.getP1(), region.getP3(), temp);</span>
<span class="nc" id="L91">			s3 = region3.getSquare();</span>
<span class="nc" id="L92">			count++;</span>
			if (count &gt; 2) {
				// System.out.println(&quot;again!!!&quot;);
			}
<span class="nc bnc" id="L96" title="All 2 branches missed.">		} while ((s1 + s2 + s3) != sum);</span>
<span class="nc" id="L97">		return temp;</span>
	}

	// 使用向量生成(非均匀)
	public TestCase randomTC2(Random random, TriangleRegion region) {
		// Vector a=new Vector&lt;&gt;();
		// Vector2D vector1=new Vector2D(region.getP1().p,region.getP1().q);
		// Vector
<span class="nc" id="L105">		TestCase ji = region.getP1();</span>
<span class="nc" id="L106">		TestCase jiao1 = region.getP2();</span>
<span class="nc" id="L107">		TestCase jiao2 = region.getP3();</span>
		/*
		 * System.out.println(&quot;ji:&quot;+ji); System.out.println(&quot;jiao1:&quot;+jiao1);
		 * System.out.println(&quot;jiao2:&quot;+jiao2);
		 */
<span class="nc" id="L112">		TestCase u = new TestCase(jiao1.p - ji.p, jiao1.q - ji.q);</span>
<span class="nc" id="L113">		TestCase v = new TestCase(jiao2.p - ji.p, jiao2.q - ji.q);</span>
		// System.out.println(&quot;U:&quot;+u+&quot;,v:&quot;+v);
<span class="nc" id="L115">		TestCase temp = null;</span>
<span class="nc" id="L116">		temp = randomTC(random);</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">		while (temp.p + temp.q &gt; 1) {</span>
<span class="nc" id="L118">			temp = randomTC(random);</span>
		}
		// System.out.println(&quot;temp:&quot;+temp);
<span class="nc" id="L121">		double x = u.p * temp.p + v.p * temp.q;</span>
<span class="nc" id="L122">		double y = u.q * temp.p + v.q * temp.q;</span>
		// System.out.println(&quot;---------------&quot;);
<span class="nc" id="L124">		return new TestCase(x + ji.p, y + ji.q);</span>

	}

	// 非均匀
	public TestCase randomTC2(Random random, TriangleRegion region, double radius) {
		// Vector a=new Vector&lt;&gt;();
		// Vector2D vector1=new Vector2D(region.getP1().p,region.getP1().q);
		// Vector
<span class="nc" id="L133">		System.out.println(region);</span>
<span class="nc" id="L134">		TestCase p1 = region.getP1();</span>
<span class="nc" id="L135">		TestCase p2 = region.getP2();</span>
<span class="nc" id="L136">		TestCase p3 = region.getP3();</span>

<span class="nc" id="L138">		TestCase ji = p2;</span>
<span class="nc" id="L139">		TestCase temp1 = new TestCase();</span>
<span class="nc" id="L140">		temp1.p = (p1.p * radius - p2.p * radius + p1.p * p2.q - p1.p * p1.q) / (p2.q - p1.q);</span>
<span class="nc" id="L141">		temp1.q = p1.q - radius;</span>
<span class="nc" id="L142">		TestCase temp2 = new TestCase();</span>
<span class="nc" id="L143">		temp2.p = (p1.p * radius - p3.p * radius + p1.p * p3.q - p1.p * p1.q) / (p3.q - p1.q);</span>
<span class="nc" id="L144">		temp2.q = p1.q - radius;</span>
<span class="nc" id="L145">		TestCase u1 = new TestCase(temp1.p - ji.p, temp1.q - ji.q);</span>
<span class="nc" id="L146">		TestCase v1 = new TestCase(temp2.p - ji.p, temp2.q - ji.q);</span>
<span class="nc" id="L147">		TestCase w1 = new TestCase(p3.p - ji.p, p3.q - ji.q);</span>
		//
<span class="nc" id="L149">		TestCase result1 = null;</span>
<span class="nc" id="L150">		result1 = randomTC(random);</span>
<span class="nc bnc" id="L151" title="All 2 branches missed.">		while (result1.p + result1.q &gt; 1) {</span>
<span class="nc" id="L152">			result1 = randomTC(random);</span>
		}
		// System.out.println(&quot;temp:&quot;+temp);
<span class="nc" id="L155">		result1.p = u1.p * result1.p + v1.p * result1.q;</span>
<span class="nc" id="L156">		result1.q = u1.q * result1.p + v1.q * result1.q;</span>
		// System.out.println(&quot;---------------&quot;);
<span class="nc" id="L158">		TestCase result2 = null;</span>
<span class="nc" id="L159">		result2 = randomTC(random);</span>
<span class="nc bnc" id="L160" title="All 2 branches missed.">		while (result2.p + result2.q &gt; 1) {</span>
<span class="nc" id="L161">			result2 = randomTC(random);</span>
		}
		// System.out.println(&quot;temp:&quot;+temp);
<span class="nc" id="L164">		result2.p = v1.p * result2.p + w1.p * result2.q;</span>
<span class="nc" id="L165">		result2.q = v1.q * result2.p + w1.q * result2.q;</span>

<span class="nc" id="L167">		System.out.println(&quot;result1:&quot; + result1);</span>
<span class="nc" id="L168">		System.out.println(&quot;result2:&quot; + result2);</span>
<span class="nc bnc" id="L169" title="All 2 branches missed.">		if (random.nextBoolean()) {</span>
<span class="nc" id="L170">			return result1;</span>
		} else {
<span class="nc" id="L172">			return result2;</span>
		}
	}

	// 使用向量P=(1−√r1)A+(√r1(1−r2))B+(r2√r1)C
	public TestCase randomTC3(Random random, TriangleRegion region) {
		// Vector a=new Vector&lt;&gt;();
		// Vector2D vector1=new Vector2D(region.getP1().p,region.getP1().q);
		// Vector
<span class="nc" id="L181">		TestCase t1 = region.getP1();</span>
<span class="nc" id="L182">		TestCase t2 = region.getP2();</span>
<span class="nc" id="L183">		TestCase t3 = region.getP3();</span>
		// TestCase vector1=new TestCase(t2.p-t1.p,t2.q-t1.q);
		// TestCase vector2=new TestCase(t3.p-t1.p,t3.q-t1.q);
<span class="nc" id="L186">		double r1 = randomTC(random).p;</span>
<span class="nc" id="L187">		double r2 = randomTC(random).q;</span>
<span class="nc" id="L188">		double x = (1 - Math.sqrt(r1)) * t1.p + Math.sqrt(r1) * (1 - r2) * t2.p + r2 * Math.sqrt(r1) * t3.p;</span>
<span class="nc" id="L189">		double y = (1 - Math.sqrt(r1)) * t1.q + Math.sqrt(r1) * (1 - r2) * t2.q + r2 * Math.sqrt(r1) * t3.q;</span>
<span class="nc" id="L190">		return new TestCase(x, y);</span>
	}

	public TestCase randomTC3(Random random, TriangleRegion region, double radius) {
<span class="nc" id="L194">		TestCase p1 = region.getP1();</span>
<span class="nc" id="L195">		TestCase p2 = region.getP2();</span>
<span class="nc" id="L196">		TestCase p3 = region.getP3();</span>
<span class="nc" id="L197">		TestCase p4 = new TestCase();</span>
<span class="nc" id="L198">		TestCase p5 = new TestCase();</span>
<span class="nc" id="L199">		p4.p = 1 - radius * ((p1.p - p2.p) / (p1.q - p2.q));</span>
<span class="nc" id="L200">		p4.q = p1.q - radius;</span>
<span class="nc" id="L201">		p5.p = 1 - radius * ((p1.p - p3.p) / (p1.q - p3.q));</span>
<span class="nc" id="L202">		p5.q = p1.q - radius;</span>
		// p2,p4,p5 一个三角形
		// p2,p3,p5
<span class="nc" id="L205">		double r1 = randomTC(random).p;</span>
<span class="nc" id="L206">		double r2 = randomTC(random).q;</span>
<span class="nc" id="L207">		double x = (1 - Math.sqrt(r1)) * p2.p + Math.sqrt(r1) * (1 - r2) * p4.p + r2 * Math.sqrt(r1) * p5.p;</span>
<span class="nc" id="L208">		double y = (1 - Math.sqrt(r1)) * p2.q + Math.sqrt(r1) * (1 - r2) * p4.q + r2 * Math.sqrt(r1) * p5.q;</span>
<span class="nc" id="L209">		TestCase result1 = new TestCase(x, y);</span>
		//
<span class="nc" id="L211">		r1 = randomTC(random).p;</span>
<span class="nc" id="L212">		r2 = randomTC(random).q;</span>
<span class="nc" id="L213">		x = (1 - Math.sqrt(r1)) * p2.p + Math.sqrt(r1) * (1 - r2) * p3.p + r2 * Math.sqrt(r1) * p5.p;</span>
<span class="nc" id="L214">		y = (1 - Math.sqrt(r1)) * p2.q + Math.sqrt(r1) * (1 - r2) * p3.q + r2 * Math.sqrt(r1) * p5.q;</span>
<span class="nc" id="L215">		TestCase result2 = new TestCase(x, y);</span>
<span class="nc bnc" id="L216" title="All 2 branches missed.">		if (randomTC(random).p &lt;= 0.5) {</span>
<span class="nc" id="L217">			return result1;</span>
		} else {
<span class="nc" id="L219">			return result2;</span>
		}
	}

	public int run() {
<span class="nc" id="L224">		int count = 0;</span>
<span class="nc" id="L225">		Random random = new Random(seed);</span>
<span class="nc" id="L226">		ArrayList&lt;TriangleRegion&gt; regionlist = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L227">		ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L228">		fail_start[0] = random.nextDouble() * (max[0] - min[0] - Math.sqrt(fail_regionS)) + min[0];</span>
<span class="nc" id="L229">		fail_start[1] = random.nextDouble() * (max[1] - min[1] - Math.sqrt(fail_regionS)) + min[1];</span>
<span class="nc" id="L230">		System.out.println(&quot;failstart:&quot; + (fail_start[0] + &quot;,&quot; + fail_start[1]));</span>
		// 产生第一个测试用例
<span class="nc" id="L232">		TestCase p = randomTC(random);</span>
<span class="nc" id="L233">		tests.add(p);</span>
<span class="nc" id="L234">		System.out.println(&quot;p0:&quot; + p);</span>
		// 添加四块新区域
<span class="nc" id="L236">		TriangleRegion region1 = new TriangleRegion();</span>
<span class="nc" id="L237">		region1.setPs(p, new TestCase(0, 0), new TestCase(0, 1));</span>
<span class="nc" id="L238">		regionlist.add(region1);</span>
<span class="nc" id="L239">		TriangleRegion region2 = new TriangleRegion();</span>
<span class="nc" id="L240">		region2.setPs(p, new TestCase(0, 1), new TestCase(1, 1));</span>
<span class="nc" id="L241">		regionlist.add(region2);</span>
<span class="nc" id="L242">		TriangleRegion region3 = new TriangleRegion();</span>
<span class="nc" id="L243">		region3.setPs(p, new TestCase(1, 0), new TestCase(1, 1));</span>
<span class="nc" id="L244">		regionlist.add(region3);</span>
<span class="nc" id="L245">		TriangleRegion region4 = new TriangleRegion();</span>
<span class="nc" id="L246">		region4.setPs(p, new TestCase(0, 0), new TestCase(1, 0));</span>
<span class="nc" id="L247">		regionlist.add(region4);</span>
<span class="nc bnc" id="L248" title="All 2 branches missed.">		while (isCorrect(p)) {</span>
<span class="nc" id="L249">			count++;</span>
			//
			// if (count == 3) {
			// // StdDraw.filledRectangle(0.5, 0.5, 0.5, 0.5);
			// StdDraw.setPenColor(StdDraw.BOOK_BLUE);
			// // StdDraw.line(0, 1, 1, 0);
			// // StdDraw.line(0, 0, 1, 1);
			// double tempradius = Math.sqrt(
			// 0.4 * (this.max[1] - this.min[1]) * (this.max[0] - this.min[0]) /
			// (tests.size() * Math.PI));
			// // StdDraw.line(0, 0, 1, 1);
			// for (int i = 0; i &lt; tests.size(); i++) {
			//
			// TestCase temp = tests.get(i);
			// StdDraw.line(0, temp.q - tempradius, 1, temp.q - tempradius);
			//
			// StdDraw.filledCircle(temp.p, temp.q, 0.01);
			// }
			// }

			// 找出最大区域
<span class="nc" id="L270">			double max = 0;</span>
<span class="nc" id="L271">			TriangleRegion maxregion = null;</span>
<span class="nc" id="L272">			int maxindex = 0;</span>
<span class="nc bnc" id="L273" title="All 2 branches missed.">			for (int i = 0; i &lt; regionlist.size(); i++) {</span>
<span class="nc" id="L274">				double temp = regionlist.get(i).getSquare();</span>
<span class="nc bnc" id="L275" title="All 2 branches missed.">				if (temp &gt; max) {</span>
<span class="nc" id="L276">					max = temp;</span>
<span class="nc" id="L277">					maxregion = regionlist.get(i);</span>
<span class="nc" id="L278">					maxindex = i;</span>
				}
			}
			/// 从这里开始加强
			// double radius = Math
			// .sqrt(0.4 * (this.max[1] - this.min[1]) * (this.max[0] -
			// this.min[0]) / (tests.size() * Math.PI));
<span class="nc" id="L285">			p = new TestCase();</span>
			// maxregion=smallerRegion(maxregion);
			// p = randomTC3(random, maxregion);
<span class="nc" id="L288">			p.p = (maxregion.p1.p + maxregion.p2.p + maxregion.p3.p) / 3.0;</span>
<span class="nc" id="L289">			p.q = (maxregion.p1.q + maxregion.p2.q + maxregion.p3.q) / 3.0;</span>
<span class="nc" id="L290">			tests.add(p);</span>
<span class="nc" id="L291">			System.out.println(&quot;p&quot; + count + &quot;:&quot; + p);</span>
			// 删除该最大区域，并重新划分最大区域
			// regionlist.remove(maxindex);
<span class="nc" id="L294">			regionlist.set(maxindex, new TriangleRegion(new TestCase(0, 0), new TestCase(0, 0), new TestCase(0, 0)));</span>

<span class="nc" id="L296">			TriangleRegion temp1 = new TriangleRegion();</span>
<span class="nc" id="L297">			temp1.setPs(p, maxregion.getP1(), maxregion.getP2());</span>
<span class="nc" id="L298">			regionlist.add(temp1);</span>
<span class="nc" id="L299">			TriangleRegion temp2 = new TriangleRegion();</span>
<span class="nc" id="L300">			temp2.setPs(p, maxregion.getP1(), maxregion.getP3());</span>
<span class="nc" id="L301">			regionlist.add(temp2);</span>
<span class="nc" id="L302">			TriangleRegion temp3 = new TriangleRegion();</span>
<span class="nc" id="L303">			temp3.setPs(p, maxregion.getP2(), maxregion.getP3());</span>
<span class="nc" id="L304">			regionlist.add(temp3);</span>

		}
<span class="nc" id="L307">		return count;</span>
	}

	public TriangleRegion smallerRegion(TriangleRegion region) {
<span class="nc" id="L311">		TriangleRegion result = new TriangleRegion();</span>
<span class="nc" id="L312">		result = region;</span>
		// 待完成
<span class="nc" id="L314">		return result;</span>
	}
}

class TriangleRegion {
	TestCase p1;
	TestCase p2;
	TestCase p3;

	public TriangleRegion() {
<span class="nc" id="L324">		super();</span>
<span class="nc" id="L325">	}</span>

	public TriangleRegion(TestCase p1, TestCase p2, TestCase p3) {
<span class="nc" id="L328">		super();</span>
<span class="nc" id="L329">		this.p1 = p1;</span>
<span class="nc" id="L330">		this.p2 = p2;</span>
<span class="nc" id="L331">		this.p3 = p3;</span>
<span class="nc" id="L332">	}</span>

	public TestCase getP1() {
<span class="nc" id="L335">		return p1;</span>
	}

	public TestCase getP2() {
<span class="nc" id="L339">		return p2;</span>
	}

	public TestCase getP3() {
<span class="nc" id="L343">		return p3;</span>
	}

	public double getSquare() {
		// (1/2)*(p1.pp2.q+p2.pp3.q+p3.pp1.q-p1.pp3.q-p2.pp1.q-p3.pp2.q)
		/*
		 * System.out.println(&quot;p1:&quot; + p1); System.out.println(&quot;p2:&quot; + p2);
		 * System.out.println(&quot;p3:&quot; + p3); System.out.println(&quot;square:&quot; + ((0.5)
		 * Math.abs(p1.p * p2.q + p2.p * p3.q + p3.p * p1.q - p1.p * p3.q - p2.p * p1.q
		 * - p3.p * p2.q)));
		 */
<span class="nc" id="L354">		return (0.5) * Math.abs(p1.p * p2.q + p2.p * p3.q + p3.p * p1.q - p1.p * p3.q - p2.p * p1.q - p3.p * p2.q);</span>
	}

	public void setP1(TestCase p1) {
<span class="nc" id="L358">		this.p1 = p1;</span>
<span class="nc" id="L359">	}</span>

	public void setP2(TestCase p2) {
<span class="nc" id="L362">		this.p2 = p2;</span>
<span class="nc" id="L363">	}</span>

	public void setP3(TestCase p3) {
<span class="nc" id="L366">		this.p3 = p3;</span>
<span class="nc" id="L367">	}</span>

	public void setPs(TestCase p1, TestCase p2, TestCase p3) {
<span class="nc" id="L370">		this.p1 = p1;</span>
<span class="nc" id="L371">		this.p2 = p2;</span>
<span class="nc" id="L372">		this.p3 = p3;</span>
<span class="nc" id="L373">	}</span>

	@Override
	public String toString() {
<span class="nc" id="L377">		return &quot;TriangleRegion [p1=&quot; + p1 + &quot;, p2=&quot; + p2 + &quot;, p3=&quot; + p3 + &quot;]&quot;;</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>