package test.reality.rt;

import java.util.Random;

import Fault_10d.CalGCD;
import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import tested.airy;
import tested.el2;
import tested.*;

public class RT_ND_E_Time {
	private int em;
	double[] min;
	double[] max;
	int dimension;
	FailurePattern failPattern;

	Random random;

	public RT_ND_E_Time(double[] min, double[] max, Random random, FailurePattern pattern, int em) {
		this.min = min;
		this.max = max;
		this.em=em;

		this.dimension = min.length;

		this.random = random;

		pattern.random = random;
		pattern.genFailurePattern();

		this.failPattern = pattern;
	}

	public NPoint randomTC() {
		NPoint point = new NPoint();
		point.dimension = this.dimension;
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];
		}
		point.setXn(xn);
		return point;
	}

	public int run() {
		int count = 0;
		int emCount = 0;
		NPoint p = randomTC();
		while (count<em) {
		if (this.failPattern.isCorrect(p)) {
			count++;
	  } else{
			  emCount++;
    	  	  count++;
		}
			p = randomTC();
		}
		return emCount;
	}

	public static void main(String[] args) {
		int times = 2000;
		int em =10000000;
		//airy beTested=new airy();
		//erfcc beTested=new erfcc();
		//tanh beTested=new tanh();
		//probks beTested=new probks();
		//bessj0 beTested=new bessj0();
		//bessj beTested=new bessj();
		//gammq beTested=new gammq();
		//sncndn beTested=new sncndn();
		//golden beTested=new golden();
		//plgndr beTested=new plgndr();
		//cel beTested=new cel();
		//el2 beTested=new el2();
		CalGCD beTested = new CalGCD();
		

		double[] min = beTested.min;
		double[] max = beTested.max;
		FailurePattern failurePattern = new RealityFailPattern(beTested.getClass().getSimpleName());
		failurePattern.fail_rate = beTested.failureRate;
		failurePattern.min = min;
		failurePattern.max = max;
		failurePattern.dimension = beTested.Dimension;

		int fm = 0;
		long startTime = System.currentTimeMillis();
		for (int i = 0; i < times; i++) {
			RT_ND_E_Time rt = new RT_ND_E_Time(min, max, new Random(i * 3), failurePattern, em);
			int temp = rt.run();
			fm += temp;
		}
		long endTime = System.currentTimeMillis();

		System.out.println("Fm:" + (fm / (double) times) + " times:" + ((endTime - startTime) / (double) times));
	}
}
