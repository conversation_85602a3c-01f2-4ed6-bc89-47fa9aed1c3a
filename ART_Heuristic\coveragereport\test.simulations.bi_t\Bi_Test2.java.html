<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>Bi_Test2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.bi_t</a> &gt; <span class="el_source">Bi_Test2.java</span></div><h1>Bi_Test2.java</h1><pre class="source lang-java linenums">package test.simulations.bi_t;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

/*
 * random select test cases;
 * 
 */
public class Bi_Test2 {
	public static void main(String[] args) {
<span class="nc" id="L14">		int cishu = 1;</span>
<span class="nc" id="L15">		long sumOfF = 0;</span>
<span class="nc" id="L16">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L17" title="All 2 branches missed.">		for (int i = 0; i &lt; cishu; i++) {</span>
<span class="nc" id="L18">			Bi_Test2 bi_t = new Bi_Test2((i * 4));</span>

<span class="nc" id="L20">			int f_measure = bi_t.run();</span>
<span class="nc" id="L21">			sumOfF += f_measure;</span>
		}
<span class="nc" id="L23">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L24">		System.out.println(&quot;Fm: &quot; + sumOfF / (double) cishu);</span>
<span class="nc" id="L25">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) cishu);</span>
<span class="nc" id="L26">	}</span>
	double fail_start;
<span class="nc" id="L28">	double fail_rate = 0.01;</span>

	int randomseed;
	// ArrayList&lt;Double&gt; al = new ArrayList&lt;&gt;();

<span class="nc" id="L33">	public Bi_Test2(int seed) {</span>
<span class="nc" id="L34">		randomseed = seed;</span>
<span class="nc" id="L35">	}</span>

	public int run() {
<span class="nc" id="L38">		Random random = new Random(randomseed);</span>
<span class="nc" id="L39">		int count = 0;</span>
<span class="nc" id="L40">		fail_start = random.nextDouble() * (1 - fail_rate);</span>
<span class="nc" id="L41">		double p = 0.5;</span>
<span class="nc" id="L42">		int i = 1, m = 0;</span>
<span class="nc" id="L43">		boolean flag = true;</span>
<span class="nc" id="L44">		ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L45" title="All 2 branches missed.">		while (flag) {</span>
<span class="nc" id="L46">			System.out.println(p);</span>
<span class="nc" id="L47">			count++;</span>
<span class="nc" id="L48">			m = (int) (count + 1 - Math.pow(2, i));</span>
<span class="nc" id="L49">			TestCase temp1=new TestCase();</span>
<span class="nc" id="L50">			temp1.p=(Math.pow(2, -(i + 1))) * (2 * m + 1);</span>
<span class="nc" id="L51">			tests.add(temp1);</span>
<span class="nc bnc" id="L52" title="All 2 branches missed.">			if (2 * m + 1 == (Math.pow(2, i + 1)) - 1) {</span>
				// 下一轮
<span class="nc" id="L54">				i++;</span>
				// 当前轮测试，随机挑选测试用例
<span class="nc" id="L56">				ArrayList&lt;TestCase&gt; temp = new ArrayList&lt;&gt;(tests);</span>
<span class="nc bnc" id="L57" title="All 2 branches missed.">				for (int l = 0; l &lt; tests.size(); l++) {</span>

<span class="nc" id="L59">					int index = random.nextInt(temp.size());</span>
<span class="nc" id="L60">					System.out.println(&quot;index:&quot;+index);</span>
<span class="nc bnc" id="L61" title="All 2 branches missed.">					if (!test(temp.get(index).p)) {</span>
<span class="nc" id="L62">						flag = false;</span>
					}
<span class="nc" id="L64">					temp.remove(index);</span>
				}
			}

		}
<span class="nc" id="L69">		return count;</span>
	}

	public boolean test(double p) {
		boolean flag;
<span class="nc bnc" id="L74" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L75">			flag = false;</span>
<span class="nc" id="L76">		} else {</span>
<span class="nc" id="L77">			flag = true;</span>
		}
<span class="nc" id="L79">		return flag;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>