<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRTtp2D.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rrttp.partion</a> &gt; <span class="el_source">RRTtp2D.java</span></div><h1>RRTtp2D.java</h1><pre class="source lang-java linenums">package test.simulations.rrttp.partion;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import test.simulations.rrt.RRT_ND;
import util.data.DataCreator;
import util.data.ZeroOneCreator;
import util.draw.StdDraw;

/**
 * <AUTHOR>
 * @date 2017/11/26
 * problems
 */
public class RRTtp2D extends RRT_ND {
	
<span class="nc" id="L24">	List&lt;NPoint&gt; tests = new ArrayList&lt;&gt;((int) (1.0 / this.failPattern.fail_rate));</span>

<span class="nc" id="L26">	List&lt;NRectRegion&gt; regions = new ArrayList&lt;&gt;();</span>

	public RRTtp2D(double[] min, double[] max, Random random, FailurePattern failurePattern, double r) {
<span class="nc" id="L29">		super(min, max, failurePattern, random, r);</span>
<span class="nc" id="L30">	}</span>

	public void calAvailableRegions(NRectRegion currentRegion, NPoint p, double radius) {
		// cut the currentRegion into 8 regions
		// only for 2 dimesion now
		//
<span class="nc" id="L36">		drawSplitLines(currentRegion,p);</span>
<span class="nc" id="L37">		calAvailableRegionsIn2D(currentRegion, p, radius);</span>
<span class="nc" id="L38">	}</span>
	public void drawSplitLines(NRectRegion currentRegion, NPoint p){
		
<span class="nc" id="L41">	}</span>
	public void calAvailableRegionsIn2D(NRectRegion currentRegion, NPoint p, double radius) {
<span class="nc" id="L43">		double[] start = currentRegion.getStart().getXn();</span>
<span class="nc" id="L44">		double[] end = currentRegion.getEnd().getXn();</span>
<span class="nc" id="L45">		double[] pxn = p.getXn();</span>

		// 1st part(contains two small parts)//
<span class="nc" id="L48">		double xlength = pxn[0] - start[0];</span>
<span class="nc" id="L49">		double ylength = pxn[1] - start[1];</span>
<span class="nc bnc" id="L50" title="All 2 branches missed.">		if (ylength &gt; radius) {</span>
			// 有下半部分
<span class="nc" id="L52">			NRectRegion region1 = new NRectRegion(new NPoint(new double[] { start[0], start[1] }),</span>
<span class="nc" id="L53">					new NPoint(new double[] { pxn[0], pxn[1] - radius }));</span>
<span class="nc" id="L54">			this.regions.add(region1);</span>
		}
<span class="nc bnc" id="L56" title="All 2 branches missed.">		if (xlength &gt; radius) {</span>
			// 有左半部分
<span class="nc" id="L58">			NRectRegion region2 = new NRectRegion(</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">					new NPoint(new double[] { start[0], pxn[1] - radius &gt; start[1] ? pxn[1] - radius : start[1] }),</span>
<span class="nc" id="L60">					new NPoint(new double[] { pxn[0] - radius, pxn[1] }));</span>
<span class="nc" id="L61">			this.regions.add(region2);</span>
		}

		// 2st part
<span class="nc" id="L65">		xlength=end[0]-pxn[0];</span>
<span class="nc" id="L66">		ylength=pxn[1]-start[1];</span>
<span class="nc bnc" id="L67" title="All 2 branches missed.">		if(xlength&gt;radius){</span>
			//有右半部份
<span class="nc" id="L69">			NRectRegion region3=new NRectRegion(</span>
<span class="nc" id="L70">					new NPoint(new double[]{pxn[0]+radius,start[1]}), </span>
<span class="nc" id="L71">					new NPoint(new double[]{end[0],pxn[1]}));</span>
<span class="nc" id="L72">			this.regions.add(region3);</span>
		}
<span class="nc bnc" id="L74" title="All 2 branches missed.">		if(ylength&gt;radius){</span>
			//左半部分
<span class="nc" id="L76">			NRectRegion region4=new NRectRegion(</span>
<span class="nc" id="L77">					new NPoint(new double[]{pxn[0],start[1]}),</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">					new NPoint(new double[]{pxn[0]+radius&lt;end[0]?pxn[0]+radius:end[0],pxn[1]-radius}));</span>
<span class="nc" id="L79">			this.regions.add(region4);</span>
		}
		
		//3st part
<span class="nc" id="L83">		xlength=end[0]-pxn[0];</span>
<span class="nc" id="L84">		ylength=end[1]-pxn[1];</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">		if(xlength&gt;radius){</span>
			//右半部份
<span class="nc" id="L87">			NRectRegion region5=new NRectRegion(</span>
<span class="nc" id="L88">					new NPoint(new double[]{pxn[0]+radius,pxn[1]}), </span>
<span class="nc" id="L89">					new NPoint(new double[]{end[0],end[1]}));</span>
<span class="nc" id="L90">			this.regions.add(region5);</span>
		}
<span class="nc bnc" id="L92" title="All 2 branches missed.">		if(ylength&gt;radius){</span>
			//左半部分
<span class="nc" id="L94">			NRectRegion region6=new NRectRegion(</span>
<span class="nc" id="L95">					new NPoint(new double[]{pxn[0],pxn[1]+radius}),</span>
<span class="nc bnc" id="L96" title="All 2 branches missed.">					new NPoint(new double[]{pxn[0]+radius&lt;end[0]?pxn[0]+radius:end[0],end[1]}));</span>
<span class="nc" id="L97">			this.regions.add(region6);</span>
		}
		
		//4st part
<span class="nc" id="L101">		xlength=pxn[0] - start[0];</span>
<span class="nc" id="L102">		ylength=end[1]-pxn[1];</span>
<span class="nc bnc" id="L103" title="All 2 branches missed.">		if(xlength&gt;radius){</span>
			//左边
<span class="nc" id="L105">			NRectRegion region7=new NRectRegion(</span>
<span class="nc" id="L106">					new NPoint(new double[]{start[0],pxn[1]}),</span>
<span class="nc bnc" id="L107" title="All 2 branches missed.">					new NPoint(new double[]{pxn[0]-radius,pxn[1]+radius&lt;end[1]?pxn[1]+radius:end[1]}));</span>
<span class="nc" id="L108">			this.regions.add(region7);</span>
		}
<span class="nc bnc" id="L110" title="All 2 branches missed.">		if(ylength&gt;radius){</span>
			//上半部分
<span class="nc" id="L112">			NRectRegion region8=new NRectRegion(</span>
<span class="nc" id="L113">					new NPoint(new double[]{start[0],pxn[1]+radius}),</span>
<span class="nc" id="L114">					new NPoint(new double[]{pxn[0],end[1]}));</span>
<span class="nc" id="L115">			this.regions.add(region8);</span>
		}
		
<span class="nc bnc" id="L118" title="All 2 branches missed.">		for(int i=0;i&lt;regions.size();i++){</span>
<span class="nc" id="L119">			System.out.println(regions.get(i).toString());</span>
		}
	//	System.out.println(this.regions);
<span class="nc" id="L122">	}</span>

	public NPoint genNextPoint() {
<span class="nc" id="L125">		double T = random.nextDouble();</span>
<span class="nc" id="L126">		double allAvailableRegionsSize = 0.0;</span>
<span class="nc" id="L127">		double Co = 0;</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">		for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc" id="L129">			allAvailableRegionsSize += regions.get(i).size();</span>
		}
<span class="nc" id="L131">		Co = 1.0 / allAvailableRegionsSize;</span>
<span class="nc" id="L132">		double PreIntegral = 0.0;</span>
<span class="nc" id="L133">		double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L134">		int temp = 0;// 落在哪个区间</span>
<span class="nc bnc" id="L135" title="All 2 branches missed.">		for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">			if (SumIntegral &lt; T) {</span>
<span class="nc" id="L137">				PreIntegral = SumIntegral;</span>
<span class="nc" id="L138">				temp = i;</span>
			}
<span class="nc" id="L140">			SumIntegral += Co * regions.get(i).size();</span>
		}
		//
<span class="nc" id="L143">		NRectRegion currentRegion = regions.remove(temp);</span>
<span class="nc" id="L144">		NPoint p = randomCreator.randomPoint(currentRegion);</span>
<span class="nc" id="L145">		System.out.println(&quot;p:&quot;+p.toString());</span>
<span class="nc" id="L146">		double radius=calculateRadius(this.tests.size()+1);</span>
<span class="nc" id="L147">		System.out.println(&quot;radius:&quot;+radius);</span>
<span class="nc" id="L148">		calAvailableRegions(currentRegion, p,radius );</span>

<span class="nc" id="L150">		return p;</span>

	}

	@Override
	public int run() {
<span class="nc" id="L156">		int count = 0;</span>
		
<span class="nc" id="L158">		StdDraw.rectangle(0.5, 0.5, 0.5, 0.5);</span>
<span class="nc" id="L159">		failPattern.showFailurePattern();</span>
		// random a point
<span class="nc" id="L161">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc" id="L162">		StdDraw.filledCircle(p.getXn()[0], p.getXn()[1],0.02);</span>
<span class="nc" id="L163">		tests.add(p);</span>
		
<span class="nc" id="L165">		System.out.println(&quot;p:&quot;+p);</span>
		//System.out.println(&quot;isCorrect:&quot;+);
<span class="nc" id="L167">		double radius = calculateRadius(1);</span>
<span class="nc" id="L168">		System.out.println(&quot;radius:&quot;+radius);</span>
		
<span class="nc" id="L170">		calAvailableRegions(new NRectRegion(new NPoint(min), new NPoint(max)), p, radius);</span>
<span class="nc" id="L171">		System.out.println(&quot;-------------------&quot;);</span>
<span class="nc" id="L172">		while(true){</span>
<span class="nc" id="L173">			p=genNextPoint();</span>
<span class="nc" id="L174">			tests.add(p);</span>
<span class="nc" id="L175">			count++;</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">			if(!this.failPattern.isCorrect(p)){</span>
<span class="nc" id="L177">				return count;</span>
			}
<span class="nc" id="L179">			System.out.println(&quot;---------------&quot;);</span>
		}
//		while (this.failPattern.isCorrect(p)) {
//			tests.add(p);
//			count++;
//			System.out.println(&quot;count:&quot;+count);
//			p = genNextPoint();
//			System.out.println(&quot;p:&quot;+p);
//		}
		//return count;
	}
	
	public static void main(String[] args) {
<span class="nc" id="L192">		int times=1;</span>
<span class="nc" id="L193">		int d=2;</span>
<span class="nc" id="L194">		double fail_rate=0.01;</span>
<span class="nc" id="L195">		double R=1;</span>
		
<span class="nc" id="L197">		DataCreator dataCreator=new ZeroOneCreator();</span>
		
<span class="nc" id="L199">		double[] min=dataCreator.minCreator(d);</span>
<span class="nc" id="L200">		double[] max=dataCreator.maxCreator(d);</span>
		
<span class="nc" id="L202">		FailurePattern pattern=new BlockPattern();</span>
<span class="nc" id="L203">		pattern.fail_rate=fail_rate;</span>
		
<span class="nc" id="L205">		int fm=0;</span>
<span class="nc bnc" id="L206" title="All 2 branches missed.">		for(int i=0;i&lt;times;i++){</span>
<span class="nc" id="L207">			ART method=new RRTtp2D(min, max, new Random(i*3), pattern, R);</span>
<span class="nc" id="L208">			int temp=method.run();</span>
<span class="nc" id="L209">			fm+=temp;</span>
		}
<span class="nc" id="L211">		System.out.println(&quot;fm:&quot;+(fm/(double)times));</span>
<span class="nc" id="L212">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>