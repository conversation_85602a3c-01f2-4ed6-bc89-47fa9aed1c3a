<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>TriangleUtil2D.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util.triangle</a> &gt; <span class="el_source">TriangleUtil2D.java</span></div><h1>TriangleUtil2D.java</h1><pre class="source lang-java linenums">package util.triangle;


import datastructure.ND.NPoint;
import datastructure.TD.TriangleRegion2D;

<span class="nc" id="L7">public class TriangleUtil2D {</span>
	public static  boolean IsPointInTriangle(NPoint pointA,NPoint pointB,NPoint pointC, NPoint  pointP)
    {
<span class="nc" id="L10">		TriangleRegion2D region=new TriangleRegion2D(pointA, pointB, pointC);</span>
<span class="nc" id="L11">		double S=region.getSquare();</span>
		
<span class="nc" id="L13">		TriangleRegion2D region1 = new TriangleRegion2D();</span>
<span class="nc" id="L14">		region1.setPs(pointA, pointB, pointP);</span>
<span class="nc" id="L15">		double s1 = region1.getSquare();</span>
<span class="nc" id="L16">		TriangleRegion2D region2 = new TriangleRegion2D();</span>
<span class="nc" id="L17">		region2.setPs(pointB, pointC, pointP);</span>
<span class="nc" id="L18">		double s2 = region2.getSquare();</span>
<span class="nc" id="L19">		TriangleRegion2D region3 = new TriangleRegion2D();</span>
<span class="nc" id="L20">		region3.setPs(pointA, pointC, pointP);</span>
<span class="nc" id="L21">		double s3 = region3.getSquare();</span>
<span class="nc bnc" id="L22" title="All 2 branches missed.">		if(Math.abs(s1+s2+s3-S)&lt;0.0000000001){</span>
<span class="nc" id="L23">			return true;</span>
		}else{
<span class="nc" id="L25">			return false;</span>
		}
    }
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>