<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>PointPattern.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.failurepattern.impl</a> &gt; <span class="el_source">PointPattern.java</span></div><h1>PointPattern.java</h1><pre class="source lang-java linenums">package datastructure.failurepattern.impl;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import util.RandomCreator;

/*
 * 
 * */
<span class="nc" id="L10">public class PointPattern extends FailurePattern {</span>

	private int count;// 1维50，二维7*7=49 三维4*4*4=64 四维3*3*3*3=81
	private int eachCount;
	private double fail_regionS;

	private double eachPointArea;

	private NPoint[] points;
	RandomCreator randomCreator;

	@Override
	public void genFailurePattern() {
<span class="nc bnc" id="L23" title="All 5 branches missed.">		switch (this.dimension) {</span>
		case 1:
<span class="nc" id="L25">			count = 50;</span>
<span class="nc" id="L26">			eachCount = 50;</span>
<span class="nc" id="L27">			break;</span>
		case 2:
<span class="nc" id="L29">			count = 49;</span>
<span class="nc" id="L30">			eachCount = 7;</span>
<span class="nc" id="L31">			break;</span>
		case 3:
<span class="nc" id="L33">			count = 64;</span>
<span class="nc" id="L34">			eachCount = 4;</span>
<span class="nc" id="L35">			break;</span>
		case 4:
<span class="nc" id="L37">			count = 81;</span>
<span class="nc" id="L38">			eachCount = 3;</span>
<span class="nc" id="L39">			break;</span>
		default:
<span class="nc" id="L41">			System.out.println(&quot;dimension is too high! error in PointPattern genFailurePattern()&quot;);</span>
		}
<span class="nc" id="L43">		double totalArea = 1.0;</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc" id="L45">			totalArea *= (max[i] - min[i]);</span>
		}
<span class="nc" id="L47">		fail_regionS = this.fail_rate * totalArea;</span>

<span class="nc" id="L49">		eachPointArea = fail_regionS / (double) this.count;</span>

<span class="nc" id="L51">		points = new NPoint[this.count];</span>
		// 求出每个点的位置即可
<span class="nc" id="L53">		double x = 1 / (double) eachCount;</span>
<span class="nc" id="L54">		x = x * 0.5;</span>
<span class="nc bnc" id="L55" title="All 5 branches missed.">		switch (dimension) {</span>
		case 1:
<span class="nc bnc" id="L57" title="All 2 branches missed.">			for (int i = 0; i &lt; this.count; i++) {</span>
<span class="nc" id="L58">				points[i] = new NPoint(new double[] { (i + 1) * x });</span>
			}
<span class="nc" id="L60">			break;</span>
		case 2:
<span class="nc bnc" id="L62" title="All 2 branches missed.">			for (int i = 0; i &lt; eachCount; i++) {</span>
<span class="nc bnc" id="L63" title="All 2 branches missed.">				for (int j = 0; j &lt; eachCount; j++) {</span>
<span class="nc" id="L64">					points[i] = new NPoint(new double[] { (i + 1) * x, (j + 1) * x });</span>
				}
			}
<span class="nc" id="L67">			break;</span>
		case 3:
<span class="nc bnc" id="L69" title="All 2 branches missed.">			for (int i = 0; i &lt; eachCount; i++) {</span>
<span class="nc bnc" id="L70" title="All 2 branches missed.">				for (int j = 0; j &lt; eachCount; j++) {</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">					for (int k = 0; k &lt; eachCount; k++) {</span>
<span class="nc" id="L72">						points[i] = new NPoint(new double[] { (i + 1) * x, (j + 1) * x, (k + 1) * x });</span>
					}
				}
			}
<span class="nc" id="L76">			break;</span>
		case 4:
<span class="nc bnc" id="L78" title="All 2 branches missed.">			for (int i = 0; i &lt; eachCount; i++) {</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">				for (int j = 0; j &lt; eachCount; j++) {</span>
<span class="nc bnc" id="L80" title="All 2 branches missed.">					for (int k = 0; k &lt; eachCount; k++) {</span>
<span class="nc bnc" id="L81" title="All 2 branches missed.">						for (int l = 0; l &lt; eachCount; l++) {</span>
<span class="nc" id="L82">							points[i] = new NPoint(new double[] { (i + 1) * x, (j + 1) * x, (k + 1) * x, (l + 1) * x });</span>
						}
					}
				}
			}
		}

<span class="nc" id="L89">	}</span>
	public double calculateRadius(int count) {
<span class="nc bnc" id="L91" title="All 2 branches missed.">		if (this.dimension % 2 == 0) {</span>
<span class="nc" id="L92">			int k = this.dimension / 2;</span>
<span class="nc" id="L93">			double kjie = 1;</span>
<span class="nc bnc" id="L94" title="All 2 branches missed.">			for (int i = k; i &gt; 0; i--) {</span>
<span class="nc" id="L95">				kjie *= i;</span>
			}
<span class="nc" id="L97">			double temp = (this.fail_regionS * kjie) / (count * Math.pow(Math.PI, k));</span>

<span class="nc" id="L99">			return Math.pow(temp, 1 / (double) this.dimension);</span>
		} else {
<span class="nc" id="L101">			int k = this.dimension / 2;</span>
<span class="nc" id="L102">			double kjie = 1;</span>
<span class="nc" id="L103">			double k2jie = 1;</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">			for (int i = k; i &gt; 0; i--) {</span>
<span class="nc" id="L105">				kjie *= i;</span>
			}
<span class="nc bnc" id="L107" title="All 2 branches missed.">			for (int i = (2 * k + 1); i &gt; 0; i--) {</span>
<span class="nc" id="L108">				k2jie *= i;</span>
			}
<span class="nc" id="L110">			double temp = (this.fail_regionS * k2jie) / (kjie * Math.pow(2, 2 * k + 1) * Math.pow(Math.PI, k) * count);</span>
			// System.out.println(&quot;return R&quot;);
<span class="nc" id="L112">			return Math.pow(temp, 1 / (double) this.dimension);</span>
		}
	}
	@Override
	public boolean isCorrect(NPoint p) {
<span class="nc" id="L117">		boolean flag = true;</span>
		//double radius = Math.sqrt(this.eachPointArea / Math.PI);
<span class="nc" id="L119">		double radius=calculateRadius(1);</span>
<span class="nc" id="L120">		double pxn[] = p.getXn();</span>
<span class="nc bnc" id="L121" title="All 2 branches missed.">		for (int i = 0; i &lt; this.count; i++) {</span>
<span class="nc" id="L122">			double[] tempxn = points[i].getXn();</span>
<span class="nc" id="L123">			double distance = 0.0;</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">			for (int j = 0; j &lt; tempxn.length; j++) {</span>
<span class="nc" id="L125">				distance += (tempxn[j] - pxn[j]) * (tempxn[j] - pxn[j]);</span>
			}
<span class="nc" id="L127">			distance = Math.sqrt(distance);</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">			if (distance &lt; radius) {</span>
<span class="nc" id="L129">				flag = false;</span>
<span class="nc" id="L130">				break;</span>
			}
		}

<span class="nc" id="L134">		return flag;</span>
	}

	@Override
	public void showFailurePattern() {

<span class="nc" id="L140">	}</span>

	public static void main(String[] args) {
		
<span class="nc" id="L144">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>