<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>TriangleRegion2D.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.TD</a> &gt; <span class="el_source">TriangleRegion2D.java</span></div><h1>TriangleRegion2D.java</h1><pre class="source lang-java linenums">package datastructure.TD;

import datastructure.ND.NPoint;

public class TriangleRegion2D {

	NPoint p1;
	NPoint p2;
	NPoint p3;

	public TriangleRegion2D() {
<span class="nc" id="L12">		super();</span>
<span class="nc" id="L13">	}</span>

	public TriangleRegion2D(NPoint p1, NPoint p2, NPoint p3) {
<span class="nc" id="L16">		super();</span>
<span class="nc" id="L17">		this.p1 = p1;</span>
<span class="nc" id="L18">		this.p2 = p2;</span>
<span class="nc" id="L19">		this.p3 = p3;</span>
<span class="nc" id="L20">	}</span>

	public NPoint getP1() {
<span class="nc" id="L23">		return p1;</span>
	}

	public NPoint getP2() {
<span class="nc" id="L27">		return p2;</span>
	}

	public NPoint getP3() {
<span class="nc" id="L31">		return p3;</span>
	}

	public double getSquare() {
		// (1/2)*(p1.pp2.q+p2.pp3.q+p3.pp1.q-p1.pp3.q-p2.pp1.q-p3.pp2.q)
		/*
		 * System.out.println(&quot;p1:&quot; + p1); System.out.println(&quot;p2:&quot; + p2);
		 * System.out.println(&quot;p3:&quot; + p3); System.out.println(&quot;square:&quot; + ((0.5)
		 * Math.abs(p1.p * p2.q + p2.p * p3.q + p3.p * p1.q - p1.p * p3.q - p2.p * p1.q
		 * - p3.p * p2.q)));
		 */
		
<span class="nc" id="L43">		return (0.5) * Math.abs(p1.getXn()[0] * p2.getXn()[1] + p2.getXn()[0]* p3.getXn()[1] + p3.getXn()[0] * p1.getXn()[1] - p1.getXn()[0] * p3.getXn()[1]- p2.getXn()[0] * p1.getXn()[1] - p3.getXn()[0] * p2.getXn()[1]);</span>
	}

	public void setP1(NPoint p1) {
<span class="nc" id="L47">		this.p1 = p1;</span>
<span class="nc" id="L48">	}</span>

	public void setP2(NPoint p2) {
<span class="nc" id="L51">		this.p2 = p2;</span>
<span class="nc" id="L52">	}</span>

	public void setP3(NPoint p3) {
<span class="nc" id="L55">		this.p3 = p3;</span>
<span class="nc" id="L56">	}</span>

	public void setPs(NPoint p1, NPoint p2, NPoint p3) {
<span class="nc" id="L59">		this.p1 = p1;</span>
<span class="nc" id="L60">		this.p2 = p2;</span>
<span class="nc" id="L61">		this.p3 = p3;</span>
<span class="nc" id="L62">	}</span>

	@Override
	public String toString() {
<span class="nc" id="L66">		return &quot;TriangleRegion [p1=&quot; + p1 + &quot;, p2=&quot; + p2 + &quot;, p3=&quot; + p3 + &quot;]&quot;;</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>