<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename resource &apos;ART_main/src/test/simulations/ART_ORB/RRT_TD_TwoDs.java&apos; to &apos;RRT_TD_TwoDs_blockFailure.java&apos;" description="Rename resource &apos;RRT_TD_TwoDs.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/ART_ORB/RRT_TD_TwoDs.java" name="RRT_TD_TwoDs_blockFailure.java" stamp="1507629510452" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_main/src/test/simulations/ART_ORB/ORB_ND_Restrict.java&apos; to &apos;ORB_ND_squareRestrict.java&apos;" description="Rename resource &apos;ORB_ND_Restrict.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/ART_ORB/ORB_ND_Restrict.java" name="ORB_ND_squareRestrict.java" stamp="1507629694968" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_main/src/test/simulations/ART_ORB/ORB_ND(1).java&apos; to &apos;ORB_ND_Xi.java&apos;" description="Rename resource &apos;ORB_ND(1).java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/ART_ORB/ORB_ND(1).java" name="ORB_ND_Xi.java" stamp="1507633725775" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_main/src/test/simulations/ART_ORB/ComplexRegion.java&apos; to &apos;ComplexRegion_Xi.java&apos;" description="Rename resource &apos;ComplexRegion.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/ART_ORB/ComplexRegion.java" name="ComplexRegion_Xi.java" stamp="1507633749501" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_main/src/test/simulations/ART_ORB/ORB_RRT_ND.java&apos; to &apos;ORB_RRT_ND_Xi.java&apos;" description="Rename resource &apos;ORB_RRT_ND.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/simulations/ART_ORB/ORB_RRT_ND.java" name="ORB_RRT_ND_Xi.java" stamp="1507633784737" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_main/src/datastructure/ND/ComplexRegion_Xi.java&apos; to &apos;ComplexRegion.java&apos;" description="Rename resource &apos;ComplexRegion_Xi.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/datastructure/ND/ComplexRegion_Xi.java" name="ComplexRegion.java" stamp="1507634309251" updateReferences="true"/>
</session>