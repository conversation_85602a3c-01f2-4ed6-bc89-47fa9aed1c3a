<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MySimulationTD0.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">MySimulationTD0.java</span></div><h1>MySimulationTD0.java</h1><pre class="source lang-java linenums">package tested;

<span class="nc" id="L3">public class MySimulationTD0 {</span>
<span class="nc" id="L4">	public double[] min = { 0.0, 1.0 };</span>
<span class="nc" id="L5">	public double[] max = { 0.0, 1.0 };</span>

	public String correct(double x, double y) {
<span class="nc bnc" id="L8" title="All 2 branches missed.">		if (x == 0.1)</span>
<span class="nc" id="L9">			return &quot;aaa&quot;;</span>
		else {
<span class="nc" id="L11">			return &quot;bbb&quot;;</span>
		}
	}

	public boolean isCorrect(double x, double y) {
		// System.out.println(&quot;correct:&quot;+correct(x));
		// System.out.println(&quot;wrong:&quot;+wrong(x));
<span class="nc" id="L18">		return correct(x, y).equals(wrong(x, y));</span>
	}

	public String wrong(double x, double y) {
<span class="nc bnc" id="L22" title="All 2 branches missed.">		if (x == 0) {</span>
<span class="nc" id="L23">			return &quot;aaa&quot;;</span>
		} else {
<span class="nc" id="L25">			return &quot;bbb&quot;;</span>
		}
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>