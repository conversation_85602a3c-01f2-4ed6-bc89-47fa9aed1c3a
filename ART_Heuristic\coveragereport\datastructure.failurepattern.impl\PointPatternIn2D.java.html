<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>PointPatternIn2D.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.failurepattern.impl</a> &gt; <span class="el_source">PointPatternIn2D.java</span></div><h1>PointPatternIn2D.java</h1><pre class="source lang-java linenums">package datastructure.failurepattern.impl;

import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import util.draw.StdDraw;

<span class="nc" id="L9">public class PointPatternIn2D extends FailurePattern {</span>

	public static void main(String[] args) {
<span class="nc" id="L12">		PointPatternIn2D test = new PointPatternIn2D();</span>
<span class="nc" id="L13">		test.dimension = 2;</span>
<span class="nc" id="L14">		test.min = new double[] { 0, 0 };</span>
<span class="nc" id="L15">		test.max = new double[] { 1, 1 };</span>
<span class="nc" id="L16">		test.fail_rate = 0.01;</span>
<span class="nc" id="L17">		test.random = new Random(5);</span>
<span class="nc" id="L18">		test.fail_regionS = 0.01;</span>
<span class="nc" id="L19">		test.eachPointArea = 0.01 / 50.0;</span>
<span class="nc" id="L20">		test.points = new NPoint[50];</span>
<span class="nc" id="L21">		StdDraw.rectangle(0.5, 0.5, 0.5, 0.5);</span>
<span class="nc bnc" id="L22" title="All 2 branches missed.">		for (int i = 0; i &lt; 50; i++) {</span>
<span class="nc" id="L23">			NPoint temp = test.genRandomPoint();</span>
<span class="nc" id="L24">			System.out.println(temp);</span>
<span class="nc" id="L25">			StdDraw.circle(temp.getXn()[0], temp.getXn()[1], Math.sqrt(test.eachPointArea / Math.PI));</span>
<span class="nc" id="L26">			test.points[i] = temp;</span>
		}
		// test.genFailurePattern();
<span class="nc" id="L29">		NPoint p1 = new NPoint(new double[] { 0.33842373215897027, 0.2875843418937697 });</span>
<span class="nc" id="L30">		StdDraw.filledCircle(p1.getXn()[0], p1.getXn()[1], 0.001);</span>

<span class="nc" id="L32">		System.out.println(&quot;isCorrect:&quot; + test.isCorrect(p1));</span>
<span class="nc" id="L33">	}</span>

<span class="nc" id="L35">	private int count = 50;</span>

	private double fail_regionS;

	private double eachPointArea;

	private NPoint[] points;

	@Override
	public void genFailurePattern() {
<span class="nc" id="L45">		double totalArea = 1.0;</span>
<span class="nc bnc" id="L46" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc" id="L47">			totalArea *= (max[i] - min[i]);</span>
		}
<span class="nc" id="L49">		fail_regionS = this.fail_rate * totalArea;</span>

<span class="nc" id="L51">		eachPointArea = fail_regionS / (double) this.count;</span>

<span class="nc" id="L53">		points = new NPoint[this.count];</span>

<span class="nc bnc" id="L55" title="All 2 branches missed.">		for (int i = 0; i &lt; this.count; i++) {</span>
<span class="nc" id="L56">			points[i] = genRandomPoint();</span>
		}
<span class="nc" id="L58">	}</span>

	private NPoint genRandomPoint() {
<span class="nc" id="L61">		double x = random.nextDouble() * (this.max[0] - this.min[0]) + min[0];</span>
<span class="nc" id="L62">		double y = random.nextDouble() * (this.max[1] - this.min[1]) + min[1];</span>
<span class="nc" id="L63">		return new NPoint(new double[] { x, y });</span>
	}

	public int getCount() {
<span class="nc" id="L67">		return count;</span>
	}

	@Override
	public boolean isCorrect(NPoint p) {
<span class="nc" id="L72">		boolean flag = true;</span>
<span class="nc" id="L73">		double radius = Math.sqrt(this.eachPointArea / Math.PI);</span>
<span class="nc" id="L74">		double x = p.getXn()[0];</span>
<span class="nc" id="L75">		double y = p.getXn()[1];</span>
<span class="nc bnc" id="L76" title="All 2 branches missed.">		for (int i = 0; i &lt; this.count; i++) {</span>
<span class="nc" id="L77">			double x1 = points[i].getXn()[0];</span>
<span class="nc" id="L78">			double y1 = points[i].getXn()[1];</span>
<span class="nc" id="L79">			double distance = Math.sqrt((x1 - x) * (x1 - x) + (y1 - y) * (y1 - y));</span>
<span class="nc bnc" id="L80" title="All 2 branches missed.">			if (distance &lt; radius) {</span>
<span class="nc" id="L81">				flag = false;</span>
			}
		}

<span class="nc" id="L85">		return flag;</span>
	}

	public void setCount(int count) {
<span class="nc" id="L89">		this.count = count;</span>
<span class="nc" id="L90">	}</span>

	@Override
	public void showFailurePattern() {
		
<span class="nc" id="L95">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>