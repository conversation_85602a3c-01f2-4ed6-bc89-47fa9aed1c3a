<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>FileUtils.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util.file</a> &gt; <span class="el_source">FileUtils.java</span></div><h1>FileUtils.java</h1><pre class="source lang-java linenums">package util.file;

import java.io.File;
import java.io.IOException;

import org.apache.poi.util.IOUtils;

<span class="nc" id="L8">public class FileUtils {</span>
	public static File createNewFile(String name) {
<span class="nc" id="L10">		File f = new File(&quot;outputs&quot; + File.separator + name);</span>
<span class="nc bnc" id="L11" title="All 2 branches missed.">		if (f.exists()) {</span>
<span class="nc" id="L12">			f.delete();</span>
<span class="nc" id="L13">		} else {</span>
			try {
<span class="nc" id="L15">				f.createNewFile();</span>
<span class="nc" id="L16">			} catch (IOException e) {</span>
				// TODO Auto-generated catch block
<span class="nc" id="L18">				System.out.println(&quot;创建文件f失败&quot;);</span>
<span class="nc" id="L19">				e.printStackTrace();</span>
			}
		}
<span class="nc" id="L22">		return f;</span>
	}

	public static File createNewFile(String path, String name) {
<span class="nc" id="L26">		File f = new File(path + File.separator + name);</span>
<span class="nc bnc" id="L27" title="All 2 branches missed.">		if (f.exists()) {</span>
<span class="nc" id="L28">			f.delete();</span>
<span class="nc" id="L29">		} else {</span>
			try {
<span class="nc" id="L31">				f.createNewFile();</span>
<span class="nc" id="L32">			} catch (IOException e) {</span>
				// TODO Auto-generated catch block
<span class="nc" id="L34">				System.out.println(&quot;创建文件f失败&quot;);</span>
<span class="nc" id="L35">				e.printStackTrace();</span>
			}
		}
<span class="nc" id="L38">		return f;</span>
	}

	public static void main(String[] args) {
<span class="nc" id="L42">	}</span>

	public static void writeLine(File f) {
		
<span class="nc" id="L46">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>