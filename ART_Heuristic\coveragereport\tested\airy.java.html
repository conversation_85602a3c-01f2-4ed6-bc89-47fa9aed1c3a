<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>airy.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">airy.java</span></div><h1>airy.java</h1><pre class="source lang-java linenums">package tested;

import util.TestProgram;

<span class="nc" id="L5">public class airy  {</span>
<span class="nc" id="L6">	public static  double[] min = { -5000 };</span>
<span class="nc" id="L7">	public static  double[] max = { 5000 };</span>
<span class="nc" id="L8">	public static double failureRate = 0.000716;</span>
<span class="nc" id="L9">	public static  int Dimension = 1;</span>

	public boolean isCorrect(double x) {
		// System.out.println(&quot;correct:&quot;+correct(x));
		// System.out.println(&quot;wrong:&quot;+wrong(x));
<span class="nc" id="L14">		return TestProgram.test_airy(x);</span>
	}

	public boolean isCorrect(double[] x) {
<span class="nc bnc" id="L18" title="All 2 branches missed.">		if (x.length == this.Dimension) {</span>
<span class="nc" id="L19">			return TestProgram.test_airy(x[0]);</span>
		} else {
<span class="nc" id="L21">			return true;</span>
		}
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>