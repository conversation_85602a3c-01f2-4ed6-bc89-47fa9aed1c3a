<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RT_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rt</a> &gt; <span class="el_source">RT_ND.java</span></div><h1>RT_ND.java</h1><pre class="source lang-java linenums">package test.simulations.rt;

import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.PointPatternIn1D;
import test.simulations.rrt.RRT_ND;
import util.data.ZeroOneCreator;

public class RT_ND {
	public static void main(String[] args) {
		// testTCTime();
<span class="nc" id="L15">		double failrate = 0.001;</span>
<span class="nc" id="L16">		int d = 3;</span>
<span class="nc" id="L17">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L18">		double[] min = dataCreator.minCreator(d);</span>
<span class="nc" id="L19">		double[] max = dataCreator.maxCreator(d);</span>
<span class="nc" id="L20">		FailurePattern failurePattern = new PointPatternIn1D();</span>
<span class="nc" id="L21">		failurePattern.fail_rate = failrate;</span>
<span class="nc" id="L22">		failurePattern.min = min;</span>
<span class="nc" id="L23">		failurePattern.max = max;</span>
<span class="nc" id="L24">		failurePattern.dimension = d;</span>

<span class="nc" id="L26">		RT_ND rt = new RT_ND(min, max, new Random( 3), failurePattern);</span>
		//rt.pmCount=69314;
<span class="nc" id="L28">		System.out.println(rt.pm());</span>

<span class="nc" id="L30">	}</span>

	public static void testFm() {
<span class="nc" id="L33">		int times = 3000;</span>
<span class="nc" id="L34">		double failrates[] = { 0.005, 0.002, 0.001, 0.0015, 0.0001 };</span>
<span class="nc bnc" id="L35" title="All 2 branches missed.">		for (int j = 0; j &lt; failrates.length; j++) {</span>
<span class="nc" id="L36">			double failrate = failrates[j];</span>
<span class="nc" id="L37">			int d = 3;</span>
<span class="nc" id="L38">			ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L39">			double[] min = dataCreator.minCreator(d);</span>
<span class="nc" id="L40">			double[] max = dataCreator.maxCreator(d);</span>
<span class="nc" id="L41">			FailurePattern failurePattern = new PointPatternIn1D();</span>
<span class="nc" id="L42">			failurePattern.fail_rate = failrate;</span>
<span class="nc" id="L43">			failurePattern.min = min;</span>
<span class="nc" id="L44">			failurePattern.max = max;</span>
<span class="nc" id="L45">			failurePattern.dimension = d;</span>

<span class="nc" id="L47">			int fm = 0;</span>
<span class="nc" id="L48">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L49" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L50">				RT_ND rt = new RT_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L51">				int temp = rt.run();</span>
<span class="nc" id="L52">				fm += temp;</span>
			}
<span class="nc" id="L54">			long endTime = System.currentTimeMillis();</span>

<span class="nc" id="L56">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
		}
<span class="nc" id="L58">	}</span>

	public static double testTCTime(int d, int tcCount) {

<span class="nc" id="L62">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L63">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L64">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L66">		int times = 1;</span>

<span class="nc" id="L68">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L69">		failurePattern.fail_rate = 0.001;</span>
<span class="nc" id="L70">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L72">			RT_ND rt = new RT_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L73">			rt.tcCount = tcCount;</span>
<span class="nc" id="L74">			rt.time();</span>
		}
<span class="nc" id="L76">		long endTime = System.currentTimeMillis();</span>

<span class="nc" id="L78">		return ((endTime - startTime) / (double) times);</span>
	}

	public static double[] testEm(int dimension, double failrate) {
<span class="nc" id="L82">		int emTimes = 6;</span>
<span class="nc" id="L83">		double[] result = new double[emTimes];</span>

<span class="nc" id="L85">		int d = dimension;</span>
<span class="nc" id="L86">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L87">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L88">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L90">		int times = 2000;</span>

<span class="nc" id="L92">		int temp = 0;</span>

<span class="nc" id="L94">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L95">		failurePattern.fail_rate = failrate;</span>
<span class="nc bnc" id="L96" title="All 2 branches missed.">		for (int k = 0; k &lt; emTimes; k++) {</span>
<span class="nc" id="L97">			long sums = 0;</span>
<span class="nc" id="L98">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L100">				RT_ND rt = new RT_ND(min, max, new Random(), failurePattern);</span>
<span class="nc" id="L101">				rt.emCount = (k + 1) * 500;</span>
<span class="nc" id="L102">				temp = rt.em();</span>
<span class="nc" id="L103">				sums += temp;</span>
			}
<span class="nc" id="L105">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L106">			double em = sums / (double) times;</span>
<span class="nc" id="L107">			System.out.println(&quot;em:&quot; + em + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
<span class="nc" id="L108">			result[k] = em;</span>
		}
<span class="nc" id="L110">		System.out.println();</span>
<span class="nc" id="L111">		return result;</span>
	}

<span class="nc" id="L114">	public int tcCount = 0;</span>
<span class="nc" id="L115">	public int emCount = 0;</span>
<span class="nc" id="L116">	public int pmCount=0;</span>
	double[] min;
	double[] max;
	int dimension;
	FailurePattern failPattern;

	Random random;

<span class="nc" id="L124">	public RT_ND(double[] min, double[] max, Random random, FailurePattern pattern) {</span>
<span class="nc" id="L125">		this.min = min;</span>
<span class="nc" id="L126">		this.max = max;</span>

<span class="nc" id="L128">		this.dimension = min.length;</span>

<span class="nc" id="L130">		this.random = random;</span>
<span class="nc" id="L131">		pattern.min = min;</span>
<span class="nc" id="L132">		pattern.max = max;</span>
<span class="nc" id="L133">		pattern.dimension = this.dimension;</span>
<span class="nc" id="L134">		pattern.random = random;</span>
<span class="nc" id="L135">		pattern.genFailurePattern();</span>

<span class="nc" id="L137">		this.failPattern = pattern;</span>
		
<span class="nc" id="L139">		this.pmCount=(int) Math.round(Math.log(0.5)/Math.log(1-failPattern.fail_rate)); //95% CI</span>
<span class="nc" id="L140">	}</span>

	public NPoint randomTC() {
<span class="nc" id="L143">		NPoint point = new NPoint();</span>
<span class="nc" id="L144">		point.dimension = this.dimension;</span>
<span class="nc" id="L145">		double[] xn = new double[this.dimension];</span>
<span class="nc bnc" id="L146" title="All 2 branches missed.">		for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="nc" id="L147">			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];</span>
		}
<span class="nc" id="L149">		point.setXn(xn);</span>
<span class="nc" id="L150">		return point;</span>
	}

	public int run() {
<span class="nc" id="L154">		int count = 0;</span>
<span class="nc" id="L155">		NPoint p = randomTC();</span>
<span class="nc bnc" id="L156" title="All 2 branches missed.">		while (this.failPattern.isCorrect(p)) {</span>
			// while(count&lt;=tcCount){
<span class="nc" id="L158">			count++;</span>
<span class="nc" id="L159">			p = randomTC();</span>
		}
<span class="nc" id="L161">		return count;</span>
	}

	public int em() {
<span class="nc" id="L165">		int count = 0;</span>
<span class="nc" id="L166">		int emTemp = 0;</span>
<span class="nc" id="L167">		NPoint p = randomTC();</span>
<span class="nc bnc" id="L168" title="All 2 branches missed.">		while (count &lt;= emCount) {</span>
<span class="nc bnc" id="L169" title="All 2 branches missed.">			if (!this.failPattern.isCorrect(p)) {</span>
<span class="nc" id="L170">				emTemp++;</span>
			}
<span class="nc" id="L172">			count++;</span>
<span class="nc" id="L173">			p = new NPoint();</span>
<span class="nc" id="L174">			p = randomTC();</span>
		}
<span class="nc" id="L176">		return emTemp;</span>
	}

	public NPoint generateNextTC() {
<span class="nc" id="L180">		NPoint p = randomTC();</span>
<span class="nc" id="L181">		return p;</span>
	}

	public void time() {
<span class="nc" id="L185">		int count = 0;</span>
<span class="nc" id="L186">		NPoint p = generateNextTC();</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">		while (count &lt; this.tcCount) {</span>
<span class="nc" id="L188">			count++;</span>
<span class="nc" id="L189">			p = generateNextTC();</span>
		}
<span class="nc" id="L191">	}</span>

	public double pm() {
<span class="nc" id="L194">		this.pmCount=(int) Math.round(Math.log(0.5)/Math.log(1-failPattern.fail_rate));</span>
<span class="nc" id="L195">		return 1 - Math.pow((1 - failPattern.fail_rate), pmCount);</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>