<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRTtpRegion</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.rrttp.partion</a> &gt; <span class="el_class">RRTtpRegion</span></div><h1>RRTtpRegion</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,569 of 2,569</td><td class="ctr2">0%</td><td class="bar">182 of 182</td><td class="ctr2">0%</td><td class="ctr1">100</td><td class="ctr2">100</td><td class="ctr1">168</td><td class="ctr2">168</td><td class="ctr1">9</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a8"><a href="RRTtpRegion.java.html#L109" class="el_method">twoPointRegion(double[], double[], double, int)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="1,285" alt="1,285"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="106" height="10" title="48" alt="48"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">25</td><td class="ctr2" id="g1">25</td><td class="ctr1" id="h0">50</td><td class="ctr2" id="i0">50</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a3"><a href="RRTtpRegion.java.html#L180" class="el_method">onePointRegion(double[], double[], double, int)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="76" height="10" title="817" alt="817"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="54" alt="54"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">28</td><td class="ctr2" id="g0">28</td><td class="ctr1" id="h1">36</td><td class="ctr2" id="i1">36</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a7"><a href="RRTtpRegion.java.html#L63" class="el_method">twoPointIn(double, double, double)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="13" height="10" title="143" alt="143"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="57" height="10" title="26" alt="26"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f2">14</td><td class="ctr2" id="g2">14</td><td class="ctr1" id="h4">17</td><td class="ctr2" id="i4">17</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a1"><a href="RRTtpRegion.java.html#L26" class="el_method">calAvailSize(double)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="9" height="10" title="106" alt="106"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="44" height="10" title="20" alt="20"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f3">11</td><td class="ctr2" id="g3">11</td><td class="ctr1" id="h2">23</td><td class="ctr2" id="i2">23</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a0"><a href="RRTtpRegion.java.html#L225" class="el_method">availRegions(double)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="9" height="10" title="101" alt="101"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="44" height="10" title="20" alt="20"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">11</td><td class="ctr2" id="g4">11</td><td class="ctr1" id="h3">22</td><td class="ctr2" id="i3">22</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a2"><a href="RRTtpRegion.java.html#L85" class="el_method">onePointIn(double, double, double)</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="6" height="10" title="68" alt="68"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="31" height="10" title="14" alt="14"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">8</td><td class="ctr2" id="g5">8</td><td class="ctr1" id="h5">12</td><td class="ctr2" id="i5">12</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a6"><a href="RRTtpRegion.java.html#L104" class="el_method">toString()</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="3" height="10" title="36" alt="36"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a5"><a href="RRTtpRegion.java.html#L19" class="el_method">RRTtpRegion(NRectRegion)</a></td><td class="bar" id="b7"/><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h6">4</td><td class="ctr2" id="i6">4</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="RRTtpRegion.java.html#L16" class="el_method">RRTtpRegion()</a></td><td class="bar" id="b8"/><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>