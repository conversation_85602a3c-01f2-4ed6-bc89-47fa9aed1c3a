<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<catalog xmlns="urn:oasis:names:tc:entity:xmlns:xml:catalog">
  <system systemId="http://maven.apache.org/maven-v4_0_0.xsd" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.m2e.model.edit_1.7.0.20160603-1933.jar!/xsd/maven-v4_0_0.xsd"/>
  <system systemId="http://maven.apache.org/xsd/maven-4.0.0.xsd" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.m2e.model.edit_1.7.0.20160603-1933.jar!/xsd/maven-v4_0_0.xsd"/>
  <system systemId="http://maven.apache.org/xsd/settings-1.0.0.xsd" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.m2e.model.edit_1.7.0.20160603-1933.jar!/xsd/settings-v1_0_0.xsd"/>
  <system systemId="http://maven.apache.org/xsd/profiles-1.0.0.xsd" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.m2e.model.edit_1.7.0.20160603-1933.jar!/xsd/profiles-v1_0_0.xsd"/>
  <system systemId="http://maven.apache.org/xsd/archetype-1.0.0.xsd" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.m2e.model.edit_1.7.0.20160603-1933.jar!/xsd/archetype-1.0.0.xsd"/>
  <system systemId="http://maven.apache.org/xsd/archetype-catalog-1.0.0.xsd" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.m2e.model.edit_1.7.0.20160603-1933.jar!/xsd/archetype-catalog-1.0.0.xsd"/>
  <system systemId="http://maven.apache.org/xsd/archetype-descriptor-1.0.0.xsd" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.m2e.model.edit_1.7.0.20160603-1933.jar!/xsd/archetype-descriptor-1.0.0.xsd"/>
  <public publicId="-//WAPFORUM//DTD WML 1.1//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/wml11.dtd"/>
  <public publicId="-//W3C//DTD XHTML 1.0 Strict//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/xhtml1-strict.dtd" webURL="http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"/>
  <public publicId="-//W3C//DTD XHTML 1.0 Transitional//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/xhtml1-transitional.dtd" webURL="http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd"/>
  <public publicId="-//W3C//DTD XHTML 1.0 Frameset//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/xhtml1-frameset.dtd" webURL="http://www.w3.org/TR/xhtml1/DTD/xhtml1-frameset.dtd"/>
  <public publicId="-//W3C//DTD XHTML Basic 1.0//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/xhtml-basic10-f.dtd" webURL="http://www.w3.org/TR/xhtml-basic/xhtml-basic10.dtd"/>
  <public publicId="-//W3C//DTD XHTML 1.1//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/xhtml11-flat.dtd" webURL="http://www.w3.org/TR/xhtml11/DTD/xhtml11.dtd"/>
  <public publicId="-//WAPFORUM//DTD XHTML Mobile 1.0//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/xhtml-mobile10-flat.dtd" webURL="http://www.wapforum.org/DTD/xhtml-mobile10.dtd"/>
  <public publicId="-//WAPFORUM//DTD WML 1.3//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/wml13.dtd" webURL="http://www.wapforum.org/DTD/wml13.dtd"/>
  <public publicId="-//W3C//DTD HTML 4.01 Frameset//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/frameset.dtd" webURL="http://www.w3.org/TR/html4/frameset.dtd"/>
  <public publicId="-//W3C//ENTITIES Latin 1//EN//HTML" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/HTMLlat1.ent" webURL="HTMLlat1.ent"/>
  <public publicId="-//W3C//ENTITIES Special//EN//HTM" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/HTMLspecial.ent" webURL="HTMLspecial.ent"/>
  <public publicId="-//W3C//ENTITIES Symbols//EN//HTML" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/HTMLsymbol.ent" webURL="HTMLsymbol.ent"/>
  <public publicId="-//W3C//DTD HTML 4.01 Transitional//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/loose.dtd" webURL="http://www.w3.org/TR/html4/loose.dtd"/>
  <public publicId="-//W3C//DTD HTML 4.01//EN" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/strict.dtd" webURL="http://www.w3.org/TR/html4/strict.dtd"/>
  <uri name="http://schemas.xmlsoap.org/wsdl/" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/xsd/wsdl.xsd"/>
  <uri name="http://schemas.xmlsoap.org/wsdl/soap/" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/xsd/soap.xsd"/>
  <uri name="http://schemas.xmlsoap.org/wsdl/http/" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/xsd/http.xsd"/>
  <uri name="http://schemas.xmlsoap.org/wsdl/mime/" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/xsd/wsdl-mime.xsd"/>
  <uri name="http://schemas.xmlsoap.org/soap/encoding/" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/xsd/soapenc.xsd"/>
  <uri name="http://schemas.xmlsoap.org/soap/envelope/" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/xsd/soapenv.xsd"/>
  <uri name="urn:oasis:names:tc:entity:xmlns:xml:catalog" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/xsd/xmlcatalog11.xsd"/>
  <uri name="http://www.w3.org/TR/html4/loose.dtd" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/loose.dtd"/>
  <uri name="http://www.w3.org/TR/html4/strict.dtd" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/dtds/strict.dtd"/>
  <uri name="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200702" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/xsd/ws-securitypolicy-1.2.xsd"/>
  <uri name="http://docs.oasis-open.org/ws-sx/ws-securitypolicy/200802" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.wst.standard.schemas_1.0.700.v201304171715.jar!/xsd/ws-securitypolicy.xsd"/>
  <system systemId="http://www.w3.org/2001/xml.xsd" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.xsd_2.12.0.v20160526-0356.jar!/cache/www.w3.org/2001/xml.xsd"/>
  <uri name="http://www.w3.org/2001/XMLSchema" uri="jar:file:/C:/Users/<USER>/eclipse/java-neon/eclipse/../../../.p2/pool/plugins/org.eclipse.xsd_2.12.0.v20160526-0356.jar!/cache/www.w3.org/2001/XMLSchema.xsd"/>
</catalog>
