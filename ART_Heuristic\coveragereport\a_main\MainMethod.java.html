<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MainMethod.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">a_main</a> &gt; <span class="el_source">MainMethod.java</span></div><h1>MainMethod.java</h1><pre class="source lang-java linenums">package a_main;

import java.awt.Dimension;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.Random;

import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.PointPattern;
import datastructure.failurepattern.impl.PointPatternIn1D;
import datastructure.failurepattern.impl.PointPatternIn2D;
import datastructure.failurepattern.impl.PointPatternIn3D;
import datastructure.failurepattern.impl.PointPatternIn4D;
import datastructure.failurepattern.impl.StripPattern;
import datastructure.failurepattern.impl.StripPatternIn2D;
import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tp._ND.ART_TP_ND;
import test.simulations.art_tpp.ART_TPP;
import test.simulations.fscs.FSCS_ND;
import test.simulations.rrt.RRT_ND;
import test.simulations.rrttp.hilbert.RRTtp1D;
import test.simulations.rrttp.hilbert.RRTtpND_H;
import test.simulations.rt.RT_ND;
import util.data.ZeroOneCreator;
import util.file.FileUtils;

<span class="nc" id="L31">public class MainMethod {</span>
	// TODO 完成参数化
	public static void main(String[] args)throws Exception {
<span class="nc" id="L34">		int times = 2000;</span>
<span class="nc" id="L35">		double failrates[] = { 0.005,0.002,0.001,0.0015 };</span>
<span class="nc" id="L36">		String path = &quot;C:\\Users\\<USER>\\Desktop\\研究方向和内容\\小论文\\补充数据\\fm\\point&quot;;</span>
		
<span class="nc bnc" id="L38" title="All 2 branches missed.">		for (int j = 0; j &lt; failrates.length; j++) {</span>
<span class="nc" id="L39">			double failrate = failrates[j];</span>
<span class="nc" id="L40">			System.out.println(&quot;failure rate:&quot;+failrate);</span>
			
<span class="nc" id="L42">			int d = 4;</span>
			
<span class="nc" id="L44">			File rtf = FileUtils.createNewFile(path, d+&quot;维-&quot;+failrate+&quot;all.txt&quot;);</span>
<span class="nc" id="L45">			BufferedWriter writer = get(rtf);</span>
			
<span class="nc" id="L47">			ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L48">			double[] min = dataCreator.minCreator(d);</span>
<span class="nc" id="L49">			double[] max = dataCreator.maxCreator(d);</span>
			//FailurePattern failurePattern = new BlockPattern();
			//FailurePattern failurePattern=new StripPattern();
<span class="nc" id="L52">			FailurePattern failurePattern = new PointPatternIn4D();</span>
<span class="nc" id="L53">			failurePattern.fail_rate = failrate;</span>
<span class="nc" id="L54">			failurePattern.min = min;</span>
<span class="nc" id="L55">			failurePattern.max = max;</span>
<span class="nc" id="L56">			failurePattern.dimension = d;</span>

			//rt
<span class="nc" id="L59">			int fm = 0;</span>
<span class="nc" id="L60">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L61" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L62">				RT_ND rt = new RT_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L63">				int temp = rt.run();</span>
<span class="nc" id="L64">				fm += temp;</span>
			}
<span class="nc" id="L66">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L67">			writer.write((fm / (double) times)+&quot;&quot;);</span>
<span class="nc" id="L68">			writer.newLine();</span>
<span class="nc" id="L69">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
		
			//rrt
<span class="nc" id="L72">			double r=0.75;</span>
<span class="nc bnc" id="L73" title="All 2 branches missed.">			if(d&gt;=2){</span>
<span class="nc" id="L74">				r=1.5;</span>
			}
<span class="nc" id="L76">			fm=0;</span>
<span class="nc" id="L77">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L79">				RRT_ND rt = new RRT_ND(min, max,  failurePattern,new Random(i * 3),r);</span>
<span class="nc" id="L80">				int temp = rt.run();</span>
<span class="nc" id="L81">				fm += temp;</span>
			}
<span class="nc" id="L83">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L84">			writer.write((fm / (double) times)+&quot;&quot;);</span>
<span class="nc" id="L85">			writer.newLine();</span>
<span class="nc" id="L86">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//fscs
<span class="nc" id="L89">			fm=0;</span>
<span class="nc" id="L90">			int s=10;</span>
<span class="nc" id="L91">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L92" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L93">				FSCS_ND rt = new FSCS_ND(min, max, s, failurePattern,new Random(i * 3));</span>
<span class="nc" id="L94">				int temp = rt.run();</span>
<span class="nc" id="L95">				fm += temp;</span>
			}
<span class="nc" id="L97">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L98">			writer.write((fm / (double) times)+&quot;&quot;);</span>
<span class="nc" id="L99">			writer.newLine();</span>
<span class="nc" id="L100">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			
			//art_b
<span class="nc" id="L104">			fm=0;</span>
<span class="nc" id="L105">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L106" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L107">				ART_B_ND rt = new ART_B_ND(min, max, new Random(i * 3),failurePattern);</span>
<span class="nc" id="L108">				int temp = rt.run();</span>
<span class="nc" id="L109">				fm += temp;</span>
			}
<span class="nc" id="L111">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L112">			writer.write((fm / (double) times)+&quot;&quot;);</span>
<span class="nc" id="L113">			writer.newLine();</span>
<span class="nc" id="L114">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//art_rp
<span class="nc" id="L117">			fm=0;</span>
<span class="nc" id="L118">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L119" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L120">				ART_RP_ND rt = new ART_RP_ND(min, max, new Random(i * 3),failurePattern);</span>
<span class="nc" id="L121">				int temp = rt.run();</span>
<span class="nc" id="L122">				fm += temp;</span>
			}
<span class="nc" id="L124">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L125">			writer.write((fm / (double) times)+&quot;&quot;);</span>
<span class="nc" id="L126">			writer.newLine();</span>
<span class="nc" id="L127">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//art_tp
<span class="nc" id="L130">			fm=0;</span>
<span class="nc" id="L131">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L132" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L133">				ART_TP_ND rt = new ART_TP_ND(min, max, failurePattern,new Random(i * 3));</span>
<span class="nc" id="L134">				int temp = rt.run();</span>
<span class="nc" id="L135">				fm += temp;</span>
			}
<span class="nc" id="L137">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L138">			writer.write((fm / (double) times)+&quot;&quot;);</span>
<span class="nc" id="L139">			writer.newLine();</span>
<span class="nc" id="L140">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//art_tpp
<span class="nc" id="L143">			fm=0;</span>
<span class="nc" id="L144">			int k=10;</span>
<span class="nc" id="L145">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L146" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L147">				ART_TPP rt = new ART_TPP(min, max, new Random(i * 3),failurePattern,k);</span>
<span class="nc" id="L148">				int temp = rt.run();</span>
<span class="nc" id="L149">				fm += temp;</span>
			}
<span class="nc" id="L151">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L152">			writer.write((fm / (double) times)+&quot;&quot;);</span>
<span class="nc" id="L153">			writer.newLine();</span>
<span class="nc" id="L154">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//my method
			//1dimension
<span class="nc" id="L158">			r=0.75;</span>
<span class="nc" id="L159">			fm=0;</span>
<span class="nc" id="L160">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L161" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
//				/min, max, 0.75, pattern, new Random(i * 3+v*5)
<span class="nc" id="L163">				RRTtpND_H rt = new RRTtpND_H(min, max, r, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L164">				int temp = rt.run();</span>
<span class="nc" id="L165">				fm += temp;</span>
			}
<span class="nc" id="L167">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L168">			writer.write((fm / (double) times)+&quot;&quot;);</span>
<span class="nc" id="L169">			writer.newLine();</span>
<span class="nc" id="L170">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
<span class="nc" id="L172">			writer.close();</span>
		}
<span class="nc" id="L174">	}</span>
	public static BufferedWriter get(File f) throws Exception{
<span class="nc" id="L176">		return new BufferedWriter(new OutputStreamWriter(new FileOutputStream(f),&quot;UTF-8&quot;));</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>