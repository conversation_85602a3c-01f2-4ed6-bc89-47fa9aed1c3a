<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_Center_ND</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.art_center</a> &gt; <span class="el_class">ART_Center_ND</span></div><h1>ART_Center_ND</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">504 of 504</td><td class="ctr2">0%</td><td class="bar">26 of 26</td><td class="ctr2">0%</td><td class="ctr1">24</td><td class="ctr2">24</td><td class="ctr1">101</td><td class="ctr2">101</td><td class="ctr1">11</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a0"><a href="ART_Center_ND.java.html#L110" class="el_method">addRegionsInND(NRectRegion, NPoint)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="99" alt="99"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">4</td><td class="ctr2" id="g0">4</td><td class="ctr1" id="h0">18</td><td class="ctr2" id="i0">18</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="ART_Center_ND.java.html#L76" class="el_method">makeMaxRegionSmall(NRectRegion)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="99" height="10" title="82" alt="82"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h4">9</td><td class="ctr2" id="i4">9</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="ART_Center_ND.java.html#L164" class="el_method">main(String[])</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="96" height="10" title="80" alt="80"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h1">18</td><td class="ctr2" id="i1">18</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a8"><a href="ART_Center_ND.java.html#L32" class="el_method">run()</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="73" height="10" title="61" alt="61"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h2">13</td><td class="ctr2" id="i2">13</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a4"><a href="ART_Center_ND.java.html#L59" class="el_method">generateTC()</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="69" height="10" title="57" alt="57"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h3">11</td><td class="ctr2" id="i3">11</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a10"><a href="ART_Center_ND.java.html#L135" class="el_method">splitRegions(double[], double[])</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="53" height="10" title="44" alt="44"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="40" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h5">9</td><td class="ctr2" id="i5">9</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a7"><a href="ART_Center_ND.java.html#L150" class="el_method">maxRegion()</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="36" height="10" title="30" alt="30"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h6">8</td><td class="ctr2" id="i6">8</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a9"><a href="ART_Center_ND.java.html#L95" class="el_method">splitRegion(int, NPoint)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="32" height="10" title="27" alt="27"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="80" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h7">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><a href="ART_Center_ND.java.html#L21" class="el_method">ART_Center_ND(double[], double[], Random, FailurePattern)</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="24" height="10" title="20" alt="20"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">5</td><td class="ctr2" id="i8">5</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="ART_Center_ND.java.html#L192" class="el_method">em()</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">1</td><td class="ctr2" id="i9">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a3"><a href="ART_Center_ND.java.html#L198" class="el_method">generateNextTC()</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">1</td><td class="ctr2" id="i10">1</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>