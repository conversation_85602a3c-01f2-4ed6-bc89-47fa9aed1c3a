<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MyART_OD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.myart._1D</a> &gt; <span class="el_source">MyART_OD.java</span></div><h1>MyART_OD.java</h1><pre class="source lang-java linenums">package test.simulations.myart._1D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

/**
 * 模拟实验，beta取固定的值 左曲线：
 * 
 */
public class MyART_OD {
<span class="nc" id="L13">	public static int ZUOQUXIAN = 1;</span>
<span class="nc" id="L14">	public static int YOUQUXIAN = 2;</span>
	public static void main(String[] args) throws Exception {
<span class="nc" id="L16">		int times = 300;</span>
<span class="nc" id="L17">		long sums = 0;</span>
<span class="nc" id="L18">		int temp = 0;</span>
<span class="nc" id="L19">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L20" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L21">			MyART_OD ccrr = new MyART_OD(0, 1, 0.0001, 10, i * 3);</span>
<span class="nc" id="L22">			temp = ccrr.test();</span>
<span class="nc" id="L23">			sums += temp;</span>
		}
<span class="nc" id="L25">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L26">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L27">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L28">	}</span>
	double min;
	double max;
	int seed;
	double fail_rate;
	double fail_start;
	double beta;

<span class="nc" id="L36">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L38">	public MyART_OD(double min, double max, double fail_rate, double beta, int seed) {</span>
<span class="nc" id="L39">		this.min = min;</span>
<span class="nc" id="L40">		this.max = max;</span>
<span class="nc" id="L41">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L42">		this.seed = seed;</span>
<span class="nc" id="L43">		this.beta = beta;</span>
<span class="nc" id="L44">	}</span>

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L47" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + (max - min) * fail_rate)) {</span>
<span class="nc" id="L48">			return false;</span>
		} else {
<span class="nc" id="L50">			return true;</span>
		}
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L55">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L56" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L57">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L58" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L59">				low = mid + 1;</span>
<span class="nc" id="L60">			} else {</span>
<span class="nc" id="L61">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L64" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L65">			mid = mid - 1;</span>
		}
<span class="nc" id="L67">		tests.add(mid + 1, p);</span>
<span class="nc" id="L68">	}</span>

	/**
	 * @return F-measure
	 * @throws Exception
	 */
	public int test() {
<span class="nc" id="L75">		Random random = new Random(seed);</span>
<span class="nc" id="L76">		double fail_size = (max - min) * (fail_rate);</span>
<span class="nc" id="L77">		fail_start = random.nextDouble() * (max - fail_size);</span>
<span class="nc" id="L78">		int count = 0;</span>
<span class="nc" id="L79">		TestCase p = new TestCase();</span>
		// 执行第一个测试用例
<span class="nc" id="L81">		p.p = random.nextDouble() * (max - min) + min;</span>
<span class="nc bnc" id="L82" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L83">			count++;</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L85">				tests.add(p);</span>
<span class="nc" id="L86">			} else</span>
<span class="nc" id="L87">				sortTestCases(p);</span>
			///
			double datum_line;// 基准线，待会求出
<span class="nc" id="L90">			double tempProbability = 0.0;</span>
<span class="nc" id="L91">			double sumProbability = 0.0;</span>
<span class="nc" id="L92">			ArrayList&lt;double[]&gt; integrals = new ArrayList&lt;&gt;();</span>
			/// 下面产生下一个测试用例,根据自己的概率曲线图
			// 先求第一段
<span class="nc" id="L95">			tempProbability = Math.pow((tests.get(0).p - min), 2.0) / (beta + 1.0);</span>
<span class="nc" id="L96">			sumProbability += tempProbability;</span>
<span class="nc" id="L97">			double[] informations = new double[5];</span>
<span class="nc" id="L98">			informations[0] = tempProbability;</span>
<span class="nc" id="L99">			informations[1] = ZUOQUXIAN;</span>
<span class="nc" id="L100">			informations[2] = min;</span>
<span class="nc" id="L101">			informations[3] = tests.get(0).p;</span>
<span class="nc" id="L102">			informations[4] = beta;</span>
<span class="nc" id="L103">			integrals.add(informations);</span>
			// 求中间一段的积分值
<span class="nc bnc" id="L105" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size() - 1; i++) {</span>
				// 右边
<span class="nc" id="L107">				tempProbability = Math.pow(((tests.get(i + 1).p - tests.get(i).p) / 2.0), 2.0) * (1.0 / (beta + 1.0));</span>
<span class="nc" id="L108">				sumProbability += tempProbability;</span>
<span class="nc" id="L109">				informations = new double[5];</span>
<span class="nc" id="L110">				informations[0] = tempProbability;</span>
<span class="nc" id="L111">				informations[1] = YOUQUXIAN;</span>
<span class="nc" id="L112">				informations[2] = tests.get(i).p;</span>
<span class="nc" id="L113">				informations[3] = (tests.get(i + 1).p + tests.get(i).p) / 2.0;</span>
<span class="nc" id="L114">				informations[4] = beta;</span>
<span class="nc" id="L115">				integrals.add(informations);</span>
				// 下一个点的左边
<span class="nc" id="L117">				tempProbability = Math.pow(((tests.get(i + 1).p - tests.get(i).p) / 2.0), 2.0) * (1.0 / (beta + 1.0));</span>
<span class="nc" id="L118">				sumProbability += tempProbability;</span>
<span class="nc" id="L119">				informations = new double[5];</span>
<span class="nc" id="L120">				informations[0] = tempProbability;</span>
<span class="nc" id="L121">				informations[1] = ZUOQUXIAN;</span>
<span class="nc" id="L122">				informations[2] = (tests.get(i + 1).p + tests.get(i).p) / 2.0;</span>
<span class="nc" id="L123">				informations[3] = tests.get(i + 1).p;</span>
<span class="nc" id="L124">				informations[4] = beta;</span>
<span class="nc" id="L125">				integrals.add(informations);</span>
			}
<span class="nc" id="L127">			tempProbability = Math.pow((max - tests.get(tests.size() - 1).p), 2.0) / (beta + 1.0);</span>
<span class="nc" id="L128">			sumProbability += tempProbability;</span>
<span class="nc" id="L129">			informations = new double[5];</span>
<span class="nc" id="L130">			informations[0] = tempProbability;</span>
<span class="nc" id="L131">			informations[1] = YOUQUXIAN;</span>
<span class="nc" id="L132">			informations[2] = tests.get(tests.size() - 1).p;</span>
<span class="nc" id="L133">			informations[3] = max;</span>
<span class="nc" id="L134">			informations[4] = beta;</span>
<span class="nc" id="L135">			integrals.add(informations);</span>
<span class="nc" id="L136">			datum_line = 1.0 / sumProbability;</span>
<span class="nc" id="L137">			double T = random.nextDouble() * 1.0;</span>
			// 确定在哪一段，然后求解出下一个点
<span class="nc" id="L139">			double SumIntegral = 0.0;</span>
<span class="nc" id="L140">			double PreIntegral = 0.0;</span>
<span class="nc" id="L141">			int temp = 0;</span>
			// 这里有问题，temp的值不一定就是前面一个的temp,理解错了，其实没有错
<span class="nc bnc" id="L143" title="All 2 branches missed.">			for (int i = 0; i &lt; integrals.size(); i++) {</span>
<span class="nc bnc" id="L144" title="All 2 branches missed.">				if (SumIntegral &lt; T) {</span>
<span class="nc" id="L145">					PreIntegral = SumIntegral;</span>
<span class="nc" id="L146">					temp = i;</span>
				}
<span class="nc" id="L148">				SumIntegral += integrals.get(i)[0] * datum_line;</span>
			}
			// draw picture
			// double drawtemp[]=integrals.get(0);

			// 求下一个测试用例
<span class="nc" id="L154">			int type = (int) integrals.get(temp)[1];</span>
<span class="nc" id="L155">			double start = integrals.get(temp)[2];</span>
<span class="nc" id="L156">			double end = integrals.get(temp)[3];</span>
<span class="nc bnc" id="L157" title="All 2 branches missed.">			if (type == ZUOQUXIAN) {</span>
<span class="nc" id="L158">				double temp1 = end - start;</span>
<span class="nc" id="L159">				double temp2 = integrals.get(temp)[4] + 1.0;</span>
<span class="nc" id="L160">				p = new TestCase();</span>
<span class="nc" id="L161">				double temp3 = (1.0 - (T - PreIntegral) * (temp2) / ((datum_line) * (Math.pow(temp1, 2.0))));</span>
<span class="nc" id="L162">				p.p = end - temp1 * Math.pow(temp3, (1.0 / temp2));</span>
<span class="nc" id="L163">			} else {</span>
<span class="nc" id="L164">				double temp1 = end - start;</span>
				// 这里也是错误的-&gt;double temp2=p.coverage+1.0
<span class="nc" id="L166">				double temp2 = integrals.get(temp)[4] + 1.0;</span>
<span class="nc" id="L167">				p = new TestCase();</span>
<span class="nc" id="L168">				p.p = start + temp1</span>
<span class="nc" id="L169">						* Math.pow((T - PreIntegral) * temp2 / (datum_line * Math.pow(temp1, 2.0)), (1.0 / temp2));</span>
				// p.coverage=10;
			}
<span class="nc bnc" id="L172" title="All 6 branches missed.">			if (Double.isNaN(p.p) || p.p &lt; min || p.p &gt; max) {</span>
<span class="nc" id="L173">				System.out.println(&quot;Interrupt!!&quot;);</span>
			}
		}

<span class="nc" id="L177">		return count;</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>