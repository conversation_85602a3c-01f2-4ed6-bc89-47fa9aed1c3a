<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ZeroOneCreator.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util.data</a> &gt; <span class="el_source">ZeroOneCreator.java</span></div><h1>ZeroOneCreator.java</h1><pre class="source lang-java linenums">package util.data;

<span class="nc" id="L3">public class ZeroOneCreator implements DataCreator {</span>
	@Override
	public double[] maxCreator(int n) {
<span class="nc" id="L6">		double[] max = new double[n];</span>
<span class="nc bnc" id="L7" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++) {</span>
<span class="nc" id="L8">			max[i] = 1.0;</span>
		}
<span class="nc" id="L10">		return max;</span>
	}

	@Override
	public double[] minCreator(int n) {
<span class="nc" id="L15">		double[] min = new double[n];</span>
<span class="nc bnc" id="L16" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++) {</span>
<span class="nc" id="L17">			min[i] = 0;</span>
		}
<span class="nc" id="L19">		return min;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>