<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ORB_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_orb</a> &gt; <span class="el_source">ORB_ND.java</span></div><h1>ORB_ND.java</h1><pre class="source lang-java linenums">package test.simulations.art_orb;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.StripPatternIn2D;

@Deprecated
public class ORB_ND {
	public static void main(String[] args) {
<span class="nc" id="L15">		double[] min = { 0.0, 0.0 };</span>
<span class="nc" id="L16">		double[] max = { 1.0, 1.0 };</span>
<span class="nc" id="L17">		int times = 1000;</span>
<span class="nc" id="L18">		int fm = 0;</span>
<span class="nc bnc" id="L19" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
			// FailurePattern pattern=new BlockPattern();
<span class="nc" id="L21">			FailurePattern pattern = new StripPatternIn2D();</span>
<span class="nc" id="L22">			pattern.min = min;</span>
<span class="nc" id="L23">			pattern.max = max;</span>
<span class="nc" id="L24">			pattern.fail_rate = 0.001;</span>
<span class="nc" id="L25">			pattern.dimension = 2;</span>

<span class="nc" id="L27">			ORB_ND test = new ORB_ND(min, max, pattern, i * 5 + 3);</span>
<span class="nc" id="L28">			int temp = test.run();</span>
<span class="nc" id="L29">			fm += temp;</span>
		}
<span class="nc" id="L31">		System.out.println(fm / (double) times);</span>
<span class="nc" id="L32">	}</span>
	double[] min;
	double[] max;
	double fail_rate;
	// String fail_mode;
	// double[] fail_start;
	long seed;
	int dimension;
	double totalAreaS;
	double failAreaS;
	Random random;
	// double eachFailLength;
<span class="nc" id="L44">	ArrayList&lt;ComplexRegion&gt; regions = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L46">	FailurePattern failurePattern = null;</span>

<span class="nc" id="L48">	public ORB_ND(double[] min, double[] max, FailurePattern failurePattern, long seed) {</span>
<span class="nc" id="L49">		this.min = min;</span>
<span class="nc" id="L50">		this.max = max;</span>
		// this.fail_rate = fail_rate;
<span class="nc" id="L52">		this.seed = seed;</span>
<span class="nc" id="L53">		this.dimension = min.length;</span>
<span class="nc" id="L54">		random = new Random(seed);</span>
<span class="nc" id="L55">		totalAreaS = 1.0;</span>
<span class="nc bnc" id="L56" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc" id="L57">			totalAreaS *= max[i] - min[i];</span>
		}
<span class="nc" id="L59">		this.failAreaS = fail_rate * totalAreaS;</span>

<span class="nc" id="L61">		failurePattern.random = random;</span>
<span class="nc" id="L62">		failurePattern.genFailurePattern();</span>
<span class="nc" id="L63">		this.failurePattern = failurePattern;</span>
		// this.eachFailLength = Math.pow(failAreaS, 1.0 / (double)
		// this.dimension);
		// generate Fail start;
		// fail_start = new double[this.dimension];
		// for (int i = 0; i &lt; this.dimension; i++) {
		// fail_start[i] = random.nextDouble() * (max[i] - min[i] -
		// this.eachFailLength) + min[i];
		// }
<span class="nc" id="L72">	}</span>

	public int findMaxRegion() {
<span class="nc" id="L75">		double maxSize = -1;</span>
<span class="nc" id="L76">		int index = 0;</span>
<span class="nc bnc" id="L77" title="All 2 branches missed.">		for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">			if (regions.get(i).region.size() &gt; maxSize) {</span>
<span class="nc" id="L79">				maxSize = regions.get(i).region.size();</span>
<span class="nc" id="L80">				index = i;</span>
			}
		}
<span class="nc" id="L83">		return index;</span>
	}

	public boolean isPointInRegion(NRectRegion region, NPoint p) {
<span class="nc" id="L87">		boolean flag = true;</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">		for (int i = 0; i &lt; p.getXn().length; i++) {</span>
<span class="nc bnc" id="L89" title="All 4 branches missed.">			if (p.getXn()[i] &lt; region.getStart().getXn()[i] || p.getXn()[i] &gt; region.getEnd().getXn()[i]) {</span>
<span class="nc" id="L90">				flag = false;</span>
			}
		}
<span class="nc" id="L93">		return flag;</span>
	}

	public NPoint randomTC() {
		// generate from the input domain
<span class="nc" id="L98">		NPoint point = new NPoint();</span>
<span class="nc" id="L99">		double[] xn = new double[this.dimension];</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">		for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="nc" id="L101">			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];</span>
		}
<span class="nc" id="L103">		point.setDimension(this.dimension);</span>
<span class="nc" id="L104">		point.setXn(xn);</span>
<span class="nc" id="L105">		return point;</span>
	}

	// public boolean isCorrect(NPoint p) {
	// // boolean flag=true;
	// double[] xn = p.getXn();
	// boolean lead2Fail = false;
	// for (int i = 0; i &lt; this.dimension; i++) {
	// if (xn[i] &lt; this.fail_start[i] || xn[i] &gt; (this.fail_start[i] +
	// this.eachFailLength)) {
	// lead2Fail = true;
	// }
	// }
	// // System.out.println(Arrays.toString(nDPoints));
	// // System.out.println(&quot;isFail:&quot;+lead2Fail);
	// // lead2Fail=false,失效，=true不失效
	// return lead2Fail;
	// }

	public NPoint randomTC(NRectRegion region) {
<span class="nc" id="L125">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L126">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L127">		double[] xn = new double[this.dimension];</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">		for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="nc" id="L129">			xn[i] = random.nextDouble() * (end[i] - start[i]) + start[i];</span>
		}
<span class="nc" id="L131">		NPoint result = new NPoint(xn);</span>
<span class="nc" id="L132">		result.setDimension(this.dimension);</span>
<span class="nc" id="L133">		return result;</span>
	}

	public int run() {

<span class="nc" id="L138">		int count = 0;</span>
		// first test case
<span class="nc" id="L140">		NPoint p = randomTC();</span>
		// System.out.println(&quot;t1:&quot; + p.toString());
<span class="nc" id="L142">		ComplexRegion region = new ComplexRegion();</span>
<span class="nc" id="L143">		region.region = new NRectRegion(new NPoint(min), new NPoint(max));</span>
<span class="nc" id="L144">		region.pointInRegion = p;</span>
<span class="nc" id="L145">		regions.add(region);</span>
<span class="nc bnc" id="L146" title="All 2 branches missed.">		if (!failurePattern.isCorrect(p)) {</span>
<span class="nc" id="L147">			return 1;</span>
		}
<span class="nc" id="L149">		count++;</span>
<span class="nc" id="L150">		NPoint t2 = randomTC();</span>
		// System.out.println(&quot;t2:&quot; + t2.toString());
<span class="nc" id="L152">		splitRegion(p, t2, regions.get(0));</span>
<span class="nc" id="L153">		regions.remove(0);</span>
		// System.out.println(&quot;------------------&quot;);
<span class="nc bnc" id="L155" title="All 2 branches missed.">		while (failurePattern.isCorrect(t2)) {</span>
<span class="nc" id="L156">			count++;</span>

			// 再生成一个测试用例t2

<span class="nc" id="L160">			int index = findMaxRegion();</span>
<span class="nc" id="L161">			ComplexRegion maxRegion = regions.get(index);</span>
			// System.out.println(region.toString());
			// System.out.println(&quot;find max Region:&quot;+(maxRegion.region));
<span class="nc" id="L164">			t2 = randomTC(maxRegion.region);</span>
			// System.out.println(&quot;t2:&quot; + t2.toString());
			// System.out.println(&quot;is T2 leads failure:&quot;+!isCorrect(t2));

<span class="nc bnc" id="L168" title="All 2 branches missed.">			if (!failurePattern.isCorrect(t2)) {</span>
<span class="nc" id="L169">				break;</span>
			}
			// split region
			// System.out.println(&quot;split region&quot;);
<span class="nc" id="L173">			regions.remove(index);</span>
<span class="nc" id="L174">			splitRegion(maxRegion.pointInRegion, t2, maxRegion);</span>
			// System.out.println(&quot;------------------&quot;);
			// // 生成p
			// index = findMaxRegion();
			// System.out.println(&quot;max region index:&quot; + index);
			//
			// region = regions.get(index);
			// System.out.println(&quot;max region:&quot;+region);
			// p = null;
			// p = randomTC(region);
			// System.out.println(&quot;t1:&quot; + p.toString());
		}
<span class="nc" id="L186">		return count;</span>
	}

	public void splitRegion(NPoint p, NPoint t2, ComplexRegion region) {
<span class="nc" id="L190">		double[] pn = p.getXn();</span>
<span class="nc" id="L191">		double[] t2n = t2.getXn();</span>

<span class="nc" id="L193">		double[] mid = new double[pn.length];</span>
<span class="nc bnc" id="L194" title="All 2 branches missed.">		for (int i = 0; i &lt; mid.length; i++) {</span>
<span class="nc" id="L195">			mid[i] = (pn[i] + t2n[i]) / 2.0;</span>
		}
		// System.out.println(&quot;middle point:&quot;+Arrays.toString(mid));
		// 找到最大的边
<span class="nc" id="L199">		double maxBian = 0.0;</span>
<span class="nc" id="L200">		int maxIndex = 0;</span>
<span class="nc bnc" id="L201" title="All 2 branches missed.">		for (int i = 0; i &lt; region.region.getStart().getXn().length; i++) {</span>
<span class="nc bnc" id="L202" title="All 2 branches missed.">			if (region.region.getEnd().getXn()[i] - region.region.getStart().getXn()[i] &gt; maxBian) {</span>
<span class="nc" id="L203">				maxBian = region.region.getEnd().getXn()[i] - region.region.getStart().getXn()[i];</span>
<span class="nc" id="L204">				maxIndex = i;</span>
			}
		}
		// System.out.println(&quot;1 &quot;+(region.region.getEnd().getXn()[1] -
		// region.region.getStart().getXn()[1]));
		// System.out.println(&quot;2 &quot;+(region.region.getEnd().getXn()[0] -
		// region.region.getStart().getXn()[0]));

		// System.out.println(&quot;max edge:&quot;+(maxIndex+1));
		// 一分为二
<span class="nc" id="L214">		NRectRegion region1 = new NRectRegion();</span>
<span class="nc" id="L215">		NRectRegion region2 = new NRectRegion();</span>

<span class="nc" id="L217">		region1.setStart(region.region.getStart());</span>
<span class="nc" id="L218">		double[] end = Arrays.copyOf(region.region.getEnd().getXn(), region.region.getEnd().getXn().length);</span>
<span class="nc" id="L219">		end[maxIndex] = mid[maxIndex];</span>
<span class="nc" id="L220">		region1.setEnd(new NPoint(end));</span>

<span class="nc" id="L222">		double[] start = Arrays.copyOf(region.region.getStart().getXn(), region.region.getStart().getXn().length);</span>
<span class="nc" id="L223">		start[maxIndex] = mid[maxIndex];</span>
<span class="nc" id="L224">		region2.setStart(new NPoint(start));</span>
<span class="nc" id="L225">		region2.setEnd(region.region.getEnd());</span>
		// if (maxIndex == 0) {// x轴长
		// region1.setStart(region.region.getStart());
		// region1.setEnd(new NPoint(new double[] { mid[0],
		// region.region.getEnd().getXn()[1] }));
		//
		// region2.setStart(new NPoint(new double[] { mid[0],
		// region.region.getStart().getXn()[1] }));
		// region2.setEnd(region.region.getEnd());
		// } else if (maxIndex == 1) {// y轴长
		// region1.setStart(region.region.getStart());
		// region1.setEnd(new NPoint(new double[] { region.region.getEnd().getXn()[0],
		// mid[1] }));
		//
		// region2.setStart(new NPoint(new double[] {
		// region.region.getStart().getXn()[0], mid[1] }));
		// region2.setEnd(region.region.getEnd());
		// }
<span class="nc" id="L243">		ComplexRegion cr1 = new ComplexRegion();</span>
<span class="nc" id="L244">		cr1.region = region1;</span>
<span class="nc bnc" id="L245" title="All 2 branches missed.">		if (isPointInRegion(region1, p)) {</span>
<span class="nc" id="L246">			cr1.pointInRegion = p;</span>
<span class="nc" id="L247">		} else {</span>
<span class="nc" id="L248">			cr1.pointInRegion = t2;</span>
		}
<span class="nc" id="L250">		ComplexRegion cr2 = new ComplexRegion();</span>
<span class="nc" id="L251">		cr2.region = region2;</span>
<span class="nc bnc" id="L252" title="All 2 branches missed.">		if (isPointInRegion(region2, t2)) {</span>
<span class="nc" id="L253">			cr2.pointInRegion = t2;</span>
<span class="nc" id="L254">		} else {</span>
<span class="nc" id="L255">			cr2.pointInRegion = p;</span>
		}
<span class="nc" id="L257">		regions.add(cr1);</span>
<span class="nc" id="L258">		regions.add(cr2);</span>
<span class="nc" id="L259">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>