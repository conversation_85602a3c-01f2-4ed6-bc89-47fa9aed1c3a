<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_E_OD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._1D</a> &gt; <span class="el_source">ART_TP_E_OD.java</span></div><h1>ART_TP_E_OD.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._1D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

/**
 * ART_ETP（FROM SPE）
 */
public class ART_TP_E_OD {
	public static void main(String[] args) throws Exception {
<span class="nc" id="L13">		int times = 3000;</span>
<span class="nc" id="L14">		long sums = 0;</span>
<span class="nc" id="L15">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L16" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
			// 暂不支持测试其他类，没有考虑清楚
<span class="nc" id="L18">			ART_TP_E_OD art_tp_e_od = new ART_TP_E_OD(0, 1, 0.75, 0.001, i * 9);</span>
<span class="nc" id="L19">			int fm = art_tp_e_od.run();</span>
<span class="nc" id="L20">			sums += fm;</span>
		}
<span class="nc" id="L22">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L23">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L24">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L25">	}</span>
	int seedOfRandom;
	double min;
	double max;
	double R;
	double fail_rate;
	double fail_start;

<span class="nc" id="L33">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	// 构造函数
<span class="nc" id="L36">	public ART_TP_E_OD(double min, double max, double R, double fail_rate, int seedOfRandom) {</span>
<span class="nc" id="L37">		this.seedOfRandom = seedOfRandom;</span>
<span class="nc" id="L38">		this.min = min;</span>
<span class="nc" id="L39">		this.max = max;</span>
<span class="nc" id="L40">		this.R = R;</span>
<span class="nc" id="L41">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L42">	}</span>

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L45" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L46">			return false;</span>
		} else {
<span class="nc" id="L48">			return true;</span>
		}
	}

	public int run() {
<span class="nc" id="L53">		Random random = new Random(seedOfRandom);</span>
<span class="nc" id="L54">		fail_start = random.nextDouble() * (max - min - fail_rate);</span>
<span class="nc" id="L55">		TestCase p = new TestCase();</span>
<span class="nc" id="L56">		p.p = random.nextDouble() * (max - min) + min;</span>
<span class="nc" id="L57">		int count = 0;</span>
<span class="nc bnc" id="L58" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L59">			count++;</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L61">				tests.add(p);</span>
<span class="nc" id="L62">			} else {</span>
<span class="nc" id="L63">				sortTestCases(p);</span>
			}
			/////////////////////
			// 生成概率分布函数
			// An-&gt;(-e1,0) Bn-&gt;(1,2-en)
<span class="nc" id="L68">			double e1 = tests.get(0).p;// first node</span>
<span class="nc" id="L69">			double en = tests.get(tests.size() - 1).p;// last node</span>
<span class="nc" id="L70">			double An = random.nextDouble() * (e1 - min) + (2 * min - e1);</span>
<span class="nc" id="L71">			double Bn = random.nextDouble() * (max - en) + max;</span>
<span class="nc" id="L72">			double radius = R / (2 * tests.size());</span>
			// System.out.println(&quot;An:&quot;+An+&quot; Bn:&quot;+Bn);
			// 计算系数
<span class="nc" id="L75">			double Co = 0.0;</span>
<span class="nc" id="L76">			ArrayList&lt;double[]&gt; integrals = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L77">			double[] informations = null;</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size() + 1; i++) {</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">				if (i == 0) {</span>
<span class="nc bnc" id="L80" title="All 2 branches missed.">					if (e1 - radius &gt; min) {</span>
<span class="nc" id="L81">						double from = min;</span>
<span class="nc" id="L82">						double to = e1 - radius;</span>
						// calculate by wolf
<span class="nc" id="L84">						double temp = (-An * from * from / 2.0) + (An * from * e1) - (An * e1 * to)</span>
<span class="nc" id="L85">								+ (An * to * to / 2.0) + (from * from * from / 3.0) - (from * from * e1 / 2.0)</span>
<span class="nc" id="L86">								+ (e1 * to * to / 2.0) - (to * to * to / 3.0);</span>
<span class="nc" id="L87">						Co += temp;</span>
<span class="nc" id="L88">						informations = new double[6];</span>
<span class="nc" id="L89">						informations[0] = temp;</span>
<span class="nc" id="L90">						informations[1] = from;</span>
<span class="nc" id="L91">						informations[2] = to;</span>
<span class="nc" id="L92">						informations[3] = An;</span>
<span class="nc" id="L93">						informations[4] = e1;</span>
<span class="nc" id="L94">						informations[5] = 0;// 用不到</span>
<span class="nc" id="L95">						integrals.add(informations);</span>
					}
<span class="nc bnc" id="L97" title="All 2 branches missed.">				} else if (i == tests.size()) {</span>
<span class="nc bnc" id="L98" title="All 2 branches missed.">					if (en + radius &lt; max) {</span>
<span class="nc" id="L99">						double from = en + radius;</span>
<span class="nc" id="L100">						double to = max;</span>
						// calculate by wolf
<span class="nc" id="L102">						double temp = (-Bn * from * from / 2.0) + (Bn * from * en) - (Bn * en * to)</span>
<span class="nc" id="L103">								+ (Bn * to * to / 2.0) + (from * from * from / 3.0) - (from * from * en / 2.0)</span>
<span class="nc" id="L104">								+ (en * to * to / 2.0) - (to * to * to / 3.0);</span>
<span class="nc" id="L105">						Co += temp;</span>
<span class="nc" id="L106">						informations = new double[6];</span>
<span class="nc" id="L107">						informations[0] = temp;</span>
<span class="nc" id="L108">						informations[1] = from;</span>
<span class="nc" id="L109">						informations[2] = to;</span>
<span class="nc" id="L110">						informations[3] = en;</span>
<span class="nc" id="L111">						informations[4] = Bn;</span>
<span class="nc" id="L112">						informations[5] = 0;// 用不到</span>
<span class="nc" id="L113">						integrals.add(informations);</span>
					}
<span class="nc" id="L115">				} else {</span>
<span class="nc" id="L116">					double ei_1 = tests.get(i - 1).p;</span>
<span class="nc" id="L117">					double ei = tests.get(i).p;</span>
<span class="nc bnc" id="L118" title="All 2 branches missed.">					if (ei - ei_1 &gt; 2 * radius) {</span>
<span class="nc" id="L119">						double from = ei_1 + radius;</span>
<span class="nc" id="L120">						double to = ei - radius;</span>
						// calculate by wolf
<span class="nc" id="L122">						double temp = (-ei * from * from / 2.0) + (ei * from * ei_1) - (ei * ei_1 * to)</span>
<span class="nc" id="L123">								+ (ei * to * to / 2.0) + (from * from * from / 3.0) - (from * from * ei_1 / 2.0)</span>
<span class="nc" id="L124">								+ (ei_1 * to * to / 2.0) - (to * to * to / 3.0);</span>
<span class="nc" id="L125">						Co += temp;</span>
<span class="nc" id="L126">						informations = new double[6];</span>
<span class="nc" id="L127">						informations[0] = temp;</span>
<span class="nc" id="L128">						informations[1] = from;</span>
<span class="nc" id="L129">						informations[2] = to;</span>
<span class="nc" id="L130">						informations[3] = ei_1;</span>
<span class="nc" id="L131">						informations[4] = ei;</span>
<span class="nc" id="L132">						informations[5] = 0;// 用不到</span>
<span class="nc" id="L133">						integrals.add(informations);</span>
					}
				}
			}
<span class="nc" id="L137">			Co = 1.0 / Co;</span>

			// 随机生成一个0-1的数
<span class="nc" id="L140">			double T = random.nextDouble();</span>
			// 看T落在哪个区间
<span class="nc" id="L142">			double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L143">			double PreIntegral = 0.0;</span>
<span class="nc" id="L144">			int temp = 0;</span>
<span class="nc bnc" id="L145" title="All 2 branches missed.">			for (int i = 0; i &lt; integrals.size(); i++) {</span>
<span class="nc bnc" id="L146" title="All 2 branches missed.">				if (SumIntegral &lt; T) {</span>
<span class="nc" id="L147">					PreIntegral = SumIntegral;</span>
<span class="nc" id="L148">					temp = i;</span>
				}
<span class="nc" id="L150">				SumIntegral += integrals.get(i)[0] * Co;</span>
			}
			//
			double A, B, C, D;
<span class="nc" id="L154">			double start = integrals.get(temp)[1];</span>
<span class="nc" id="L155">			double end = integrals.get(temp)[2];</span>
<span class="nc" id="L156">			double m = integrals.get(temp)[3];</span>
<span class="nc" id="L157">			double n = integrals.get(temp)[4];</span>
<span class="nc" id="L158">			A = -Co / 3.0;</span>
<span class="nc" id="L159">			B = Co * (m + n) / 2.0;</span>
<span class="nc" id="L160">			C = -Co * m * n;</span>
<span class="nc" id="L161">			D = PreIntegral - T</span>
<span class="nc" id="L162">					- Co * ((-start * start * start / 3.0) + ((m + n) * start * start / 2.0) - m * n * start);</span>
<span class="nc" id="L163">			double[] roots = shengjinFormula(A, B, C, D);</span>
<span class="nc" id="L164">			boolean haveAanswer = false;</span>
<span class="nc bnc" id="L165" title="All 2 branches missed.">			for (double root : roots) {</span>
<span class="nc bnc" id="L166" title="All 4 branches missed.">				if (root &gt;= start &amp;&amp; root &lt;= end) {</span>
<span class="nc" id="L167">					p = new TestCase();</span>
<span class="nc" id="L168">					p.p = root;</span>
<span class="nc" id="L169">					haveAanswer = true;</span>
				}
			}
<span class="nc bnc" id="L172" title="All 2 branches missed.">			if (!haveAanswer) {</span>
<span class="nc" id="L173">				System.out.println(&quot;error &quot;);</span>
			}
		}
<span class="nc" id="L176">		return count;</span>
	}

	public double[] shengjinFormula(double acof, double bcof, double cof, double dof) {
<span class="nc" id="L180">		double A = bcof * bcof - 3.0 * acof * cof;// A=b^2-3ac</span>
<span class="nc" id="L181">		double B = bcof * cof - 9.0 * acof * dof;// B=bc-9ad</span>
<span class="nc" id="L182">		double C = cof * cof - 3.0 * bcof * dof;// C=c^2-3bd</span>
<span class="nc" id="L183">		double delta = B * B - 4.0 * A * C;</span>
<span class="nc" id="L184">		double root = 0.0;</span>
<span class="nc" id="L185">		double r1 = 0.0;</span>
<span class="nc" id="L186">		double r2 = 0.0;</span>
<span class="nc" id="L187">		double[] roots = new double[3];</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">		if (delta &gt; 0) {</span>
<span class="nc" id="L189">			double Y1 = A * bcof + 3.0 * acof * (-B + Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
<span class="nc" id="L190">			double Y2 = A * bcof + 3.0 * acof * (-B - Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
			double powY1;
			double powY2;
<span class="nc bnc" id="L193" title="All 2 branches missed.">			if (Y1 &lt; 0) {</span>
<span class="nc" id="L194">				powY1 = -Math.pow(-Y1, 1.0 / 3.0);</span>
<span class="nc" id="L195">			} else {</span>
<span class="nc" id="L196">				powY1 = Math.pow(Y1, 1.0 / 3.0);</span>
			}
<span class="nc bnc" id="L198" title="All 2 branches missed.">			if (Y2 &lt; 0) {</span>
<span class="nc" id="L199">				powY2 = -Math.pow(-Y2, 1.0 / 3.0);</span>
<span class="nc" id="L200">			} else {</span>
<span class="nc" id="L201">				powY2 = Math.pow(Y2, 1.0 / 3.0);</span>
			}
<span class="nc" id="L203">			root = (-bcof - powY1 - powY2) / (3.0 * acof);</span>
<span class="nc" id="L204">			r1 = root;</span>
<span class="nc" id="L205">			r2 = root;</span>
<span class="nc bnc" id="L206" title="All 2 branches missed.">		} else if (delta == 0) {</span>
<span class="nc" id="L207">			root = -bcof / acof + B / A;</span>
<span class="nc" id="L208">			r1 = -B / (2.0 * A);</span>
<span class="nc" id="L209">			r2 = r1;</span>

<span class="nc bnc" id="L211" title="All 2 branches missed.">		} else if (delta &lt; 0) {</span>
<span class="nc" id="L212">			double T = (2.0 * A * bcof - 3.0 * acof * B) / (2.0 * Math.pow(A, 3.0 / 2.0));</span>
<span class="nc" id="L213">			double theta = Math.acos(T);</span>
<span class="nc" id="L214">			root = (-bcof - 2.0 * Math.sqrt(A) * Math.cos(theta / 3.0)) / (3.0 * acof);</span>
<span class="nc" id="L215">			r1 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) + Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L216">					/ (3.0 * acof);</span>
<span class="nc" id="L217">			r2 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) - Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L218">					/ (3.0 * acof);</span>
		}
<span class="nc" id="L220">		roots[0] = root;</span>
<span class="nc" id="L221">		roots[1] = r1;</span>
<span class="nc" id="L222">		roots[2] = r2;</span>
<span class="nc" id="L223">		return roots;</span>
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L227">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L228" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L229">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L230" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L231">				low = mid + 1;</span>
<span class="nc" id="L232">			} else {</span>
<span class="nc" id="L233">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L236" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L237">			mid = mid - 1;</span>
		}
<span class="nc" id="L239">		tests.add(mid + 1, p);</span>
<span class="nc" id="L240">	}</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>