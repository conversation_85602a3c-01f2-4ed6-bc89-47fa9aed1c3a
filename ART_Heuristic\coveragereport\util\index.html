<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>util</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb', 'coveragetable'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="index.source.html" class="el_source">Source Files</a><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <span class="el_package">util</span></div><h1>util</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td><td class="sortable ctr1" id="l" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="m" onclick="toggleSort(this)">Classes</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">4,185 of 4,246</td><td class="ctr2">1%</td><td class="bar">278 of 280</td><td class="ctr2">1%</td><td class="ctr1">188</td><td class="ctr2">191</td><td class="ctr1">689</td><td class="ctr2">702</td><td class="ctr1">43</td><td class="ctr2">45</td><td class="ctr1">7</td><td class="ctr2">8</td></tr></tfoot><tbody><tr><td id="a2"><a href="HilbertCurve2.html" class="el_class">HilbertCurve2</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="2,102" alt="2,102"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="144" alt="144"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">90</td><td class="ctr2" id="g0">90</td><td class="ctr1" id="h0">335</td><td class="ctr2" id="i0">335</td><td class="ctr1" id="j0">15</td><td class="ctr2" id="k0">15</td><td class="ctr1" id="l0">1</td><td class="ctr2" id="m0">1</td></tr><tr><td id="a1"><a href="HilbertCurve.html" class="el_class">HilbertCurve</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="73" height="10" title="1,293" alt="1,293"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="73" height="10" title="88" alt="88"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f1">57</td><td class="ctr2" id="g1">57</td><td class="ctr1" id="h1">207</td><td class="ctr2" id="i1">207</td><td class="ctr1" id="j1">10</td><td class="ctr2" id="k1">10</td><td class="ctr1" id="l1">1</td><td class="ctr2" id="m1">1</td></tr><tr><td id="a3"><a href="PaiLie.html" class="el_class">PaiLie</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="15" height="10" title="278" alt="278"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="16" height="10" title="20" alt="20"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">15</td><td class="ctr2" id="g2">15</td><td class="ctr1" id="h2">52</td><td class="ctr2" id="i3">52</td><td class="ctr1" id="j2">5</td><td class="ctr2" id="k3">5</td><td class="ctr1" id="l2">1</td><td class="ctr2" id="m2">1</td></tr><tr><td id="a7"><a href="X3.html" class="el_class">X3</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="15" height="10" title="275" alt="275"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="8" height="10" title="10" alt="10"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f4">7</td><td class="ctr2" id="g4">7</td><td class="ctr1" id="h4">39</td><td class="ctr2" id="i4">39</td><td class="ctr1" id="j6">2</td><td class="ctr2" id="k6">2</td><td class="ctr1" id="l3">1</td><td class="ctr2" id="m3">1</td></tr><tr><td id="a4"><a href="RandomCreator.html" class="el_class">RandomCreator</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="11" height="10" title="200" alt="200"/><img src="../.resources/greenbar.gif" width="3" height="10" title="61" alt="61"/></td><td class="ctr2" id="c0">23%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="11" height="10" title="14" alt="14"/><img src="../.resources/greenbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">12%</td><td class="ctr1" id="f3">11</td><td class="ctr2" id="g3">14</td><td class="ctr1" id="h3">41</td><td class="ctr2" id="i2">54</td><td class="ctr1" id="j3">4</td><td class="ctr2" id="k2">6</td><td class="ctr1" id="l7">0</td><td class="ctr2" id="m4">1</td></tr><tr><td id="a0"><a href="CRandomNumber.html" class="el_class">CRandomNumber</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="1" height="10" title="19" alt="19"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h5">7</td><td class="ctr2" id="i5">7</td><td class="ctr1" id="j4">3</td><td class="ctr2" id="k4">3</td><td class="ctr1" id="l4">1</td><td class="ctr2" id="m5">1</td></tr><tr><td id="a6"><a href="TestProgram.html" class="el_class">TestProgram</a></td><td class="bar" id="b6"/><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h6">5</td><td class="ctr2" id="i6">5</td><td class="ctr1" id="j5">3</td><td class="ctr2" id="k5">3</td><td class="ctr1" id="l5">1</td><td class="ctr2" id="m6">1</td></tr><tr><td id="a5"><a href="RefObject.html" class="el_class">RefObject</a></td><td class="bar" id="b7"/><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">3</td><td class="ctr2" id="i7">3</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td><td class="ctr1" id="l6">1</td><td class="ctr2" id="m7">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>