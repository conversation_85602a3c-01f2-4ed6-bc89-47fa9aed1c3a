<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_OD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._1D</a> &gt; <span class="el_source">ART_TP_OD.java</span></div><h1>ART_TP_OD.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._1D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class ART_TP_OD {
	public static void main(String[] args) throws Exception {
<span class="nc" id="L10">		double fail_rate = 0.005;</span>
<span class="nc" id="L11">		int times = 3000;</span>
<span class="nc" id="L12">		long sums = 0;</span>
<span class="nc" id="L13">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L14" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
			// 暂不支持测试其他类，没有考虑清楚
<span class="nc" id="L16">			ART_TP_OD art_tp_od = new ART_TP_OD(0, 1, fail_rate, i * 3);</span>
<span class="nc" id="L17">			int fm = art_tp_od.run();</span>
<span class="nc" id="L18">			sums += fm;</span>
		}
<span class="nc" id="L20">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L21">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L22">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L23">	}</span>
	int seedOfRandom;
	double min;
	double max;
	double fail_rate;
	double fail_start;
<span class="nc" id="L29">	Random random = null;</span>
	double An;
	double Bn;

<span class="nc" id="L33">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	// 构造函数
<span class="nc" id="L36">	public ART_TP_OD(double min, double max, double fail_rate, int seedOfRandom) {</span>
<span class="nc" id="L37">		this.seedOfRandom = seedOfRandom;</span>
<span class="nc" id="L38">		this.min = min;</span>
<span class="nc" id="L39">		this.max = max;</span>
<span class="nc" id="L40">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L41">		random = new Random(this.seedOfRandom);</span>
<span class="nc" id="L42">	}</span>

	public void genFail_start() {
<span class="nc" id="L45">		double size = (max - min) * fail_rate;</span>
<span class="nc" id="L46">		fail_start = random.nextDouble() * (max - size);</span>
<span class="nc" id="L47">	}</span>

	public TestCase genNext(ArrayList&lt;Double&gt; integrals, double Co, TestCase p) {
<span class="nc" id="L50">		double e1 = tests.get(0).p;// first node</span>
<span class="nc" id="L51">		double en = tests.get(tests.size() - 1).p;// last node</span>
		// 随机生成一个0-1的数
<span class="nc" id="L53">		double T = random.nextDouble();</span>
		// System.out.println(&quot;An:&quot;+An+&quot;,Bn:&quot;+Bn+&quot;,Co:&quot;+Co+&quot;,T:&quot;+T);
		// 看T落在哪个区间
<span class="nc" id="L56">		double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L57">		double PreIntegral = 0.0;</span>
<span class="nc" id="L58">		int temp = 0;</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">		for (int i = 0; i &lt; integrals.size(); i++) {</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">			if (SumIntegral &lt; T) {</span>
<span class="nc" id="L61">				PreIntegral = SumIntegral;</span>
<span class="nc" id="L62">				temp = i;</span>
			}
<span class="nc" id="L64">			SumIntegral += integrals.get(i) * Co;</span>
		}
		//
		double A, B, C, D;
<span class="nc bnc" id="L68" title="All 2 branches missed.">		if (temp == 0) {</span>
<span class="nc" id="L69">			A = -Co / 3.0;</span>
<span class="nc" id="L70">			B = (Co / 2.0) * (e1 + An);</span>
<span class="nc" id="L71">			C = -Co * e1 * An;</span>
<span class="nc" id="L72">			D = Co * ((1.0 / 3.0) * min * min * min - ((An + e1) / 2.0) * min * min + An * e1 * min) - T;</span>
<span class="nc bnc" id="L73" title="All 2 branches missed.">		} else if (temp == integrals.size() - 1) {</span>
<span class="nc" id="L74">			A = -Co / 3.0;</span>
<span class="nc" id="L75">			B = (Co / 2.0) * (en + Bn);</span>
<span class="nc" id="L76">			C = -Co * (Bn) * (en);</span>
<span class="nc" id="L77">			D = PreIntegral - T - Co * ((1.0 / 6.0) * (Math.pow(en, 3.0)))</span>
<span class="nc" id="L78">					+ Co * ((1.0 / 2.0) * Bn * Math.pow(en, 2.0));</span>
<span class="nc" id="L79">		} else {</span>
<span class="nc" id="L80">			A = -Co / 3.0;</span>
<span class="nc" id="L81">			B = (Co / 2.0) * (tests.get(temp - 1).p + tests.get(temp).p);</span>
<span class="nc" id="L82">			C = -Co * tests.get(temp - 1).p * tests.get(temp).p;</span>
<span class="nc" id="L83">			D = -T + PreIntegral - Co * ((1.0 / 6.0) * (Math.pow(tests.get(temp - 1).p, 3.0))</span>
<span class="nc" id="L84">					- (1.0 / 2.0) * (tests.get(temp).p) * (Math.pow(tests.get(temp - 1).p, 2.0)));</span>
		}
<span class="nc" id="L86">		double[] roots = shengjinFormula(A, B, C, D);</span>
<span class="nc" id="L87">		boolean haveAanswer = false;</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">		for (double root : roots) {</span>
<span class="nc bnc" id="L89" title="All 2 branches missed.">			if (temp == 0) {</span>
<span class="nc bnc" id="L90" title="All 4 branches missed.">				if (root &gt;= min &amp;&amp; root &lt;= e1) {</span>
<span class="nc" id="L91">					p = new TestCase();</span>
<span class="nc" id="L92">					p.p = root;</span>
<span class="nc" id="L93">					haveAanswer = true;</span>
				}
<span class="nc bnc" id="L95" title="All 2 branches missed.">			} else if (temp == integrals.size() - 1) {</span>
<span class="nc bnc" id="L96" title="All 4 branches missed.">				if (root &gt;= en &amp;&amp; root &lt;= max) {</span>
<span class="nc" id="L97">					p = new TestCase();</span>
<span class="nc" id="L98">					p.p = root;</span>
<span class="nc" id="L99">					haveAanswer = true;</span>
				}
<span class="nc" id="L101">			} else {</span>
<span class="nc bnc" id="L102" title="All 4 branches missed.">				if (root &gt;= tests.get(temp - 1).p &amp;&amp; root &lt;= tests.get(temp).p) {</span>
<span class="nc" id="L103">					p = new TestCase();</span>
<span class="nc" id="L104">					p.p = root;</span>
<span class="nc" id="L105">					haveAanswer = true;</span>
				}
			}
		}
<span class="nc bnc" id="L109" title="All 2 branches missed.">		if (!haveAanswer) {</span>
<span class="nc" id="L110">			return null;</span>
		} else {
<span class="nc" id="L112">			return p;</span>
		}
	}

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L117" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + (max - min) * fail_rate)) {</span>
<span class="nc" id="L118">			return false;</span>
		} else {
<span class="nc" id="L120">			return true;</span>
		}
	}

	public int run() {
<span class="nc" id="L125">		TestCase p = new TestCase();</span>
<span class="nc" id="L126">		p.p = random.nextDouble() * (max - min) + min;</span>
<span class="nc" id="L127">		int count = 0;</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L129">			count++;</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L131">				tests.add(p);</span>
<span class="nc" id="L132">			} else {</span>
<span class="nc" id="L133">				sortTestCases(p);</span>
			}
			/////////////////////
			// 生成概率分布函数
			// An-&gt;(-e1,0) Bn-&gt;(1,2-en)
<span class="nc" id="L138">			double e1 = tests.get(0).p;// first node</span>
<span class="nc" id="L139">			double en = tests.get(tests.size() - 1).p;// last node</span>
			// calculate An and Bn
			// An (min-(e1-min)) to min |||| Bn max to max+max-en
<span class="nc" id="L142">			An = random.nextDouble() * (e1 - min) + (2 * min - e1);</span>
<span class="nc" id="L143">			Bn = random.nextDouble() * (max - en) + max;</span>
			// 计算系数
<span class="nc" id="L145">			double Co = 0.0;</span>
<span class="nc" id="L146">			ArrayList&lt;Double&gt; integrals = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L147" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size() + 1; i++) {</span>
<span class="nc bnc" id="L148" title="All 2 branches missed.">				if (i == 0) {</span>
					// 1/6*e1^3-1/2*an*e1^2-(1/3)
<span class="nc" id="L150">					double temp = ((1.0 / 6.0) * (Math.pow(e1, 3))) - ((1.0 / 2.0) * An * (Math.pow(e1, 2)));</span>
<span class="nc" id="L151">					double temp1 = (-(1.0 / 3.0) * min * min * min + ((An + e1) / 2.0) * min * min - An * e1 * min);</span>
<span class="nc" id="L152">					Co += (temp - temp1);</span>
<span class="nc" id="L153">					integrals.add(temp - temp1);</span>
<span class="nc bnc" id="L154" title="All 2 branches missed.">				} else if (i == tests.size()) {</span>
					// (-1/6)en^3+(1/2)*bn*en^2-en*bn+(1/2)*(bn+en)-(1/3)
<span class="nc" id="L156">					double temp = (-1.0 / 6.0) * Math.pow(en, 3.0) + (1.0 / 2.0) * (Bn) * Math.pow(en, 2.0)</span>
<span class="nc" id="L157">							- en * Bn * max + (1.0 / 2.0) * (Bn + en) * max * max - (1.0 / 3.0) * max * max * max;</span>
<span class="nc" id="L158">					Co += temp;</span>
<span class="nc" id="L159">					integrals.add(temp);</span>
<span class="nc" id="L160">				} else {</span>
<span class="nc" id="L161">					double ei_1 = tests.get(i - 1).p;</span>
<span class="nc" id="L162">					double ei = tests.get(i).p;</span>
					// (1/6)*(ei1^3-ei^3)+(1/2)*(ei*ei1)*(ei-ei1)
<span class="nc" id="L164">					double temp = (1.0 / 6.0) * (Math.pow(ei, 3.0) - Math.pow(ei_1, 3))</span>
<span class="nc" id="L165">							+ (1.0 / 2.0) * (ei_1 * ei) * (ei_1 - ei);</span>
<span class="nc" id="L166">					Co += temp;</span>
<span class="nc" id="L167">					integrals.add(temp);</span>
				}
			}
<span class="nc" id="L170">			Co = 1.0 / Co;</span>
<span class="nc" id="L171">			p = genNext(integrals, Co, p);</span>
<span class="nc" id="L172">			int countErr = 0;</span>
<span class="nc bnc" id="L173" title="All 2 branches missed.">			while (p == null) {</span>
<span class="nc" id="L174">				p = genNext(integrals, Co, p);</span>
<span class="nc" id="L175">				System.out.println(&quot;error:&quot; + countErr++);</span>
			}
		}
<span class="nc" id="L178">		return count;</span>
	}

	public double[] shengjinFormula(double acof, double bcof, double cof, double dof) {
<span class="nc" id="L182">		double A = bcof * bcof - 3.0 * acof * cof;// A=b^2-3ac</span>
<span class="nc" id="L183">		double B = bcof * cof - 9.0 * acof * dof;// B=bc-9ad</span>
<span class="nc" id="L184">		double C = cof * cof - 3.0 * bcof * dof;// C=c^2-3bd</span>
<span class="nc" id="L185">		double delta = B * B - 4.0 * A * C;</span>
<span class="nc" id="L186">		double root = 0.0;</span>
<span class="nc" id="L187">		double r1 = 0.0;</span>
<span class="nc" id="L188">		double r2 = 0.0;</span>
<span class="nc" id="L189">		double[] roots = new double[3];</span>
<span class="nc bnc" id="L190" title="All 2 branches missed.">		if (delta &gt; 0) {</span>
<span class="nc" id="L191">			double Y1 = A * bcof + 3.0 * acof * (-B + Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
<span class="nc" id="L192">			double Y2 = A * bcof + 3.0 * acof * (-B - Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
			double powY1;
			double powY2;
<span class="nc bnc" id="L195" title="All 2 branches missed.">			if (Y1 &lt; 0) {</span>
<span class="nc" id="L196">				powY1 = -Math.pow(-Y1, 1.0 / 3.0);</span>
<span class="nc" id="L197">			} else {</span>
<span class="nc" id="L198">				powY1 = Math.pow(Y1, 1.0 / 3.0);</span>
			}
<span class="nc bnc" id="L200" title="All 2 branches missed.">			if (Y2 &lt; 0) {</span>
<span class="nc" id="L201">				powY2 = -Math.pow(-Y2, 1.0 / 3.0);</span>
<span class="nc" id="L202">			} else {</span>
<span class="nc" id="L203">				powY2 = Math.pow(Y2, 1.0 / 3.0);</span>
			}
<span class="nc" id="L205">			root = (-bcof - powY1 - powY2) / (3.0 * acof);</span>
<span class="nc" id="L206">			r1 = root;</span>
<span class="nc" id="L207">			r2 = root;</span>
<span class="nc bnc" id="L208" title="All 2 branches missed.">		} else if (delta == 0) {</span>
<span class="nc" id="L209">			root = -bcof / acof + B / A;</span>
<span class="nc" id="L210">			r1 = -B / (2.0 * A);</span>
<span class="nc" id="L211">			r2 = r1;</span>

<span class="nc bnc" id="L213" title="All 2 branches missed.">		} else if (delta &lt; 0) {</span>
<span class="nc" id="L214">			double T = (2.0 * A * bcof - 3.0 * acof * B) / (2.0 * Math.pow(A, 3.0 / 2.0));</span>
<span class="nc" id="L215">			double theta = Math.acos(T);</span>
<span class="nc" id="L216">			root = (-bcof - 2.0 * Math.sqrt(A) * Math.cos(theta / 3.0)) / (3.0 * acof);</span>
<span class="nc" id="L217">			r1 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) + Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L218">					/ (3.0 * acof);</span>
<span class="nc" id="L219">			r2 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) - Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L220">					/ (3.0 * acof);</span>
		}
<span class="nc" id="L222">		roots[0] = root;</span>
<span class="nc" id="L223">		roots[1] = r1;</span>
<span class="nc" id="L224">		roots[2] = r2;</span>
<span class="nc" id="L225">		return roots;</span>
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L229">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L230" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L231">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L232" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L233">				low = mid + 1;</span>
<span class="nc" id="L234">			} else {</span>
<span class="nc" id="L235">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L238" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L239">			mid = mid - 1;</span>
		}
<span class="nc" id="L241">		tests.add(mid + 1, p);</span>
<span class="nc" id="L242">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>