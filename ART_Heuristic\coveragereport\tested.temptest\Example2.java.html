<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>Example2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested.temptest</a> &gt; <span class="el_source">Example2.java</span></div><h1>Example2.java</h1><pre class="source lang-java linenums">package tested.temptest;

<span class="nc" id="L3">public class Example2 {</span>
	public static void main(String args[]) {
<span class="nc" id="L5">		int n = 23, start, end, middle;</span>
<span class="nc" id="L6">		int a[] = { -2, 1, 4, 5, 8, 12, 17, 23, 45, 56, 90, 100 };</span>
<span class="nc" id="L7">		start = 0;</span>
<span class="nc" id="L8">		end = a.length;</span>
<span class="nc" id="L9">		middle = (start + end) / 2;</span>
<span class="nc" id="L10">		int count = 0;</span>
<span class="nc bnc" id="L11" title="All 2 branches missed.">		while (n != a[middle]) {</span>
<span class="nc bnc" id="L12" title="All 2 branches missed.">			if (n &gt; a[middle]) {</span>
<span class="nc" id="L13">				start = middle;</span>
<span class="nc bnc" id="L14" title="All 2 branches missed.">			} else if (n &lt; a[middle]) {</span>
<span class="nc" id="L15">				end = middle;</span>
			}
<span class="nc" id="L17">			middle = (start + end) / 2;</span>
<span class="nc" id="L18">			count++;</span>
<span class="nc bnc" id="L19" title="All 2 branches missed.">			if (count &gt; a.length / 2)</span>
<span class="nc" id="L20">				break;</span>
		}
<span class="nc bnc" id="L22" title="All 2 branches missed.">		if (count &gt; a.length / 2)</span>
<span class="nc" id="L23">			System.out.println(&quot;:&quot; + n + &quot;不在数组中&quot;);</span>
		else
<span class="nc" id="L25">			System.out.println(&quot;:&quot; + n + &quot;是数组中的第&quot; + middle + &quot;个元素&quot;);</span>
<span class="nc" id="L26">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>