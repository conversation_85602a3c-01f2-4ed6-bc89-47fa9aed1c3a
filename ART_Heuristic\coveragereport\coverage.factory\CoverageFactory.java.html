<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>CoverageFactory.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">coverage.factory</a> &gt; <span class="el_source">CoverageFactory.java</span></div><h1>CoverageFactory.java</h1><pre class="source lang-java linenums">package coverage.factory;

import java.io.IOException;
import java.io.InputStream;
import java.lang.reflect.InvocationTargetException;

import org.jacoco.core.analysis.Analyzer;
import org.jacoco.core.analysis.CoverageBuilder;
import org.jacoco.core.analysis.IClassCoverage;
import org.jacoco.core.analysis.ICounter;
import org.jacoco.core.data.ExecutionDataStore;
import org.jacoco.core.data.SessionInfoStore;
import org.jacoco.core.instr.Instrumenter;
import org.jacoco.core.runtime.IRuntime;
import org.jacoco.core.runtime.LoggerRuntime;
import org.jacoco.core.runtime.RuntimeData;

import coverage.classloader.MemoryClassLoader;

<span class="nc" id="L20">public class CoverageFactory {</span>
	public static void main(String[] args) {
<span class="nc" id="L22">		CoverageFactory factory = new CoverageFactory();</span>
<span class="nc" id="L23">		factory.setClassName(&quot;tested.bessj0&quot;);</span>
<span class="nc" id="L24">		System.out.println(factory.execute(12));</span>
		// tested.bessj0 bessj = new tested.bessj0();

<span class="nc" id="L27">	}</span>

	String name;

	public double execute(double a) {
		// �õ��������������
<span class="nc" id="L33">		final String targetName = name;</span>
		// For instrumentation and runtime we need a IRuntime instance to
		// collect execution data:
		// jacoco ����ṩ
<span class="nc" id="L37">		final IRuntime runtime = new LoggerRuntime();</span>
		// The Instrumenter creates a modified version of our test target class
		// that contains additional probes for execution data recording:
<span class="nc" id="L40">		final Instrumenter instr = new Instrumenter(runtime);</span>
<span class="nc" id="L41">		byte[] instrumented = null;</span>
		try {
			// get the bytes that has been instructed

<span class="nc" id="L45">			instrumented = instr.instrument(getTargetClass(targetName), targetName);</span>
			// for (byte b : instrumented) {
			// System.out.print(b);
			// }
			// System.out.println();
			// File f=new
			// File(&quot;F:/Users/<USER>/workspace/ST_CodeCoverageOfART_TP/bin/tested/bessj0_instr.class&quot;);
			// if(!f.exists()){
			// f.createNewFile();
			// FileOutputStream foutput=new FileOutputStream(f);
			// foutput.write(instrumented);
			// }
			// FileInputStream finput=new FileInputStream(f);
			// int aa;
			// int count=0;
			// instrumented=new byte[(int) f.length()];
			// while((aa=finput.read())!=-1){
			// instrumented[count]=()aa;
			// }
			// FileInputStream finput=new FileInputStream(f);
			// int aa;
			// while((aa=finput.read())!=-1){
			// System.out.print(aa);
			// }
<span class="nc" id="L69">		} catch (IOException e) {</span>
<span class="nc" id="L70">			e.printStackTrace();</span>
		}
		// Now we're ready to run our instrumented class and need to startup the
		// runtime first:
<span class="nc" id="L74">		final RuntimeData data = new RuntimeData();</span>
		try {
<span class="nc" id="L76">			runtime.startup(data);</span>
<span class="nc" id="L77">		} catch (Exception e) {</span>
<span class="nc" id="L78">			e.printStackTrace();</span>
		}
		// In this tutorial we use a special class loader to directly load the
		// instrumented class definition from a byte[] instances.
<span class="nc" id="L82">		final MemoryClassLoader memoryClassLoader = new MemoryClassLoader();</span>
		// name and bytes to be a key and value
<span class="nc" id="L84">		memoryClassLoader.addDefinition(targetName, instrumented);</span>
<span class="nc" id="L85">		Class&lt;?&gt; targetClass = null;</span>
		try {
<span class="nc" id="L87">			targetClass = memoryClassLoader.loadClass(targetName);</span>
<span class="nc" id="L88">		} catch (ClassNotFoundException e) {</span>
<span class="nc" id="L89">			e.printStackTrace();</span>
		}

		// Here we execute our test target class through its Runnable interface:
		// final Runnable targetInstance = (Runnable) targetClass.newInstance();
		// targetInstance.run();
<span class="nc" id="L95">		Object targetInstance = null;</span>
		try {
<span class="nc" id="L97">			targetInstance = targetClass.newInstance();</span>
<span class="nc" id="L98">		} catch (InstantiationException | IllegalAccessException e) {</span>
<span class="nc" id="L99">			e.printStackTrace();</span>
		}
		// targetClass.getMethod(&quot;isPrimer&quot;,Boolean.class);

		try {
<span class="nc" id="L104">			targetClass.getMethod(&quot;wrong&quot;, double.class).invoke(targetInstance, a);</span>
<span class="nc" id="L105">		} catch (IllegalAccessException | IllegalArgumentException | InvocationTargetException | NoSuchMethodException</span>
<span class="nc" id="L106">				| SecurityException e) {</span>
<span class="nc" id="L107">			e.printStackTrace();</span>
		}
		// classs.isPrime(3);
		// At the end of test execution we collect execution data and shutdown
		// the runtime:
<span class="nc" id="L112">		final ExecutionDataStore executionData = new ExecutionDataStore();</span>
<span class="nc" id="L113">		final SessionInfoStore sessionInfos = new SessionInfoStore();</span>
<span class="nc" id="L114">		data.collect(executionData, sessionInfos, false);</span>
<span class="nc" id="L115">		runtime.shutdown();</span>
		// Together with the original class definition we can calculate coverage
		// information:
<span class="nc" id="L118">		final CoverageBuilder coverageBuilder = new CoverageBuilder();</span>
<span class="nc" id="L119">		final Analyzer analyzer = new Analyzer(executionData, coverageBuilder);</span>
		try {
<span class="nc" id="L121">			analyzer.analyzeClass(getTargetClass(targetName), targetName);</span>
<span class="nc" id="L122">		} catch (IOException e) {</span>
			// TODO Auto-generated catch block
<span class="nc" id="L124">			e.printStackTrace();</span>
		}
		// Let's dump some metrics and line coverage information:
<span class="nc" id="L127">		double codeCoverage = 0;</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">		for (final IClassCoverage cc : coverageBuilder.getClasses()) {</span>
			// System.out.printf(&quot;Coverage of class %s%n&quot;, cc.getName());
			//
			// printCounter(&quot;instructions&quot;, cc.getInstructionCounter());
			// printCounter(&quot;branches:&quot;, cc.getBranchCounter());
<span class="nc" id="L133">			codeCoverage = printCounter(&quot;lines&quot;, cc.getLineCounter());</span>
			// printCounter(&quot;methods&quot;, cc.getMethodCounter());
			// printCounter(&quot;complexity&quot;, cc.getComplexityCounter());
		}
		// System.out.println(10-9*codeCoverage);
<span class="nc" id="L138">		return 10.0 - 9 * codeCoverage;</span>
	}

	private InputStream getTargetClass(final String name) {
<span class="nc" id="L142">		final String resource = '/' + name.replace('.', '/') + &quot;.class&quot;;</span>
<span class="nc" id="L143">		return getClass().getResourceAsStream(resource);</span>
	}

	private double printCounter(final String unit, final ICounter counter) {
<span class="nc" id="L147">		final Integer missed = Integer.valueOf(counter.getMissedCount());</span>
<span class="nc" id="L148">		final Integer total = Integer.valueOf(counter.getTotalCount());</span>
		// System.out.printf(&quot;%s of %s %s missed%n&quot;, missed, total, unit);
		// System.out.println((total-missed)/(double)total);
<span class="nc" id="L151">		return (total - missed) / (double) total;</span>
	}

	public void setClassName(String name) {
<span class="nc" id="L155">		this.name = name;</span>
<span class="nc" id="L156">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>