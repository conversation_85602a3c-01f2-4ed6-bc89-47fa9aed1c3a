<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>tan0.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">tan0.java</span></div><h1>tan0.java</h1><pre class="source lang-java linenums">package tested;

import util.TestProgram;

/*
 * Input Domain:(-500,500)
 * failure rate:0.001817 1/0.001817=550.35773252614199229499174463401
 *errors: 4 errors(1 AOR, 1 ROR, 1SVR ,1CR)
 * */
<span class="nc" id="L10">public class tan0 {</span>
<span class="nc" id="L11">	public  static double[] min = { -500 };</span>
<span class="nc" id="L12">	public  static double[] max = { 500 };</span>
<span class="nc" id="L13">	public  static double failureRate = 0.001817;</span>
<span class="nc" id="L14">	public static  int Dimension = 1;</span>

	public boolean isCorrect(double x) {
<span class="nc" id="L17">		return TestProgram.test_tanh(x);</span>
	}
	/*
	 * public double correct(double u){
	 * 
	 * double epu = Math.exp(u); double emu = 1.0/epu;
	 * 
	 * if (Math.abs(u) &lt; 0.3) { double u2 = u*u; return (
	 * 2*u*(1+u2/6*(1+u2/20*(1+u2/42*(1+u2/72))))/(epu+emu) ); } else { double
	 * difference = epu - emu; double sum = epu + emu; double fraction =
	 * difference/sum; return fraction; } } public double wrong(double u){ double
	 * epu = Math.exp(u); double emu = 1.0/epu;
	 * 
	 * if (Math.abs(u) &lt;= 0.9) { double u2 = u*u; return (
	 * 2*u*(1+u2/6*(1+u/20*(1+u2/42*(1-u2/72))))/(epu+emu) ); } else { double
	 * difference = epu - emu; double sum = epu + emu; double fraction =
	 * difference/sum; return fraction; } }
	 * 
	 * public boolean isCorrect(double x){ //
	 * System.out.println(&quot;correct:&quot;+correct(x)); //
	 * System.out.println(&quot;wrong:&quot;+wrong(x)); return correct(x)==(wrong(x)); }
	 */
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>