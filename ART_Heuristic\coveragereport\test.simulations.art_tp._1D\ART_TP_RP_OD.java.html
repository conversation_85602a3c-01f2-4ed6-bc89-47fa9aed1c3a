<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_RP_OD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._1D</a> &gt; <span class="el_source">ART_TP_RP_OD.java</span></div><h1>ART_TP_RP_OD.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._1D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class ART_TP_RP_OD {
	public static void main(String[] args) throws Exception {
<span class="nc" id="L10">		double fail_rate = 0.005;</span>
<span class="nc" id="L11">		int times = 3000;</span>
<span class="nc" id="L12">		long sums = 0;</span>
<span class="nc" id="L13">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L14" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
			// 暂不支持测试其他类，没有考虑清楚
<span class="nc" id="L16">			ART_TP_RP_OD art_tp_od = new ART_TP_RP_OD(0, 1, fail_rate, i * 3);</span>
<span class="nc" id="L17">			int fm = art_tp_od.run();</span>
<span class="nc" id="L18">			sums += fm;</span>
		}
<span class="nc" id="L20">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L21">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L22">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L23">	}</span>
	int seedOfRandom;
	double min;
	double max;
	double fail_rate;
	double fail_start;
	Random Trandom;

<span class="nc" id="L31">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	// 构造函数
<span class="nc" id="L34">	public ART_TP_RP_OD(double min, double max, double fail_rate, int seedOfRandom) {</span>
<span class="nc" id="L35">		this.seedOfRandom = seedOfRandom;</span>
<span class="nc" id="L36">		this.min = min;</span>
<span class="nc" id="L37">		this.max = max;</span>
<span class="nc" id="L38">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L39">		Trandom = new Random(seedOfRandom);</span>
<span class="nc" id="L40">	}</span>

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L43" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L44">			return false;</span>
		} else {
<span class="nc" id="L46">			return true;</span>
		}
	}

	public int run() {
<span class="nc" id="L51">		Random random = new Random(seedOfRandom);</span>
<span class="nc" id="L52">		fail_start = random.nextDouble() * (1 - fail_rate);</span>
<span class="nc" id="L53">		TestCase p = new TestCase();</span>
<span class="nc" id="L54">		p.p = random.nextDouble() * (max - min) + min;</span>
<span class="nc" id="L55">		System.out.println(&quot;p.p:&quot; + p.p);</span>
<span class="nc" id="L56">		int count = 0;</span>
<span class="nc bnc" id="L57" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L58">			count++;</span>
<span class="nc bnc" id="L59" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L60">				tests.add(p);</span>
<span class="nc" id="L61">			} else {</span>
<span class="nc" id="L62">				sortTestCases(p);</span>
			}
			/////////////////////
			// 生成概率分布函数
<span class="nc" id="L66">			double e1 = tests.get(0).p;// first node</span>
<span class="nc" id="L67">			double en = tests.get(tests.size() - 1).p;// last node</span>
			// calculate An and Bn
			// An (min-(e1-min)) to min |||| Bn max to max+max-en
<span class="nc" id="L70">			double Bn = random.nextDouble() * (max - en) + max;</span>
<span class="nc" id="L71">			double An = random.nextDouble() * (e1 - min) + (2 * min - e1);</span>
			// double Bn = random.nextDouble() * (max - en) + max;
			// 计算系数
<span class="nc" id="L74">			double Co = 0.0;</span>
<span class="nc" id="L75">			double start = An;</span>
<span class="nc" id="L76">			double end = 0;</span>
			// from to 積分的上下限
<span class="nc" id="L78">			double from = min;</span>
<span class="nc" id="L79">			double to = 0;</span>
<span class="nc" id="L80">			double maxdistance = e1 - min;</span>
<span class="nc" id="L81">			end = e1;</span>
<span class="nc" id="L82">			to = e1;</span>
<span class="nc bnc" id="L83" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size() - 1; i++) {</span>
<span class="nc" id="L84">				double distance = tests.get(i + 1).p - tests.get(i).p;</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">				if (distance &gt; maxdistance) {</span>
<span class="nc" id="L86">					maxdistance = distance;</span>
<span class="nc" id="L87">					start = tests.get(i).p;</span>
<span class="nc" id="L88">					end = tests.get(i + 1).p;</span>
<span class="nc" id="L89">					from = tests.get(i).p;</span>
<span class="nc" id="L90">					to = tests.get(i + 1).p;</span>
				}
			}
<span class="nc bnc" id="L93" title="All 2 branches missed.">			if (maxdistance &lt; (max - en)) {</span>
<span class="nc" id="L94">				start = en;</span>
<span class="nc" id="L95">				end = Bn;</span>
<span class="nc" id="L96">				from = en;</span>
<span class="nc" id="L97">				to = max;</span>
			}
			// System.out.println(&quot;start:&quot;+start+&quot; end:&quot;+end+&quot; from:&quot;+from+&quot; to:&quot;+to);
			// calculate by wolf
<span class="nc" id="L101">			Co = (1.0 / 3.0) * (from * from * from - to * to * to) + (0.5 * (start + end)) * (to * to - from * from)</span>
<span class="nc" id="L102">					+ start * end * (from - to);</span>
			// System.out.println(start+&quot; &quot;+end+&quot; &quot;+from+&quot; &quot;+to);
			// System.out.println(&quot;fx int:&quot;+Co);
<span class="nc" id="L105">			Co = 1.0 / Co;</span>
			// 随机生成一个0-1的数
<span class="nc" id="L107">			double T = Trandom.nextDouble();</span>
			// System.out.println(&quot;T:&quot;+T);
			double A, B, C, D;
<span class="nc" id="L110">			A = -Co / 3.0;</span>
<span class="nc" id="L111">			B = Co * (start + end) / 2.0;</span>
<span class="nc" id="L112">			C = -Co * start * end;</span>
<span class="nc" id="L113">			D = -Co * ((-1.0 / 3.0) * from * from * from + 0.5 * (start + end) * from * from - start * end * from) - T;</span>
			// System.out.println(&quot;start:&quot;+An);
			// System.out.println(&quot;end:&quot;+Bn);
			// System.out.println(&quot;Co:&quot;+Co);
			// System.out.println(&quot;A:&quot;+A+&quot; B:&quot;+B+&quot; C:&quot;+C+&quot; D:&quot;+D);
<span class="nc" id="L118">			double[] roots = shengjinFormula(A, B, C, D);</span>
<span class="nc" id="L119">			boolean haveAanswer = false;</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">			for (double root : roots) {</span>
<span class="nc bnc" id="L121" title="All 4 branches missed.">				if (root &gt;= from &amp;&amp; root &lt;= to) {</span>
<span class="nc" id="L122">					p = new TestCase();</span>
<span class="nc" id="L123">					p.p = root;</span>
<span class="nc" id="L124">					System.out.println(&quot;---------------------&quot;);</span>
<span class="nc" id="L125">					System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.p);</span>
<span class="nc" id="L126">					haveAanswer = true;</span>
				}
			}
<span class="nc bnc" id="L129" title="All 2 branches missed.">			if (!haveAanswer) {</span>
<span class="nc" id="L130">				System.out.println(&quot;error &quot; + roots[0] + &quot; &quot; + roots[1] + &quot; &quot; + roots[2]);</span>
<span class="nc" id="L131">				System.out.println(from + &quot; &quot; + to);</span>
			}
		}
<span class="nc" id="L134">		return count;</span>
	}

	public double[] shengjinFormula(double acof, double bcof, double cof, double dof) {
<span class="nc" id="L138">		double A = bcof * bcof - 3.0 * acof * cof;// A=b^2-3ac</span>
<span class="nc" id="L139">		double B = bcof * cof - 9.0 * acof * dof;// B=bc-9ad</span>
<span class="nc" id="L140">		double C = cof * cof - 3.0 * bcof * dof;// C=c^2-3bd</span>
<span class="nc" id="L141">		double delta = B * B - 4.0 * A * C;</span>
<span class="nc" id="L142">		double root = 0.0;</span>
<span class="nc" id="L143">		double r1 = 0.0;</span>
<span class="nc" id="L144">		double r2 = 0.0;</span>
<span class="nc" id="L145">		double[] roots = new double[3];</span>
<span class="nc bnc" id="L146" title="All 2 branches missed.">		if (delta &gt; 0) {</span>
<span class="nc" id="L147">			double Y1 = A * bcof + 3.0 * acof * (-B + Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
<span class="nc" id="L148">			double Y2 = A * bcof + 3.0 * acof * (-B - Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
			double powY1;
			double powY2;
<span class="nc bnc" id="L151" title="All 2 branches missed.">			if (Y1 &lt; 0) {</span>
<span class="nc" id="L152">				powY1 = -Math.pow(-Y1, 1.0 / 3.0);</span>
<span class="nc" id="L153">			} else {</span>
<span class="nc" id="L154">				powY1 = Math.pow(Y1, 1.0 / 3.0);</span>
			}
<span class="nc bnc" id="L156" title="All 2 branches missed.">			if (Y2 &lt; 0) {</span>
<span class="nc" id="L157">				powY2 = -Math.pow(-Y2, 1.0 / 3.0);</span>
<span class="nc" id="L158">			} else {</span>
<span class="nc" id="L159">				powY2 = Math.pow(Y2, 1.0 / 3.0);</span>
			}
<span class="nc" id="L161">			root = (-bcof - powY1 - powY2) / (3.0 * acof);</span>
<span class="nc" id="L162">			r1 = root;</span>
<span class="nc" id="L163">			r2 = root;</span>
<span class="nc bnc" id="L164" title="All 2 branches missed.">		} else if (delta == 0) {</span>
<span class="nc" id="L165">			root = -bcof / acof + B / A;</span>
<span class="nc" id="L166">			r1 = -B / (2.0 * A);</span>
<span class="nc" id="L167">			r2 = r1;</span>

<span class="nc bnc" id="L169" title="All 2 branches missed.">		} else if (delta &lt; 0) {</span>
<span class="nc" id="L170">			double T = (2.0 * A * bcof - 3.0 * acof * B) / (2.0 * Math.pow(A, 3.0 / 2.0));</span>
<span class="nc" id="L171">			double theta = Math.acos(T);</span>
<span class="nc" id="L172">			root = (-bcof - 2.0 * Math.sqrt(A) * Math.cos(theta / 3.0)) / (3.0 * acof);</span>
<span class="nc" id="L173">			r1 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) + Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L174">					/ (3.0 * acof);</span>
<span class="nc" id="L175">			r2 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) - Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L176">					/ (3.0 * acof);</span>
		}
<span class="nc" id="L178">		roots[0] = root;</span>
<span class="nc" id="L179">		roots[1] = r1;</span>
<span class="nc" id="L180">		roots[2] = r2;</span>
<span class="nc" id="L181">		return roots;</span>
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L185">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L186" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L187">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L189">				low = mid + 1;</span>
<span class="nc" id="L190">			} else {</span>
<span class="nc" id="L191">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L194" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L195">			mid = mid - 1;</span>
		}
<span class="nc" id="L197">		tests.add(mid + 1, p);</span>
<span class="nc" id="L198">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>