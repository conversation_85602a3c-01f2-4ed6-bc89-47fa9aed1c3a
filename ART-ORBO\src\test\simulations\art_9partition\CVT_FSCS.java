package test.simulations.art_9partition;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import util.RandomCreator;

import java.util.Collection;
import java.util.HashSet;
import java.util.Random;
import java.util.Set;

/**
 * Created by xijiaxiang on 2018/6/10.
 */
public class CVT_FSCS {


    public static void main(String[] args) {
        double[] min=new double[]{0,0};
        double[] max=new double[]{1,1};
        RandomCreator random =new RandomCreator(new Random(3),2,min,max);
        NRectRegion testRegion=new NRectRegion(new NPoint(min),new NPoint(max));

        NPoint[] points=new NPoint[3];

        for(int i=0;i<points.length;i++){
            points[i]=random.randomPoint(testRegion);
        }
    }
    public static void CVT(NPoint[] point,NRectRegion region){

    }
}
