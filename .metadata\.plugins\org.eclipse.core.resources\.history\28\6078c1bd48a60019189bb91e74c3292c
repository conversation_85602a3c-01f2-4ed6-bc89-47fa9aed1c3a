package test.simulations.fscs;
/*
 * n维实现,包含1维2维等
 * */

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.PointPatternIn2D;
import datastructure.failurepattern.impl.StripPatternIn2D;
import test.ART;
import util.data.ZeroOneCreator;

public class FSCS_ND_SimEm extends ART {
	public static void main(String[] args) {

		int d = 7;
		ZeroOneCreator dataCreator = new ZeroOneCreator();
		double[] min = dataCreator.minCreator(d);
		double[] max = dataCreator.maxCreator(d);
		int s = 10;

		int[] ems = {1000, 2000, 3000, 4000, 5000, 6000, 7000};
		//int [] ems = {500, 1000, 1500, 2000, 2500, 3000};
		for (int f = 0; f < ems.length; f++) {
			int times = 5000;

			double fail_rate = 0.001;
			//FailurePattern failurePattern = new BlockPattern();
			FailurePattern failurePattern = new StripPatternIn2D();
			//FailurePattern failurePattern = new PointPatternIn2D();
			failurePattern.fail_rate = fail_rate;

			int Em = 0;
			long startTime = System.currentTimeMillis();
			for (int i = 0; i < times; i++) {
				FSCS_ND_SimEm test = new FSCS_ND_SimEm(min, max, s, failurePattern, new Random(i * 3), ems[f]);
				int temp = test.run();
				Em += temp;
			}
			long endTime = System.currentTimeMillis();
			System.out.println("Em for " + ems[f] + " test cases: " + "Em:" + (Em / (double) times) + " time:"
					+ ((endTime - startTime) / (double) times));
			// System.out.println(fm / (double) times);
			// System.out.println((endTime - startTime) / (double) times);
		}
	}

	int s = 10;// 表示候选集的数量初始值为10

	ArrayList<NPoint> tests = new ArrayList<>();
	private int em;

	public FSCS_ND_SimEm(double[] min, double[] max, int s, FailurePattern pattern, Random random, int em) {
		super(min, max, random, pattern);
		this.s = s;
		this.em = em;
	}

	public double calTwoPointDistance(NPoint p1, NPoint p2) {
		double[] p1xn = p1.getXn();
		double[] p2xn = p2.getXn();
		double distance = 0.0;
		for (int i = 0; i < p1xn.length; i++) {
			distance += Math.pow((p2xn[i] - p1xn[i]), 2);
		}
		distance = Math.sqrt(distance);
		return distance;
	}

	public NPoint randomTC() {
		NPoint point = new NPoint();
		point.dimension = this.dimension;
		double[] xn = new double[this.dimension];
		for (int i = 0; i < xn.length; i++) {
			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];
		}
		point.setXn(xn);
		return point;
	}

	public int run() {
		int count = 0;
		int emCount1 = 0;
		NPoint p = randomTC();

		// while (this.failPattern.isCorrect(p)) {
		while (count < em) {
			if (!failPattern.isCorrect(p)) {
				// return 1;
				emCount1++;
			}
			count++;
			tests.add(p);
			p = new NPoint();
			double maxDistance = -1.0;
			NPoint bestCandidate = null;
			for (int i = 0; i < s; i++) {
				NPoint candidate = randomTC();
				// 计算两个点的距离
				double minDistance = Double.MAX_VALUE;

				for (int j = 0; j < this.tests.size(); j++) {
					double tempDistance = calTwoPointDistance(candidate, tests.get(j));
					if (tempDistance < minDistance) {
						minDistance = tempDistance;
					}
				}
				if (maxDistance < minDistance) {
					maxDistance = minDistance;
					bestCandidate = candidate;
				}
			}
			p = null;
			p = bestCandidate;
		}
		// return count;
		return emCount1;
	}
}
