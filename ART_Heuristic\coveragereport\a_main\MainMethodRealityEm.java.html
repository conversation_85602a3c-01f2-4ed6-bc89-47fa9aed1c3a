<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MainMethodRealityEm.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">a_main</a> &gt; <span class="el_source">MainMethodRealityEm.java</span></div><h1>MainMethodRealityEm.java</h1><pre class="source lang-java linenums">package a_main;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.Random;

import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tp._ND.ART_TP_ND;
import test.simulations.art_tpp.ART_TPP;
import test.simulations.fscs.FSCS_ND;
import test.simulations.rrt.RRT_ND;
import test.simulations.rt.RT_ND;
import util.data.RealityClasses;
import util.file.FileUtils;

<span class="nc" id="L21">public class MainMethodRealityEm {</span>
	// TODO 完成参数化
	public static void main(String[] args)
			throws Exception, IllegalAccessException, NoSuchFieldException, SecurityException, InstantiationException {
<span class="nc" id="L25">		int times = 8;</span>
<span class="nc" id="L26">		Class[] classes = RealityClasses.get();</span>
<span class="nc" id="L27">		String path = &quot;C:\\Users\\<USER>\\Desktop\\研究方向和内容\\小论文\\补充数据\\em\\&quot;;</span>
<span class="nc" id="L28">		File rtf = FileUtils.createNewFile(path, &quot;all2.txt&quot;);</span>
<span class="nc" id="L29">		BufferedWriter writer = get(rtf);</span>
<span class="nc bnc" id="L30" title="All 2 branches missed.">		for (int j = 1; j &lt; classes.length; j++) {</span>

			// =SUM(A2:GS2)/((ROW(GT2)-1)*500)

<span class="nc" id="L34">			Class tempClass = classes[j];</span>

<span class="nc" id="L36">			System.out.println(&quot;now class:&quot; + tempClass.getName());</span>

<span class="nc" id="L38">			writer.write(&quot;now class:&quot; + tempClass.getName());</span>
<span class="nc" id="L39">			writer.newLine();</span>

<span class="nc" id="L41">			double failureRate = tempClass.getDeclaredField(&quot;failureRate&quot;).getDouble(null);</span>
<span class="nc" id="L42">			double[] min = (double[]) tempClass.getDeclaredField(&quot;min&quot;).get(null);</span>
<span class="nc" id="L43">			double[] max = (double[]) tempClass.getDeclaredField(&quot;max&quot;).get(null);</span>
<span class="nc" id="L44">			int Dimension = (int) tempClass.getDeclaredField(&quot;Dimension&quot;).get(null);</span>

<span class="nc" id="L46">			FailurePattern failurePattern = new RealityFailPattern(tempClass.newInstance().getClass().getSimpleName());</span>
<span class="nc" id="L47">			failurePattern.fail_rate = failureRate;</span>
<span class="nc" id="L48">			failurePattern.min = min;</span>
<span class="nc" id="L49">			failurePattern.max = max;</span>
<span class="nc" id="L50">			failurePattern.dimension = Dimension;</span>

			// rt
<span class="nc" id="L53">			int fm = 0;</span>
<span class="nc" id="L54">			long startTime = System.currentTimeMillis();</span>
			/*
			 * writer.write(&quot;rt=[&quot;); for (int i = 0; i &lt; times; i++) { RT_ND rt
			 * = new RT_ND(min, max, new Random(i * 3), failurePattern); int
			 * temp = rt.run(); writer.write(temp+&quot;&quot;); writer.newLine();
			 * writer.flush(); fm += temp; }
			 */
<span class="nc" id="L61">			long endTime = System.currentTimeMillis();</span>
			// writer.write(&quot;];&quot;);
			// writer.newLine();
			// System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; +
			// ((endTime - startTime) / (double) times));

			// rrt
<span class="nc" id="L68">			double r = 1.5;</span>
<span class="nc bnc" id="L69" title="All 2 branches missed.">			if (Dimension == 1) {</span>
<span class="nc" id="L70">				r = 0.75;</span>
<span class="nc" id="L71">			} else {</span>
<span class="nc" id="L72">				r = 1.5;</span>
			}
<span class="nc" id="L74">			fm = 0;</span>
<span class="nc" id="L75">			startTime = System.currentTimeMillis();</span>
<span class="nc" id="L76">			writer.write(&quot;   RRT:&quot;);</span>
<span class="nc" id="L77">			writer.newLine();</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L79">				int temp2 = 0;</span>
<span class="nc bnc" id="L80" title="All 2 branches missed.">				for (int m = 0; m &lt; 100; m++) {</span>
<span class="nc" id="L81">					RRT_ND rt = new RRT_ND(min, max, failurePattern, new Random(i * 3), r);</span>
<span class="nc" id="L82">					rt.emCount = (i + 1) * 500;</span>
<span class="nc" id="L83">					int temp = rt.run();</span>
<span class="nc" id="L84">					temp2 += temp;</span>
				}
<span class="nc" id="L86">				fm += temp2 / 100.0;</span>
<span class="nc" id="L87">				writer.write((temp2 /(double)100.0) + &quot;&quot;);</span>
<span class="nc" id="L88">				writer.newLine();</span>
<span class="nc" id="L89">				writer.flush();</span>
			}
			// writer.write(&quot;];&quot;);
<span class="nc" id="L92">			writer.newLine();</span>
<span class="nc" id="L93">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L94">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>

			// fscs
<span class="nc" id="L97">			fm = 0;</span>
<span class="nc" id="L98">			int s = 10;</span>
<span class="nc" id="L99">			startTime = System.currentTimeMillis();</span>
<span class="nc" id="L100">			writer.write(&quot;   FSCS:&quot;);</span>
<span class="nc" id="L101">			writer.newLine();</span>
<span class="nc bnc" id="L102" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L103">				int temp2 = 0;</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">				for (int m = 0; m &lt; 100; m++) {</span>
<span class="nc" id="L105">					FSCS_ND rt = new FSCS_ND(min, max, s, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L106">					rt.emCount = (i + 1) * 500;</span>
<span class="nc" id="L107">					int temp = rt.run();</span>
<span class="nc" id="L108">					temp2 += temp;</span>
				}
<span class="nc" id="L110">				writer.write((temp2 /(double) 100.0) + &quot;&quot;);</span>
<span class="nc" id="L111">				writer.newLine();</span>
<span class="nc" id="L112">				writer.flush();</span>
				// fm += temp;
			}
			// writer.write(&quot;];&quot;);
<span class="nc" id="L116">			writer.newLine();</span>
<span class="nc" id="L117">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L118">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>

			// art_b
			/*
			 * fm=0; startTime = System.currentTimeMillis();
			 * writer.write(&quot;artb=[&quot;); for (int i = 0; i &lt; times; i++) {
			 * ART_B_ND rt = new ART_B_ND(min, max, new Random(i *
			 * 3),failurePattern); int temp = rt.run(); writer.write(temp+&quot;&quot;);
			 * writer.newLine(); writer.flush(); fm += temp; }
			 * writer.write(&quot;];&quot;); writer.newLine(); endTime =
			 * System.currentTimeMillis(); System.out.println(&quot;Fm:&quot; + (fm /
			 * (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double)
			 * times));
			 */
			// art_rp
			/*
			 * fm=0; startTime = System.currentTimeMillis();
			 * writer.write(&quot;artrp=[&quot;); for (int i = 0; i &lt; times; i++) {
			 * ART_RP_ND rt = new ART_RP_ND(min, max, new Random(i *
			 * 3),failurePattern); int temp = rt.run(); writer.write(temp+&quot;&quot;);
			 * writer.newLine(); writer.flush(); fm += temp; }
			 * writer.write(&quot;];&quot;); writer.newLine(); endTime =
			 * System.currentTimeMillis(); System.out.println(&quot;Fm:&quot; + (fm /
			 * (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double)
			 * times));
			 * 
			 * 
			 * //art_tpp fm=0; int k=10; writer.write(&quot;arttpp=[&quot;); startTime =
			 * System.currentTimeMillis(); for (int i = 0; i &lt; times; i++) {
			 * ART_TPP rt = new ART_TPP(min, max, new Random(i *
			 * 3),failurePattern,k); int temp = rt.run(); writer.write(temp+&quot;&quot;);
			 * writer.newLine(); writer.flush(); fm += temp; }
			 * writer.write(&quot;];&quot;); writer.newLine(); endTime =
			 * System.currentTimeMillis(); System.out.println(&quot;Fm:&quot; + (fm /
			 * (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double)
			 * times));
			 */
			// art_tp
<span class="nc" id="L156">			fm = 0;</span>
<span class="nc" id="L157">			writer.write(&quot;    ART_TP:&quot;);</span>
<span class="nc" id="L158">			writer.newLine();</span>
<span class="nc" id="L159">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L160" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L161">				int temp2 = 0;</span>
<span class="nc bnc" id="L162" title="All 2 branches missed.">				for (int m = 0; m &lt; 100.0; m++) {</span>
<span class="nc" id="L163">					ART_TP_ND rt = new ART_TP_ND(min, max, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L164">					rt.emCount=(i+1)*500;</span>
<span class="nc" id="L165">					int temp = rt.run();</span>
<span class="nc" id="L166">					temp2 += temp;</span>
				}
<span class="nc" id="L168">				writer.write((temp2 /(double) 100.0) + &quot;&quot;);</span>
<span class="nc" id="L169">				writer.newLine();</span>
<span class="nc" id="L170">				writer.flush();</span>
				//fm += temp;
			}
			// writer.write(&quot;];&quot;);
<span class="nc" id="L174">			writer.newLine();</span>
<span class="nc" id="L175">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L176">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>

			
			// my method
			// 1dimension
			// fm=0;
			// startTime = System.currentTimeMillis();
			// for (int i = 0; i &lt; times; i++) {
			// RRTtp1D rt = new RRTtp1D(min, max, new Random(i *
			// 3),failurePattern,k);
			// int temp = rt.run();
			// fm += temp;
			// }
			// endTime = System.currentTimeMillis();
			// System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; +
			// ((endTime - startTime) / (double) times));
			//
		}
<span class="nc" id="L194">		writer.close();</span>
<span class="nc" id="L195">	}</span>

	public static BufferedWriter get(File f) throws Exception {
<span class="nc" id="L198">		return new BufferedWriter(new OutputStreamWriter(new FileOutputStream(f), &quot;UTF-8&quot;));</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>