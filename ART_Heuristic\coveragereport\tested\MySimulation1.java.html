<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MySimulation1.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">MySimulation1.java</span></div><h1>MySimulation1.java</h1><pre class="source lang-java linenums">package tested;

<span class="nc" id="L3">public class MySimulation1 {</span>
	public String wrong(double x) {
<span class="nc" id="L5">		StringBuffer buffer = new StringBuffer();</span>
		// (x&gt;0&amp;&amp;x&lt;0.582)||(x&gt;0.587&amp;&amp;x&lt;1.0)
<span class="nc bnc" id="L7" title="All 4 branches missed.">		if ((x &gt; 0 &amp;&amp; x &lt; 1.0)) {</span>
<span class="nc" id="L8">			buffer.append(&quot;yes1&quot;);</span>

<span class="nc bnc" id="L10" title="All 4 branches missed.">			if (x &gt; 0.5 &amp;&amp; x &lt; 0.95) {</span>
<span class="nc" id="L11">				buffer.append(&quot;yes11&quot;);</span>
<span class="nc" id="L12">				buffer.append(&quot;yes22&quot;);</span>

<span class="nc bnc" id="L14" title="All 4 branches missed.">				if ((x &gt; 0.65 &amp;&amp; x &lt; 0.655)) {</span>
<span class="nc" id="L15">					buffer.append(&quot;yes111&quot;);</span>
<span class="nc" id="L16">					buffer.append(&quot;yes222&quot;);</span>
<span class="nc" id="L17">					buffer.append(&quot;yes333&quot;);</span>
<span class="nc" id="L18">					buffer.append(&quot;yes444&quot;);</span>
<span class="nc" id="L19">					buffer.append(&quot;yes555&quot;);</span>
<span class="nc" id="L20">					buffer.append(&quot;yes666&quot;);</span>
<span class="nc" id="L21">					buffer.append(&quot;yes777&quot;);</span>
<span class="nc" id="L22">					buffer.append(&quot;yes888&quot;);</span>
<span class="nc" id="L23">					buffer.append(&quot;yes999&quot;);</span>
<span class="nc" id="L24">					buffer.append(&quot;yes111&quot;);</span>
<span class="nc" id="L25">					buffer.append(&quot;yes222&quot;);</span>
<span class="nc" id="L26">					buffer.append(&quot;yes333&quot;);</span>
<span class="nc" id="L27">					buffer.append(&quot;yes444&quot;);</span>
<span class="nc" id="L28">					buffer.append(&quot;yes555&quot;);</span>
<span class="nc" id="L29">					buffer.append(&quot;yes666&quot;);</span>
<span class="nc" id="L30">					buffer.append(&quot;yes777&quot;);</span>
<span class="nc" id="L31">					buffer.append(&quot;yes888&quot;);</span>
<span class="nc" id="L32">					buffer.append(&quot;yes999&quot;);</span>
<span class="nc" id="L33">					buffer.append(&quot;yes101010&quot;);</span>
<span class="nc" id="L34">					buffer.append(&quot;yes11111&quot;);</span>
				}
			}
<span class="nc" id="L37">		} else {</span>
<span class="nc" id="L38">			buffer.append(&quot;no1&quot;);</span>
		}
<span class="nc" id="L40">		return buffer.toString();</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>