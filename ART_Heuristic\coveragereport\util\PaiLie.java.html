<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>PaiLie.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util</a> &gt; <span class="el_source">PaiLie.java</span></div><h1>PaiLie.java</h1><pre class="source lang-java linenums">package util;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

<span class="nc" id="L7">public class PaiLie {</span>
	public static ArrayList&lt;double[]&gt; GetAll(double[] xn, double[] yn) {
<span class="nc" id="L9">		double b[][] = new double[xn.length][2];</span>
<span class="nc bnc" id="L10" title="All 2 branches missed.">		for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="nc" id="L11">			b[i] = new double[] { xn[i], yn[i] };</span>
		}

<span class="nc" id="L14">		double a[][] = b;</span>
		String s;
		// 总循环次数，控制循环量
<span class="nc" id="L17">		int it = (int) Math.pow((a[0].length), (a.length)) - 1;</span>
<span class="nc" id="L18">		ArrayList&lt;double[]&gt; list = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L19" title="All 2 branches missed.">		while (it &gt;= 0) {</span>
<span class="nc" id="L20">			s = &quot;&quot;;</span>
			// it % a[0].length;
			// (it / a[0].length) % a[0].length;
			// (it / a[0].length) / a[0].length % a[0].length;
			// 临时变量，保存迭代器
<span class="nc" id="L25">			int temp = it;</span>
<span class="nc bnc" id="L26" title="All 2 branches missed.">			for (int m = 0; m &lt; a.length; m++) {</span>
<span class="nc bnc" id="L27" title="All 2 branches missed.">				if (temp / a[0].length &gt;= 0) {</span>
<span class="nc" id="L28">					s += a[m][temp % a[0].length] + &quot; &quot;;</span>
<span class="nc" id="L29">					temp /= a[0].length;</span>
				}
			}
<span class="nc" id="L32">			String[] tempStr = s.split(&quot; &quot;);</span>
<span class="nc" id="L33">			double[] tempVal = new double[tempStr.length];</span>
<span class="nc bnc" id="L34" title="All 2 branches missed.">			for (int i = 0; i &lt; tempStr.length; i++) {</span>
<span class="nc" id="L35">				tempVal[i] = Double.parseDouble(tempStr[i]);</span>
			}
<span class="nc" id="L37">			list.add(tempVal);</span>
			// System.out.println(s);
<span class="nc" id="L39">			it--;</span>
		}
<span class="nc" id="L41">		return list;</span>
	}

	public static void main(String[] args) {
		// 测试数组
		// double a[][] = { { 1.1, 2.1 }, { 1.2, 2.2 }, { 1.3, 2.3} };
		// String s;
		// // 总循环次数，控制循环量
		// int it = (int) Math.pow((a[0].length), (a.length)) - 1;
		// ArrayList&lt;String&gt; list=new ArrayList&lt;&gt;();
		// while (it &gt;= 0) {
		// s = &quot;&quot;;
		// // it % a[0].length;
		// // (it / a[0].length) % a[0].length;
		// // (it / a[0].length) / a[0].length % a[0].length;
		// // 临时变量，保存迭代器
		// int temp = it;
		// for (int m = 0; m &lt; a.length; m++) {
		// if (temp / a[0].length &gt;= 0) {
		// s += a[m][temp % a[0].length]+&quot; &quot;;
		// temp /= a[0].length;
		// }
		// }
		// list.add(s);
		// System.out.println(s);
		// it--;
		// }
		// double[] xn={1.1,2.1};
		// double[] yn={2.2,3.2};
		//
		//
		// ArrayList&lt;double[]&gt; lists=PaiLie.GetAll(xn, yn);
		// System.out.println(&quot;count:&quot;+lists.size());
		// for (int i = 0; i &lt; lists.size(); i++) {
		// System.out.println(Arrays.toString(lists.get(i)));
		// }
		// double p[]=new double[]{1.5,2.5};
		// ArrayList&lt;List&lt;double[]&gt;&gt; results=PaiLie.reOrder(p, lists);
		// List&lt;double[]&gt; results0=results.get(0);
		// List&lt;double[]&gt; results1=results.get(1);
		// for (int i = 0; i &lt; results0.size(); i++) {
		// System.out.println(&quot;0:&quot;+Arrays.toString(results0.get(i)));
		// System.out.println(&quot;1:&quot;+Arrays.toString(results1.get(i)));
		// }
		// double[] start= {0.1,0.2,0.3};
		// double[] pxn= {0.4,0.5,0.6};
		// //change to 0.1,0.4 agroup 0.2,0.5 a group
		// double[] end= {0.5,0.6,0.7};
		//
		// List&lt;List&lt;Double&gt;&gt; result1=splitRegions(start, pxn);
		// List&lt;List&lt;Double&gt;&gt; result2=splitRegions(pxn, end);
		//
		// for(int i=0;i&lt;result1.size();i++) {
		// List&lt;Double&gt; temp1=result1.get(i);
		// List&lt;Double&gt; temp2=result2.get(i);
		// System.out.println(temp1+&quot;,&quot;+temp2);
		// }

<span class="nc" id="L99">	}</span>
	// public static List&lt;List&lt;Double&gt;&gt; splitRegions(double[] start,double[] end){
	// ArrayList&lt;double[]&gt; values=new ArrayList&lt;&gt;();
	// for(int i=0;i&lt;start.length;i++) {
	// double[] temp=new double[2];
	//
	// temp[0]=start[i];
	// temp[1]=end[i];
	// values.add(temp);
	// }
	//
	//
	//
	// ArrayList&lt;List&lt;Double&gt;&gt; result=new ArrayList&lt;&gt;();
	// per(values, 0, new ArrayList&lt;&gt;(), result);
	// return result;
	// }

	public static void per(ArrayList&lt;double[]&gt; values, int index, ArrayList&lt;Double&gt; currentList,
			ArrayList&lt;List&lt;Double&gt;&gt; result) {

<span class="nc bnc" id="L120" title="All 2 branches missed.">		if (index == values.size()) {</span>
			// System.out.println(currentList);
<span class="nc" id="L122">			List&lt;Double&gt; temp = new ArrayList&lt;&gt;(currentList);</span>
<span class="nc" id="L123">			result.add(temp);</span>
<span class="nc" id="L124">			return;</span>
		}
<span class="nc" id="L126">		double[] temp = values.get(index);</span>

<span class="nc" id="L128">		index++;</span>
<span class="nc bnc" id="L129" title="All 2 branches missed.">		for (int i = 0; i &lt; temp.length; i++) {</span>
			// System.out.println(&quot;index:&quot; + index + &quot; value;&quot; + temp[i]);
<span class="nc" id="L131">			currentList.add(temp[i]);</span>
<span class="nc" id="L132">			per(values, index, currentList, result);</span>
<span class="nc" id="L133">			currentList.remove(currentList.size() - 1);</span>
		}
<span class="nc" id="L135">	}</span>

	public static ArrayList&lt;List&lt;double[]&gt;&gt; reOrder(double p[], ArrayList&lt;double[]&gt; lists) {

<span class="nc" id="L139">		ArrayList&lt;double[]&gt; start = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L140">		ArrayList&lt;double[]&gt; end = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L141">		ArrayList&lt;List&lt;double[]&gt;&gt; results = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L142" title="All 2 branches missed.">		for (int i = 0; i &lt; lists.size(); i++) {</span>
<span class="nc" id="L143">			double q[] = Arrays.copyOf(lists.get(i), lists.get(i).length);</span>
<span class="nc" id="L144">			double min[] = new double[p.length];</span>
<span class="nc" id="L145">			double max[] = new double[p.length];</span>
<span class="nc bnc" id="L146" title="All 2 branches missed.">			for (int j = 0; j &lt; p.length; j++) {</span>

<span class="nc bnc" id="L148" title="All 2 branches missed.">				if (p[j] &lt; q[j]) {</span>
<span class="nc" id="L149">					min[j] = p[j];</span>
<span class="nc" id="L150">					max[j] = q[j];</span>
<span class="nc" id="L151">				} else {</span>
<span class="nc" id="L152">					max[j] = p[j];</span>
<span class="nc" id="L153">					min[j] = q[j];</span>
				}
			}
<span class="nc" id="L156">			start.add(min);</span>
<span class="nc" id="L157">			end.add(max);</span>
<span class="nc" id="L158">			results.add(start);</span>
<span class="nc" id="L159">			results.add(end);</span>
			// System.out.println(&quot;pailie:&quot;+Arrays.toString(min)+&quot;,&quot;+Arrays.toString(max));
		}

<span class="nc" id="L163">		return results;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>