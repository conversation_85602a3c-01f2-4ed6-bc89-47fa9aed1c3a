<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MainMethodBoxPlot.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">a_main</a> &gt; <span class="el_source">MainMethodBoxPlot.java</span></div><h1>MainMethodBoxPlot.java</h1><pre class="source lang-java linenums">package a_main;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.Random;

import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tp._ND.ART_TP_ND;
import test.simulations.art_tpp.ART_TPP;
import test.simulations.fscs.FSCS_ND;
import test.simulations.rrt.RRT_ND;
import test.simulations.rrttp.hilbert.RRTtpND_H;
import test.simulations.rt.RT_ND;
import util.data.ZeroOneCreator;
import util.file.FileUtils;

<span class="nc" id="L23">public class MainMethodBoxPlot {</span>
	// TODO 完成参数化
	public static void main(String[] args) throws Exception {
<span class="nc" id="L26">		int times = 100;</span>
<span class="nc" id="L27">		double failrates[] = { 0.005 };</span>
<span class="nc" id="L28">		String path=&quot;C:\\Users\\<USER>\\Desktop\\研究方向和内容\\小论文\\补充数据\\boxplot\\2维0.005&quot;;</span>
		//File tempFile=new File(&quot;C:\\Users\\<USER>\\Desktop\\研究方向和内容\\小论文\\补充数据\\boxplot&quot;);
<span class="nc bnc" id="L30" title="All 2 branches missed.">		for (int j = 0; j &lt; failrates.length; j++) {</span>
<span class="nc" id="L31">			double failrate = failrates[j];</span>
<span class="nc" id="L32">			int d = 2;</span>
			
			
<span class="nc" id="L35">			ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L36">			double[] min = dataCreator.minCreator(d);</span>
<span class="nc" id="L37">			double[] max = dataCreator.maxCreator(d);</span>
<span class="nc" id="L38">			FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L39">			failurePattern.fail_rate = failrate;</span>
<span class="nc" id="L40">			failurePattern.min = min;</span>
<span class="nc" id="L41">			failurePattern.max = max;</span>
<span class="nc" id="L42">			failurePattern.dimension = d;</span>

			//rt
<span class="nc" id="L45">			File rtf=FileUtils.createNewFile(path, &quot;rt.txt&quot;);</span>
<span class="nc" id="L46">			BufferedWriter writer=get(rtf);</span>
<span class="nc" id="L47">			int fm = 0;</span>
<span class="nc" id="L48">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L49" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L50">				RT_ND rt = new RT_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L51">				int temp = rt.run();</span>
<span class="nc" id="L52">				fm += temp;</span>
<span class="nc" id="L53">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L54">				writer.newLine();</span>
<span class="nc" id="L55">				writer.flush();</span>
			}
<span class="nc" id="L57">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L58">			writer.close();</span>
<span class="nc" id="L59">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
		
			//rrt
<span class="nc" id="L62">			File rrtf=FileUtils.createNewFile(path, &quot;rrt.txt&quot;);</span>
<span class="nc" id="L63">			 writer=get(rrtf);</span>
<span class="nc" id="L64">			double r=0.75;</span>
<span class="nc" id="L65">			fm=0;</span>
<span class="nc" id="L66">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L67" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L68">				RRT_ND rt = new RRT_ND(min, max,  failurePattern,new Random(i * 3),r);</span>
<span class="nc" id="L69">				int temp = rt.run();</span>
<span class="nc" id="L70">				fm += temp;</span>
<span class="nc" id="L71">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L72">				writer.newLine();</span>
<span class="nc" id="L73">				writer.flush();</span>
			}
<span class="nc" id="L75">			writer.close();</span>
<span class="nc" id="L76">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L77">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//fscs
<span class="nc" id="L80">			File fscs=FileUtils.createNewFile(path, &quot;fscs.txt&quot;);</span>
<span class="nc" id="L81">			 writer=get(fscs);</span>
<span class="nc" id="L82">			fm=0;</span>
<span class="nc" id="L83">			int s=10;</span>
<span class="nc" id="L84">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L85" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L86">				FSCS_ND rt = new FSCS_ND(min, max, s, failurePattern,new Random(i * 3));</span>
<span class="nc" id="L87">				int temp = rt.run();</span>
<span class="nc" id="L88">				fm += temp;</span>
<span class="nc" id="L89">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L90">				writer.newLine();</span>
<span class="nc" id="L91">				writer.flush();</span>
			}
<span class="nc" id="L93">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L94">			writer.close();</span>
<span class="nc" id="L95">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			
			//art_b
<span class="nc" id="L99">			File artbf=FileUtils.createNewFile(path, &quot;artb.txt&quot;);</span>
<span class="nc" id="L100">			 writer=get(artbf);</span>
<span class="nc" id="L101">			fm=0;</span>
<span class="nc" id="L102">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L103" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L104">				ART_B_ND rt = new ART_B_ND(min, max, new Random(i * 3),failurePattern);</span>
<span class="nc" id="L105">				int temp = rt.run();</span>
<span class="nc" id="L106">				fm += temp;</span>
<span class="nc" id="L107">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L108">				writer.newLine();</span>
<span class="nc" id="L109">				writer.newLine();</span>
			}
<span class="nc" id="L111">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L112">			writer.close();</span>
<span class="nc" id="L113">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//art_rp
<span class="nc" id="L116">			File artrpf=FileUtils.createNewFile(path, &quot;artrp.txt&quot;);</span>
<span class="nc" id="L117">			 writer=get(artrpf);</span>
<span class="nc" id="L118">			fm=0;</span>
<span class="nc" id="L119">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L120" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L121">				ART_RP_ND rt = new ART_RP_ND(min, max, new Random(i * 3),failurePattern);</span>
<span class="nc" id="L122">				int temp = rt.run();</span>
<span class="nc" id="L123">				fm += temp;</span>
<span class="nc" id="L124">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L125">				writer.newLine();</span>
<span class="nc" id="L126">				writer.flush();</span>
			}
<span class="nc" id="L128">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L129">			writer.close();</span>
<span class="nc" id="L130">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//art_tp
<span class="nc" id="L133">			File arttpf=FileUtils.createNewFile(path, &quot;arttp.txt&quot;);</span>
<span class="nc" id="L134">			 writer=get(arttpf);</span>
<span class="nc" id="L135">			fm=0;</span>
<span class="nc" id="L136">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L137" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L138">				ART_TP_ND rt = new ART_TP_ND(min, max, failurePattern,new Random(i * 3));</span>
<span class="nc" id="L139">				int temp = rt.run();</span>
<span class="nc" id="L140">				fm += temp;</span>
<span class="nc" id="L141">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L142">				writer.newLine();</span>
<span class="nc" id="L143">				writer.flush();</span>
			}
<span class="nc" id="L145">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L146">			writer.close();</span>
<span class="nc" id="L147">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//art_tpp
<span class="nc" id="L150">			File arttppf=FileUtils.createNewFile(path, &quot;arttpp.txt&quot;);</span>
<span class="nc" id="L151">			 writer=get(arttppf);</span>
<span class="nc" id="L152">			fm=0;</span>
<span class="nc" id="L153">			int k=10;</span>
<span class="nc" id="L154">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L155" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L156">				ART_TPP rt = new ART_TPP(min, max, new Random(i * 3),failurePattern,k);</span>
<span class="nc" id="L157">				int temp = rt.run();</span>
<span class="nc" id="L158">				fm += temp;</span>
<span class="nc" id="L159">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L160">				writer.newLine();</span>
<span class="nc" id="L161">				writer.flush();</span>
			}
<span class="nc" id="L163">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L164">			writer.close();</span>
<span class="nc" id="L165">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//my method
			//1dimension
<span class="nc" id="L169">			File lazf=FileUtils.createNewFile(path, &quot;laz.txt&quot;);</span>
<span class="nc" id="L170">			 writer=get(lazf);</span>
<span class="nc" id="L171">			r=0.75;</span>
<span class="nc" id="L172">			fm=0;</span>
<span class="nc" id="L173">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
//				/min, max, 0.75, pattern, new Random(i * 3+v*5)
<span class="nc" id="L176">				RRTtpND_H rt = new RRTtpND_H(min, max, r, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L177">				int temp = rt.run();</span>
<span class="nc" id="L178">				fm += temp;</span>
<span class="nc" id="L179">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L180">				writer.newLine();</span>
<span class="nc" id="L181">				writer.flush();</span>
			}
<span class="nc" id="L183">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L184">			writer.close();</span>
<span class="nc" id="L185">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
		}
<span class="nc" id="L188">	}</span>
	public static BufferedWriter get(File f) throws Exception{
<span class="nc" id="L190">		return new BufferedWriter(new OutputStreamWriter(new FileOutputStream(f),&quot;UTF-8&quot;));</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>