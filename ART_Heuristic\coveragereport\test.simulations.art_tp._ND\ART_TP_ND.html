<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_ND</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.art_tp._ND</a> &gt; <span class="el_class">ART_TP_ND</span></div><h1>ART_TP_ND</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,202 of 1,202</td><td class="ctr2">0%</td><td class="bar">60 of 60</td><td class="ctr2">0%</td><td class="ctr1">46</td><td class="ctr2">46</td><td class="ctr1">215</td><td class="ctr2">215</td><td class="ctr1">16</td><td class="ctr2">16</td></tr></tfoot><tbody><tr><td id="a1"><a href="ART_TP_ND.java.html#L71" class="el_method">addRegions(double[], double[], double[])</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="138" alt="138"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">4</td><td class="ctr2" id="g1">4</td><td class="ctr1" id="h4">18</td><td class="ctr2" id="i4">18</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a0"><a href="ART_TP_ND.java.html#L46" class="el_method">addFromAndTo(TPInfo2)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="103" height="10" title="119" alt="119"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">4</td><td class="ctr2" id="g2">4</td><td class="ctr1" id="h2">20</td><td class="ctr2" id="i2">20</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a11"><a href="ART_TP_ND.java.html#L338" class="el_method">testEm(int, double)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="100" height="10" title="116" alt="116"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f7">3</td><td class="ctr2" id="g7">3</td><td class="ctr1" id="h0">25</td><td class="ctr2" id="i0">25</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a8"><a href="ART_TP_ND.java.html#L200" class="el_method">genNextTestCase(int, double)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="91" height="10" title="105" alt="105"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d9"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h9">12</td><td class="ctr2" id="i9">12</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a5"><a href="ART_TP_ND.java.html#L124" class="el_method">calEachRegion()</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="89" height="10" title="103" alt="103"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d8"><img src="../.resources/redbar.gif" width="60" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f8">3</td><td class="ctr2" id="g8">3</td><td class="ctr1" id="h7">15</td><td class="ctr2" id="i7">15</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="ART_TP_ND.java.html#L171" class="el_method">genNextEachDimension(double, double, double, double, double, double, double, double)</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="88" height="10" title="102" alt="102"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="8" alt="8"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f0">5</td><td class="ctr2" id="g0">5</td><td class="ctr1" id="h6">17</td><td class="ctr2" id="i6">17</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a12"><a href="ART_TP_ND.java.html#L295" class="el_method">testFm()</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="77" height="10" title="89" alt="89"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d10"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h5">18</td><td class="ctr2" id="i5">18</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a6"><a href="ART_TP_ND.java.html#L237" class="el_method">generateNextTC()</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="73" height="10" title="85" alt="85"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f3">4</td><td class="ctr2" id="g3">4</td><td class="ctr1" id="h1">22</td><td class="ctr2" id="i1">22</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a14"><a href="ART_TP_ND.java.html#L269" class="el_method">time()</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="66" height="10" title="76" alt="76"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h3">20</td><td class="ctr2" id="i3">20</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a13"><a href="ART_TP_ND.java.html#L318" class="el_method">testTCTime(int, int)</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="60" height="10" title="70" alt="70"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d11"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f11">2</td><td class="ctr2" id="g11">2</td><td class="ctr1" id="h8">14</td><td class="ctr2" id="i8">14</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a2"><a href="ART_TP_ND.java.html#L100" class="el_method">addRegionsRec(int, int, List, List, ArrayList)</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="52" height="10" title="60" alt="60"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f5">4</td><td class="ctr2" id="g5">4</td><td class="ctr1" id="h10">12</td><td class="ctr2" id="i10">12</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a4"><a href="ART_TP_ND.java.html#L119" class="el_method">calEachIntEC(double, double, double, double)</a></td><td class="bar" id="b11"><img src="../.resources/redbar.gif" width="43" height="10" title="50" alt="50"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a9"><a href="ART_TP_ND.java.html#L224" class="el_method">isInRegion(NPoint, NPoint, NPoint)</a></td><td class="bar" id="b12"><img src="../.resources/redbar.gif" width="33" height="10" title="39" alt="39"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="90" height="10" title="6" alt="6"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f6">4</td><td class="ctr2" id="g6">4</td><td class="ctr1" id="h11">8</td><td class="ctr2" id="i11">8</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a15"><a href="ART_TP_ND.java.html#L216" class="el_method">updateRegions(NPoint, int)</a></td><td class="bar" id="b13"><img src="../.resources/redbar.gif" width="26" height="10" title="30" alt="30"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d12"><img src="../.resources/redbar.gif" width="30" height="10" title="2" alt="2"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f12">2</td><td class="ctr2" id="g12">2</td><td class="ctr1" id="h12">6</td><td class="ctr2" id="i12">6</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a3"><a href="ART_TP_ND.java.html#L35" class="el_method">ART_TP_ND(double[], double[], FailurePattern, Random)</a></td><td class="bar" id="b14"><img src="../.resources/redbar.gif" width="14" height="10" title="17" alt="17"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h13">4</td><td class="ctr2" id="i13">4</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a10"><a href="ART_TP_ND.java.html#L32" class="el_method">main(String[])</a></td><td class="bar" id="b15"><img src="../.resources/redbar.gif" width="2" height="10" title="3" alt="3"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">2</td><td class="ctr2" id="i15">2</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>