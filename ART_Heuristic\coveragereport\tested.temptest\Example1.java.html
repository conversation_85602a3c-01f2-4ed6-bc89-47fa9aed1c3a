<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>Example1.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested.temptest</a> &gt; <span class="el_source">Example1.java</span></div><h1>Example1.java</h1><pre class="source lang-java linenums">package tested.temptest;

<span class="nc" id="L3">class Example1 {</span>
	public static void main(String args[]) {
<span class="nc" id="L5">		float x = 12.56f, y;</span>
<span class="nc bnc" id="L6" title="All 2 branches missed.">		if (x &lt;= 0) {</span>
<span class="nc" id="L7">			y = x + 1;</span>
<span class="nc bnc" id="L8" title="All 4 branches missed.">		} else if (x &gt; 0 &amp;&amp; x &lt;= 16) {</span>
<span class="nc" id="L9">			y = 2 * x + 1;</span>
<span class="nc" id="L10">		} else {</span>
<span class="nc" id="L11">			y = 3 * x + 3;</span>
		}
<span class="nc" id="L13">		System.out.println(y);</span>
<span class="nc" id="L14">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>