<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_TD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._2D</a> &gt; <span class="el_source">ART_TP_TD.java</span></div><h1>ART_TP_TD.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._2D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.TPInfo;
import util.X3;

/*
 * 测试
 * 生成二维的ART_TP，用来探索n维的测试用例
 * 其中使用二维特殊化的，均有二维特殊化提醒
 * */
public class ART_TP_TD {
	public static void main(String[] args) {
		// start,end 表示边界，from to表示Anbn
<span class="nc" id="L18">		double[] min = { 0, 0 };</span>
<span class="nc" id="L19">		double[] max = { 1, 1 };</span>
<span class="nc" id="L20">		int times = 3000;</span>
<span class="nc" id="L21">		int fm = 0;</span>
<span class="nc" id="L22">		long start = System.currentTimeMillis();</span>
<span class="nc bnc" id="L23" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L24">			ART_TP_TD test = new ART_TP_TD(min, max, 0.01, i * 3 + 3);</span>
<span class="nc" id="L25">			int temp = test.run();</span>
<span class="nc" id="L26">			fm += temp;</span>
		}
<span class="nc" id="L28">		long end = System.currentTimeMillis();</span>
<span class="nc" id="L29">		System.out.println(fm / (double) times);</span>
<span class="nc" id="L30">		System.out.println((end - start) / (double) times);</span>
<span class="nc" id="L31">	}</span>
	double[] min;
	double[] max;
	double fail_rate;
	double[] fail_start;
	long seed;
	int dimension;
	double totalAreaS;
	double failAreaS;
	double eachFailLength;
	Random random;
<span class="nc" id="L42">	ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L43">	ArrayList&lt;TPInfo&gt; regions = new ArrayList&lt;&gt;();</span>

	double C;// 常数

<span class="nc" id="L47">	public ART_TP_TD(double[] min, double[] max, double fail_rate, long seed) {</span>
<span class="nc" id="L48">		this.min = min;</span>
<span class="nc" id="L49">		this.max = max;</span>
<span class="nc" id="L50">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L51">		this.seed = seed;</span>
<span class="nc" id="L52">		random = new Random(this.seed);</span>
<span class="nc bnc" id="L53" title="All 2 branches missed.">		if (min.length == max.length) {</span>
<span class="nc" id="L54">			this.dimension = min.length;</span>
		}
<span class="nc" id="L56">		totalAreaS = 1.0;</span>
<span class="nc bnc" id="L57" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc" id="L58">			totalAreaS *= max[i] - min[i];</span>
		}
<span class="nc" id="L60">		this.failAreaS = fail_rate * totalAreaS;</span>
<span class="nc" id="L61">		this.eachFailLength = Math.pow(failAreaS, 1.0 / (double) this.dimension);</span>
		// generate Fail start;
<span class="nc" id="L63">		fail_start = new double[this.dimension];</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc" id="L65">			fail_start[i] = random.nextDouble() * (max[i] - min[i] - this.eachFailLength) + min[i];</span>
		}
<span class="nc" id="L67">	}</span>

	public void addFromAndTo(TPInfo region) {
<span class="nc" id="L70">		NPoint start = region.start;</span>
<span class="nc" id="L71">		NPoint end = region.end;</span>
<span class="nc" id="L72">		double from1 = 0.0;</span>
<span class="nc" id="L73">		double to1 = 0.0;</span>
<span class="nc" id="L74">		double from2 = 0.0;</span>
<span class="nc" id="L75">		double to2 = 0.0;</span>
<span class="nc bnc" id="L76" title="All 2 branches missed.">		if (start.getXn()[0] == min[0]) {</span>
<span class="nc" id="L77">			from1 = random.nextDouble() * (end.getXn()[0] - min[0]) + (2 * min[0] - end.getXn()[0]);</span>
<span class="nc" id="L78">		} else {</span>
<span class="nc" id="L79">			from1 = start.getXn()[0];</span>
		}
<span class="nc bnc" id="L81" title="All 2 branches missed.">		if (start.getXn()[1] == min[1]) {</span>
<span class="nc" id="L82">			from2 = random.nextDouble() * (end.getXn()[1] - min[1]) + (2 * min[1] - end.getXn()[1]);</span>
<span class="nc" id="L83">		} else {</span>
<span class="nc" id="L84">			from2 = start.getXn()[1];</span>
		}
<span class="nc bnc" id="L86" title="All 2 branches missed.">		if (end.getXn()[0] == max[0]) {</span>
<span class="nc" id="L87">			to1 = random.nextDouble() * (max[0] - end.getXn()[0]) + max[0];</span>
<span class="nc" id="L88">		} else {</span>
<span class="nc" id="L89">			to1 = end.getXn()[0];</span>
		}
<span class="nc bnc" id="L91" title="All 2 branches missed.">		if (end.getXn()[1] == max[1]) {</span>
<span class="nc" id="L92">			to2 = random.nextDouble() * (max[1] - end.getXn()[1]) + max[1];</span>
<span class="nc" id="L93">		} else {</span>
<span class="nc" id="L94">			to2 = end.getXn()[1];</span>
		}
<span class="nc" id="L96">		region.from = new NPoint(new double[] { from1, from2 });</span>
<span class="nc" id="L97">		region.to = new NPoint(new double[] { to1, to2 });</span>
<span class="nc" id="L98">	}</span>

	public void addTDRegions(double[] min, double[] max, NPoint p) {

<span class="nc" id="L102">		TPInfo info1 = new TPInfo();</span>
<span class="nc" id="L103">		info1.start = new NPoint(new double[] { min[0], min[1] });</span>
<span class="nc" id="L104">		info1.end = new NPoint(p.getXn());</span>
<span class="nc" id="L105">		addFromAndTo(info1);</span>

<span class="nc" id="L107">		TPInfo info2 = new TPInfo();</span>
<span class="nc" id="L108">		info2.start = new NPoint(new double[] { p.getXn()[0], min[1] });</span>
<span class="nc" id="L109">		info2.end = new NPoint(new double[] { max[0], p.getXn()[1] });</span>
<span class="nc" id="L110">		addFromAndTo(info2);</span>

<span class="nc" id="L112">		TPInfo info3 = new TPInfo();</span>
<span class="nc" id="L113">		info3.start = new NPoint(p.getXn());</span>
<span class="nc" id="L114">		info3.end = new NPoint(new double[] { max[0], max[1] });</span>
<span class="nc" id="L115">		addFromAndTo(info3);</span>

<span class="nc" id="L117">		TPInfo info4 = new TPInfo();</span>
<span class="nc" id="L118">		info4.start = new NPoint(new double[] { min[0], p.getXn()[1] });</span>
<span class="nc" id="L119">		info4.end = new NPoint(new double[] { p.getXn()[0], max[1] });</span>
<span class="nc" id="L120">		addFromAndTo(info4);</span>

<span class="nc" id="L122">		regions.add(info1);</span>
<span class="nc" id="L123">		regions.add(info2);</span>
<span class="nc" id="L124">		regions.add(info3);</span>
<span class="nc" id="L125">		regions.add(info4);</span>

		// System.out.println(&quot;add four regions:&quot;);
		// System.out.println(info1.start + &quot;,&quot; + info1.end);
		// System.out.println(info2.start + &quot;,&quot; + info2.end);
		// System.out.println(info3.start + &quot;,&quot; + info3.end);
		// System.out.println(info4.start + &quot;,&quot; + info4.end);
		// System.out.println(&quot;-------------------&quot;);
<span class="nc" id="L133">	}</span>

	// public double calEachIntEC(double u, double v, double f, double t) {
	// // int((x-u)*(v-x),f,t)
	// return -(1.0 / 3.0) * (t * t * t - f * f * f) + (0.5 * (u + v) * (t * t -
	// f * f)) - u * v * (t - f);
	// }
	public double calEachIntEC2(double s, double e, double f, double t) {
		// int(x-f)*(t-x) s,t
<span class="nc" id="L142">		return (-1.0 / 6.0) * (e - s)</span>
<span class="nc" id="L143">				* (e * (-3 * f + 2 * s - 3 * t) - 3 * f * s + 6 * f * t + 2 * s * s - 3 * s * t + 2 * e * e);</span>
	}

	public double calEachRegion() {
<span class="nc" id="L147">		double tempC = 0.0;</span>
		// System.out.println(&quot;each region cdf:&quot;);
<span class="nc bnc" id="L149" title="All 2 branches missed.">		for (int i = 0; i &lt; regions.size(); i++) {</span>
			// 二维特殊化
			// NRectRegion temp=regions.get(i);
<span class="nc" id="L152">			NPoint start = regions.get(i).start;</span>
<span class="nc" id="L153">			NPoint end = regions.get(i).end;</span>
<span class="nc" id="L154">			NPoint from = regions.get(i).from;</span>
<span class="nc" id="L155">			NPoint to = regions.get(i).to;</span>
<span class="nc" id="L156">			double a = calEachIntEC2(start.getXn()[0], end.getXn()[0], from.getXn()[0], to.getXn()[0]);</span>
<span class="nc" id="L157">			double b = calEachIntEC2(start.getXn()[1], end.getXn()[1], from.getXn()[1], to.getXn()[1]);</span>
<span class="nc" id="L158">			regions.get(i).probality = a * b;</span>
<span class="nc" id="L159">			regions.get(i).proa = a;</span>
<span class="nc" id="L160">			regions.get(i).prob = b;</span>
<span class="nc" id="L161">			tempC += (a * b);</span>
			// System.out.println(&quot;int (x-&quot; + from.getXn()[0] + &quot;)*(&quot; + to.getXn()[0] + &quot;-x&quot;
			// + &quot;) from &quot; + start.getXn()[0]
			// + &quot; to &quot; + end.getXn()[0]);
			// System.out.println(&quot;int (x-&quot; + from.getXn()[1] + &quot;)*(&quot; + to.getXn()[1] + &quot;-x&quot;
			// + &quot;) from &quot; + start.getXn()[1]
			// + &quot; to &quot; + end.getXn()[1]);
			// System.out.println(&quot;from:&quot;+(from1)+&quot;,&quot;+from2+&quot; to:&quot;+to1+&quot;,&quot;+to2);
			// System.out.println(&quot;start:&quot;+(start.getXn()[0])+&quot;,&quot;+start.getXn()[1]+&quot;
			// to:&quot;+end.getXn()[0]+&quot;,&quot;+end.getXn()[1]);

			// System.out.println(&quot;eachValue:&quot; + (a) + &quot;,&quot; + b + &quot; multi:&quot; + (a * b));
			// System.out.println(&quot;*********&quot;);
		}
		// System.out.println(&quot;tempC:&quot; + tempC);
		// System.out.println(&quot;------------&quot;);
<span class="nc" id="L177">		return tempC;</span>
	}

	public double genNextEachDimension(double start, double end, double from, double to, double C, double aorb,
			double Pre, double T) {

		// System.out.println(&quot;cal next test case&quot;);
		// System.out.println(&quot;start:&quot;+start+&quot;,end:&quot;+end+&quot;,from:&quot;+from+&quot;,to:&quot;+to+&quot;,C:&quot;+C+&quot;,aorb:&quot;+aorb+&quot;,Pre:&quot;+Pre+&quot;,T:&quot;+T);
		// pre+c*(a|b)*int(start to x)((x-from)*(to-x))=T;
<span class="nc" id="L186">		double A = (-1.0 / 3.0) * C * aorb;</span>
<span class="nc" id="L187">		double B = 0.5 * (from + to) * (C) * (aorb);</span>
<span class="nc" id="L188">		double C1 = -from * to * C * aorb;</span>
<span class="nc" id="L189">		double D = C * aorb</span>
<span class="nc" id="L190">				* ((1.0 / 3.0) * (start * start * start) - 0.5 * (start * start) * (from + to) + from * to * start) - T</span>
<span class="nc" id="L191">				+ Pre;</span>
<span class="nc" id="L192">		double[] roots = X3.shengjinFormula(A, B, C1, D);</span>
		// System.out.println(&quot;roots:&quot;+Arrays.toString(roots));
<span class="nc" id="L194">		double next = -1.0;</span>
<span class="nc" id="L195">		boolean flag = false;</span>
<span class="nc bnc" id="L196" title="All 2 branches missed.">		for (int i = 0; i &lt; roots.length; i++) {</span>
<span class="nc bnc" id="L197" title="All 4 branches missed.">			if (roots[i] &gt; start &amp;&amp; roots[i] &lt; end) {</span>
<span class="nc" id="L198">				flag = true;</span>
<span class="nc" id="L199">				next = roots[i];</span>
<span class="nc" id="L200">				break;</span>
			}
		}
<span class="nc bnc" id="L203" title="All 2 branches missed.">		if (!flag) {</span>
<span class="nc" id="L204">			System.out.println(&quot;x3 error!&quot;);</span>
			// genNextEachDimension(start, end, from, to, C1, aorb, Pre, next)
<span class="nc" id="L206">			return Double.MIN_VALUE;</span>
		}
<span class="nc" id="L208">		return next;</span>
	}

	public boolean isCorrect(NPoint p) {
		// boolean flag=true;
<span class="nc" id="L213">		double[] xn = p.getXn();</span>
<span class="nc" id="L214">		boolean lead2Fail = false;</span>
<span class="nc bnc" id="L215" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc bnc" id="L216" title="All 4 branches missed.">			if (xn[i] &lt; this.fail_start[i] || xn[i] &gt; (this.fail_start[i] + this.eachFailLength)) {</span>
<span class="nc" id="L217">				lead2Fail = true;</span>
			}
		}
		// System.out.println(Arrays.toString(nDPoints));
		// System.out.println(&quot;isFail:&quot;+lead2Fail);
		// lead2Fail=false,失效，=true不失效
<span class="nc" id="L223">		return lead2Fail;</span>
	}

	public boolean isInRegion(NPoint start1, NPoint end1, NPoint p) {
<span class="nc" id="L227">		boolean flag = true;</span>
<span class="nc" id="L228">		double[] pxn = p.getXn();</span>
<span class="nc" id="L229">		double[] start = start1.getXn();</span>
<span class="nc" id="L230">		double[] end = end1.getXn();</span>
<span class="nc bnc" id="L231" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc bnc" id="L232" title="All 4 branches missed.">			if (pxn[i] &lt; start[i] || pxn[i] &gt; end[i]) {</span>
<span class="nc" id="L233">				flag = false;</span>
			}
		}
<span class="nc" id="L236">		return flag;</span>
	}

	public NPoint randomTC() {
		// generate from the input domain
<span class="nc" id="L241">		NPoint point = new NPoint();</span>
<span class="nc" id="L242">		double[] xn = new double[this.dimension];</span>
<span class="nc bnc" id="L243" title="All 2 branches missed.">		for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="nc" id="L244">			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];</span>
		}
<span class="nc" id="L246">		point.setDimension(this.dimension);</span>
<span class="nc" id="L247">		point.setXn(xn);</span>
<span class="nc" id="L248">		return point;</span>
	}

	public int run() {
<span class="nc" id="L252">		int count = 0;</span>
		// generate a new test case random
<span class="nc" id="L254">		NPoint p = randomTC();</span>
		// System.out.println(&quot;p:&quot; + p.toString());
<span class="nc bnc" id="L256" title="All 2 branches missed.">		while (isCorrect(p)) {</span>
<span class="nc" id="L257">			count++;</span>
<span class="nc" id="L258">			tests.add(p);</span>
<span class="nc" id="L259">			updateRegions(p);// 重新计算现有的区域</span>
			// generate next test case by test profile
			// ArrayList&lt;Double&gt; eachRegionProbility = new
			// ArrayList&lt;&gt;(this.regions.size());
<span class="nc" id="L263">			C = calEachRegion();</span>
<span class="nc" id="L264">			C = 1.0 / C;</span>
			// 随机产生一个0-1的数
<span class="nc" id="L266">			double T = random.nextDouble();</span>
			// double T2=random.nextDouble();
			// System.out.println(&quot;C:&quot; + C);
			// System.out.println(&quot;T:&quot; + T);
<span class="nc" id="L270">			double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L271">			double PreIntegral = 0.0;</span>
<span class="nc" id="L272">			int temp = 0;// 落在哪个区间</span>
<span class="nc bnc" id="L273" title="All 2 branches missed.">			for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc bnc" id="L274" title="All 2 branches missed.">				if (SumIntegral &lt; T) {</span>
<span class="nc" id="L275">					PreIntegral = SumIntegral;</span>
<span class="nc" id="L276">					temp = i;</span>
				}
				// System.out.println(&quot;proba:&quot; + regions.get(i).probality * C);
<span class="nc" id="L279">				SumIntegral += regions.get(i).probality * C;</span>
			}
			// System.out.println(&quot;temp index:&quot; + temp);
			// 计算积分值Pre+int((C)*(x1-from1)*(to1-x1)*(x2-from2)*(to2-x2))
			// 二维特殊化
<span class="nc" id="L284">			double[] start = this.regions.get(temp).start.getXn();</span>
<span class="nc" id="L285">			double[] end = this.regions.get(temp).end.getXn();</span>
<span class="nc" id="L286">			double[] from = this.regions.get(temp).from.getXn();</span>
<span class="nc" id="L287">			double[] to = this.regions.get(temp).to.getXn();</span>

<span class="nc" id="L289">			double T1 = random.nextDouble() * (regions.get(temp).probality * C) + PreIntegral;</span>
<span class="nc" id="L290">			double T2 = random.nextDouble() * (regions.get(temp).probality * C) + PreIntegral;</span>
<span class="nc" id="L291">			double x = genNextEachDimension(start[0], end[0], from[0], to[0], C, this.regions.get(temp).prob,</span>
<span class="nc" id="L292">					PreIntegral, T1);</span>
<span class="nc bnc" id="L293" title="All 2 branches missed.">			while (x == Double.MIN_VALUE) {</span>
<span class="nc" id="L294">				T1 = random.nextDouble() * (regions.get(temp).probality * C) + PreIntegral;</span>
<span class="nc" id="L295">				x = genNextEachDimension(start[0], end[0], from[0], to[0], C, this.regions.get(temp).prob, PreIntegral,</span>
<span class="nc" id="L296">						T1);</span>
			}
<span class="nc" id="L298">			double y = genNextEachDimension(start[1], end[1], from[1], to[1], C, this.regions.get(temp).proa,</span>
<span class="nc" id="L299">					PreIntegral, T);</span>
<span class="nc bnc" id="L300" title="All 2 branches missed.">			while (y == Double.MIN_VALUE) {</span>
<span class="nc" id="L301">				T2 = random.nextDouble() * (regions.get(temp).probality * C) + PreIntegral;</span>
<span class="nc" id="L302">				y = genNextEachDimension(start[1], end[1], from[1], to[1], C, this.regions.get(temp).proa, PreIntegral,</span>
<span class="nc" id="L303">						T2);</span>
			}
<span class="nc" id="L305">			p = null;</span>
<span class="nc" id="L306">			p = new NPoint(new double[] { x, y });</span>
			// System.out.println(&quot;p&quot; + count + &quot;:&quot; + p);
		}
<span class="nc" id="L309">		return count;</span>
	}

	public void updateRegions(NPoint p) {
<span class="nc bnc" id="L313" title="All 2 branches missed.">		if (regions.size() == 0) {</span>
			// 初始区域，添加四块区域（2的m次快）
<span class="nc" id="L315">			int regionsCount = (int) Math.pow(2, this.dimension);</span>
			// 这里针对二维的
<span class="nc" id="L317">			addTDRegions(this.min, this.max, p);</span>
			/// 二维特殊化
<span class="nc" id="L319">		} else {</span>
<span class="nc bnc" id="L320" title="All 2 branches missed.">			for (int i = 0; i &lt; regions.size(); i++) {</span>
				// 先判断这个点在不在这个区域内,要先删除区域
<span class="nc bnc" id="L322" title="All 2 branches missed.">				if (isInRegion(regions.get(i).start, regions.get(i).end, p)) {</span>
<span class="nc" id="L323">					TPInfo region = regions.remove(i);</span>
<span class="nc" id="L324">					i--;</span>
<span class="nc" id="L325">					addTDRegions(region.start.getXn(), region.end.getXn(), p);</span>
					/// 二维特殊化
<span class="nc" id="L327">					break;</span>
				}
			}
		}
<span class="nc" id="L331">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>