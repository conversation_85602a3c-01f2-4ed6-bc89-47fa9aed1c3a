<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_RP_TD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._2D</a> &gt; <span class="el_source">ART_TP_RP_TD.java</span></div><h1>ART_TP_RP_TD.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._2D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import datastructure.TD._2DPoint;
import datastructure.TD._2DRegion;

/**
 * 放弃开发二维的，直接开发N维ART_TP_RP
 */
public class ART_TP_RP_TD {
	// public double genNextK(Random random,double fixu,double fixv,double u,double
	// v){
	//
	// }
	public static void main(String[] args) {
<span class="nc" id="L19">		long fm = 0;</span>
<span class="nc bnc" id="L20" title="All 2 branches missed.">		for (int i = 0; i &lt; 100; i++) {</span>
<span class="nc" id="L21">			ART_TP_RP_TD art_TP_RP_TD = new ART_TP_RP_TD(null, 0.005, 3);</span>
			// System.out.println(art_TP_RP_TD.partOfCo(0.5, 0.8, 0.5, 0.8));;
		}
<span class="nc" id="L24">	}</span>
	int seedOfRandom;
	public _2DRegion initRegion;
	double fail_rate;
	double xfail_start;
	double yfail_start;
<span class="nc" id="L30">	Random random = null;</span>
<span class="nc" id="L31">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L33">	ArrayList&lt;_2DRegion&gt; regions = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L35">	public ART_TP_RP_TD(_2DRegion region, double fail_rate, int sed) {</span>
<span class="nc" id="L36">		this.initRegion = region;</span>
<span class="nc" id="L37">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L38">		this.seedOfRandom = sed;</span>
<span class="nc" id="L39">		random = new Random(seedOfRandom);</span>
<span class="nc" id="L40">	}</span>

	public boolean isCorrect(TestCase p) {
<span class="nc" id="L43">		double x = p.p;</span>
<span class="nc" id="L44">		double y = p.q;</span>
<span class="nc" id="L45">		double bianchang = Math.pow(fail_rate, 0.5);</span>
<span class="nc bnc" id="L46" title="All 8 branches missed.">		if ((x &gt; xfail_start &amp;&amp; x &lt; xfail_start + bianchang) &amp;&amp; (y &gt; yfail_start &amp;&amp; y &lt; yfail_start + bianchang)) {</span>
<span class="nc" id="L47">			return false;</span>
		} else {
<span class="nc" id="L49">			return true;</span>
		}
	}

	public double partOfCo(double u, double v, double min, double max) {
<span class="nc" id="L54">		return (-1.0 / 3.0) * (max * max * max - min * min * min) + ((u + v) / 2.0) * (max * max - min * min)</span>
<span class="nc" id="L55">				- u * v * (max - min);</span>
	}

	public int run() {
<span class="nc" id="L59">		int count = 0;</span>
<span class="nc" id="L60">		xfail_start = random.nextDouble() * (1.0 - Math.sqrt(fail_rate));</span>
<span class="nc" id="L61">		yfail_start = random.nextDouble() * (1.0 - Math.sqrt(fail_rate));</span>
<span class="nc" id="L62">		_2DRegion region = initRegion;</span>
<span class="nc" id="L63">		TestCase p = new TestCase();</span>
<span class="nc" id="L64">		p.p = random.nextDouble() * (region.max.x - region.min.x) + region.min.x;</span>
<span class="nc" id="L65">		p.q = random.nextDouble() * (region.max.y - region.max.y) + region.min.y;</span>
<span class="nc bnc" id="L66" title="All 2 branches missed.">		while (isCorrect(p)) {</span>
<span class="nc" id="L67">			count++;</span>
<span class="nc" id="L68">			double maxsize = 0;</span>
<span class="nc" id="L69">			_2DRegion maxregion = null;</span>
<span class="nc" id="L70">			int maxregion_index = 0;</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">			for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc" id="L72">				_2DRegion temp = regions.get(i);</span>
<span class="nc bnc" id="L73" title="All 2 branches missed.">				if (temp.size() &gt; maxsize) {</span>
<span class="nc" id="L74">					maxsize = temp.size();</span>
<span class="nc" id="L75">					maxregion = temp;</span>
<span class="nc" id="L76">					maxregion_index = i;</span>
				}
			}
			//
<span class="nc" id="L80">			regions.remove(maxregion_index);</span>
			// generate next one test case by art_tp
			// 积分式：：int(xmin:xmax,ymin:ymax) (x1-u1)*(v1-x1)*(x2-u2)*(v2-x2)
			// 积分的上下限
<span class="nc" id="L84">			double xmin = maxregion.min.x;</span>
<span class="nc" id="L85">			double xmax = maxregion.min.x + maxregion.max.x - maxregion.min.x;</span>
<span class="nc" id="L86">			double ymin = maxregion.min.y;</span>
<span class="nc" id="L87">			double ymax = maxregion.min.y + maxregion.max.y - maxregion.min.y;</span>

			// 从哪到哪
<span class="nc" id="L90">			double u1 = xmin;</span>
<span class="nc" id="L91">			double v1 = xmax;</span>
<span class="nc" id="L92">			double u2 = ymin;</span>
<span class="nc" id="L93">			double v2 = ymax;</span>
<span class="nc" id="L94">			if (xmin == initRegion.min.x) {</span>
				//
			}
<span class="nc" id="L97">			if (ymin == initRegion.min.y) {</span>

			}
<span class="nc" id="L100">			if (xmax == initRegion.max.x) {</span>

			}
<span class="nc" id="L103">			if (ymax == initRegion.max.y) {</span>

			}
			double Co;
			// Co=1.0/(x-xmin)*(xmax-x)*(y-ymin)*(ymax-y)的曲面积分
<span class="nc" id="L108">			double intTempx = partOfCo(u1, v1, xmin, xmax);</span>
<span class="nc" id="L109">			double intTempy = partOfCo(u2, v2, ymin, ymax);</span>
<span class="nc" id="L110">			Co = 1.0 / (intTempx * intTempy);</span>
			// random T
<span class="nc" id="L112">			double T = random.nextDouble();</span>

<span class="nc" id="L114">			p = new TestCase();</span>
			// p.p = random.nextDouble() * (xmax - xmin) + xmin;
			// p.q = random.nextDouble() * (ymax - ymin) + ymin;

			// add four regions
<span class="nc" id="L119">			_2DRegion first = new _2DRegion(new _2DPoint(xmin, ymin), new _2DPoint(p.p, p.q));</span>
<span class="nc" id="L120">			_2DRegion second = new _2DRegion(new _2DPoint(xmin, p.q), new _2DPoint(p.p, ymax));</span>
<span class="nc" id="L121">			_2DRegion third = new _2DRegion(new _2DPoint(p.p, p.q), new _2DPoint(xmax, ymax));</span>
<span class="nc" id="L122">			_2DRegion fourth = new _2DRegion(new _2DPoint(p.p, ymin), new _2DPoint(xmax, p.q));</span>
<span class="nc" id="L123">			regions.add(first);</span>
<span class="nc" id="L124">			regions.add(second);</span>
<span class="nc" id="L125">			regions.add(third);</span>
<span class="nc" id="L126">			regions.add(fourth);</span>
		}
<span class="nc" id="L128">		return count;</span>
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L132">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L133" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L134">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L135" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L136">				low = mid + 1;</span>
<span class="nc" id="L137">			} else {</span>
<span class="nc" id="L138">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L141" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L142">			mid = mid - 1;</span>
		}
<span class="nc" id="L144">		tests.add(mid + 1, p);</span>
<span class="nc" id="L145">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>