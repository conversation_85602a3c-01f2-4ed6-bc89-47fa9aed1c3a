<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>StripPatternIn2D.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.failurepattern.impl</a> &gt; <span class="el_source">StripPatternIn2D.java</span></div><h1>StripPatternIn2D.java</h1><pre class="source lang-java linenums">package datastructure.failurepattern.impl;

import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import util.draw.StdDraw;

<span class="nc" id="L9">public class StripPatternIn2D extends FailurePattern {</span>
	public static void main(String[] args) {
<span class="nc" id="L11">		StripPatternIn2D strip = new StripPatternIn2D();</span>
<span class="nc" id="L12">		strip.fail_rate = 0.01;</span>
<span class="nc" id="L13">		strip.dimension = 2;</span>
<span class="nc" id="L14">		strip.min = new double[] { 0, 0 };</span>
<span class="nc" id="L15">		strip.max = new double[] { 1, 1 };</span>
<span class="nc" id="L16">		strip.random = new Random(5);</span>

<span class="nc" id="L18">		strip.genFailurePattern();</span>

<span class="nc" id="L20">		StdDraw.rectangle(0.5, 0.5, 0.5, 0.5);</span>
<span class="nc" id="L21">		double x1 = strip.p1.getXn()[0];</span>
<span class="nc" id="L22">		double y1 = strip.p1.getXn()[1];</span>
<span class="nc" id="L23">		double x2 = strip.p3.getXn()[0];</span>
<span class="nc" id="L24">		double y2 = strip.p3.getXn()[1];</span>
<span class="nc" id="L25">		System.out.println(&quot;p1:(&quot; + x1 + &quot;,&quot; + y1 + &quot;),p3:(&quot; + x2 + &quot;,&quot; + y2 + &quot;)&quot;);</span>
<span class="nc" id="L26">		StdDraw.line(x1, y1, x2, y2);</span>

<span class="nc" id="L28">		double x3 = strip.p2.getXn()[0];</span>
<span class="nc" id="L29">		double y3 = strip.p2.getXn()[1];</span>
<span class="nc" id="L30">		double x4 = strip.p4.getXn()[0];</span>
<span class="nc" id="L31">		double y4 = strip.p4.getXn()[1];</span>
<span class="nc" id="L32">		System.out.println(&quot;p2:(&quot; + x3 + &quot;,&quot; + y3 + &quot;),p4:(&quot; + x4 + &quot;,&quot; + y4 + &quot;)&quot;);</span>
<span class="nc" id="L33">		StdDraw.line(x3, y3, x4, y4);</span>

<span class="nc" id="L35">		NPoint p1 = new NPoint(new double[] { 0.13, 0.13 });</span>
		// StdDraw.point(0.1, 0.1);
<span class="nc" id="L37">		StdDraw.circle(0.13, 0.13, 0.01);</span>
<span class="nc" id="L38">		System.out.println();</span>
<span class="nc" id="L39">		NPoint p2 = new NPoint(new double[] { 0.78, 0.03 });</span>

<span class="nc" id="L41">		StdDraw.circle(0.78, 0.03, 0.01);</span>
<span class="nc" id="L42">		System.out.println(strip.isCorrect(p2));</span>
<span class="nc" id="L43">	}</span>
	private NPoint p1;
	private NPoint p2;
	private NPoint p3;
	private NPoint p4;
	private double x;
	private double y;
	double fail_regionS;

	private int mode;

	private boolean check0(NPoint p) {
<span class="nc" id="L55">		double[] pxn = p.getXn();</span>
<span class="nc bnc" id="L56" title="All 4 branches missed.">		if (pxn[0] &gt; p1.getXn()[0] &amp;&amp; pxn[0] &lt; p2.getXn()[0]) {</span>
<span class="nc" id="L57">			return false;</span>
		} else {
<span class="nc" id="L59">			return true;</span>
		}
	}

	private boolean check1(NPoint p) {
<span class="nc" id="L64">		double[] pxn = p.getXn();</span>
<span class="nc bnc" id="L65" title="All 4 branches missed.">		if (pxn[1] &gt; p1.getXn()[1] &amp;&amp; pxn[1] &lt; p2.getXn()[1]) {</span>
<span class="nc" id="L66">			return false;</span>
		} else {
<span class="nc" id="L68">			return true;</span>
		}
	}

	private boolean check2(NPoint p) {
<span class="nc" id="L73">		double x = p.getXn()[0];</span>
<span class="nc" id="L74">		double y = p.getXn()[1];</span>

<span class="nc" id="L76">		double y1 = (x - p1.getXn()[0]) * (p3.getXn()[1] - p1.getXn()[1]) / (p3.getXn()[0] - p1.getXn()[0])</span>
<span class="nc" id="L77">				+ p1.getXn()[1];</span>
<span class="nc" id="L78">		double y2 = (x - p2.getXn()[0]) * (p4.getXn()[1] - p2.getXn()[1]) / (p4.getXn()[0] - p2.getXn()[0])</span>
<span class="nc" id="L79">				+ p2.getXn()[1];</span>

<span class="nc bnc" id="L81" title="All 4 branches missed.">		if (y &gt; y1 &amp;&amp; y &lt; y2) {</span>
<span class="nc" id="L82">			return false;</span>
		} else {
<span class="nc" id="L84">			return true;</span>
		}
	}

	private void gen0() {
<span class="nc" id="L89">		double[] pxn = random();</span>
<span class="nc" id="L90">		double width = fail_regionS;</span>
<span class="nc bnc" id="L91" title="All 2 branches missed.">		if (pxn[0] + width &lt; 1.0) {</span>
<span class="nc" id="L92">			p1 = new NPoint(new double[] { pxn[0], 0 });</span>
<span class="nc" id="L93">			p2 = new NPoint(new double[] { pxn[0] + width, 0 });</span>
<span class="nc" id="L94">			p3 = new NPoint(new double[] { pxn[0], 1 });</span>
<span class="nc" id="L95">			p4 = new NPoint(new double[] { pxn[0] + width, 1 });</span>
<span class="nc" id="L96">		} else {</span>
<span class="nc" id="L97">			gen0();</span>
		}
<span class="nc" id="L99">	}</span>

	private void gen1() {
<span class="nc" id="L102">		double[] pxn = random();</span>
<span class="nc" id="L103">		double width = fail_regionS;</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">		if (pxn[1] + width &lt; 1.0) {</span>
<span class="nc" id="L105">			p1 = new NPoint(new double[] { 0, pxn[1] });</span>
<span class="nc" id="L106">			p2 = new NPoint(new double[] { 0, pxn[1] + width });</span>
<span class="nc" id="L107">			p3 = new NPoint(new double[] { 1, pxn[1] });</span>
<span class="nc" id="L108">			p4 = new NPoint(new double[] { 1, pxn[1] + width });</span>
<span class="nc" id="L109">		} else {</span>
<span class="nc" id="L110">			gen1();</span>
		}
<span class="nc" id="L112">	}</span>

	private void gen2() {
<span class="nc" id="L115">		double x = random.nextDouble();</span>
<span class="nc" id="L116">		double y = 1 - x - Math.sqrt(2 * (0.5 * (1 - x) * (1 - x) - fail_regionS));</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">		if (y &lt; 1 - x) {</span>
<span class="nc" id="L118">			p1 = new NPoint(new double[] { 0, x });</span>
<span class="nc" id="L119">			p2 = new NPoint(new double[] { 0, x + y });</span>
<span class="nc" id="L120">			p3 = new NPoint(new double[] { 1 - x, 1 });</span>
<span class="nc" id="L121">			p4 = new NPoint(new double[] { 1 - x - y, 1 });</span>
<span class="nc" id="L122">		} else {</span>
<span class="nc" id="L123">			gen2();</span>
		}
<span class="nc" id="L125">	}</span>

	private void gen3() {
<span class="nc" id="L128">		double x = random.nextDouble();</span>
<span class="nc" id="L129">		double y = 1 - x - Math.sqrt(2 * (0.5 * (1 - x) * (1 - x) - fail_regionS));</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">		if (y &lt; 1 - x) {</span>
<span class="nc" id="L131">			p1 = new NPoint(new double[] { 0, 1 - x - y });</span>
<span class="nc" id="L132">			p2 = new NPoint(new double[] { 0, 1 - x });</span>
<span class="nc" id="L133">			p3 = new NPoint(new double[] { 1 - x - y, 0 });</span>
<span class="nc" id="L134">			p4 = new NPoint(new double[] { 1 - x, 0 });</span>
<span class="nc" id="L135">		} else {</span>
<span class="nc" id="L136">			gen3();</span>
		}
<span class="nc" id="L138">	}</span>

	private void gen4() {
<span class="nc" id="L141">		double x = random.nextDouble();</span>
<span class="nc" id="L142">		double y = 1 - x - Math.sqrt(2 * (0.5 * (1 - x) * (1 - x) - fail_regionS));</span>
<span class="nc bnc" id="L143" title="All 2 branches missed.">		if (y &lt; 1 - x) {</span>
<span class="nc" id="L144">			p1 = new NPoint(new double[] { x, 1 });</span>
<span class="nc" id="L145">			p2 = new NPoint(new double[] { x + y, 1 });</span>
<span class="nc" id="L146">			p3 = new NPoint(new double[] { 1, x });</span>
<span class="nc" id="L147">			p4 = new NPoint(new double[] { 1, x + y });</span>
<span class="nc" id="L148">		} else {</span>
<span class="nc" id="L149">			gen4();</span>
		}
<span class="nc" id="L151">	}</span>

	private void gen5() {
<span class="nc" id="L154">		double x = random.nextDouble();</span>
<span class="nc" id="L155">		double y = 1 - x - Math.sqrt(2 * (0.5 * (1 - x) * (1 - x) - fail_regionS));</span>
<span class="nc bnc" id="L156" title="All 2 branches missed.">		if (y &lt; 1 - x) {</span>
<span class="nc" id="L157">			p1 = new NPoint(new double[] { x + y, 0 });</span>
<span class="nc" id="L158">			p2 = new NPoint(new double[] { x, 0 });</span>
<span class="nc" id="L159">			p3 = new NPoint(new double[] { 1, 1 - x - y });</span>
<span class="nc" id="L160">			p4 = new NPoint(new double[] { 1, 1 - x });</span>
<span class="nc" id="L161">		} else {</span>
<span class="nc" id="L162">			gen5();</span>
		}
<span class="nc" id="L164">	}</span>

	@Override
	public void genFailurePattern() {
		// TODO Auto-generated method stub
<span class="nc" id="L169">		double totalArea = 1.0;</span>
<span class="nc bnc" id="L170" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc" id="L171">			totalArea *= (max[i] - min[i]);</span>
		}
<span class="nc" id="L173">		fail_regionS = this.fail_rate * totalArea;</span>
		/*
		 * p1 = new NPoint(new double[] { min[0], max[1] }); p2 = new NPoint(new
		 * double[] { min[1], max[0] }); double temp1 = max[1] - min[1]; double temp2 =
		 * max[0] - min[0]; this.y = Math.sqrt((temp1 * temp2 - 2 * fail_regionS) *
		 * (temp2 / temp1)); this.x = y * ((temp1) / temp2); p3 = new NPoint(new
		 * double[] { min[0], x }); p4 = new NPoint(new double[] { min[1], y });
		 */

		// method 2
<span class="nc" id="L183">		mode = random.nextInt(6);</span>
		// int mode=3;
		// System.out.println(&quot;mode:&quot;+mode);
<span class="nc bnc" id="L186" title="All 7 branches missed.">		switch (mode) {</span>
		case 0:
			// =
<span class="nc" id="L189">			gen0();</span>
<span class="nc" id="L190">			break;</span>
		case 1:
			// ||
<span class="nc" id="L193">			gen1();</span>
<span class="nc" id="L194">			break;</span>
		case 2:
			// |-
<span class="nc" id="L197">			gen2();</span>
<span class="nc" id="L198">			break;</span>
		case 3:
			// |_
<span class="nc" id="L201">			gen3();</span>
<span class="nc" id="L202">			break;</span>
		case 4:
			// -|
<span class="nc" id="L205">			gen4();</span>
<span class="nc" id="L206">			break;</span>
		case 5:
			// _|
<span class="nc" id="L209">			gen5();</span>
<span class="nc" id="L210">			break;</span>
		default:
			break;
		}
<span class="nc" id="L214">	}</span>

	public double getX() {
<span class="nc" id="L217">		return x;</span>
	}

	public double getY() {
<span class="nc" id="L221">		return y;</span>
	}

	@Override
	public boolean isCorrect(NPoint p) {
<span class="nc" id="L226">		boolean flag = true;</span>
		/*
		 * double[] pxn = p.getXn(); double yy = pxn[1]; double xx = pxn[0]; if (min[0]
		 * &lt; xx &amp;&amp; xx &lt; min[0] + y) { double yy1 = -x / y * xx + x; double yy2 = -xx +
		 * 1; if (yy &gt; yy1 &amp;&amp; yy &lt; yy2) { flag = false; } } else { double yy2 = -xx + 1;
		 * if (yy &gt; 0 &amp;&amp; yy &lt; yy2) { flag = false; } } return flag;
		 */

		// method2
<span class="nc bnc" id="L235" title="All 7 branches missed.">		switch (mode) {</span>
		case 0:
<span class="nc" id="L237">			flag = check0(p);</span>
<span class="nc" id="L238">			break;</span>
		case 1:
<span class="nc" id="L240">			flag = check1(p);</span>
<span class="nc" id="L241">			break;</span>
		case 2:
<span class="nc" id="L243">			flag = check2(p);</span>
<span class="nc" id="L244">			break;</span>
		case 3:
<span class="nc" id="L246">			flag = check2(p);</span>
<span class="nc" id="L247">			break;</span>
		case 4:
<span class="nc" id="L249">			flag = check2(p);</span>
<span class="nc" id="L250">			break;</span>
		case 5:
<span class="nc" id="L252">			flag = check2(p);</span>
<span class="nc" id="L253">			break;</span>
		default:
<span class="nc" id="L255">			flag = true;</span>
			break;
		}
<span class="nc" id="L258">		return flag;</span>
	}

	public double[] random() {
<span class="nc" id="L262">		double x = this.random.nextDouble();</span>
<span class="nc" id="L263">		double y = this.random.nextDouble();</span>
<span class="nc" id="L264">		return new double[] { x, y };</span>
	}

	public void setX(double x) {
<span class="nc" id="L268">		this.x = x;</span>
<span class="nc" id="L269">	}</span>

	public void setY(double y) {
<span class="nc" id="L272">		this.y = y;</span>
<span class="nc" id="L273">	}</span>

	@Override
	public void showFailurePattern() {
		// TODO Auto-generated method stub
		
<span class="nc" id="L279">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>