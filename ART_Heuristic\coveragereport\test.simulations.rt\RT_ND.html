<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RT_ND</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.rt</a> &gt; <span class="el_class">RT_ND</span></div><h1>RT_ND</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">532 of 532</td><td class="ctr2">0%</td><td class="bar">20 of 20</td><td class="ctr2">0%</td><td class="ctr1">21</td><td class="ctr2">21</td><td class="ctr1">121</td><td class="ctr2">121</td><td class="ctr1">11</td><td class="ctr2">11</td></tr></tfoot><tbody><tr><td id="a8"><a href="RT_ND.java.html#L33" class="el_method">testFm()</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="125" alt="125"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f0">3</td><td class="ctr2" id="g0">3</td><td class="ctr1" id="h1">22</td><td class="ctr2" id="i1">22</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a7"><a href="RT_ND.java.html#L82" class="el_method">testEm(int, double)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="103" height="10" title="108" alt="108"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f1">3</td><td class="ctr2" id="g1">3</td><td class="ctr1" id="h0">24</td><td class="ctr2" id="i0">24</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a9"><a href="RT_ND.java.html#L62" class="el_method">testTCTime(int, int)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="56" height="10" title="59" alt="59"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">2</td><td class="ctr2" id="g3">2</td><td class="ctr1" id="h3">13</td><td class="ctr2" id="i3">13</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a5"><a href="RT_ND.java.html#L114" class="el_method">RT_ND(double[], double[], Random, FailurePattern)</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="53" height="10" title="56" alt="56"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i2">16</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="RT_ND.java.html#L15" class="el_method">main(String[])</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="46" height="10" title="48" alt="48"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h4">13</td><td class="ctr2" id="i4">13</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a4"><a href="RT_ND.java.html#L143" class="el_method">randomTC()</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="44" height="10" title="46" alt="46"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h6">7</td><td class="ctr2" id="i6">7</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="RT_ND.java.html#L165" class="el_method">em()</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="26" height="10" title="28" alt="28"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="120" height="10" title="4" alt="4"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h5">10</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a3"><a href="RT_ND.java.html#L194" class="el_method">pm()</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="24" height="10" title="25" alt="25"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d9"/><td class="ctr2" id="e9">n/a</td><td class="ctr1" id="f9">1</td><td class="ctr2" id="g9">1</td><td class="ctr1" id="h9">2</td><td class="ctr2" id="i9">2</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a6"><a href="RT_ND.java.html#L154" class="el_method">run()</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="16" height="10" title="17" alt="17"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h7">6</td><td class="ctr2" id="i7">6</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a10"><a href="RT_ND.java.html#L185" class="el_method">time()</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="14" height="10" title="15" alt="15"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="60" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h8">6</td><td class="ctr2" id="i8">6</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a1"><a href="RT_ND.java.html#L180" class="el_method">generateNextTC()</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="4" height="10" title="5" alt="5"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h10">2</td><td class="ctr2" id="i10">2</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>