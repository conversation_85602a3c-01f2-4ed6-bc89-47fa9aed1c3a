<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>EmResultExcel.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util.excel</a> &gt; <span class="el_source">EmResultExcel.java</span></div><h1>EmResultExcel.java</h1><pre class="source lang-java linenums">package util.excel;

import org.apache.poi.hssf.usermodel.HSSFSheet;

<span class="nc" id="L5">public class EmResultExcel {</span>
//	public static void main(String[] args) {
//		Excel excel = new Excel();
//		HSSFSheet sheettest = excel.createSheet(&quot;test&quot;);
//		String[][] styles = genValues();
//		excel.writeCell2Sheet(sheettest, styles);
//		
//		String[][] values={{&quot;1&quot;,&quot;2&quot;},{&quot;11&quot;,&quot;22&quot;}};
//		excel.writeCell2Sheet(sheettest, values, 1, 1);
//		excel.saveAsFile(&quot;D://test.xls&quot;);
//	}
<span class="nc" id="L16">	private static Excel excel = new Excel();</span>

	public static void save(String sheetName,String[][] values){
<span class="nc" id="L19">		HSSFSheet sheettest = excel.createSheet(sheetName);</span>
<span class="nc" id="L20">		String[][] styles = genValues();</span>
<span class="nc" id="L21">		excel.writeCell2Sheet(sheettest, styles);</span>
<span class="nc" id="L22">		excel.writeCell2Sheet(sheettest, values, 1, 1);</span>
		//excel.saveAsFile(&quot;D://test.xls&quot;);
<span class="nc" id="L24">	}</span>
	public static void save(String fileName){
<span class="nc" id="L26">		excel.saveAsFile(fileName);</span>
<span class="nc" id="L27">	}</span>
	public static String[][] genValues() {
<span class="nc" id="L29">		String[][] values = new String[41][201];</span>
<span class="nc bnc" id="L30" title="All 2 branches missed.">		for (int i = 0; i &lt; 41; i++) {</span>
<span class="nc bnc" id="L31" title="All 2 branches missed.">			for (int j = 0; j &lt; 201; j++) {</span>
<span class="nc bnc" id="L32" title="All 4 branches missed.">				if (i == 0 &amp;&amp; j == 0) {</span>
<span class="nc" id="L33">					values[i][j] = &quot;&quot;;</span>
				}

<span class="nc bnc" id="L36" title="All 4 branches missed.">				if (i == 0 &amp;&amp; j != 0) {</span>
<span class="nc" id="L37">					values[i][j] = j + &quot;&quot;;</span>
				}
<span class="nc bnc" id="L39" title="All 4 branches missed.">				if (j == 0 &amp;&amp; i != 0) {</span>
<span class="nc" id="L40">					values[i][j] = (((i-1) % 8 ) * 500+500) + &quot;&quot;;</span>
					
				}

			}
		}
<span class="nc" id="L46">		return values;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>