<?xml version="1.0" encoding="UTF-8"?>
<section name="Workbench">
	<item value="false" key="TypeHierarchyViewPart.qualifiednames"/>
	<item value="3" key="TypeHierarchyViewPart.orientation"/>
	<item value="false" key="TypeHierarchyViewPart.linkeditors"/>
	<item value="0" key="CallHierarchyViewPart.call_mode"/>
	<item value="2" key="CallHierarchyViewPart.field_mode"/>
	<item value="2" key="TypeHierarchyViewPart.hierarchyview"/>
	<item value="3" key="CallHierarchyViewPart.orientation"/>
	<item value="500" key="CallHierarchyViewPart.ratio1"/>
	<item value="1" key="SearchScopeActionGroup.search_scope_type"/>
	<item value="500" key="CallHierarchyViewPart.ratio3"/>
	<list key="SearchScopeActionGroup.working_set">
	</list>
	<section name="ProblemSeveritiesConfigurationBlock">
		<item value="false" key="expanded2"/>
		<item value="false" key="expanded1"/>
		<item value="false" key="expanded4"/>
		<item value="true" key="expanded3"/>
		<item value="false" key="expanded6"/>
		<item value="false" key="expanded5"/>
		<item value="false" key="expanded7"/>
		<item value="false" key="expanded0"/>
	</section>
	<section name="SearchInDialog">
		<item value="true" key="SearchInProjects"/>
		<item value="true" key="SearchInAppLibs"/>
		<item value="true" key="SearchInJRE"/>
		<item value="true" key="SearchInSources"/>
	</section>
	<section name="org.eclipse.jdt.internal.ui.packageview.PackageExplorerPart">
		<item value="2" key="layout"/>
		<item value="true" key="group_libraries"/>
		<item value="&lt;?xml version=&quot;1.0&quot; encoding=&quot;UTF-8&quot;?&gt;&#x0D;&#x0A;&lt;packageExplorer group_libraries=&quot;1&quot; layout=&quot;2&quot; linkWithEditor=&quot;0&quot; rootMode=&quot;1&quot; workingSetName=&quot;Aggregate for window 1497410865875&quot;&gt;&#x0D;&#x0A;&lt;customFilters userDefinedPatternsEnabled=&quot;false&quot;&gt;&#x0D;&#x0A;&lt;xmlDefinedFilters&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.StaticsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.buildfolder&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.mylyn.java.ui.MembersFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaProjectsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer_patternFilterId_.*&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonSharedProjectsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.SyntheticMembersFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ContainedLibraryFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.HideInnerClassFilesFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyInnerPackageFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.m2e.MavenModuleFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.buildship.ui.packageexplorer.filter.gradle.subProject&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ClosedProjectsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.EmptyLibraryContainerFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.PackageDeclarationFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.ImportDeclarationFilter&quot; isEnabled=&quot;true&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonJavaElementFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LibraryFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.CuAndClassFileFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.internal.ui.PackageExplorer.EmptyPackageFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.NonPublicFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.LocalTypesFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;child filterId=&quot;org.eclipse.jdt.ui.PackageExplorer.FieldsFilter&quot; isEnabled=&quot;false&quot;/&gt;&#x0D;&#x0A;&lt;/xmlDefinedFilters&gt;&#x0D;&#x0A;&lt;/customFilters&gt;&#x0D;&#x0A;&lt;/packageExplorer&gt;" key="memento"/>
		<item value="1" key="rootMode"/>
		<item value="false" key="linkWithEditor"/>
	</section>
	<section name="NewClassWizardPage">
		<item value="false" key="create_constructor"/>
		<item value="true" key="create_unimplemented"/>
	</section>
	<section name="OptionalMessageDialog.hide.">
		<item value="true" key="org.eclipse.jdt.ui.typecomment.deprecated"/>
	</section>
	<section name="DialogBounds_GetterSetterTreeSelectionDialog">
		<item value="528" key="DIALOG_WIDTH"/>
		<item value="1|Segoe UI|9.0|0|WINDOWS|1|-12|0|0|0|400|0|0|0|1|0|0|0|0|Segoe UI" key="DIALOG_FONT_NAME"/>
		<item value="696" key="DIALOG_HEIGHT"/>
		<item value="358" key="DIALOG_X_ORIGIN"/>
		<item value="13" key="DIALOG_Y_ORIGIN"/>
	</section>
	<section name="RefactoringWizard.preview">
		<item value="600" key="width"/>
		<item value="400" key="height"/>
	</section>
	<section name="CallHierarchySearchScope">
	</section>
	<section name="JavaBuildConfigurationBlock">
		<item value="false" key="expanded2"/>
		<item value="false" key="expanded1"/>
		<item value="true" key="expanded0"/>
	</section>
	<section name="org.eclipse.jdt.internal.ui.wizards.buildpaths.NewVariableEntryDialog">
		<item value="577" key="DIALOG_WIDTH"/>
		<item value="1|Segoe UI|9.0|0|WINDOWS|1|-12|0|0|0|400|0|0|0|1|0|0|0|0|Segoe UI" key="DIALOG_FONT_NAME"/>
		<item value="398" key="DIALOG_HEIGHT"/>
		<item value="-174" key="DIALOG_X_ORIGIN"/>
		<item value="-125" key="DIALOG_Y_ORIGIN"/>
	</section>
	<section name="SourceActionDialog.methods">
		<item value="false" key="SynchronizedModifier"/>
		<item value="false" key="Comments"/>
		<item value="1" key="VisibilityModifier"/>
		<item value="false" key="FinalModifier"/>
	</section>
	<section name="BreadcrumbItemDropDown">
	</section>
	<section name="quick_assist_proposal_size">
	</section>
	<section name="BuildPathsPropertyPage">
		<item value="2" key="pageIndex"/>
	</section>
	<section name="NewPackageWizardPage">
		<item value="false" key="create_package_info_java"/>
	</section>
	<section name="completion_proposal_size">
	</section>
	<section name="AddGetterSetterDialog">
		<item value="false" key="SortOrdering"/>
		<item value="false" key="RemoveFinal"/>
	</section>
	<section name="JavaElementSearchActions">
	</section>
</section>
