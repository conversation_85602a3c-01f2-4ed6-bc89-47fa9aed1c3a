<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RealityClasses.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util.data</a> &gt; <span class="el_source">RealityClasses.java</span></div><h1>RealityClasses.java</h1><pre class="source lang-java linenums">package util.data;

import tested.airy;
import tested.bessj;
import tested.bessj0;
import tested.cel;
import tested.el2;
import tested.erfcc;
import tested.gammq;
import tested.golden;
import tested.plgndr;
import tested.probks;
import tested.sncndn;
import tested.tanh;

<span class="nc" id="L16">public class RealityClasses {</span>
	public static Class&lt;?&gt;[] get(){
<span class="nc" id="L18">		 Class&lt;?&gt;[] classes={airy.class,bessj0.class,erfcc.class,probks.class,tanh.class,</span>
<span class="nc" id="L19">				 	sncndn.class,golden.class,el2.class,</span>
<span class="nc" id="L20">				 	bessj.class,gammq.class,cel.class,plgndr.class};</span>
		//Class&lt;?&gt;[] classes={airy.class,bessj.class};
<span class="nc" id="L22">		return classes;</span>
	}
//	public static double[] getDoubles(Class classes,String colum){
//		try {
//			return (double[])(classes.getDeclaredField(colum).get(null));
//		} catch (Exception e) {
//			System.out.println(&quot;error in RealityClasses&quot;);
//			e.printStackTrace();
//		}finally {
//			return null;
//		} 
//		
//	}
//	public static int getInt(Class classes,String colum){
//		try {
//			return (int)(classes.getDeclaredField(colum).get(null));
//		} catch (Exception e) {
//			System.out.println(&quot;error in RealityClasses&quot;);
//			e.printStackTrace();
//		}finally {
//			return 0;
//		} 
//	}
//	public static double getDouble(Class classes,String colum){
//		try {
//			return (double)(classes.getDeclaredField(colum).get(null));
//		} catch (Exception e) {
//			System.out.println(&quot;error in RealityClasses&quot;);
//			e.printStackTrace();
//		}finally {
//			return 0.0;
//		} 
//	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>