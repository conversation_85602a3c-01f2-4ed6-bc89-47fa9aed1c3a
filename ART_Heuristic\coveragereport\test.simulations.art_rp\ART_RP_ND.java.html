<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_RP_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_rp</a> &gt; <span class="el_source">ART_RP_ND.java</span></div><h1>ART_RP_ND.java</h1><pre class="source lang-java linenums">package test.simulations.art_rp;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import test.simulations.art_b.ART_B_ND;
import test.simulations.fscs.FSCS_ND;
import util.PaiLie;
import util.data.ZeroOneCreator;

public class ART_RP_ND extends ART {

	public static void main(String[] args) {
		// testEm(1, 0.01);
<span class="nc" id="L21">		 testTCTime(2,5000);</span>
		//testFm();
<span class="nc" id="L23">	}</span>

	public NRectRegion initRegion;
<span class="nc" id="L26">	ArrayList&lt;NRectRegion&gt; regions = new ArrayList&lt;&gt;();</span>

	public ART_RP_ND(double[] min, double[] max, Random random, FailurePattern pattern) {
<span class="nc" id="L29">		super(min, max, random, pattern);</span>
		// initRegion = region;
<span class="nc" id="L31">		NRectRegion initRegion = new NRectRegion();</span>
<span class="nc" id="L32">		initRegion.setStart(new NPoint(min));</span>
<span class="nc" id="L33">		initRegion.setEnd(new NPoint(max));</span>
<span class="nc" id="L34">		this.initRegion = initRegion;</span>
<span class="nc" id="L35">		regions.add(initRegion);</span>
<span class="nc" id="L36">	}</span>

	public void addRegionsIn2D(NRectRegion region, NPoint p) {
<span class="nc" id="L39">		double xmin = region.getStart().getXn()[0];</span>
<span class="nc" id="L40">		double ymin = region.getStart().getXn()[1];</span>
<span class="nc" id="L41">		double xmax = region.getEnd().getXn()[0];</span>
<span class="nc" id="L42">		double ymax = region.getEnd().getXn()[1];</span>
<span class="nc" id="L43">		double pp = p.getXn()[0];</span>
<span class="nc" id="L44">		double qq = p.getXn()[1];</span>

<span class="nc" id="L46">		NRectRegion first = new NRectRegion(new NPoint(new double[] { xmin, ymin }),</span>
<span class="nc" id="L47">				new NPoint(new double[] { pp, qq }));</span>
<span class="nc" id="L48">		NRectRegion second = new NRectRegion(new NPoint(new double[] { pp, ymin }),</span>
<span class="nc" id="L49">				new NPoint(new double[] { xmax, qq }));</span>
<span class="nc" id="L50">		NRectRegion third = new NRectRegion(new NPoint(new double[] { pp, qq }),</span>
<span class="nc" id="L51">				new NPoint(new double[] { xmax, ymax }));</span>
<span class="nc" id="L52">		NRectRegion fourth = new NRectRegion(new NPoint(new double[] { xmin, qq }),</span>
<span class="nc" id="L53">				new NPoint(new double[] { pp, ymax }));</span>

<span class="nc" id="L55">		this.regions.add(first);</span>
<span class="nc" id="L56">		this.regions.add(second);</span>
<span class="nc" id="L57">		this.regions.add(third);</span>
<span class="nc" id="L58">		this.regions.add(fourth);</span>
<span class="nc" id="L59">	}</span>

	public void addRegionsInND(NRectRegion region, NPoint p) throws Exception {
<span class="nc" id="L62">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L63">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L64">		double[] pxn = p.getXn();</span>
<span class="nc" id="L65">		List&lt;List&lt;Double&gt;&gt; result1 = splitRegions(start, pxn);</span>
<span class="nc" id="L66">		List&lt;List&lt;Double&gt;&gt; result2 = splitRegions(pxn, end);</span>
<span class="nc bnc" id="L67" title="All 2 branches missed.">		if (result1.size() != result2.size()) {</span>
<span class="nc" id="L68">			throw new Exception(&quot;result1's size!=result2's size ,split region wrong&quot;);</span>
		}
<span class="nc bnc" id="L70" title="All 2 branches missed.">		for (int i = 0; i &lt; result1.size(); i++) {</span>
<span class="nc" id="L71">			List&lt;Double&gt; temp1 = result1.get(i);</span>
<span class="nc" id="L72">			List&lt;Double&gt; temp2 = result2.get(i);</span>
<span class="nc" id="L73">			double[] newStart = new double[temp1.size()];</span>
<span class="nc" id="L74">			double[] newEnd = new double[temp2.size()];</span>
<span class="nc bnc" id="L75" title="All 2 branches missed.">			for (int j = 0; j &lt; temp1.size(); j++) {</span>
<span class="nc" id="L76">				newStart[j] = temp1.get(j);</span>
<span class="nc" id="L77">				newEnd[j] = temp2.get(j);</span>
			}
<span class="nc" id="L79">			NRectRegion tempRegion = new NRectRegion(new NPoint(newStart), new NPoint(newEnd));</span>
<span class="nc" id="L80">			this.regions.add(tempRegion);</span>
		}
<span class="nc" id="L82">	}</span>

	public List&lt;List&lt;Double&gt;&gt; splitRegions(double[] start, double[] end) {
<span class="nc" id="L85">		ArrayList&lt;double[]&gt; values = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L86" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L87">			double[] temp = new double[2];</span>

<span class="nc" id="L89">			temp[0] = start[i];</span>
<span class="nc" id="L90">			temp[1] = end[i];</span>
<span class="nc" id="L91">			values.add(temp);</span>
		}

<span class="nc" id="L94">		ArrayList&lt;List&lt;Double&gt;&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L95">		PaiLie.per(values, 0, new ArrayList&lt;&gt;(), result);</span>
<span class="nc" id="L96">		return result;</span>
	}

	@Override
	public NPoint generateNextTC() {
<span class="nc" id="L101">		NPoint p = null;</span>
		//find max 
<span class="nc" id="L103">		NRectRegion maxregion = null;</span>
<span class="nc" id="L104">		int maxregion_index = 0;</span>
<span class="nc" id="L105">		double maxsize = 0;</span>
<span class="nc bnc" id="L106" title="All 2 branches missed.">		for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc" id="L107">			NRectRegion temp = regions.get(i);</span>
<span class="nc bnc" id="L108" title="All 2 branches missed.">			if (temp.size() &gt; maxsize) {</span>
<span class="nc" id="L109">				maxsize = temp.size();</span>
<span class="nc" id="L110">				maxregion = temp;</span>
<span class="nc" id="L111">				maxregion_index = i;</span>
			}
		}
<span class="nc" id="L114">		regions.remove(maxregion_index);</span>
<span class="nc" id="L115">		p = new NPoint();</span>
<span class="nc" id="L116">		p = randomCreator.randomPoint(maxregion);</span>
		try {
<span class="nc" id="L118">			addRegionsInND(maxregion, p);</span>
<span class="nc" id="L119">		} catch (Exception e) {</span>
<span class="nc" id="L120">			e.printStackTrace();</span>
		}
<span class="nc" id="L122">		return p;</span>
	}


	public void time() {
<span class="nc" id="L127">		int count = 0;</span>
		//regions.add(initRegion);
<span class="nc" id="L129">		NPoint p = randomCreator.randomPoint(initRegion);</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">		while (count &lt; tcCount) {</span>
<span class="nc" id="L131">			count++;</span>
			// 找出最大区域
<span class="nc" id="L133">			double maxsize = 0;</span>
<span class="nc" id="L134">			NRectRegion maxregion = null;</span>
<span class="nc" id="L135">			int maxregion_index = 0;</span>
<span class="nc bnc" id="L136" title="All 2 branches missed.">			for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc" id="L137">				NRectRegion temp = regions.get(i);</span>
<span class="nc bnc" id="L138" title="All 2 branches missed.">				if (temp.size() &gt; maxsize) {</span>
<span class="nc" id="L139">					maxsize = temp.size();</span>
<span class="nc" id="L140">					maxregion = temp;</span>
<span class="nc" id="L141">					maxregion_index = i;</span>
				}
			}
			//
<span class="nc" id="L145">			regions.remove(maxregion_index);</span>
			//// generate next one test case
<span class="nc" id="L147">			p = new NPoint();</span>
<span class="nc" id="L148">			p = randomCreator.randomPoint(maxregion);</span>
			// add 2^m 次方的
			try {
<span class="nc" id="L151">				addRegionsInND(maxregion, p);</span>
<span class="nc" id="L152">			} catch (Exception e) {</span>
<span class="nc" id="L153">				e.printStackTrace();</span>
			}
		}
<span class="nc" id="L156">	}</span>


	public static double testFm() {
<span class="nc" id="L160">		int d = 2;</span>
<span class="nc" id="L161">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L162">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L163">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L165">		int times = 2000;</span>

<span class="nc" id="L167">		int temp = 0;</span>
<span class="nc" id="L168">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L169">		failurePattern.fail_rate = 0.005;</span>
<span class="nc" id="L170">		long sums = 0;</span>
<span class="nc" id="L171">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L172" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L173">			ART_RP_ND rt = new ART_RP_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L174">			temp = rt.run();</span>
<span class="nc" id="L175">			sums += temp;</span>
		}
<span class="nc" id="L177">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L178">		double fm = sums / (double) times;</span>
<span class="nc" id="L179">		System.out.println(&quot;fm:&quot; + fm + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
<span class="nc" id="L180">		return fm;</span>
	}

	
	public static double[] testEm(int dimension, double failrate) {
<span class="nc" id="L185">		int d = dimension;</span>
<span class="nc" id="L186">		int emTime = 6;</span>
<span class="nc" id="L187">		double result[] = new double[emTime];</span>
<span class="nc" id="L188">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L189">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L190">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L192">		int times = 2000;</span>

<span class="nc" id="L194">		int temp = 0;</span>
<span class="nc" id="L195">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L196">		failurePattern.fail_rate = failrate;</span>
<span class="nc bnc" id="L197" title="All 2 branches missed.">		for (int k = 0; k &lt; emTime; k++) {</span>
<span class="nc" id="L198">			long sums = 0;</span>
<span class="nc" id="L199">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L200" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L201">				ART_RP_ND rt = new ART_RP_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L202">				rt.emCount = (k + 1) * 500;</span>
<span class="nc" id="L203">				temp = rt.em();</span>
<span class="nc" id="L204">				sums += temp;</span>
			}
<span class="nc" id="L206">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L207">			double em = sums / (double) times;</span>
<span class="nc" id="L208">			result[k] = em;</span>
<span class="nc" id="L209">			System.out.println(&quot;em:&quot; + em + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
		}
<span class="nc" id="L211">		System.out.println();</span>
<span class="nc" id="L212">		return result;</span>
	}

	public static double testTCTime(int d, int tcCount) {
<span class="nc" id="L216">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L217">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L218">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L220">		int times = 1;</span>

<span class="nc" id="L222">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L223">		failurePattern.fail_rate = 0.001;</span>
<span class="nc" id="L224">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L225" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L226">			ART_RP_ND rt = new ART_RP_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L227">			rt.tcCount = tcCount;</span>
<span class="nc" id="L228">			rt.time2();</span>
		}
<span class="nc" id="L230">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L231">		System.out.println((endTime - startTime) / (double) times);</span>
<span class="nc" id="L232">		return ((endTime - startTime) / (double) times);</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>