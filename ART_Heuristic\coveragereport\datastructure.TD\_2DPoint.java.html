<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>_2DPoint.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.TD</a> &gt; <span class="el_source">_2DPoint.java</span></div><h1>_2DPoint.java</h1><pre class="source lang-java linenums">package datastructure.TD;

/**
 * two dimension point
 */
public class _2DPoint {
	public double x;
	public double y;

<span class="nc" id="L10">	public _2DPoint() {</span>
<span class="nc" id="L11">	}</span>

<span class="nc" id="L13">	public _2DPoint(double x, double y) {</span>
<span class="nc" id="L14">		this.x = x;</span>
<span class="nc" id="L15">		this.y = y;</span>
<span class="nc" id="L16">	}</span>

	@Override
	public String toString() {
<span class="nc" id="L20">		return &quot;Point [x=&quot; + x + &quot;, y=&quot; + y + &quot;]&quot;;</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>