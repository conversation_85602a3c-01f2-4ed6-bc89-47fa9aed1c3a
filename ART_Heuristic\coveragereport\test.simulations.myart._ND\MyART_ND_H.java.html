<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MyART_ND_H.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.myart._ND</a> &gt; <span class="el_source">MyART_ND_H.java</span></div><h1>MyART_ND_H.java</h1><pre class="source lang-java linenums">package test.simulations.myart._ND;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import util.HilbertCurve2;

/**
 * 模拟实验，beta取固定的值 左曲线：
 * 
 */
public class MyART_ND_H {
<span class="nc" id="L14">	public static int ZUOQUXIAN = 1;</span>
<span class="nc" id="L15">	public static int YOUQUXIAN = 2;</span>
	public static void main(String[] args) throws Exception {
<span class="nc" id="L17">		int times = 9000;</span>
<span class="nc" id="L18">		long sums = 0;</span>
<span class="nc" id="L19">		int temp = 0;</span>
<span class="nc" id="L20">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L21" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L22">			MyART_ND_H ccrr = new MyART_ND_H(0, 1, 0.001, 10, 3, i * 3);</span>
<span class="nc" id="L23">			temp = ccrr.test();</span>
<span class="nc" id="L24">			sums += temp;</span>
		}
<span class="nc" id="L26">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L27">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L28">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L29">	}</span>
	double min;
	double max;
	int seed;
	double fail_rate;
	double fail_start[];
	double beta;
	int dimension;
<span class="nc" id="L37">	Random random = null;</span>

<span class="nc" id="L39">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L41">	public MyART_ND_H(double min, double max, double fail_rate, double beta, int dimension, int seed) {</span>
<span class="nc" id="L42">		this.min = min;</span>
<span class="nc" id="L43">		this.max = max;</span>
<span class="nc" id="L44">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L45">		this.seed = seed;</span>
<span class="nc" id="L46">		this.beta = beta;</span>
<span class="nc" id="L47">		this.dimension = dimension;</span>
<span class="nc" id="L48">		fail_start = new double[dimension];</span>
<span class="nc" id="L49">	}</span>

	public void genFail_start() {
<span class="nc" id="L52">		double fail_size = (max - min) * fail_rate;</span>
<span class="nc" id="L53">		fail_start = new double[dimension];</span>
<span class="nc bnc" id="L54" title="All 2 branches missed.">		for (int i = 0; i &lt; dimension; i++) {</span>
<span class="nc" id="L55">			fail_start[i] = random.nextDouble() * (max - min - Math.pow(fail_size, 1 / (double) dimension));</span>
		}
<span class="nc" id="L57">	}</span>

	public boolean isCorrect(double p) {
<span class="nc" id="L60">		double results[] = new HilbertCurve2().oneD_2_nD(p, dimension);</span>
<span class="nc" id="L61">		boolean flag = true;</span>
<span class="nc" id="L62">		double fail_size = (max - min) * fail_rate;</span>
<span class="nc bnc" id="L63" title="All 2 branches missed.">		for (int i = 0; i &lt; results.length; i++) {</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">			if (results[i] &lt; fail_start[i]</span>
<span class="nc bnc" id="L65" title="All 2 branches missed.">					|| results[i] &gt; (fail_start[i] + Math.pow(fail_size, 1.0 / (double) dimension))) {</span>
<span class="nc" id="L66">				flag = false;</span>
			}
		}
<span class="nc bnc" id="L69" title="All 2 branches missed.">		flag = !flag;</span>
<span class="nc" id="L70">		return flag;</span>
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L74">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L75" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L76">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L77" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L78">				low = mid + 1;</span>
<span class="nc" id="L79">			} else {</span>
<span class="nc" id="L80">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L83" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L84">			mid = mid - 1;</span>
		}
<span class="nc" id="L86">		tests.add(mid + 1, p);</span>
<span class="nc" id="L87">	}</span>

	/**
	 * @return F-measure
	 * @throws Exception
	 */
	public int test() {
<span class="nc" id="L94">		random = new Random(seed);</span>
		// double fail_size=(max-min)*(fail_rate);
		// fail_start = random.nextDouble() * (max-fail_size);
<span class="nc" id="L97">		genFail_start();</span>
<span class="nc" id="L98">		int count = 0;</span>
<span class="nc" id="L99">		TestCase p = new TestCase();</span>
		// 执行第一个测试用例
<span class="nc" id="L101">		p.p = random.nextDouble() * (max - min) + min;</span>
<span class="nc bnc" id="L102" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L103">			count++;</span>
<span class="nc bnc" id="L104" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L105">				tests.add(p);</span>
<span class="nc" id="L106">			} else</span>
<span class="nc" id="L107">				sortTestCases(p);</span>
			///
			double datum_line;// 基准线，待会求出
<span class="nc" id="L110">			double tempProbability = 0.0;</span>
<span class="nc" id="L111">			double sumProbability = 0.0;</span>
<span class="nc" id="L112">			ArrayList&lt;double[]&gt; integrals = new ArrayList&lt;&gt;();</span>
			/// 下面产生下一个测试用例,根据自己的概率曲线图
			// 先求第一段
<span class="nc" id="L115">			tempProbability = Math.pow((tests.get(0).p - min), 2.0) / (beta + 1.0);</span>
<span class="nc" id="L116">			sumProbability += tempProbability;</span>
<span class="nc" id="L117">			double[] informations = new double[5];</span>
<span class="nc" id="L118">			informations[0] = tempProbability;</span>
<span class="nc" id="L119">			informations[1] = ZUOQUXIAN;</span>
<span class="nc" id="L120">			informations[2] = min;</span>
<span class="nc" id="L121">			informations[3] = tests.get(0).p;</span>
<span class="nc" id="L122">			informations[4] = beta;</span>
<span class="nc" id="L123">			integrals.add(informations);</span>
			// 求中间一段的积分值
<span class="nc bnc" id="L125" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size() - 1; i++) {</span>
				// 右边
<span class="nc" id="L127">				tempProbability = Math.pow(((tests.get(i + 1).p - tests.get(i).p) / 2.0), 2.0) * (1.0 / (beta + 1.0));</span>
<span class="nc" id="L128">				sumProbability += tempProbability;</span>
<span class="nc" id="L129">				informations = new double[5];</span>
<span class="nc" id="L130">				informations[0] = tempProbability;</span>
<span class="nc" id="L131">				informations[1] = YOUQUXIAN;</span>
<span class="nc" id="L132">				informations[2] = tests.get(i).p;</span>
<span class="nc" id="L133">				informations[3] = (tests.get(i + 1).p + tests.get(i).p) / 2.0;</span>
<span class="nc" id="L134">				informations[4] = beta;</span>
<span class="nc" id="L135">				integrals.add(informations);</span>
				// 下一个点的左边
<span class="nc" id="L137">				tempProbability = Math.pow(((tests.get(i + 1).p - tests.get(i).p) / 2.0), 2.0) * (1.0 / (beta + 1.0));</span>
<span class="nc" id="L138">				sumProbability += tempProbability;</span>
<span class="nc" id="L139">				informations = new double[5];</span>
<span class="nc" id="L140">				informations[0] = tempProbability;</span>
<span class="nc" id="L141">				informations[1] = ZUOQUXIAN;</span>
<span class="nc" id="L142">				informations[2] = (tests.get(i + 1).p + tests.get(i).p) / 2.0;</span>
<span class="nc" id="L143">				informations[3] = tests.get(i + 1).p;</span>
<span class="nc" id="L144">				informations[4] = beta;</span>
<span class="nc" id="L145">				integrals.add(informations);</span>
			}
<span class="nc" id="L147">			tempProbability = Math.pow((max - tests.get(tests.size() - 1).p), 2.0) / (beta + 1.0);</span>
<span class="nc" id="L148">			sumProbability += tempProbability;</span>
<span class="nc" id="L149">			informations = new double[5];</span>
<span class="nc" id="L150">			informations[0] = tempProbability;</span>
<span class="nc" id="L151">			informations[1] = YOUQUXIAN;</span>
<span class="nc" id="L152">			informations[2] = tests.get(tests.size() - 1).p;</span>
<span class="nc" id="L153">			informations[3] = max;</span>
<span class="nc" id="L154">			informations[4] = beta;</span>
<span class="nc" id="L155">			integrals.add(informations);</span>
<span class="nc" id="L156">			datum_line = 1.0 / sumProbability;</span>
<span class="nc" id="L157">			double T = random.nextDouble() * 1.0;</span>
			// 确定在哪一段，然后求解出下一个点
<span class="nc" id="L159">			double SumIntegral = 0.0;</span>
<span class="nc" id="L160">			double PreIntegral = 0.0;</span>
<span class="nc" id="L161">			int temp = 0;</span>
			// 这里有问题，temp的值不一定就是前面一个的temp,理解错了，其实没有错
<span class="nc bnc" id="L163" title="All 2 branches missed.">			for (int i = 0; i &lt; integrals.size(); i++) {</span>
<span class="nc bnc" id="L164" title="All 2 branches missed.">				if (SumIntegral &lt; T) {</span>
<span class="nc" id="L165">					PreIntegral = SumIntegral;</span>
<span class="nc" id="L166">					temp = i;</span>
				}
<span class="nc" id="L168">				SumIntegral += integrals.get(i)[0] * datum_line;</span>
			}
			// draw picture
			// double drawtemp[]=integrals.get(0);

			// 求下一个测试用例
<span class="nc" id="L174">			int type = (int) integrals.get(temp)[1];</span>
<span class="nc" id="L175">			double start = integrals.get(temp)[2];</span>
<span class="nc" id="L176">			double end = integrals.get(temp)[3];</span>
<span class="nc bnc" id="L177" title="All 2 branches missed.">			if (type == ZUOQUXIAN) {</span>
<span class="nc" id="L178">				double temp1 = end - start;</span>
<span class="nc" id="L179">				double temp2 = integrals.get(temp)[4] + 1.0;</span>
<span class="nc" id="L180">				p = new TestCase();</span>
<span class="nc" id="L181">				double temp3 = (1.0 - (T - PreIntegral) * (temp2) / ((datum_line) * (Math.pow(temp1, 2.0))));</span>
<span class="nc" id="L182">				p.p = end - temp1 * Math.pow(temp3, (1.0 / temp2));</span>
<span class="nc" id="L183">			} else {</span>
<span class="nc" id="L184">				double temp1 = end - start;</span>
				// 这里也是错误的-&gt;double temp2=p.coverage+1.0
<span class="nc" id="L186">				double temp2 = integrals.get(temp)[4] + 1.0;</span>
<span class="nc" id="L187">				p = new TestCase();</span>
<span class="nc" id="L188">				p.p = start + temp1</span>
<span class="nc" id="L189">						* Math.pow((T - PreIntegral) * temp2 / (datum_line * Math.pow(temp1, 2.0)), (1.0 / temp2));</span>
			}
<span class="nc bnc" id="L191" title="All 6 branches missed.">			if (Double.isNaN(p.p) || p.p &lt; min || p.p &gt; max) {</span>
<span class="nc" id="L192">				System.out.println(&quot;Interrupt!!&quot;);</span>
			}
		}

<span class="nc" id="L196">		return count;</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>