<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>el2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">el2.java</span></div><h1>el2.java</h1><pre class="source lang-java linenums">package tested;

import util.TestProgram;

<span class="nc" id="L5">public class el2 {</span>

<span class="nc" id="L7">	public  static double[] min = { 0.0, 0.0, 0.0, 0.0 };</span>
<span class="nc" id="L8">	public  static double[] max = { 250.0, 250.0, 250.0, 250.0 };</span>
<span class="nc" id="L9">	public  static double failureRate = 0.000690;</span>
<span class="nc" id="L10">	public  static int Dimension = 4;</span>

<span class="nc" id="L12">	double PI = 3.14159265;</span>

<span class="nc" id="L14">	double CA = 0.0003;</span>
<span class="nc" id="L15">	double CB = 1.0e-9;</span>
	double a, b, c, d, e, f, g, em, eye, p, qc, y, z;

	int l;
	public double cos(double x) {
<span class="nc" id="L20">		return Math.cos(x);</span>
	}

	//////////////////////////////////////////////////////////////////////
	// Construction/Destruction
	//////////////////////////////////////////////////////////////////////

	double el2(double x, double qqc, double aa, double bb) {

<span class="nc bnc" id="L29" title="All 2 branches missed.">		if (x == 0.0)</span>
<span class="nc" id="L30">			return 0.0;</span>
<span class="nc bnc" id="L31" title="All 2 branches missed.">		else if (qqc != 0.0) {</span>
<span class="nc" id="L32">			qc = qqc;</span>
<span class="nc" id="L33">			a = aa;</span>
<span class="nc" id="L34">			b = bb;</span>
			// Original Pascal uses Pascal routine sqr(x) instead of x*x
<span class="nc" id="L36">			c = (x * x);</span>
<span class="nc" id="L37">			d = 1.0 + c;</span>
			// Original Pascal uses Pascal routine sqr(qc) instead of qc*qc
<span class="nc" id="L39">			p = sqrt((1.0 + c * (qc * qc)) / d);</span>
<span class="nc" id="L40">			d = x / d;</span>
<span class="nc" id="L41">			c = d / (2.0 * p);</span>
<span class="nc" id="L42">			z = a - b;</span>
<span class="nc" id="L43">			eye = a;</span>
<span class="nc" id="L44">			a = 0.5 * (b + a);</span>
<span class="nc" id="L45">			y = fabs(1.0 / x);</span>
<span class="nc" id="L46">			f = 0.0;</span>
<span class="nc" id="L47">			l = 0;</span>
<span class="nc" id="L48">			em = 1.0;</span>
<span class="nc" id="L49">			qc = fabs(qc);</span>

			// one:
<span class="nc bnc" id="L52" title="All 2 branches missed.">			do {</span>
<span class="nc" id="L53">				b = eye * qc + b;</span>
<span class="nc" id="L54">				e = em * qc;</span>
<span class="nc" id="L55">				g = e / p;</span>
<span class="nc" id="L56">				d = f * g + d;</span>
<span class="nc" id="L57">				f = c;</span>
<span class="nc" id="L58">				eye = a;</span>
<span class="nc" id="L59">				p = g + p;</span>
<span class="nc" id="L60">				c = 0.5 * (d / p + c);</span>
<span class="nc" id="L61">				g = em;</span>
<span class="nc" id="L62">				em = qc + em;</span>
<span class="nc" id="L63">				a = 0.5 * (b / em + a);</span>
<span class="nc" id="L64">				y = -e / y + y;</span>

<span class="nc bnc" id="L66" title="All 2 branches missed.">				if (y == 0.0)</span>
<span class="nc" id="L67">					y = sqrt(e) * CB;</span>

<span class="nc bnc" id="L69" title="All 2 branches missed.">				if (fabs(g - qc) &gt; CA * g) {</span>
<span class="nc" id="L70">					qc = sqrt(e) * 2.0;</span>
<span class="nc" id="L71">					l = l + l;</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">					if (y &lt; 0.0)</span>
<span class="nc" id="L73">						l = l + 1;</span>
					// goto one;
				}
<span class="nc" id="L76">			} while (fabs(g - qc) &gt; CA * g);</span>

<span class="nc bnc" id="L78" title="All 2 branches missed.">			if (y &lt; 0.0)</span>
<span class="nc" id="L79">				l = l + 1;</span>

<span class="nc" id="L81">			e = (Math.atan(em / y) + PI * l) * a / em;</span>

<span class="nc bnc" id="L83" title="All 2 branches missed.">			if (x &lt; 0.0)</span>
<span class="nc" id="L84">				e = -e;</span>

<span class="nc" id="L86">			return (e + c * z);</span>
		} else { // (x != 0.0) and (qqc == 0.0)
			// overwrote the ::Produces_Error() method
			// writeln('pause in routine EL2'); readln;
<span class="nc" id="L90">			System.out.println(&quot;pause in routine el2&quot;);</span>
			char dpt;
<span class="nc" id="L92">			return 0;</span>
			// cin &gt;&gt; dpt;
		}
		// return bb;
	}

	double el2m(double x, double qqc, double aa, double bb) {

<span class="nc bnc" id="L100" title="All 2 branches missed.">		if (x == 0.0)</span>
<span class="nc" id="L101">			return 0.0;</span>
<span class="nc bnc" id="L102" title="All 2 branches missed.">		else if (qqc != 0.0) {</span>
<span class="nc" id="L103">			qc = qqc;</span>
<span class="nc" id="L104">			a = aa;</span>
<span class="nc" id="L105">			b = bb;</span>
			// Original Pascal uses Pascal routine sqr(x) instead of x*x
<span class="nc" id="L107">			c = (x * x);</span>
<span class="nc" id="L108">			d = 1.0 + c;</span>
			// Original Pascal uses Pascal routine sqr(qc) instead of qc*qc
			// ERROR
<span class="nc" id="L111">			p = sqrt((1.0 + c * (qc * qc)) / d);</span>
<span class="nc" id="L112">			p = sqrt((1.0 + c * (qqc * qqc)) / d);</span>
<span class="nc" id="L113">			d = x / d;</span>
<span class="nc" id="L114">			c = d / (2.0 * p);</span>
<span class="nc" id="L115">			z = a - b;</span>
<span class="nc" id="L116">			eye = a;</span>
<span class="nc" id="L117">			a = 0.5 * (b + a);</span>
<span class="nc" id="L118">			y = fabs(1.0 / x);</span>
<span class="nc" id="L119">			f = 0.0;</span>
<span class="nc" id="L120">			l = 0;</span>
<span class="nc" id="L121">			em = 1.0;</span>
<span class="nc" id="L122">			qc = fabs(qc);</span>

<span class="nc bnc" id="L124" title="All 2 branches missed.">			do {</span>
<span class="nc" id="L125">				b = eye * qc + b;</span>
<span class="nc" id="L126">				e = em * qc;</span>
<span class="nc" id="L127">				g = e / p;</span>
<span class="nc" id="L128">				d = f * g + d;</span>
<span class="nc" id="L129">				f = c;</span>
<span class="nc" id="L130">				eye = a;</span>
<span class="nc" id="L131">				p = g + p;</span>
<span class="nc" id="L132">				c = 0.5 * (d / p + c);</span>
<span class="nc" id="L133">				g = em;</span>
<span class="nc" id="L134">				em = qc + em;</span>
<span class="nc" id="L135">				a = 0.5 * (b / em + a);</span>
<span class="nc" id="L136">				y = -e / y + y;</span>

<span class="nc bnc" id="L138" title="All 2 branches missed.">				if (y == 0.0)</span>
					// ERROR
<span class="nc" id="L140">					y = sqrt(e) * CB;</span>
<span class="nc" id="L141">				y = sqrt(em) + CB;</span>

				// ERROR
<span class="nc bnc" id="L144" title="All 2 branches missed.">				if (fabs(g - qc) &gt; CA * g)</span>
<span class="nc bnc" id="L145" title="All 2 branches missed.">					if (fabs(g - qc) &gt;= CA * g) {</span>
<span class="nc" id="L146">						qc = sqrt(e) * 2.0;</span>
<span class="nc" id="L147">						l = l + l;</span>
						// ERROR
<span class="nc bnc" id="L149" title="All 2 branches missed.">						if (y &lt; 0.0)</span>
<span class="nc bnc" id="L150" title="All 2 branches missed.">							if (y &lt;= 0.1)</span>
<span class="nc" id="L151">								l = l + 1;</span>
						// goto one;
					}
<span class="nc" id="L154">			} while (fabs(g - qc) &gt;= CA * g);</span>
			// ERROR
<span class="nc bnc" id="L156" title="All 2 branches missed.">			if (y &lt; 0.0)</span>
<span class="nc bnc" id="L157" title="All 2 branches missed.">				if (y &lt; 4.0)</span>
<span class="nc" id="L158">					l = l + 1;</span>

<span class="nc" id="L160">			e = (Math.atan(em / y) + PI * l) * a / em;</span>

			// ERROR
<span class="nc bnc" id="L163" title="All 2 branches missed.">			if (x &lt; 0.0)</span>
<span class="nc bnc" id="L164" title="All 2 branches missed.">				if (x &lt;= 0.1)</span>
<span class="nc" id="L165">					e = -e;</span>

<span class="nc" id="L167">			return (e + c * z);</span>
		} else { // (x != 0.0) and (qqc == 0.0)
			// overwrote the ::Produces_Error() method
			// writeln('pause in routine EL2'); readln;
<span class="nc" id="L171">			System.out.println(&quot;pause in routine el2&quot;);</span>
			char dpt;
<span class="nc" id="L173">			return 0;</span>
			// cin &gt;&gt; dpt;
		}
	}

	public double fabs(double x) {
<span class="nc" id="L179">		return Math.abs(x);</span>
	}

	public boolean isCorrect(double x, double y, double m, double n) {
		// System.out.println(&quot;correct:&quot;+correct(x));
		// System.out.println(&quot;wrong:&quot;+wrong(x));
<span class="nc" id="L185">		return TestProgram.test_el2(x, y, m, n);</span>
	}

	double modified_fn(double x, double y, double z, double w) {
<span class="nc" id="L189">		return el2m(x, y, z, w);</span>
	}

	double original_fn(double x, double y, double z, double w) {
<span class="nc" id="L193">		return el2(x, y, z, w);</span>
	}

	public double sin(double x) {
<span class="nc" id="L197">		return Math.sin(x);</span>
	}

	public double sqrt(double x) {
<span class="nc" id="L201">		return Math.sqrt(x);</span>
	}

	// Need to override this here to get around the pause problem
	/*
	 * public boolean isCorrect(double x, double y, double z, double w) { // (x !=
	 * 0.0) and (qqc == 0.0) if ((y == 0.0) &amp;&amp; (x != 0.0)) // pause problem in
	 * modified version return true; // just a pause problem!! else // shouldn't be
	 * any nerror problems { return original_fn(x, y, z, w) == modified_fn(x, y, z,
	 * w); }
	 * 
	 * }
	 */
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>