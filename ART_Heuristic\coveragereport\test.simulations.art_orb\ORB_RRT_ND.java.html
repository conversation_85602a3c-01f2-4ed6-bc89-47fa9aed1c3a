<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ORB_RRT_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_orb</a> &gt; <span class="el_source">ORB_RRT_ND.java</span></div><h1>ORB_RRT_ND.java</h1><pre class="source lang-java linenums">package test.simulations.art_orb;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import util.data.ZeroOneCreator;


/**
 * Hilary's method
 * */
public class ORB_RRT_ND extends ART {
	public static void main(String[] args) {
<span class="nc" id="L20">		int dimension = 3;</span>
<span class="nc" id="L21">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L22">		double[] min = dataCreator.minCreator(dimension);</span>
<span class="nc" id="L23">		double[] max = dataCreator.maxCreator(dimension);</span>
<span class="nc" id="L24">		int times = 3000;</span>
<span class="nc" id="L25">		int fm = 0;</span>
<span class="nc bnc" id="L26" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
			// FailurePattern pattern=new BlockPattern();
<span class="nc" id="L28">			FailurePattern pattern = new BlockPattern();</span>
<span class="nc" id="L29">			pattern.fail_rate = 0.01;</span>

<span class="nc" id="L31">			ORB_RRT_ND test = new ORB_RRT_ND(min, max, 0.75, pattern, new Random(i * 5 + 3));</span>
<span class="nc" id="L32">			int temp = test.run();</span>
<span class="nc" id="L33">			fm += temp;</span>
		}
<span class="nc" id="L35">		System.out.println(fm / (double) times);</span>
<span class="nc" id="L36">	}</span>
	double R;

<span class="nc" id="L39">	ArrayList&lt;ComplexRegion&gt; regions = new ArrayList&lt;&gt;();</span>

	public ORB_RRT_ND(double[] min, double[] max, double R, FailurePattern failurePattern, Random random) {
<span class="nc" id="L42">		super(min, max, random, failurePattern);</span>
<span class="nc" id="L43">		this.R = R;</span>
<span class="nc" id="L44">	}</span>

	public double calculateRadius(NRectRegion region) {
		// 二维的
<span class="nc" id="L48">		return Math.sqrt(R * region.size() / (Math.PI));</span>
	}

	public int findMaxRegion() {
<span class="nc" id="L52">		double maxSize = -1;</span>
<span class="nc" id="L53">		int index = 0;</span>
<span class="nc bnc" id="L54" title="All 2 branches missed.">		for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc bnc" id="L55" title="All 2 branches missed.">			if (regions.get(i).region.size() &gt; maxSize) {</span>
<span class="nc" id="L56">				maxSize = regions.get(i).region.size();</span>
<span class="nc" id="L57">				index = i;</span>
			}
		}
<span class="nc" id="L60">		return index;</span>
	}

	public boolean isPointInRegion(NRectRegion region, NPoint p) {
<span class="nc" id="L64">		boolean flag = true;</span>
<span class="nc bnc" id="L65" title="All 2 branches missed.">		for (int i = 0; i &lt; p.getXn().length; i++) {</span>
<span class="nc bnc" id="L66" title="All 4 branches missed.">			if (p.getXn()[i] &lt; region.getStart().getXn()[i] || p.getXn()[i] &gt; region.getEnd().getXn()[i]) {</span>
<span class="nc" id="L67">				flag = false;</span>
			}
		}
<span class="nc" id="L70">		return flag;</span>
	}

	public NPoint randomTC(ComplexRegion region) {
<span class="nc" id="L74">		double radius = calculateRadius(region.region);</span>
<span class="nc" id="L75">		NPoint result = randomCreator.randomPoint(region.region);</span>
<span class="nc" id="L76">		boolean flag = true;</span>
<span class="nc bnc" id="L77" title="All 2 branches missed.">		while (flag) {</span>
<span class="nc" id="L78">			flag = false;</span>
<span class="nc" id="L79">			result = randomCreator.randomPoint(region.region);</span>

			// 排除区域是圆
			// 计算距离
<span class="nc" id="L83">			double[] tested = region.pointInRegion.getXn();</span>
<span class="nc" id="L84">			double distance = 0;</span>
<span class="nc" id="L85">			double[] untested = result.getXn();</span>
<span class="nc bnc" id="L86" title="All 2 branches missed.">			for (int j = 0; j &lt; this.dimension; j++) {</span>
<span class="nc" id="L87">				distance += Math.pow((tested[j] - untested[j]), 2);</span>
			}
<span class="nc" id="L89">			distance = Math.sqrt(distance);</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">			if (distance &lt; radius) {</span>
<span class="nc" id="L91">				flag = true;</span>
				// break;
			}
			/*
			 * //排除区域是正方形 if(Math.abs(p.p-tests.get(i).p)&lt;radius){
			 * if(Math.abs(p.q-tests.get(i).q)&lt;radius){ flag=true; } }
			 */
		}
<span class="nc" id="L99">		return result;</span>
	}

	public int run() {
<span class="nc" id="L103">		int count = 0;</span>

		// first
<span class="nc" id="L106">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc" id="L107">		ComplexRegion region = new ComplexRegion();</span>
<span class="nc" id="L108">		region.region = new NRectRegion(new NPoint(min), new NPoint(max));</span>
<span class="nc" id="L109">		region.pointInRegion = p;</span>
<span class="nc" id="L110">		regions.add(region);</span>
<span class="nc bnc" id="L111" title="All 2 branches missed.">		if (!failPattern.isCorrect(p)) {</span>
<span class="nc" id="L112">			return 1;</span>
		}
<span class="nc" id="L114">		count++;</span>

		// second
<span class="nc" id="L117">		NPoint t2 = randomCreator.randomPoint();</span>
<span class="nc" id="L118">		splitRegion(p, t2, regions.get(0));</span>
<span class="nc" id="L119">		regions.remove(0);</span>

<span class="nc bnc" id="L121" title="All 2 branches missed.">		while (failPattern.isCorrect(t2)) {</span>
<span class="nc" id="L122">			count++;</span>

			// 再生成一个测试用例t2
<span class="nc" id="L125">			int index = findMaxRegion();</span>
<span class="nc" id="L126">			ComplexRegion maxRegion = regions.get(index);</span>
<span class="nc" id="L127">			t2 = randomTC(maxRegion);</span>

<span class="nc bnc" id="L129" title="All 2 branches missed.">			if (!failPattern.isCorrect(t2)) {</span>
<span class="nc" id="L130">				break;</span>
			}
			// split region
<span class="nc" id="L133">			regions.remove(index);</span>
<span class="nc" id="L134">			splitRegion(maxRegion.pointInRegion, t2, maxRegion);</span>
		}
<span class="nc" id="L136">		return count;</span>
	}

	public void splitRegion(NPoint p, NPoint t2, ComplexRegion region) {
<span class="nc" id="L140">		double[] pn = p.getXn();</span>
<span class="nc" id="L141">		double[] t2n = t2.getXn();</span>

<span class="nc" id="L143">		double[] mid = new double[pn.length];</span>
<span class="nc bnc" id="L144" title="All 2 branches missed.">		for (int i = 0; i &lt; mid.length; i++) {</span>
<span class="nc" id="L145">			mid[i] = (pn[i] + t2n[i]) / 2.0;</span>
		}
		// System.out.println(&quot;middle point:&quot;+Arrays.toString(mid));
		// 找到最大的边
<span class="nc" id="L149">		double maxBian = 0.0;</span>
<span class="nc" id="L150">		int maxIndex = 0;</span>
<span class="nc bnc" id="L151" title="All 2 branches missed.">		for (int i = 0; i &lt; region.region.getStart().getXn().length; i++) {</span>
<span class="nc bnc" id="L152" title="All 2 branches missed.">			if (region.region.getEnd().getXn()[i] - region.region.getStart().getXn()[i] &gt; maxBian) {</span>
<span class="nc" id="L153">				maxBian = region.region.getEnd().getXn()[i] - region.region.getStart().getXn()[i];</span>
<span class="nc" id="L154">				maxIndex = i;</span>
			}
		}

		// 一分为二
<span class="nc" id="L159">		NRectRegion region1 = new NRectRegion();</span>
<span class="nc" id="L160">		NRectRegion region2 = new NRectRegion();</span>

<span class="nc" id="L162">		region1.setStart(region.region.getStart());</span>
<span class="nc" id="L163">		double[] end = Arrays.copyOf(region.region.getEnd().getXn(), region.region.getEnd().getXn().length);</span>
<span class="nc" id="L164">		end[maxIndex] = mid[maxIndex];</span>
<span class="nc" id="L165">		region1.setEnd(new NPoint(end));</span>

<span class="nc" id="L167">		double[] start = Arrays.copyOf(region.region.getStart().getXn(), region.region.getStart().getXn().length);</span>
<span class="nc" id="L168">		start[maxIndex] = mid[maxIndex];</span>
<span class="nc" id="L169">		region2.setStart(new NPoint(start));</span>
<span class="nc" id="L170">		region2.setEnd(region.region.getEnd());</span>

		//// 二维
		// if (maxIndex == 0) {// x轴长
		// region1.setStart(region.region.getStart());
		// region1.setEnd(new NPoint(new double[] { mid[0],
		// region.region.getEnd().getXn()[1] }));
		//
		// region2.setStart(new NPoint(new double[] { mid[0],
		// region.region.getStart().getXn()[1] }));
		// region2.setEnd(region.region.getEnd());
		// } else if (maxIndex == 1) {// y轴长
		// region1.setStart(region.region.getStart());
		// region1.setEnd(new NPoint(new double[] { region.region.getEnd().getXn()[0],
		// mid[1] }));
		//
		// region2.setStart(new NPoint(new double[] {
		// region.region.getStart().getXn()[0], mid[1] }));
		// region2.setEnd(region.region.getEnd());
		// }
<span class="nc" id="L190">		ComplexRegion cr1 = new ComplexRegion();</span>
<span class="nc" id="L191">		cr1.region = region1;</span>
<span class="nc bnc" id="L192" title="All 2 branches missed.">		if (isPointInRegion(region1, p)) {</span>
<span class="nc" id="L193">			cr1.pointInRegion = p;</span>
<span class="nc" id="L194">		} else {</span>
<span class="nc" id="L195">			cr1.pointInRegion = t2;</span>
		}
<span class="nc" id="L197">		ComplexRegion cr2 = new ComplexRegion();</span>
<span class="nc" id="L198">		cr2.region = region2;</span>
<span class="nc bnc" id="L199" title="All 2 branches missed.">		if (isPointInRegion(region2, t2)) {</span>
<span class="nc" id="L200">			cr2.pointInRegion = t2;</span>
<span class="nc" id="L201">		} else {</span>
<span class="nc" id="L202">			cr2.pointInRegion = p;</span>
		}
<span class="nc" id="L204">		regions.add(cr1);</span>
<span class="nc" id="L205">		regions.add(cr2);</span>
<span class="nc" id="L206">	}</span>

	@Override
	public int em() {
		// TODO Auto-generated method stub
<span class="nc" id="L211">		return 0;</span>
	}

	@Override
	public NPoint generateNextTC() {
		// TODO Auto-generated method stub
<span class="nc" id="L217">		return null;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>