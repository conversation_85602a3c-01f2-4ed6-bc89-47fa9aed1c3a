<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MultiBlockPattern.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.failurepattern.impl</a> &gt; <span class="el_source">MultiBlockPattern.java</span></div><h1>MultiBlockPattern.java</h1><pre class="source lang-java linenums">package datastructure.failurepattern.impl;

import java.util.ArrayList;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;

<span class="nc" id="L8">public class MultiBlockPattern extends FailurePattern {</span>
	private double eachFailLength;
	private double fail_regionS;
	private ArrayList&lt;double[]&gt; fail_start;

<span class="nc" id="L13">	private int failBlockCount = 5;;</span>

	@Override
	public void genFailurePattern() {
<span class="nc" id="L17">		fail_start = new ArrayList&lt;&gt;(this.failBlockCount);</span>
		;
<span class="nc" id="L19">		double totalArea = 1.0;</span>
<span class="nc bnc" id="L20" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc" id="L21">			totalArea *= (max[i] - min[i]);</span>
		}
<span class="nc" id="L23">		this.fail_regionS = this.fail_rate * totalArea;</span>
<span class="nc" id="L24">		this.eachFailLength = Math.pow(fail_regionS, 1 / (double) this.dimension);</span>
<span class="nc" id="L25">		this.eachFailLength = this.eachFailLength / (double) failBlockCount;</span>
		// System.out.println(&quot;EachFailArea:&quot;+this.EachFailLength);
<span class="nc bnc" id="L27" title="All 2 branches missed.">		for (int j = 0; j &lt; fail_start.size(); j++) {</span>

<span class="nc" id="L29">			double[] temp=new double[this.dimension];</span>
<span class="nc bnc" id="L30" title="All 2 branches missed.">			for (int i = 0; i &lt; this.dimension; i++) {</span>
				// double start=
<span class="nc" id="L32">				temp[i] = random.nextDouble() * (max[i] - min[i] - eachFailLength) + min[i];</span>
			}
<span class="nc" id="L34">			fail_start.add(temp);</span>
		}
<span class="nc" id="L36">	}</span>

	@Override
	public boolean isCorrect(NPoint p) {
<span class="nc" id="L40">		double[] xn = p.getXn();</span>
<span class="nc" id="L41">		boolean lead2Fail = false;</span>
<span class="nc bnc" id="L42" title="All 2 branches missed.">		for(int j=0;j&lt;fail_start.size();j++){</span>
<span class="nc bnc" id="L43" title="All 2 branches missed.">		for (int i = 0; i &lt; this.dimension; i++) {</span>
<span class="nc bnc" id="L44" title="All 4 branches missed.">			if (xn[i] &lt; this.fail_start.get(j)[i] || xn[i] &gt; (this.fail_start.get(j)[i] + eachFailLength)) {</span>
<span class="nc" id="L45">				lead2Fail = true;</span>
			}
		}
		}
		// System.out.println(Arrays.toString(nDPoints));
		// System.out.println(&quot;isFail:&quot;+lead2Fail);
		// lead2Fail=false,失效，=true不失效
<span class="nc" id="L52">		return lead2Fail;</span>
	}

	@Override
	public void showFailurePattern() {
		// print block pattern
//		System.out.println(&quot;Pattern type:&quot; + getClass().getSimpleName() + &quot; Dimension:&quot; + this.dimension);
//		System.out.print(&quot;[&quot;);
//		for (int i = 0; i &lt; this.dimension; i++) {
//
//			System.out.print(&quot;(&quot; + fail_start[i] + &quot;,&quot; + (fail_start[i] + eachFailLength) + &quot;)&quot;);
//
//			if (i != this.dimension - 1) {
//				System.out.print(&quot;,&quot;);
//			}
//			if ((i + 1) % 5 == 0) {
//				System.out.println();
//			}
//		}
//		System.out.print(&quot;]&quot;);
//		System.out.println();
<span class="nc" id="L73">	}</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>