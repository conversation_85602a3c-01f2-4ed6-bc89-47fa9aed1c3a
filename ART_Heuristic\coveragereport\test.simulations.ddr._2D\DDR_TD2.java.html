<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>DDR_TD2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.ddr._2D</a> &gt; <span class="el_source">DDR_TD2.java</span></div><h1>DDR_TD2.java</h1><pre class="source lang-java linenums">package test.simulations.ddr._2D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class DDR_TD2 {
	public static void main(String[] args) {
<span class="nc" id="L10">		int times = 100;</span>
<span class="nc" id="L11">		long sums = 0;</span>
<span class="nc" id="L12">		int temp = 0;</span>
<span class="nc" id="L13">		int s = 10;</span>
		//////////////
<span class="nc" id="L15">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L16" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L17">			double min[] = { 0.0, 0.0 };</span>
<span class="nc" id="L18">			double max[] = { 1.0, 1.0 };</span>
<span class="nc" id="L19">			DDR_TD2 rrt_od = new DDR_TD2(min, max, 0.75, s, 0.001, i * 3);// 0.002 refers to failure rate</span>
<span class="nc" id="L20">			temp = rrt_od.run();</span>
<span class="nc" id="L21">			sums += temp;</span>
		}
<span class="nc" id="L23">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L24">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L25">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L26">	}</span>
	double[] min;
	double[] max;
	double[] fail_start;
	double fail_rate;
	double R;
	int s;
	int randomseed;
	double fail_regionS;

<span class="nc" id="L36">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	public DDR_TD2(double[] min, double[] max, double r, int s, double fail_rate, int randomseed) {
<span class="nc" id="L39">		super();</span>
<span class="nc" id="L40">		this.min = min;</span>
<span class="nc" id="L41">		this.max = max;</span>
<span class="nc" id="L42">		R = r;</span>
<span class="nc" id="L43">		this.s = s;</span>
<span class="nc" id="L44">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L45">		this.randomseed = randomseed;</span>
<span class="nc" id="L46">		this.fail_start = new double[this.min.length];</span>
<span class="nc" id="L47">		this.fail_regionS = fail_rate * (max[1] - min[1]) * (max[0] - min[0]);</span>
<span class="nc" id="L48">	}</span>

	public boolean isCorrect(TestCase p) {
<span class="nc" id="L51">		boolean isCorrect = true;</span>
<span class="nc bnc" id="L52" title="All 4 branches missed.">		if (p.p &gt; fail_start[0] &amp;&amp; p.p &lt; fail_start[0] + Math.sqrt(fail_regionS)) {</span>
<span class="nc bnc" id="L53" title="All 4 branches missed.">			if (p.q &gt; fail_start[1] &amp;&amp; p.q &lt; fail_start[1] + Math.sqrt(fail_regionS)) {</span>
<span class="nc" id="L54">				isCorrect = false;</span>
			}
		}
<span class="nc" id="L57">		return isCorrect;</span>
	}

	public TestCase randomTC(Random random) {
<span class="nc" id="L61">		TestCase temp = new TestCase();</span>
<span class="nc" id="L62">		double p = random.nextDouble() * (max[0] - min[0]) + min[0];</span>
<span class="nc" id="L63">		double q = random.nextDouble() * (max[1] - min[1]) + min[1];</span>
<span class="nc" id="L64">		temp.p = p;</span>
<span class="nc" id="L65">		temp.q = q;</span>
<span class="nc" id="L66">		return temp;</span>
	}

	public int run() {
<span class="nc" id="L70">		Random random = new Random(randomseed);</span>
		// 失效率的范围
<span class="nc" id="L72">		fail_start[0] = random.nextDouble() * (max[0] - min[0] - Math.sqrt(fail_regionS)) + min[0];</span>
<span class="nc" id="L73">		fail_start[1] = random.nextDouble() * (max[1] - min[1] - Math.sqrt(fail_regionS)) + min[1];</span>
<span class="nc" id="L74">		System.out.println(&quot;fail_rate:(&quot; + fail_start[0] + &quot;,&quot; + fail_start[1] + &quot;)&quot;);</span>
<span class="nc" id="L75">		int count = 0;// 记录测试用例数量</span>
<span class="nc" id="L76">		int _10CandidateCount = s;// 每十个一次的数量</span>
<span class="nc" id="L77">		TestCase p = randomTC(random);// 第一个测试用例</span>
<span class="nc" id="L78">		System.out.println(&quot;p0:&quot; + p.toString() + isCorrect(p));</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">		while (isCorrect(p)) {</span>
			// double radius =Math.sqrt(a) R / (2 * _10CandidateCount);
<span class="nc" id="L81">			double radius = Math.sqrt(R * (max[1] - min[1]) * (max[0] - min[0]) / (Math.PI * (s + _10CandidateCount)));</span>
			// 生成s个候选测试用例,从s个候选测试用例中挑选符合要求的测试用例
<span class="nc" id="L83">			p = new TestCase();</span>
<span class="nc" id="L84">			boolean all_s_has_E_flag = true;</span>
<span class="nc" id="L85">			double TS2C[] = new double[s];</span>
<span class="nc" id="L86">			double Pvalue[] = new double[s];</span>
<span class="nc" id="L87">			double Qvalue[] = new double[s];</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">			for (int k = 0; k &lt; s; k++) {</span>
				// 生成一个候选测试用例
<span class="nc" id="L90">				TestCase ck = randomTC(random);</span>
				// System.out.println(&quot;ck&quot;+k+&quot;:&quot;+ck.toString()+isCorrect(ck));
<span class="nc" id="L92">				boolean this_ck_has_E_flag = false;</span>
<span class="nc" id="L93">				double min = Double.MAX_VALUE;</span>
<span class="nc bnc" id="L94" title="All 2 branches missed.">				for (int i = 0; i &lt; tests.size(); i++) {</span>
					// 没有在圈之中
					// if((Math.pow((ck.p-tests.get(i).p)*(ck.p-tests.get(i).p)+(ck.q-tests.get(i).q)*(ck.q-tests.get(i).q),
					// 0.5))&lt;radius){
					// if(Math.abs(p.p-tests.get(i).p)&lt;radius&amp;&amp;Math.abs(p.q-tests.get(i).q)&lt;radius){
<span class="nc" id="L99">					if ((Math.pow((ck.p - tests.get(i).p) * (ck.p - tests.get(i).p)</span>
<span class="nc bnc" id="L100" title="All 2 branches missed.">							+ (ck.q - tests.get(i).q) * (ck.q - tests.get(i).q), 0.5)) &lt; radius) {</span>
<span class="nc bnc" id="L101" title="All 2 branches missed.">						if (min &gt; Math.sqrt(Math.pow(ck.p - tests.get(i).p, 2) + Math.pow(ck.q - tests.get(i).q, 2))) {</span>
<span class="nc" id="L102">							min = Math.sqrt(Math.pow(ck.p - tests.get(i).p, 2) + Math.pow(ck.q - tests.get(i).q, 2));</span>
<span class="nc" id="L103">							TS2C[k] = min;</span>
<span class="nc" id="L104">							Pvalue[k] = ck.p;</span>
<span class="nc" id="L105">							Qvalue[k] = ck.q;</span>
						}
<span class="nc" id="L107">						this_ck_has_E_flag = true;</span>
					}

				}
<span class="nc bnc" id="L111" title="All 2 branches missed.">				if (!this_ck_has_E_flag) {</span>
<span class="nc" id="L112">					all_s_has_E_flag = false;</span>
<span class="nc" id="L113">					p = new TestCase();</span>
<span class="nc" id="L114">					p.p = ck.p;</span>
<span class="nc" id="L115">					p.q = ck.q;</span>
<span class="nc" id="L116">					System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.toString() + isCorrect(p) + &quot; rrt&quot;);</span>
<span class="nc bnc" id="L117" title="All 2 branches missed.">					if (!isCorrect(p)) {</span>
<span class="nc" id="L118">						return count;</span>
					} else {
<span class="nc" id="L120">						count++;</span>
<span class="nc" id="L121">						tests.add(p);</span>
					}
				}
			}
<span class="nc bnc" id="L125" title="All 2 branches missed.">			if (all_s_has_E_flag) {</span>
<span class="nc" id="L126">				double max = 0;</span>
<span class="nc" id="L127">				int index = 0;</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">				for (int i = 0; i &lt; TS2C.length; i++) {</span>
<span class="nc bnc" id="L129" title="All 2 branches missed.">					if (max &lt; TS2C[i]) {</span>
<span class="nc" id="L130">						max = TS2C[i];</span>
<span class="nc" id="L131">						index = i;</span>
					}
				}
<span class="nc" id="L134">				p = new TestCase();</span>
<span class="nc" id="L135">				p.p = Pvalue[index];</span>
<span class="nc" id="L136">				p.q = Qvalue[index];</span>
<span class="nc" id="L137">				System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.toString() + isCorrect(p) + &quot; fscs&quot;);</span>
<span class="nc bnc" id="L138" title="All 2 branches missed.">				if (!isCorrect(p)) {</span>
<span class="nc" id="L139">					return count;</span>
				} else {
<span class="nc" id="L141">					count++;</span>
<span class="nc" id="L142">					tests.add(p);</span>
				}
			}
<span class="nc" id="L145">			_10CandidateCount++;</span>
		}
<span class="nc" id="L147">		return count;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>