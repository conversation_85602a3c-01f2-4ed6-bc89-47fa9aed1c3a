<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RandomCreator.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util</a> &gt; <span class="el_source">RandomCreator.java</span></div><h1>RandomCreator.java</h1><pre class="source lang-java linenums">package util;

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;

public class RandomCreator {
	private Random random;
	private int dimension;
	private double[] min;
	private double[] max;

<span class="fc" id="L15">	public RandomCreator(Random random, int dimension, double[] min, double[] max) {</span>
<span class="fc" id="L16">		this.random = random;</span>
<span class="fc" id="L17">		this.dimension = dimension;</span>
<span class="fc" id="L18">		this.min = min;</span>
<span class="fc" id="L19">		this.max = max;</span>
<span class="fc" id="L20">	}</span>

	public NPoint randomPoint() {
		// generate from the input domain
<span class="fc" id="L24">		NPoint point = new NPoint();</span>
<span class="fc" id="L25">		double[] xn = new double[this.dimension];</span>
<span class="fc bfc" id="L26" title="All 2 branches covered.">		for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="fc" id="L27">			xn[i] = random.nextDouble() * (max[i] - min[i]) + min[i];</span>
		}
<span class="fc" id="L29">		point.setDimension(this.dimension);</span>
<span class="fc" id="L30">		point.setXn(xn);</span>
<span class="fc" id="L31">		return point;</span>
	}

	public NPoint randomPoint(NRectRegion region) {
<span class="nc" id="L35">		NPoint p = new NPoint();</span>
<span class="nc" id="L36">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L37">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L38">		double xn[] = new double[dimension];</span>

<span class="nc bnc" id="L40" title="All 2 branches missed.">		for (int i = 0; i &lt; xn.length; i++) {</span>
<span class="nc" id="L41">			xn[i] = random.nextDouble() * (end[i] - start[i]) + start[i];</span>
		}
<span class="nc" id="L43">		p.setXn(xn);</span>
<span class="nc" id="L44">		return p;</span>
	}
	
	public NPoint randomPoint(NRectRegion[] region) {
<span class="nc" id="L48">		NPoint p = new NPoint();</span>
<span class="nc" id="L49">		double Size=0.0;</span>
<span class="nc bnc" id="L50" title="All 2 branches missed.">		for(int i=0;i&lt;region.length;i++){</span>
<span class="nc" id="L51">			Size+=region[i].size();</span>
		}
<span class="nc" id="L53">		double T=random.nextDouble()*Size;</span>
<span class="nc" id="L54">		double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L55">		double PreIntegral = 0.0;</span>
<span class="nc" id="L56">		int temp = 0;</span>
<span class="nc bnc" id="L57" title="All 2 branches missed.">		for (int i = 0; i &lt; region.length; i++) {</span>
<span class="nc bnc" id="L58" title="All 2 branches missed.">			if (SumIntegral &lt; T) {</span>
<span class="nc" id="L59">				PreIntegral = SumIntegral;</span>
<span class="nc" id="L60">				temp = i;</span>
			}
<span class="nc" id="L62">			SumIntegral += region[i].size() ;</span>
		}
		//在temp处生成下一个随机点
<span class="nc" id="L65">		NRectRegion tempRegion=region[temp];</span>
<span class="nc" id="L66">		return randomPoint(tempRegion);</span>
	}
	public NPoint randomPoint(ArrayList&lt;NRectRegion&gt; region) {
<span class="nc" id="L69">		NPoint p = new NPoint();</span>
<span class="nc" id="L70">		double Size=0.0;</span>
<span class="nc bnc" id="L71" title="All 2 branches missed.">		for(int i=0;i&lt;region.size();i++){</span>
<span class="nc" id="L72">			Size+=region.get(i).size();</span>
		}
<span class="nc" id="L74">		double T=random.nextDouble()*Size;</span>
<span class="nc" id="L75">		double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L76">		double PreIntegral = 0.0;</span>
<span class="nc" id="L77">		int temp = 0;</span>
<span class="nc bnc" id="L78" title="All 2 branches missed.">		for (int i = 0; i &lt; region.size(); i++) {</span>
<span class="nc bnc" id="L79" title="All 2 branches missed.">			if (SumIntegral &lt; T) {</span>
<span class="nc" id="L80">				PreIntegral = SumIntegral;</span>
<span class="nc" id="L81">				temp = i;</span>
			}
<span class="nc" id="L83">			SumIntegral += region.get(i).size() ;</span>
		}
		//在temp处生成下一个随机点
		//System.out.println(&quot;at region &quot;+temp+&quot; generate TC.&quot;);
<span class="nc" id="L87">		NRectRegion tempRegion=region.get(temp);</span>
<span class="nc" id="L88">		return randomPoint(tempRegion);</span>
	}
	public NPoint randomPoint(double min,double max){
		//一维模拟程序
<span class="nc" id="L92">		double value= random.nextDouble() * (max- min) + min;</span>
<span class="nc" id="L93">		NPoint p=new NPoint(new double[]{value});</span>
<span class="nc" id="L94">		return p;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>