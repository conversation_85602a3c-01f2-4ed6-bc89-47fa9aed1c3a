<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>FailurePattern.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.failurepattern</a> &gt; <span class="el_source">FailurePattern.java</span></div><h1>FailurePattern.java</h1><pre class="source lang-java linenums">package datastructure.failurepattern;

import java.util.Random;

import datastructure.ND.NPoint;

<span class="fc" id="L7">public abstract class FailurePattern {</span>
	public int dimension;
	public double[] min;
	public double[] max;
	public double fail_rate;
	public Random random;

	public abstract void genFailurePattern();

	public abstract boolean isCorrect(NPoint p);
	
	public abstract void showFailurePattern();
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>