<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>PRT_OD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.prt</a> &gt; <span class="el_source">PRT_OD.java</span></div><h1>PRT_OD.java</h1><pre class="source lang-java linenums">package test.simulations.prt;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class PRT_OD {
	public static void main(String[] args) {
<span class="nc" id="L10">		double exp = 3;</span>
<span class="nc" id="L11">		double fail_rate = 0.0005;</span>
<span class="nc" id="L12">		int times = 5000;</span>
<span class="nc" id="L13">		long sums = 0;</span>
		// long startTime=System.nanoTime();
<span class="nc" id="L15">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L16" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L17">			PRT_OD prt = new PRT_OD((i + 3) * 15, fail_rate, exp);</span>
<span class="nc" id="L18">			int f_measure = prt.run();</span>
<span class="nc" id="L19">			sums += f_measure;</span>
		}
<span class="nc" id="L21">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L22">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L23">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L24">	}</span>
	double fail_start;
	double fail_rate;
	int seedOfRandom;
	double exp;// 分布函数的指数

<span class="nc" id="L30">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L32">	public PRT_OD(int seed, double fail_rate, double exp) {</span>
<span class="nc" id="L33">		this.seedOfRandom = seed;</span>
<span class="nc" id="L34">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L35">		this.exp = exp;</span>
<span class="nc" id="L36">	}</span>

	// check is failure
	public boolean isCorrect(double p) {
<span class="nc bnc" id="L40" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L41">			return false;</span>
		} else {
<span class="nc" id="L43">			return true;</span>
		}
	}

	public int run() {
<span class="nc" id="L48">		Random random = new Random(seedOfRandom);</span>
<span class="nc" id="L49">		fail_start = random.nextDouble() * (1 - fail_rate);</span>
		// System.out.println(&quot;fail:&quot;+fail_start);
<span class="nc" id="L51">		int count = 0;</span>
<span class="nc" id="L52">		double value = random.nextDouble();</span>
<span class="nc" id="L53">		TestCase p = new TestCase();</span>
<span class="nc" id="L54">		p.p = value;</span>
<span class="nc bnc" id="L55" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L56">			count++;</span>
			/* sort tests */
<span class="nc bnc" id="L58" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L59">				tests.add(p);</span>
<span class="nc" id="L60">			} else {</span>
<span class="nc" id="L61">				sortTestCases(p);</span>
			}
			/* end sort */
			// init subRegion range
			// every subRegion low and high

			// Max subRegion low and high and index
<span class="nc" id="L68">			double Mhigh = 1.0, Mlow = 0.0;</span>
<span class="nc" id="L69">			int indexOfMnode = 0;</span>
<span class="nc" id="L70">			double S = random.nextDouble();</span>
<span class="nc" id="L71">			double mindistance = Mhigh;</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size(); i++) {</span>
<span class="nc" id="L73">				double distance = Math.abs(tests.get(i).p - S);</span>
<span class="nc bnc" id="L74" title="All 2 branches missed.">				if (distance &lt; mindistance) {</span>
<span class="nc" id="L75">					mindistance = distance;</span>
<span class="nc" id="L76">					indexOfMnode = i;</span>
				}
			}
			// System.out.println(&quot;index:&quot;+indexOfMnode);
<span class="nc bnc" id="L80" title="All 2 branches missed.">			if (indexOfMnode == 0) {</span>
<span class="nc" id="L81">				Mlow = 0.0;</span>
<span class="nc" id="L82">			} else {</span>
<span class="nc" id="L83">				Mlow = (tests.get(indexOfMnode).p + tests.get(indexOfMnode - 1).p) / 2.0;</span>
			}
<span class="nc bnc" id="L85" title="All 2 branches missed.">			if (indexOfMnode == tests.size() - 1) {</span>
<span class="nc" id="L86">				Mhigh = 1.0;</span>
<span class="nc" id="L87">			} else {</span>
<span class="nc" id="L88">				Mhigh = (tests.get(indexOfMnode).p + tests.get(indexOfMnode + 1).p) / 2.0;</span>
			}
			// System.out.println(&quot;max:(&quot;+Mlow+&quot;,&quot;+Mhigh+&quot;) &quot;+indexOfMnode);
			// 求出概率分布函数的系数
<span class="nc" id="L92">			double cMaxNode = tests.get(indexOfMnode).p;</span>
<span class="nc" id="L93">			double Co = (exp + 1.0)</span>
<span class="nc" id="L94">					/ (Math.pow((cMaxNode - Mlow), (exp + 1.0)) + Math.pow(Mhigh - cMaxNode, (exp + 1.0)));</span>
			// 概率学生成下一个测试用例
<span class="nc" id="L96">			double T = random.nextDouble();</span>
			//// 根据概率分布算出一个随机的值
			// 第一段积分值为intgral(t-r)^3 (low,t)
<span class="nc" id="L99">			double FirstIntgral = Co * (Math.pow(cMaxNode - Mlow, (exp + 1.0)) / (exp + 1.0));</span>
			// double SecondIntgral=Co*(Math.pow(Mhigh-cMaxNode,(exp+1.0))/(exp+1.0));
<span class="nc" id="L101">			p = new TestCase();</span>
<span class="nc bnc" id="L102" title="All 2 branches missed.">			if (T &lt;= FirstIntgral) {</span>
<span class="nc" id="L103">				p.p = cMaxNode - Math.pow((exp + 1.0) * (FirstIntgral - T) / Co, 1.0 / (exp + 1.0));</span>
<span class="nc" id="L104">			} else {</span>
<span class="nc" id="L105">				p.p = cMaxNode + Math.pow((exp + 1.0) * (T - FirstIntgral) / Co, 1.0 / (exp + 1.0));</span>
			}
		}
		// System.out.println(&quot;last p:&quot;+p);
<span class="nc" id="L109">		return count;</span>
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L113">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L114" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L115">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L116" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L117">				low = mid + 1;</span>
<span class="nc" id="L118">			} else {</span>
<span class="nc" id="L119">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L122" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L123">			mid = mid - 1;</span>
		}
<span class="nc" id="L125">		tests.add(mid + 1, p);</span>
<span class="nc" id="L126">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>