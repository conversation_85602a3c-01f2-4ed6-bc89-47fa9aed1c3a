<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>TestProgram.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util</a> &gt; <span class="el_source">TestProgram.java</span></div><h1>TestProgram.java</h1><pre class="source lang-java linenums">package util;

<span class="nc" id="L3">public class TestProgram {</span>
	static {
<span class="nc" id="L5">		System.loadLibrary(&quot;programlib&quot;);</span>
<span class="nc" id="L6">	}</span>

	public static void main(String[] args) {
<span class="nc" id="L9">		System.out.println(test_gammq(1456.9880675291643,38.11143387109041));</span>
		// System.out.println(new bessj0().isCorrect(-37.62759377580369));
		//System.out.println(TestProgram.test_airy(-37.62759377580369));
		//System.out.println(TestProgram.test_bessj(219.85509600636087, 135.87255603090216));
<span class="nc" id="L13">	}</span>

	public static native boolean test_airy(double a);

	public static native boolean test_bessj(double a, double b);

	public static native boolean test_bessj0(double a);

	public static native boolean test_cel(double a, double b, double c, double d);

	public static native boolean test_el2(double a, double b, double c, double d);

	public static native boolean test_erfcc(double a);

	public static native boolean test_gammq(double a, double b);

	public static native boolean test_golden(double a, double b, double c);

	public static native boolean test_plgndr(double a, double b, double c);

	public static native boolean test_probks(double a);

	public static native boolean test_sncndn(double a, double b);

	public static native boolean test_tanh(double a);
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>