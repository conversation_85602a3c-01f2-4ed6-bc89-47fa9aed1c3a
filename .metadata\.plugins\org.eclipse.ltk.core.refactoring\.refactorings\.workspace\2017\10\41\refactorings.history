<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Delete resource &apos;ART_main/src/test/simulations/ART_ORB/ORB_ND_CircularRestrict.java&apos;" deleteContents="false" description="Delete resource &apos;ART_main/src/test/simulations/ART_ORB/ORB_ND_CircularRestrict.java&apos;" element1="/ART_main/src/test/simulations/ART_ORB/ORB_ND_CircularRestrict.java" flags="7" id="org.eclipse.ltk.core.refactoring.delete.resources" resources="1" stamp="1507618558150"/>&#x0A;<refactoring comment="Delete resource &apos;ART_main/src/test/simulations/ART_ORB/ORB_ND_CircularRestrict.java&apos;" deleteContents="false" description="Delete resource &apos;ART_main/src/test/simulations/ART_ORB/ORB_ND_CircularRestrict.java&apos;" element1="/ART_main/src/test/simulations/ART_ORB/ORB_ND_CircularRestrict.java" flags="7" id="org.eclipse.ltk.core.refactoring.delete.resources" resources="1" stamp="1507625011228"/>&#x0A;<refactoring comment="Delete resource &apos;ART_main/src/test/simulations/ART_ORB/ComplexRegion_Xi.java&apos;" deleteContents="false" description="Delete resource &apos;ART_main/src/test/simulations/ART_ORB/ComplexRegion_Xi.java&apos;" element1="/ART_main/src/test/simulations/ART_ORB/ComplexRegion_Xi.java" flags="7" id="org.eclipse.ltk.core.refactoring.delete.resources" resources="1" stamp="1507634397508"/>&#x0A;<refactoring comment="Rename resource &apos;ART_main&apos; to &apos;ART_main_ROB&apos;" description="Rename resource &apos;ART_main&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="/ART_main" name="ART_main_ROB" stamp="1507637364111" updateReferences="true"/>
</session>