<?xml version="1.0" encoding="UTF-8"?>
<session version="1.0">&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_orbo_reality&apos; to &apos;art_orbo&apos;" description="Rename resource &apos;art_orbo_reality&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_orbo_reality" name="art_orbo" stamp="1542379678619" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_orbo/ORB_FSCS_ND_Real.java&apos; to &apos;ORB_FSCS_ND_Em.java&apos;" description="Rename resource &apos;ORB_FSCS_ND_Real.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_orbo/ORB_FSCS_ND_Real.java" name="ORB_FSCS_ND_Em.java" stamp="1542379713061" updateReferences="true"/>&#x0A;<refactoring comment="Rename resource &apos;ART_ORB_Em/src/test/reality/art_rp/ART_RP_ND_Test.java&apos; to &apos;ART_RP_ND_Test_Em.java&apos;" description="Rename resource &apos;ART_RP_ND_Test.java&apos;" flags="7" id="org.eclipse.ltk.core.refactoring.rename.resource" input="src/test/reality/art_rp/ART_RP_ND_Test.java" name="ART_RP_ND_Test_Em.java" stamp="1542394972307" updateReferences="true"/>
</session>