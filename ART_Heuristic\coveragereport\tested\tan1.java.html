<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>tan1.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">tan1.java</span></div><h1>tan1.java</h1><pre class="source lang-java linenums">package tested;

<span class="nc" id="L3">public class tan1 {</span>
	public double wrong(double u) {
<span class="nc" id="L5">		double epu = Math.exp(u);</span>
<span class="nc" id="L6">		double emu = 1.0 / epu;</span>

<span class="nc bnc" id="L8" title="All 2 branches missed.">		if (Math.abs(u) &lt;= 0.9) {</span>
<span class="nc" id="L9">			double u2 = u * u;</span>
<span class="nc" id="L10">			return (2 * u * (1 + u2 / 6 * (1 + u / 20 * (1 + u2 / 42 * (1 - u2 / 72)))) / (epu + emu));</span>
		} else {
<span class="nc" id="L12">			double difference = epu - emu;</span>
<span class="nc" id="L13">			double sum = epu + emu;</span>
<span class="nc" id="L14">			double fraction = difference / sum;</span>
<span class="nc" id="L15">			return fraction;</span>
		}
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>