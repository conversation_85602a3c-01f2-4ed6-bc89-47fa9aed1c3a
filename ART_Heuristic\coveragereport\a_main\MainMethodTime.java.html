<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MainMethodTime.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">a_main</a> &gt; <span class="el_source">MainMethodTime.java</span></div><h1>MainMethodTime.java</h1><pre class="source lang-java linenums">package a_main;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.OutputStreamWriter;

import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tp._ND.ART_TP_ND;
import test.simulations.art_tpp.ART_TPP;
import test.simulations.fscs.FSCS_ND;
import test.simulations.rrt.RRT_ND;
import test.simulations.rrttp.hilbert.RRTtpND_H;
import test.simulations.rt.RT_ND;
import util.file.FileUtils;

<span class="nc" id="L19">public class MainMethodTime {</span>
	public static void main(String[] args) throws Exception {
<span class="nc" id="L21">		int tcCounts[] = new int[2000];</span>
<span class="nc bnc" id="L22" title="All 2 branches missed.">		for(int i=0;i&lt;tcCounts.length;i++){</span>
<span class="nc" id="L23">			tcCounts[i]=(i+1)*500;</span>
		}
<span class="nc" id="L25">		String path = &quot;C:\\Users\\<USER>\\Desktop\\研究方向和内容\\小论文\\补充数据\\time\\0-1000000&quot;;</span>
<span class="nc" id="L26">		int d = 1;</span>

<span class="nc" id="L28">		File rtf = FileUtils.createNewFile(path, d + &quot;维&quot; + &quot;all.txt&quot;);</span>
<span class="nc" id="L29">		BufferedWriter writer = get(rtf);</span>

<span class="nc" id="L31">		writeTCTime(&quot;rt&quot;, -1, writer);</span>
<span class="nc bnc" id="L32" title="All 2 branches missed.">		for (int j = 0; j &lt; tcCounts.length; j++) {</span>
			// rt
<span class="nc" id="L34">			double time = RT_ND.testTCTime(d, tcCounts[j]);</span>
<span class="nc" id="L35">			writeTCTime(&quot;&quot;, time, writer);</span>
		}
<span class="nc" id="L37">		writeTCTime(&quot;line&quot;, -1, writer);</span>
<span class="nc" id="L38">		writeTCTime(&quot;rrt&quot;, -1, writer);</span>
<span class="nc bnc" id="L39" title="All 2 branches missed.">		for (int j = 0; j &lt; tcCounts.length; j++) {</span>
			// rrt
<span class="nc" id="L41">			double time = RRT_ND.testTCTime(d, tcCounts[j]);</span>
<span class="nc" id="L42">			writeTCTime(&quot;&quot;, time, writer);</span>
		}
<span class="nc" id="L44">		writeTCTime(&quot;line&quot;, -1, writer);</span>
<span class="nc" id="L45">		writeTCTime(&quot;fscs&quot;, -1, writer);</span>
<span class="nc bnc" id="L46" title="All 2 branches missed.">		for (int j = 0; j &lt; tcCounts.length; j++) {</span>
			// fscs
<span class="nc" id="L48">			double time = FSCS_ND.testTCTime(d, tcCounts[j]);</span>
<span class="nc" id="L49">			writeTCTime(&quot;&quot;, time, writer);</span>
		}
<span class="nc" id="L51">		writeTCTime(&quot;line&quot;, -1, writer);</span>
<span class="nc" id="L52">		writeTCTime(&quot;b&quot;, -1, writer);</span>
<span class="nc bnc" id="L53" title="All 2 branches missed.">		for (int j = 0; j &lt; tcCounts.length; j++) {</span>
			// art_b
<span class="nc" id="L55">			double time=ART_B_ND.testTCTime(d, tcCounts[j]);</span>
<span class="nc" id="L56">			writeTCTime(&quot;&quot;, time, writer);</span>
		}
<span class="nc" id="L58">		writeTCTime(&quot;line&quot;, -1, writer);</span>
		
<span class="nc" id="L60">		writeTCTime(&quot;rp&quot;, -1, writer);</span>
<span class="nc bnc" id="L61" title="All 2 branches missed.">		for (int j = 0; j &lt; tcCounts.length; j++) {</span>
			// art_rp
<span class="nc" id="L63">			double time = ART_RP_ND.testTCTime(d, tcCounts[j]);</span>
<span class="nc" id="L64">			writeTCTime(&quot;&quot;, time, writer);</span>
		}
<span class="nc" id="L66">		writeTCTime(&quot;line&quot;,-1, writer);</span>
<span class="nc" id="L67">		writeTCTime(&quot;tp&quot;, -1, writer);</span>
<span class="nc bnc" id="L68" title="All 2 branches missed.">		for (int j = 0; j &lt; tcCounts.length; j++) {</span>
			// art_tp
<span class="nc" id="L70">			double time = ART_TP_ND.testTCTime(d, tcCounts[j]);</span>
<span class="nc" id="L71">			writeTCTime(&quot;&quot;, time, writer);</span>
		}
<span class="nc" id="L73">		writeTCTime(&quot;line&quot;, -1, writer);</span>
<span class="nc" id="L74">		writeTCTime(&quot;tpp&quot;, -1, writer);</span>
<span class="nc bnc" id="L75" title="All 2 branches missed.">		for (int j = 0; j &lt; tcCounts.length; j++) {</span>
			// art_tpp
<span class="nc" id="L77">			double time = ART_TPP.testTCTime(d, tcCounts[j]);</span>
<span class="nc" id="L78">			writeTCTime(&quot;&quot;, time, writer);</span>
		}
<span class="nc" id="L80">		writeTCTime(&quot;line&quot;, -1, writer);</span>
<span class="nc" id="L81">		writeTCTime(&quot;laz&quot;, -1, writer);</span>
<span class="nc bnc" id="L82" title="All 2 branches missed.">		for (int j = 0; j &lt; tcCounts.length; j++) {</span>
			// my method
			// 1dimension
<span class="nc" id="L85">			double time = RRTtpND_H.testTCTime(d, tcCounts[j]);</span>
<span class="nc" id="L86">			writeTCTime(&quot;&quot;, time, writer);</span>
		}
<span class="nc" id="L88">		writeTCTime(&quot;line&quot;, -1, writer);</span>
<span class="nc" id="L89">		writer.close();</span>

<span class="nc" id="L91">	}</span>

	public static void writeTCTime(String message, double time, BufferedWriter writer) {
		try {
<span class="nc bnc" id="L95" title="All 2 branches missed.">			if (message.equals(&quot;line&quot;)) {</span>
<span class="nc" id="L96">				writer.newLine();</span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">			} else if (!message.equals(&quot;&quot;)) {</span>
<span class="nc" id="L98">				writer.write(message);</span>
<span class="nc" id="L99">				writer.newLine();</span>
			}

<span class="nc bnc" id="L102" title="All 2 branches missed.">			if (time != -1.0) {</span>
<span class="nc" id="L103">				writer.write(time + &quot;&quot;);</span>
<span class="nc" id="L104">				writer.newLine();</span>
			}
<span class="nc" id="L106">			writer.flush();</span>
<span class="nc" id="L107">		} catch (IOException e) {</span>
<span class="nc" id="L108">			e.printStackTrace();</span>
		}
<span class="nc" id="L110">	}</span>

	public static BufferedWriter get(File f) throws Exception {
<span class="nc" id="L113">		return new BufferedWriter(new OutputStreamWriter(new FileOutputStream(f), &quot;UTF-8&quot;));</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>