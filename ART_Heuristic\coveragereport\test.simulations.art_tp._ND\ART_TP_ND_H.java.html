<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_ND_H.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._ND</a> &gt; <span class="el_source">ART_TP_ND_H.java</span></div><h1>ART_TP_ND_H.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._ND;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;
import util.HilbertCurve2;

public class ART_TP_ND_H {
	public static void main(String[] args) throws Exception {
<span class="nc" id="L11">		double fail_rate = 0.01;</span>
<span class="nc" id="L12">		int times = 3000;</span>
<span class="nc" id="L13">		long sums = 0;</span>
<span class="nc" id="L14">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L15" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
			// 只能在0-1上，因为希尔伯特曲线代码只能生成0-1的数
<span class="nc" id="L17">			ART_TP_ND_H art_tp_od = new ART_TP_ND_H(0, 1, fail_rate, 3, i * 3);</span>
<span class="nc" id="L18">			int fm = art_tp_od.run();</span>
<span class="nc" id="L19">			sums += fm;</span>
		}
<span class="nc" id="L21">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L22">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L23">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>

<span class="nc" id="L25">	}</span>
	int seedOfRandom;
	double min;
	double max;
	double fail_rate;
	double fail_start[];
<span class="nc" id="L31">	Random random = null;</span>
	double An;
	double Bn;
<span class="nc" id="L34">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	int dimension;

	// 构造函数
<span class="nc" id="L39">	public ART_TP_ND_H(double min, double max, double fail_rate, int dimension, int seedOfRandom) {</span>
<span class="nc" id="L40">		this.seedOfRandom = seedOfRandom;</span>
<span class="nc" id="L41">		this.min = min;</span>
<span class="nc" id="L42">		this.max = max;</span>
<span class="nc" id="L43">		this.dimension = dimension;</span>
<span class="nc" id="L44">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L45">		random = new Random(this.seedOfRandom);</span>
<span class="nc" id="L46">	}</span>

	public void genFail_start() {
<span class="nc" id="L49">		double fail_size = (max - min) * fail_rate;</span>
<span class="nc" id="L50">		fail_start = new double[dimension];</span>
<span class="nc bnc" id="L51" title="All 2 branches missed.">		for (int i = 0; i &lt; dimension; i++) {</span>
<span class="nc" id="L52">			fail_start[i] = random.nextDouble() * (max - min - Math.pow(fail_size, 1 / (double) dimension));</span>
		}
<span class="nc" id="L54">	}</span>

	public TestCase genNext(ArrayList&lt;Double&gt; integrals, double Co, TestCase p) {
<span class="nc" id="L57">		double e1 = tests.get(0).p;// first node</span>
<span class="nc" id="L58">		double en = tests.get(tests.size() - 1).p;// last node</span>
		// 随机生成一个0-1的数
<span class="nc" id="L60">		double T = random.nextDouble();</span>
		// System.out.println(&quot;An:&quot;+An+&quot;,Bn:&quot;+Bn+&quot;,Co:&quot;+Co+&quot;,T:&quot;+T);
		// 看T落在哪个区间
<span class="nc" id="L63">		double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L64">		double PreIntegral = 0.0;</span>
<span class="nc" id="L65">		int temp = 0;</span>
<span class="nc bnc" id="L66" title="All 2 branches missed.">		for (int i = 0; i &lt; integrals.size(); i++) {</span>
<span class="nc bnc" id="L67" title="All 2 branches missed.">			if (SumIntegral &lt; T) {</span>
<span class="nc" id="L68">				PreIntegral = SumIntegral;</span>
<span class="nc" id="L69">				temp = i;</span>
			}
<span class="nc" id="L71">			SumIntegral += integrals.get(i) * Co;</span>
		}
		//
		double A, B, C, D;
<span class="nc bnc" id="L75" title="All 2 branches missed.">		if (temp == 0) {</span>
<span class="nc" id="L76">			A = -Co / 3.0;</span>
<span class="nc" id="L77">			B = (Co / 2.0) * (e1 + An);</span>
<span class="nc" id="L78">			C = -Co * e1 * An;</span>
<span class="nc" id="L79">			D = (1.0 / 3.0) * min * min * min - ((An + e1) / 2.0) * min * min + An * e1 * min - T;</span>
<span class="nc bnc" id="L80" title="All 2 branches missed.">		} else if (temp == integrals.size() - 1) {</span>
<span class="nc" id="L81">			A = -Co / 3.0;</span>
<span class="nc" id="L82">			B = (Co / 2.0) * (en + Bn);</span>
<span class="nc" id="L83">			C = -Co * (Bn) * (en);</span>
<span class="nc" id="L84">			D = PreIntegral - T - Co * ((1.0 / 6.0) * (Math.pow(en, 3.0)))</span>
<span class="nc" id="L85">					+ Co * ((1.0 / 2.0) * Bn * Math.pow(en, 2.0));</span>
<span class="nc" id="L86">		} else {</span>
<span class="nc" id="L87">			A = -Co / 3.0;</span>
<span class="nc" id="L88">			B = (Co / 2.0) * (tests.get(temp - 1).p + tests.get(temp).p);</span>
<span class="nc" id="L89">			C = -Co * tests.get(temp - 1).p * tests.get(temp).p;</span>
<span class="nc" id="L90">			D = -T + PreIntegral - Co * ((1.0 / 6.0) * (Math.pow(tests.get(temp - 1).p, 3.0))</span>
<span class="nc" id="L91">					- (1.0 / 2.0) * (tests.get(temp).p) * (Math.pow(tests.get(temp - 1).p, 2.0)));</span>
		}
<span class="nc" id="L93">		double[] roots = shengjinFormula(A, B, C, D);</span>
<span class="nc" id="L94">		boolean haveAanswer = false;</span>
<span class="nc bnc" id="L95" title="All 2 branches missed.">		for (double root : roots) {</span>
<span class="nc bnc" id="L96" title="All 2 branches missed.">			if (temp == 0) {</span>
<span class="nc bnc" id="L97" title="All 4 branches missed.">				if (root &gt;= min &amp;&amp; root &lt;= e1) {</span>
<span class="nc" id="L98">					p = new TestCase();</span>
<span class="nc" id="L99">					p.p = root;</span>
<span class="nc" id="L100">					haveAanswer = true;</span>
				}
<span class="nc bnc" id="L102" title="All 2 branches missed.">			} else if (temp == integrals.size() - 1) {</span>
<span class="nc bnc" id="L103" title="All 4 branches missed.">				if (root &gt;= en &amp;&amp; root &lt;= max) {</span>
<span class="nc" id="L104">					p = new TestCase();</span>
<span class="nc" id="L105">					p.p = root;</span>
<span class="nc" id="L106">					haveAanswer = true;</span>
				}
<span class="nc" id="L108">			} else {</span>
<span class="nc bnc" id="L109" title="All 4 branches missed.">				if (root &gt;= tests.get(temp - 1).p &amp;&amp; root &lt;= tests.get(temp).p) {</span>
<span class="nc" id="L110">					p = new TestCase();</span>
<span class="nc" id="L111">					p.p = root;</span>
<span class="nc" id="L112">					haveAanswer = true;</span>
				}
			}
		}
<span class="nc bnc" id="L116" title="All 2 branches missed.">		if (!haveAanswer) {</span>
<span class="nc" id="L117">			return null;</span>
		} else {
<span class="nc" id="L119">			return p;</span>
		}
	}

	public boolean isCorrect(double p) {
<span class="nc" id="L124">		double results[] = new HilbertCurve2().oneD_2_nD(p, dimension);</span>
<span class="nc" id="L125">		boolean flag = true;</span>
<span class="nc" id="L126">		double fail_size = (max - min) * fail_rate;</span>
<span class="nc bnc" id="L127" title="All 2 branches missed.">		for (int i = 0; i &lt; results.length; i++) {</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">			if (results[i] &lt; fail_start[i]</span>
<span class="nc bnc" id="L129" title="All 2 branches missed.">					|| results[i] &gt; (fail_start[i] + Math.pow(fail_size, 1.0 / (double) dimension))) {</span>
<span class="nc" id="L130">				flag = false;</span>
			}
		}
<span class="nc bnc" id="L133" title="All 2 branches missed.">		flag = !flag;</span>
<span class="nc" id="L134">		return flag;</span>
	}

	public int run() {
<span class="nc" id="L138">		genFail_start();</span>
<span class="nc" id="L139">		TestCase p = new TestCase();</span>
<span class="nc" id="L140">		p.p = random.nextDouble() * (max - min) + min;</span>
<span class="nc" id="L141">		int count = 0;</span>
<span class="nc bnc" id="L142" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L143">			count++;</span>
<span class="nc bnc" id="L144" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L145">				tests.add(p);</span>
<span class="nc" id="L146">			} else {</span>
<span class="nc" id="L147">				sortTestCases(p);</span>
			}
			/////////////////////
			// 生成概率分布函数
			// An-&gt;(-e1,0) Bn-&gt;(1,2-en)
<span class="nc" id="L152">			double e1 = tests.get(0).p;// first node</span>
<span class="nc" id="L153">			double en = tests.get(tests.size() - 1).p;// last node</span>
			// calculate An and Bn
			// An (min-(e1-min)) to min |||| Bn max to max+max-en
<span class="nc" id="L156">			An = random.nextDouble() * (e1 - min) + (2 * min - e1);</span>
<span class="nc" id="L157">			Bn = random.nextDouble() * (max - en) + max;</span>
			// 计算系数
<span class="nc" id="L159">			double Co = 0.0;</span>
<span class="nc" id="L160">			ArrayList&lt;Double&gt; integrals = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L161" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size() + 1; i++) {</span>
<span class="nc bnc" id="L162" title="All 2 branches missed.">				if (i == 0) {</span>
					// 1/6*e1^3-1/2*an*e1^2
<span class="nc" id="L164">					double temp = ((1.0 / 6.0) * (Math.pow(e1, 3))) - ((1.0 / 2.0) * An * (Math.pow(e1, 2)));</span>
<span class="nc" id="L165">					double temp1 = (-(1.0 / 3.0) * min * min * min + ((An + e1) / 2.0) * min * min - An * e1 * min);</span>
<span class="nc" id="L166">					Co += (temp - temp1);</span>
<span class="nc" id="L167">					integrals.add(temp);</span>
<span class="nc bnc" id="L168" title="All 2 branches missed.">				} else if (i == tests.size()) {</span>
					// (-1/6)en^3+(1/2)*bn*en^2-en*bn+(1/2)*(bn+en)-(1/3)
<span class="nc" id="L170">					double temp = (-1.0 / 6.0) * Math.pow(en, 3.0) + (1.0 / 2.0) * (Bn) * Math.pow(en, 2.0)</span>
<span class="nc" id="L171">							- en * Bn * max + (1.0 / 2.0) * (Bn + en) * max * max - (1.0 / 3.0) * max * max * max;</span>
<span class="nc" id="L172">					Co += temp;</span>
<span class="nc" id="L173">					integrals.add(temp);</span>
<span class="nc" id="L174">				} else {</span>
<span class="nc" id="L175">					double ei_1 = tests.get(i - 1).p;</span>
<span class="nc" id="L176">					double ei = tests.get(i).p;</span>
					// (1/6)*(ei1^3-ei^3)+(1/2)*(ei*ei1)*(ei-ei1)
<span class="nc" id="L178">					double temp = (1.0 / 6.0) * (Math.pow(ei, 3.0) - Math.pow(ei_1, 3))</span>
<span class="nc" id="L179">							+ (1.0 / 2.0) * (ei_1 * ei) * (ei_1 - ei);</span>
<span class="nc" id="L180">					Co += temp;</span>
<span class="nc" id="L181">					integrals.add(temp);</span>
				}
			}
<span class="nc" id="L184">			Co = 1.0 / Co;</span>
<span class="nc" id="L185">			p = genNext(integrals, Co, p);</span>
<span class="nc bnc" id="L186" title="All 2 branches missed.">			while (p == null) {</span>
<span class="nc" id="L187">				p = genNext(integrals, Co, p);</span>
<span class="nc" id="L188">				System.out.println(&quot;error&quot;);</span>
			}
		}
<span class="nc" id="L191">		return count;</span>
	}

	public double[] shengjinFormula(double acof, double bcof, double cof, double dof) {
<span class="nc" id="L195">		double A = bcof * bcof - 3.0 * acof * cof;// A=b^2-3ac</span>
<span class="nc" id="L196">		double B = bcof * cof - 9.0 * acof * dof;// B=bc-9ad</span>
<span class="nc" id="L197">		double C = cof * cof - 3.0 * bcof * dof;// C=c^2-3bd</span>
<span class="nc" id="L198">		double delta = B * B - 4.0 * A * C;</span>
<span class="nc" id="L199">		double root = 0.0;</span>
<span class="nc" id="L200">		double r1 = 0.0;</span>
<span class="nc" id="L201">		double r2 = 0.0;</span>
<span class="nc" id="L202">		double[] roots = new double[3];</span>
<span class="nc bnc" id="L203" title="All 2 branches missed.">		if (delta &gt; 0) {</span>
<span class="nc" id="L204">			double Y1 = A * bcof + 3.0 * acof * (-B + Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
<span class="nc" id="L205">			double Y2 = A * bcof + 3.0 * acof * (-B - Math.sqrt(B * B - 4.0 * A * C)) / 2.0;</span>
			double powY1;
			double powY2;
<span class="nc bnc" id="L208" title="All 2 branches missed.">			if (Y1 &lt; 0) {</span>
<span class="nc" id="L209">				powY1 = -Math.pow(-Y1, 1.0 / 3.0);</span>
<span class="nc" id="L210">			} else {</span>
<span class="nc" id="L211">				powY1 = Math.pow(Y1, 1.0 / 3.0);</span>
			}
<span class="nc bnc" id="L213" title="All 2 branches missed.">			if (Y2 &lt; 0) {</span>
<span class="nc" id="L214">				powY2 = -Math.pow(-Y2, 1.0 / 3.0);</span>
<span class="nc" id="L215">			} else {</span>
<span class="nc" id="L216">				powY2 = Math.pow(Y2, 1.0 / 3.0);</span>
			}
<span class="nc" id="L218">			root = (-bcof - powY1 - powY2) / (3.0 * acof);</span>
<span class="nc" id="L219">			r1 = root;</span>
<span class="nc" id="L220">			r2 = root;</span>
<span class="nc bnc" id="L221" title="All 2 branches missed.">		} else if (delta == 0) {</span>
<span class="nc" id="L222">			root = -bcof / acof + B / A;</span>
<span class="nc" id="L223">			r1 = -B / (2.0 * A);</span>
<span class="nc" id="L224">			r2 = r1;</span>

<span class="nc bnc" id="L226" title="All 2 branches missed.">		} else if (delta &lt; 0) {</span>
<span class="nc" id="L227">			double T = (2.0 * A * bcof - 3.0 * acof * B) / (2.0 * Math.pow(A, 3.0 / 2.0));</span>
<span class="nc" id="L228">			double theta = Math.acos(T);</span>
<span class="nc" id="L229">			root = (-bcof - 2.0 * Math.sqrt(A) * Math.cos(theta / 3.0)) / (3.0 * acof);</span>
<span class="nc" id="L230">			r1 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) + Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L231">					/ (3.0 * acof);</span>
<span class="nc" id="L232">			r2 = (-bcof + Math.sqrt(A) * (Math.cos(theta / 3.0) - Math.sqrt(3.0) * Math.sin(theta / 3.0)))</span>
<span class="nc" id="L233">					/ (3.0 * acof);</span>
		}
<span class="nc" id="L235">		roots[0] = root;</span>
<span class="nc" id="L236">		roots[1] = r1;</span>
<span class="nc" id="L237">		roots[2] = r2;</span>
<span class="nc" id="L238">		return roots;</span>
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L242">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L243" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L244">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L245" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L246">				low = mid + 1;</span>
<span class="nc" id="L247">			} else {</span>
<span class="nc" id="L248">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L251" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L252">			mid = mid - 1;</span>
		}
<span class="nc" id="L254">		tests.add(mid + 1, p);</span>
<span class="nc" id="L255">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>