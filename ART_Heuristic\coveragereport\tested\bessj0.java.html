<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>bessj0.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">bessj0.java</span></div><h1>bessj0.java</h1><pre class="source lang-java linenums">package tested;

import util.TestProgram;

/*
 * Input Domain:(-300000,300000)
 * failure rate:0.001298 avf=1.0/0.001298=770.4160246533127889060092449923
 *errors: 5 errors(2 AOR, 1 ROR, 1SVR ,1CR)
 * */
<span class="nc" id="L10">public class bessj0 {</span>
<span class="nc" id="L11">	public static double[] min = { -300000 };</span>
<span class="nc" id="L12">	public static  double[] max = { 300000 };</span>
<span class="nc" id="L13">	public static  double failureRate = 0.001298;</span>
<span class="nc" id="L14">	public  static int Dimension = 1;</span>

	double correct(double x) {
		double ax, z;
		double xx, y, ans, ans1, ans2;

<span class="nc bnc" id="L20" title="All 2 branches missed.">		if ((ax = Math.abs(x)) &lt; 8.0) {</span>
<span class="nc" id="L21">			y = x * x;</span>
<span class="nc" id="L22">			ans1 = 57568490574.0 + y * (-13362590354.0</span>
<span class="nc" id="L23">					+ y * (651619640.7 + y * (-11214424.18 + y * (77392.33017 + y * (-184.9052456)))));</span>
<span class="nc" id="L24">			ans2 = 57568490411.0</span>
<span class="nc" id="L25">					+ y * (1029532985.0 + y * (9494680.718 + y * (59272.64853 + y * (267.8532712 + y * 1.0))));</span>
<span class="nc" id="L26">			ans = ans1 / ans2;</span>
<span class="nc" id="L27">		} else {</span>
<span class="nc" id="L28">			z = 8.0 / ax;</span>
<span class="nc" id="L29">			y = z * z;</span>
<span class="nc" id="L30">			xx = ax - 0.785398164;</span>
<span class="nc" id="L31">			ans1 = 1.0 + y * (-0.1098628627e-2 + y * (0.2734510407e-4 + y * (-0.2073370639e-5 + y * 0.2093887211e-6)));</span>
<span class="nc" id="L32">			ans2 = -0.1562499995e-1</span>
<span class="nc" id="L33">					+ y * (0.1430488765e-3 + y * (-0.6911147651e-5 + y * (0.7621095161e-6 - y * 0.934935152e-7)));</span>
<span class="nc" id="L34">			ans = Math.sqrt(0.636619772 / ax) * (Math.cos(xx) * ans1 - z * Math.sin(xx) * ans2);</span>
		}
<span class="nc" id="L36">		return ans;</span>
	}

	public boolean isCorrect(double x) {
		// System.out.println(&quot;correct:&quot;+correct(x));
		// System.out.println(&quot;wrong:&quot;+wrong(x));
<span class="nc" id="L42">		return TestProgram.test_bessj0(x);</span>
		// System.out.println(correct(x));
		// System.out.println(wrong(x));
		// return correct(x)==wrong(x);
	}

	public boolean isCorrect(double[] x) {
<span class="nc bnc" id="L49" title="All 2 branches missed.">		if (x.length == this.Dimension) {</span>
<span class="nc" id="L50">			return TestProgram.test_bessj0(x[0]);</span>
		} else {
<span class="nc" id="L52">			return true;</span>
		}
	}

	double wrong(double x) {
		double ax;
		double z;
		double xx, y, ans, ans1, ans2;

		/* ERROR */
		/* if ((ax=fabs(x)) &lt; 8.0) { */
<span class="nc bnc" id="L63" title="All 2 branches missed.">		if ((ax = Math.abs(x)) &lt;= 10.0) {</span>
<span class="nc" id="L64">			y = x * x;</span>
<span class="nc" id="L65">			ans1 = 57568490574.0 + y * (-13362590354.0</span>
<span class="nc" id="L66">					+ y * (651619640.7 + y * (-11214424.18 + y * (77392.33017 + y * (-184.9052456)))));</span>
<span class="nc" id="L67">			ans2 = 57568490411.0 + y * (1029532985.0 + y * (9494680.718</span>
					/* ERROR */
					/* +y*(59272.64853+y*(267.8532712+y*1.0)))); */
<span class="nc" id="L70">					+ y * (59272.64853 + y * (267.8532712 + y + 1.0))));</span>
<span class="nc" id="L71">			ans = ans1 / ans2;</span>
<span class="nc" id="L72">		} else {</span>
<span class="nc" id="L73">			z = 8.0 / ax;</span>
<span class="nc" id="L74">			y = z * z;</span>
<span class="nc" id="L75">			xx = ax - 0.785398164;</span>
<span class="nc" id="L76">			ans1 = 1.0 + y * (-0.1098628627e-2 + y * (0.2734510407e-4 + y * (-0.2073370639e-5 + y * 0.2093887211e-6)));</span>
<span class="nc" id="L77">			ans2 = -0.1562499995e-1 + y * (0.1430488765e-3</span>
					/* ERROR */
					/* +y*(-0.6911147651e-5+y*(0.7621095161e-6 */
<span class="nc" id="L80">					+ y * (-0.6911147651e-5 + z * (0.7621095161e-6</span>
							/* ERROR */
							/* -y*0.934935152e-7))); */
<span class="nc" id="L83">							+ y * 0.934935152e-7)));</span>
<span class="nc" id="L84">			ans = Math.sqrt(0.636619772 / ax) * (Math.cos(xx) * ans1 - z * Math.sin(xx) * ans2);</span>
		}
<span class="nc" id="L86">		return ans;</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>