<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TPP.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tpp</a> &gt; <span class="el_source">ART_TPP.java</span></div><h1>ART_TPP.java</h1><pre class="source lang-java linenums">package test.simulations.art_tpp;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import util.PaiLie;
import util.data.ZeroOneCreator;

public class ART_TPP extends ART {

	public static void main(String[] args) {
		// testEm(1, 0.01);
<span class="nc" id="L19">		testTCTime(2,5000);</span>
		//testFm();
<span class="nc" id="L21">	}</span>

<span class="nc" id="L23">	ArrayList&lt;NRectRegion&gt; regionLists = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L24">	ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L26">	int k = 10;</span>

<span class="nc" id="L28">	NRectRegion currentRegion = null;// 执行到哪一个区域</span>

	public ART_TPP(double[] min, double[] max, Random random, FailurePattern failurePattern, int k) {
<span class="nc" id="L31">		super(min, max, random, failurePattern);</span>
<span class="nc" id="L32">		this.k = k;</span>
<span class="nc" id="L33">		regionLists.add(new NRectRegion(new NPoint(min), new NPoint(max)));</span>
<span class="nc" id="L34">	}</span>

	public void addRegionsIn2D(NRectRegion region, NPoint p) {
<span class="nc" id="L37">		double xmin = region.getStart().getXn()[0];</span>
<span class="nc" id="L38">		double ymin = region.getStart().getXn()[1];</span>
<span class="nc" id="L39">		double xmax = region.getEnd().getXn()[0];</span>
<span class="nc" id="L40">		double ymax = region.getEnd().getXn()[1];</span>
<span class="nc" id="L41">		double pp = p.getXn()[0];</span>
<span class="nc" id="L42">		double qq = p.getXn()[1];</span>

<span class="nc" id="L44">		NRectRegion first = new NRectRegion(new NPoint(new double[] { xmin, ymin }),</span>
<span class="nc" id="L45">				new NPoint(new double[] { pp, qq }));</span>
<span class="nc" id="L46">		NRectRegion second = new NRectRegion(new NPoint(new double[] { pp, ymin }),</span>
<span class="nc" id="L47">				new NPoint(new double[] { xmax, qq }));</span>
<span class="nc" id="L48">		NRectRegion third = new NRectRegion(new NPoint(new double[] { pp, qq }),</span>
<span class="nc" id="L49">				new NPoint(new double[] { xmax, ymax }));</span>
<span class="nc" id="L50">		NRectRegion fourth = new NRectRegion(new NPoint(new double[] { xmin, qq }),</span>
<span class="nc" id="L51">				new NPoint(new double[] { pp, ymax }));</span>

<span class="nc" id="L53">		this.regionLists.add(first);</span>
<span class="nc" id="L54">		this.regionLists.add(second);</span>
<span class="nc" id="L55">		this.regionLists.add(third);</span>
<span class="nc" id="L56">		this.regionLists.add(fourth);</span>
<span class="nc" id="L57">	}</span>

	public void addRegionsInND(NRectRegion region, NPoint p) throws Exception {
<span class="nc" id="L60">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L61">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L62">		double[] pxn = p.getXn();</span>
<span class="nc" id="L63">		List&lt;List&lt;Double&gt;&gt; result1 = splitRegions(start, pxn);</span>
<span class="nc" id="L64">		List&lt;List&lt;Double&gt;&gt; result2 = splitRegions(pxn, end);</span>
<span class="nc bnc" id="L65" title="All 2 branches missed.">		if (result1.size() != result2.size()) {</span>
<span class="nc" id="L66">			throw new Exception(&quot;result1's size!=result2's size ,split region wrong&quot;);</span>
		}
<span class="nc bnc" id="L68" title="All 2 branches missed.">		for (int i = 0; i &lt; result1.size(); i++) {</span>
<span class="nc" id="L69">			List&lt;Double&gt; temp1 = result1.get(i);</span>
<span class="nc" id="L70">			List&lt;Double&gt; temp2 = result2.get(i);</span>
<span class="nc" id="L71">			double[] newStart = new double[temp1.size()];</span>
<span class="nc" id="L72">			double[] newEnd = new double[temp2.size()];</span>
<span class="nc bnc" id="L73" title="All 2 branches missed.">			for (int j = 0; j &lt; temp1.size(); j++) {</span>
<span class="nc" id="L74">				newStart[j] = temp1.get(j);</span>
<span class="nc" id="L75">				newEnd[j] = temp2.get(j);</span>
			}

<span class="nc" id="L78">			NRectRegion tempRegion = new NRectRegion(new NPoint(newStart), new NPoint(newEnd));</span>
<span class="nc" id="L79">			this.regionLists.add(tempRegion);</span>
		}
<span class="nc" id="L81">	}</span>

	public double calTwoPointDistance(NPoint p1, NPoint p2) {
<span class="nc" id="L84">		double[] p1xn = p1.getXn();</span>
<span class="nc" id="L85">		double[] p2xn = p2.getXn();</span>
<span class="nc" id="L86">		double distance = 0.0;</span>
<span class="nc bnc" id="L87" title="All 2 branches missed.">		for (int i = 0; i &lt; p1xn.length; i++) {</span>
<span class="nc" id="L88">			distance += Math.pow((p2xn[i] - p1xn[i]), 2);</span>
		}
<span class="nc" id="L90">		distance = Math.sqrt(distance);</span>
<span class="nc" id="L91">		return distance;</span>
	}

	public int findMaxRegion() {
<span class="nc" id="L95">		double max = -1;</span>
<span class="nc" id="L96">		int index = 0;</span>
<span class="nc bnc" id="L97" title="All 2 branches missed.">		for (int i = 0; i &lt; regionLists.size(); i++) {</span>
<span class="nc" id="L98">			double temp = regionLists.get(i).size();</span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">			if (max &lt; temp) {</span>
<span class="nc" id="L100">				max = temp;</span>
<span class="nc" id="L101">				index = i;</span>
			}
		}
<span class="nc" id="L104">		return index;</span>
	}

	public NPoint hasPointInRegion(NRectRegion region) {
<span class="nc" id="L108">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L109">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L110">		NPoint result = null;</span>
<span class="nc bnc" id="L111" title="All 2 branches missed.">		for (int i = 0; i &lt; tests.size(); i++) {</span>
<span class="nc" id="L112">			double[] p = tests.get(i).getXn();</span>
<span class="nc" id="L113">			boolean isPIn = true;</span>
<span class="nc bnc" id="L114" title="All 2 branches missed.">			for (int j = 0; j &lt; p.length; j++) {</span>
<span class="nc bnc" id="L115" title="All 4 branches missed.">				if (p[j] &lt; start[j] || p[j] &gt; end[j]) {</span>
<span class="nc" id="L116">					isPIn = false;</span>
				}
			}
<span class="nc bnc" id="L119" title="All 2 branches missed.">			if (isPIn) {</span>
<span class="nc" id="L120">				result = tests.get(i);</span>
<span class="nc" id="L121">				break;</span>
			}
		}
<span class="nc" id="L124">		return result;</span>
	}

	public NPoint midPoint(NPoint p1, NPoint p2) {
<span class="nc" id="L128">		double[] p1xn = p1.getXn();</span>
<span class="nc" id="L129">		double[] p2xn = p2.getXn();</span>
<span class="nc" id="L130">		double[] mid = new double[this.dimension];</span>
<span class="nc bnc" id="L131" title="All 2 branches missed.">		for (int i = 0; i &lt; mid.length; i++) {</span>
<span class="nc" id="L132">			mid[i] = 0.5 * (p1xn[i] + p2xn[i]);</span>
		}
<span class="nc" id="L134">		return new NPoint(mid);</span>
	}

	public List&lt;List&lt;Double&gt;&gt; splitRegions(double[] start, double[] end) {
<span class="nc" id="L138">		ArrayList&lt;double[]&gt; values = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L139" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L140">			double[] temp = new double[2];</span>

<span class="nc" id="L142">			temp[0] = start[i];</span>
<span class="nc" id="L143">			temp[1] = end[i];</span>
<span class="nc" id="L144">			values.add(temp);</span>
		}

<span class="nc" id="L147">		ArrayList&lt;List&lt;Double&gt;&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L148">		PaiLie.per(values, 0, new ArrayList&lt;&gt;(), result);</span>
<span class="nc" id="L149">		return result;</span>
	}

<span class="nc" id="L152">	boolean Isfirst = true;</span>
<span class="nc" id="L153">	NPoint[] points = null;</span>
	@Override
	public NPoint generateNextTC() {
		
<span class="nc bnc" id="L157" title="All 2 branches missed.">		if (points == null) {</span>
<span class="nc" id="L158">			points = generateNextTwoTC();</span>
		}
		//System.out.println(points.length);
<span class="nc bnc" id="L161" title="All 2 branches missed.">		if (points.length == 1) {</span>
<span class="nc" id="L162">			NPoint temp=points[0];</span>
<span class="nc" id="L163">			points=null;</span>
<span class="nc" id="L164">			return temp;</span>
		} else {
<span class="nc bnc" id="L166" title="All 2 branches missed.">			if (Isfirst) {</span>
<span class="nc" id="L167">				Isfirst = false;</span>
<span class="nc" id="L168">				return points[0];</span>
			} else {
<span class="nc" id="L170">				NPoint temp = points[1];</span>
<span class="nc" id="L171">				Isfirst=true;</span>
<span class="nc" id="L172">				points = null;</span>
<span class="nc" id="L173">				return temp;</span>
			}
		}
	}

	public NPoint[] generateNextTwoTC() {
<span class="nc" id="L179">		NPoint[] results = null;</span>
<span class="nc" id="L180">		int maxIndex = findMaxRegion();</span>
<span class="nc" id="L181">		currentRegion = regionLists.remove(maxIndex);</span>
<span class="nc" id="L182">		NPoint alreadyPoint = hasPointInRegion(currentRegion);</span>
<span class="nc" id="L183">		NPoint p1 = null;</span>
<span class="nc" id="L184">		NPoint p2 = null;</span>

<span class="nc bnc" id="L186" title="All 2 branches missed.">		if (alreadyPoint == null) {</span>
<span class="nc" id="L187">			p1 = randomCreator.randomPoint(currentRegion);</span>
<span class="nc" id="L188">			tests.add(p1);</span>

<span class="nc" id="L190">			NPoint[] SC = new NPoint[k];</span>
<span class="nc" id="L191">			double maxLength = 0.0;</span>

<span class="nc bnc" id="L193" title="All 2 branches missed.">			for (int i = 0; i &lt; k; i++) {</span>
<span class="nc" id="L194">				SC[i] = randomCreator.randomPoint(currentRegion);</span>
<span class="nc" id="L195">				double length = calTwoPointDistance(p1, SC[i]);</span>
<span class="nc bnc" id="L196" title="All 2 branches missed.">				if (maxLength &lt; length) {</span>
<span class="nc" id="L197">					maxLength = length;</span>
<span class="nc" id="L198">					p2 = SC[i];</span>
				}
			}
<span class="nc" id="L201">			results = new NPoint[2];</span>
<span class="nc" id="L202">			results[0] = p1;</span>
<span class="nc" id="L203">			results[1] = p2;</span>
<span class="nc" id="L204">		} else {</span>
<span class="nc" id="L205">			p1 = alreadyPoint;</span>
<span class="nc" id="L206">			NPoint[] SC = new NPoint[k];</span>
<span class="nc" id="L207">			double maxLength = 0.0;</span>

<span class="nc bnc" id="L209" title="All 2 branches missed.">			for (int i = 0; i &lt; k; i++) {</span>
<span class="nc" id="L210">				SC[i] = randomCreator.randomPoint(currentRegion);</span>
<span class="nc" id="L211">				double length = calTwoPointDistance(p1, SC[i]);</span>
<span class="nc bnc" id="L212" title="All 2 branches missed.">				if (maxLength &lt; length) {</span>
<span class="nc" id="L213">					maxLength = length;</span>
<span class="nc" id="L214">					p2 = SC[i];</span>
				}
			}
<span class="nc" id="L217">			results = new NPoint[1];</span>
<span class="nc" id="L218">			results[0] = p2;</span>
		}
<span class="nc" id="L220">		tests.add(p2);</span>
		// mid point
<span class="nc" id="L222">		NPoint midPoint = midPoint(p1, p2);</span>
		// addRegionsIn2D(currentRegion, midPoint);
		try {
<span class="nc" id="L225">			addRegionsInND(currentRegion, midPoint);</span>
<span class="nc" id="L226">		} catch (Exception e) {</span>
<span class="nc" id="L227">			e.printStackTrace();</span>
		}
<span class="nc" id="L229">		return results;</span>
	}

	
	public void time() {
<span class="nc" id="L234">		int count = 0;</span>
<span class="nc" id="L235">		NRectRegion currentRegion = regionLists.get(0);</span>
<span class="nc" id="L236">		NPoint p = randomCreator.randomPoint(currentRegion);</span>
<span class="nc" id="L237">		tests.add(p);</span>
<span class="nc" id="L238">		count++;</span>
<span class="nc bnc" id="L239" title="All 2 branches missed.">		while (count &lt; tcCount) {</span>
<span class="nc" id="L240">			int maxIndex = findMaxRegion();</span>
<span class="nc" id="L241">			currentRegion = regionLists.remove(maxIndex);</span>
<span class="nc" id="L242">			NPoint alreadyPoint = hasPointInRegion(currentRegion);</span>
<span class="nc" id="L243">			NPoint p1 = null;</span>
<span class="nc" id="L244">			NPoint p2 = null;</span>

<span class="nc bnc" id="L246" title="All 2 branches missed.">			if (alreadyPoint == null) {</span>
<span class="nc" id="L247">				p1 = randomCreator.randomPoint(currentRegion);</span>
<span class="nc" id="L248">				tests.add(p1);</span>
<span class="nc" id="L249">				count++;</span>
<span class="nc bnc" id="L250" title="All 2 branches missed.">				if (count &gt; tcCount) {</span>
<span class="nc" id="L251">					break;</span>
				}
				// randomly generate k candidate points in curReg, and store
				// them in SC;
				// SC is the test input candidate set
				// select the point from SC which is the farthest from P1 as the
				// second test
				// input P2;
<span class="nc" id="L259">				NPoint[] SC = new NPoint[k];</span>
<span class="nc" id="L260">				double maxLength = 0.0;</span>

<span class="nc bnc" id="L262" title="All 2 branches missed.">				for (int i = 0; i &lt; k; i++) {</span>
<span class="nc" id="L263">					SC[i] = randomCreator.randomPoint(currentRegion);</span>
<span class="nc" id="L264">					double length = calTwoPointDistance(p1, SC[i]);</span>
<span class="nc bnc" id="L265" title="All 2 branches missed.">					if (maxLength &lt; length) {</span>
<span class="nc" id="L266">						maxLength = length;</span>
<span class="nc" id="L267">						p2 = SC[i];</span>
					}
				}

<span class="nc" id="L271">			} else {</span>
<span class="nc" id="L272">				p1 = alreadyPoint;</span>
<span class="nc" id="L273">				NPoint[] SC = new NPoint[k];</span>
<span class="nc" id="L274">				double maxLength = 0.0;</span>

<span class="nc bnc" id="L276" title="All 2 branches missed.">				for (int i = 0; i &lt; k; i++) {</span>
<span class="nc" id="L277">					SC[i] = randomCreator.randomPoint(currentRegion);</span>
<span class="nc" id="L278">					double length = calTwoPointDistance(p1, SC[i]);</span>
<span class="nc bnc" id="L279" title="All 2 branches missed.">					if (maxLength &lt; length) {</span>
<span class="nc" id="L280">						maxLength = length;</span>
<span class="nc" id="L281">						p2 = SC[i];</span>
					}
				}
			}
			//
<span class="nc" id="L286">			tests.add(p2);</span>
<span class="nc" id="L287">			count++;</span>
<span class="nc bnc" id="L288" title="All 2 branches missed.">			if (count &gt; tcCount) {</span>
<span class="nc" id="L289">				break;</span>
			}
			// mid point
<span class="nc" id="L292">			NPoint midPoint = midPoint(p1, p2);</span>
			try {
<span class="nc" id="L294">				addRegionsInND(currentRegion, midPoint);</span>
<span class="nc" id="L295">			} catch (Exception e) {</span>
<span class="nc" id="L296">				e.printStackTrace();</span>
			}
		}
<span class="nc" id="L299">	}</span>

	public static double testFm() {
<span class="nc" id="L302">		int d = 2;</span>
<span class="nc" id="L303">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L304">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L305">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L307">		int times = 2000;</span>

<span class="nc" id="L309">		int temp = 0;</span>
<span class="nc" id="L310">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L311">		failurePattern.fail_rate = 0.01;</span>
<span class="nc" id="L312">		long sums = 0;</span>
<span class="nc" id="L313">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L314" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L315">			ART_TPP rt = new ART_TPP(min, max, new Random(i * 3), failurePattern, 10);</span>
<span class="nc" id="L316">			temp = rt.run();</span>
<span class="nc" id="L317">			sums += temp;</span>
		}
<span class="nc" id="L319">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L320">		double fm = sums / (double) times;</span>
<span class="nc" id="L321">		System.out.println(&quot;fm:&quot; + fm + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
<span class="nc" id="L322">		return fm;</span>
	}

	public static double[] testEm(int dimension, double failrate) {
<span class="nc" id="L326">		int d = dimension;</span>
<span class="nc" id="L327">		int emTime = 6;</span>
<span class="nc" id="L328">		double result[] = new double[emTime];</span>
<span class="nc" id="L329">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L330">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L331">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L333">		int times = 2000;</span>

<span class="nc" id="L335">		int temp = 0;</span>
<span class="nc" id="L336">		int kk = 10;</span>
<span class="nc" id="L337">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L338">		failurePattern.fail_rate = failrate;</span>
<span class="nc bnc" id="L339" title="All 2 branches missed.">		for (int k = 0; k &lt; emTime; k++) {</span>
<span class="nc" id="L340">			long sums = 0;</span>
<span class="nc" id="L341">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L342" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L343">				ART_TPP rt = new ART_TPP(min, max, new Random(i * 3), failurePattern, 10);</span>
<span class="nc" id="L344">				rt.emCount = (k + 1) * 500;</span>
<span class="nc" id="L345">				temp = rt.em();</span>
<span class="nc" id="L346">				sums += temp;</span>
			}
<span class="nc" id="L348">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L349">			double em = sums / (double) times;</span>
<span class="nc" id="L350">			result[k] = em;</span>
<span class="nc" id="L351">			System.out.println(&quot;em:&quot; + em + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
		}
<span class="nc" id="L353">		System.out.println();</span>
<span class="nc" id="L354">		return result;</span>
	}

	public static double testTCTime(int d, int tcCount) {
<span class="nc" id="L358">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L359">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L360">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L362">		int times = 1;</span>

<span class="nc" id="L364">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L365">		failurePattern.fail_rate = 0.001;</span>
<span class="nc" id="L366">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L367" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L368">			ART_TPP rt = new ART_TPP(min, max, new Random(i * 3), failurePattern, 10);</span>
<span class="nc" id="L369">			rt.tcCount = tcCount;</span>
<span class="nc" id="L370">			rt.time2();</span>
		}
<span class="nc" id="L372">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L373">		System.out.println((endTime - startTime) / (double) times);</span>
<span class="nc" id="L374">		return ((endTime - startTime) / (double) times);</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>