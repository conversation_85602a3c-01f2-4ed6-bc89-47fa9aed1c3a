<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>gammq.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">gammq.java</span></div><h1>gammq.java</h1><pre class="source lang-java linenums">package tested;

import util.TestProgram;

<span class="nc" id="L5">public class gammq {</span>

<span class="nc" id="L7">	public static  double[] min = { 0.0, 0.0 };</span>
<span class="nc" id="L8">	public static  double[] max = { 1700.0, 40.0 };</span>
<span class="nc" id="L9">	public static  double failureRate = 0.000690;</span>
<span class="nc" id="L10">	public  static int Dimension = 2;</span>

	public boolean isCorrect(double x, double y) {
<span class="nc" id="L13">		return TestProgram.test_gammq(x, y);</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>