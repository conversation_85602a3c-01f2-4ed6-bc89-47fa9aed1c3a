<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_RP_ND</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.art_tp._ND</a> &gt; <span class="el_class">ART_TP_RP_ND</span></div><h1>ART_TP_RP_ND</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">931 of 931</td><td class="ctr2">0%</td><td class="bar">62 of 62</td><td class="ctr2">0%</td><td class="ctr1">46</td><td class="ctr2">46</td><td class="ctr1">138</td><td class="ctr2">138</td><td class="ctr1">15</td><td class="ctr2">15</td></tr></tfoot><tbody><tr><td id="a7"><a href="ART_TP_RP_ND.java.html#L144" class="el_method">genEachNext(double[], double[], double[], double[], double[])</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="154" alt="154"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="100" height="10" title="10" alt="10"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">6</td><td class="ctr2" id="g1">6</td><td class="ctr1" id="h1">19</td><td class="ctr2" id="i1">19</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a9"><a href="ART_TP_RP_ND.java.html#L173" class="el_method">genUorV(NRectRegion, String)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="94" height="10" title="121" alt="121"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">7</td><td class="ctr2" id="g0">7</td><td class="ctr1" id="h3">16</td><td class="ctr2" id="i3">16</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a13"><a href="ART_TP_RP_ND.java.html#L238" class="el_method">run()</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="88" height="10" title="113" alt="113"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="60" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f4">4</td><td class="ctr2" id="g4">4</td><td class="ctr1" id="h0">27</td><td class="ctr2" id="i0">27</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a10"><a href="ART_TP_RP_ND.java.html#L31" class="el_method">main(String[])</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="67" height="10" title="87" alt="87"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h2">17</td><td class="ctr2" id="i2">17</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a11"><a href="ART_TP_RP_ND.java.html#L200" class="el_method">maxAllTC()</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="65" height="10" title="84" alt="84"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="100" height="10" title="10" alt="10"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h5">10</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a12"><a href="ART_TP_RP_ND.java.html#L219" class="el_method">minAllTC()</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="65" height="10" title="84" alt="84"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="100" height="10" title="10" alt="10"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f3">6</td><td class="ctr2" id="g3">6</td><td class="ctr1" id="h6">10</td><td class="ctr2" id="i6">10</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a14"><a href="ART_TP_RP_ND.java.html#L290" class="el_method">updateRegions(int, NPoint)</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="56" height="10" title="72" alt="72"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d8"><img src="../.resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h4">11</td><td class="ctr2" id="i4">11</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a3"><a href="ART_TP_RP_ND.java.html#L105" class="el_method">calEachIntEC2(double, double, double, double)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="38" height="10" title="50" alt="50"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d10"/><td class="ctr2" id="e10">n/a</td><td class="ctr1" id="f10">1</td><td class="ctr2" id="g10">1</td><td class="ctr1" id="h11">2</td><td class="ctr2" id="i11">2</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a1"><a href="ART_TP_RP_ND.java.html#L62" class="el_method">calAllIntEC(double[], double[], double[], double[])</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="36" height="10" title="47" alt="47"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f5">3</td><td class="ctr2" id="g5">3</td><td class="ctr1" id="h8">7</td><td class="ctr2" id="i8">7</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a2"><a href="ART_TP_RP_ND.java.html#L101" class="el_method">calEachIntEC(double, double, double, double)</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="28" height="10" title="36" alt="36"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h12">1</td><td class="ctr2" id="i12">1</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a5"><a href="ART_TP_RP_ND.java.html#L110" class="el_method">findMaxRegion()</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="24" height="10" title="31" alt="31"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="40" height="10" title="4" alt="4"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f6">3</td><td class="ctr2" id="g6">3</td><td class="ctr1" id="h7">8</td><td class="ctr2" id="i7">8</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a6"><a href="ART_TP_RP_ND.java.html#L136" class="el_method">genEachIntEC(double[], double[], double[], double[])</a></td><td class="bar" id="b11"><img src="../.resources/redbar.gif" width="24" height="10" title="31" alt="31"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d9"><img src="../.resources/redbar.gif" width="20" height="10" title="2" alt="2"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h9">4</td><td class="ctr2" id="i9">4</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a0"><a href="ART_TP_RP_ND.java.html#L53" class="el_method">ART_TP_RP_ND(double[], double[], FailurePattern, Random)</a></td><td class="bar" id="b12"><img src="../.resources/redbar.gif" width="13" height="10" title="17" alt="17"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h10">4</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a4"><a href="ART_TP_RP_ND.java.html#L309" class="el_method">em()</a></td><td class="bar" id="b13"><img src="../.resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">1</td><td class="ctr2" id="i13">1</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a8"><a href="ART_TP_RP_ND.java.html#L315" class="el_method">generateNextTC()</a></td><td class="bar" id="b14"><img src="../.resources/redbar.gif" width="1" height="10" title="2" alt="2"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">1</td><td class="ctr2" id="i14">1</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>