<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>DDR_TD3_threshold.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.ddr._2D</a> &gt; <span class="el_source">DDR_TD3_threshold.java</span></div><h1>DDR_TD3_threshold.java</h1><pre class="source lang-java linenums">package test.simulations.ddr._2D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class DDR_TD3_threshold {
	public static void main(String[] args) {
<span class="nc" id="L10">		int times = 3000;</span>
<span class="nc" id="L11">		long sums = 0;</span>
<span class="nc" id="L12">		int temp = 0;</span>
<span class="nc" id="L13">		int s = 10;</span>
<span class="nc" id="L14">		int t = 10;</span>
		//////////////
<span class="nc" id="L16">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L17" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L18">			double min[] = { 0.0, 0.0 };</span>
<span class="nc" id="L19">			double max[] = { 1.0, 1.0 };</span>
<span class="nc" id="L20">			DDR_TD3_threshold rrt_od = new DDR_TD3_threshold(min, max, 0.75, s, t, 0.001, i * 3);// 0.002 refers to</span>
																									// failure rate
<span class="nc" id="L22">			temp = rrt_od.run();</span>
<span class="nc" id="L23">			sums += temp;</span>
		}
<span class="nc" id="L25">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L26">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L27">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L28">	}</span>
	double[] min;
	double[] max;
	double[] fail_start;
	double fail_rate;
	double R;
	int s;
	int t;
	int randomseed;
	double fail_regionS;

<span class="nc" id="L39">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	public DDR_TD3_threshold(double[] min, double[] max, double r, int s, int t, double fail_rate, int randomseed) {
<span class="nc" id="L42">		super();</span>
<span class="nc" id="L43">		this.min = min;</span>
<span class="nc" id="L44">		this.max = max;</span>
<span class="nc" id="L45">		R = r;</span>
<span class="nc" id="L46">		this.s = s;</span>
<span class="nc" id="L47">		this.t = t;</span>
<span class="nc" id="L48">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L49">		this.randomseed = randomseed;</span>
<span class="nc" id="L50">		this.fail_start = new double[this.min.length];</span>
<span class="nc" id="L51">		this.fail_regionS = fail_rate * (max[1] - min[1]) * (max[0] - min[0]);</span>
<span class="nc" id="L52">	}</span>

	public boolean isCorrect(TestCase p) {
<span class="nc" id="L55">		boolean isCorrect = true;</span>
<span class="nc bnc" id="L56" title="All 4 branches missed.">		if (p.p &gt; fail_start[0] &amp;&amp; p.p &lt; fail_start[0] + Math.sqrt(fail_regionS)) {</span>
<span class="nc bnc" id="L57" title="All 4 branches missed.">			if (p.q &gt; fail_start[1] &amp;&amp; p.q &lt; fail_start[1] + Math.sqrt(fail_regionS)) {</span>
<span class="nc" id="L58">				isCorrect = false;</span>
			}
		}
<span class="nc" id="L61">		return isCorrect;</span>
	}

	public TestCase randomTC(Random random) {
<span class="nc" id="L65">		TestCase temp = new TestCase();</span>
<span class="nc" id="L66">		double p = random.nextDouble() * (max[0] - min[0]) + min[0];</span>
<span class="nc" id="L67">		double q = random.nextDouble() * (max[1] - min[1]) + min[1];</span>
<span class="nc" id="L68">		temp.p = p;</span>
<span class="nc" id="L69">		temp.q = q;</span>
<span class="nc" id="L70">		return temp;</span>
	}

	public int run() {
<span class="nc" id="L74">		Random random = new Random(randomseed);</span>
		// 失效率的范围
<span class="nc" id="L76">		fail_start[0] = random.nextDouble() * (max[0] - min[0] - Math.sqrt(fail_regionS)) + min[0];</span>
<span class="nc" id="L77">		fail_start[1] = random.nextDouble() * (max[1] - min[1] - Math.sqrt(fail_regionS)) + min[1];</span>
<span class="nc" id="L78">		System.out.println(&quot;fail_rate:(&quot; + fail_start[0] + &quot;,&quot; + fail_start[1] + &quot;)&quot;);</span>
<span class="nc" id="L79">		int count = 0;// 记录测试用例数量</span>
<span class="nc" id="L80">		int _10CandidateCount = s;// 每十个一次的数量</span>
<span class="nc" id="L81">		int ttemp = 0;</span>
<span class="nc" id="L82">		TestCase p = randomTC(random);// 第一个测试用例</span>
<span class="nc" id="L83">		System.out.println(&quot;p0:&quot; + p.toString() + isCorrect(p));</span>
<span class="nc bnc" id="L84" title="All 2 branches missed.">		while (isCorrect(p)) {</span>

<span class="nc bnc" id="L86" title="All 2 branches missed.">			if (ttemp == t) {</span>
<span class="nc" id="L87">				_10CandidateCount = 0;</span>
<span class="nc" id="L88">				System.out.println(&quot;Candidate reset&quot;);</span>
			}
<span class="nc" id="L90">			ttemp = 0;</span>
			// double radius =Math.sqrt(a) R / (2 * _10CandidateCount);
<span class="nc" id="L92">			double radius = Math.sqrt(R * (max[1] - min[1]) * (max[0] - min[0]) / (Math.PI * (s + _10CandidateCount)));</span>
			// 生成s个候选测试用例,从s个候选测试用例中挑选符合要求的测试用例
<span class="nc" id="L94">			p = new TestCase();</span>
<span class="nc" id="L95">			boolean all_s_has_E_flag = true;</span>
<span class="nc" id="L96">			double TS2C[] = new double[s];</span>
<span class="nc" id="L97">			double Pvalue[] = new double[s];</span>
<span class="nc" id="L98">			double Qvalue[] = new double[s];</span>
<span class="nc bnc" id="L99" title="All 2 branches missed.">			for (int k = 0; k &lt; s; k++) {</span>
				// 生成一个候选测试用例
<span class="nc" id="L101">				TestCase ck = randomTC(random);</span>
				// System.out.println(&quot;ck&quot;+k+&quot;:&quot;+ck.toString()+isCorrect(ck));
<span class="nc" id="L103">				boolean this_ck_has_E_flag = false;</span>
<span class="nc" id="L104">				double min = Double.MAX_VALUE;</span>
<span class="nc bnc" id="L105" title="All 2 branches missed.">				for (int i = 0; i &lt; tests.size(); i++) {</span>
					// 没有在圈之中
					// if((Math.pow((ck.p-tests.get(i).p)*(ck.p-tests.get(i).p)+(ck.q-tests.get(i).q)*(ck.q-tests.get(i).q),
					// 0.5))&lt;radius){
					// if(Math.abs(p.p-tests.get(i).p)&lt;radius&amp;&amp;Math.abs(p.q-tests.get(i).q)&lt;radius){
<span class="nc" id="L110">					if ((Math.pow((ck.p - tests.get(i).p) * (ck.p - tests.get(i).p)</span>
<span class="nc bnc" id="L111" title="All 2 branches missed.">							+ (ck.q - tests.get(i).q) * (ck.q - tests.get(i).q), 0.5)) &lt; radius) {</span>
<span class="nc bnc" id="L112" title="All 2 branches missed.">						if (min &gt; Math.sqrt(Math.pow(ck.p - tests.get(i).p, 2) + Math.pow(ck.q - tests.get(i).q, 2))) {</span>
<span class="nc" id="L113">							min = Math.sqrt(Math.pow(ck.p - tests.get(i).p, 2) + Math.pow(ck.q - tests.get(i).q, 2));</span>
<span class="nc" id="L114">							TS2C[k] = min;</span>
<span class="nc" id="L115">							Pvalue[k] = ck.p;</span>
<span class="nc" id="L116">							Qvalue[k] = ck.q;</span>
						}
<span class="nc" id="L118">						this_ck_has_E_flag = true;</span>
					}

				}
<span class="nc bnc" id="L122" title="All 2 branches missed.">				if (!this_ck_has_E_flag) {</span>
<span class="nc" id="L123">					all_s_has_E_flag = false;</span>
<span class="nc" id="L124">					p = new TestCase();</span>
<span class="nc" id="L125">					p.p = ck.p;</span>
<span class="nc" id="L126">					p.q = ck.q;</span>
<span class="nc" id="L127">					System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.toString() + isCorrect(p) + &quot; rrt&quot;);</span>
<span class="nc bnc" id="L128" title="All 2 branches missed.">					if (!isCorrect(p)) {</span>
<span class="nc" id="L129">						return count;</span>
					} else {
<span class="nc" id="L131">						count++;</span>
<span class="nc" id="L132">						ttemp++;</span>
<span class="nc" id="L133">						tests.add(p);</span>
					}
				}
			}
<span class="nc bnc" id="L137" title="All 2 branches missed.">			if (all_s_has_E_flag) {</span>
<span class="nc" id="L138">				double max = 0;</span>
<span class="nc" id="L139">				int index = 0;</span>
<span class="nc bnc" id="L140" title="All 2 branches missed.">				for (int i = 0; i &lt; TS2C.length; i++) {</span>
<span class="nc bnc" id="L141" title="All 2 branches missed.">					if (max &lt; TS2C[i]) {</span>
<span class="nc" id="L142">						max = TS2C[i];</span>
<span class="nc" id="L143">						index = i;</span>
					}
				}
<span class="nc" id="L146">				p = new TestCase();</span>
<span class="nc" id="L147">				p.p = Pvalue[index];</span>
<span class="nc" id="L148">				p.q = Qvalue[index];</span>
<span class="nc" id="L149">				System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.toString() + isCorrect(p) + &quot; fscs&quot;);</span>
<span class="nc bnc" id="L150" title="All 2 branches missed.">				if (!isCorrect(p)) {</span>
<span class="nc" id="L151">					return count;</span>
				} else {
<span class="nc" id="L153">					count++;</span>
<span class="nc" id="L154">					tests.add(p);</span>
				}
			}
<span class="nc" id="L157">			_10CandidateCount++;</span>
		}
<span class="nc" id="L159">		return count;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>