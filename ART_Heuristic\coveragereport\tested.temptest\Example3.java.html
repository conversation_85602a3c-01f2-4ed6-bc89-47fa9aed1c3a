<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>Example3.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested.temptest</a> &gt; <span class="el_source">Example3.java</span></div><h1>Example3.java</h1><pre class="source lang-java linenums">
package tested.temptest;

import java.util.LinkedList;

<span class="nc" id="L6">public class Example3 {</span>

	public static void main(String args[]) {
<span class="nc" id="L9">		LinkedList&lt;String&gt; mylist = new LinkedList&lt;&gt;();</span>
<span class="nc" id="L10">		mylist.add(&quot;is&quot;);</span>
<span class="nc" id="L11">		mylist.add(&quot;a&quot;);</span>
<span class="nc" id="L12">		int number = mylist.size();</span>
<span class="nc" id="L13">		System.out.println(&quot;现在链表中有&quot; + number + &quot;个节点:&quot;);</span>
<span class="nc bnc" id="L14" title="All 2 branches missed.">		for (int i = 0; i &lt; number; i++) {</span>
<span class="nc" id="L15">			String temp = (String) mylist.get(i);</span>
<span class="nc" id="L16">			System.out.println(&quot;第&quot; + i + &quot;节点中的数据:&quot; + temp);</span>
		}
<span class="nc" id="L18">		mylist.addFirst(&quot;It&quot;);</span>
<span class="nc" id="L19">		mylist.addLast(&quot;door&quot;);</span>
<span class="nc" id="L20">	}</span>
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>