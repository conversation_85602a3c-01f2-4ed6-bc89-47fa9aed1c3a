<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>StdDraw.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util.draw</a> &gt; <span class="el_source">StdDraw.java</span></div><h1>StdDraw.java</h1><pre class="source lang-java linenums">package util.draw;

/******************************************************************************
 *  Compilation:  javac StdDraw.java
 *  Execution:    java StdDraw
 *  Dependencies: none
 *
 *  Standard drawing library. This class provides a basic capability for
 *  creating drawings with your programs. It uses a simple graphics model that
 *  allows you to create drawings consisting of points, lines, and curves
 *  in a window on your computer and to save the drawings to a file.
 *
 *  Todo
 *  ----
 *    -  Add support for gradient fill, etc.
 *    -  Fix setCanvasSize() so that it can only be called once.
 *    -  On some systems, drawing a line (or other shape) that extends way
 *       beyond canvas (e.g., to infinity) dimensions does not get drawn.
 *
 *  Remarks
 *  -------
 *    -  don't use AffineTransform for rescaling since it inverts
 *       images and strings
 *
 ******************************************************************************/

import java.awt.BasicStroke;
import java.awt.Color;
import java.awt.FileDialog;
import java.awt.Font;
import java.awt.FontMetrics;
import java.awt.Graphics2D;
import java.awt.Image;
import java.awt.MediaTracker;
import java.awt.RenderingHints;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.ActionListener;
import java.awt.event.KeyEvent;
import java.awt.event.KeyListener;
import java.awt.event.MouseEvent;
import java.awt.event.MouseListener;
import java.awt.event.MouseMotionListener;
import java.awt.geom.Arc2D;
import java.awt.geom.Ellipse2D;
import java.awt.geom.GeneralPath;
import java.awt.geom.Line2D;
import java.awt.geom.Rectangle2D;
import java.awt.image.BufferedImage;
import java.awt.image.DirectColorModel;
import java.awt.image.WritableRaster;
import java.io.File;
import java.io.IOException;
import java.net.URL;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.NoSuchElementException;
import java.util.TreeSet;

import javax.imageio.ImageIO;
import javax.swing.ImageIcon;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JMenu;
import javax.swing.JMenuBar;
import javax.swing.JMenuItem;
import javax.swing.KeyStroke;

/**
 * The {@code StdDraw} class provides a basic capability for creating drawings
 * with your programs. It uses a simple graphics model that allows you to create
 * drawings consisting of points, lines, squares, circles, and other geometric
 * shapes in a window on your computer and to save the drawings to a file.
 * Standard drawing also includes facilities for text, color, pictures, and
 * animation, along with user interaction via the keyboard and mouse.
 * &lt;p&gt;
 * &lt;b&gt;Getting started.&lt;/b&gt; To use standard drawing, you must have
 * &lt;tt&gt;StdDraw.class&lt;/tt&gt; in your Java classpath. If you used our autoinstaller,
 * you should be all set. Otherwise, download &lt;a href =
 * &quot;http://introcs.cs.princeton.edu/java/stdlib/StdDraw.java&quot;&gt;StdDraw.java&lt;/a&gt;
 * and put a copy in your working directory.
 * &lt;p&gt;
 * Now, type the following short program into your editor:
 * 
 * &lt;pre&gt;
 * public class TestStdDraw {
 * 	public static void main(String[] args) {
 * 		StdDraw.setPenRadius(0.05);
 * 		StdDraw.setPenColor(StdDraw.BLUE);
 * 		StdDraw.point(0.5, 0.5);
 * 		StdDraw.setPenColor(StdDraw.MAGENTA);
 * 		StdDraw.line(0.2, 0.2, 0.8, 0.2);
 * 	}
 * }
 * &lt;/pre&gt;
 * 
 * If you compile and execute the program, you should see a window appear with a
 * thick magenta line and a blue point. This program illustrates the two main
 * types of methods in standard drawing&amp;mdash;methods that draw geometric shapes
 * and methods that control drawing parameters. The methods
 * {@code StdDraw.line()} and {@code StdDraw.point()} draw lines and points; the
 * methods {@code StdDraw.setPenRadius()} and {@code StdDraw.setPenColor()}
 * control the line thickness and color.
 * &lt;p&gt;
 * &lt;b&gt;Points and lines.&lt;/b&gt; You can draw points and line segments with the
 * following methods:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #point(double x, double y)}
 * &lt;li&gt;{@link #line(double x1, double y1, double x2, double y2)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * The &lt;em&gt;x&lt;/em&gt;- and &lt;em&gt;y&lt;/em&gt;-coordinates must be in the drawing area
 * (between 0 and 1 and by default) or the points and lines will not be visible.
 * &lt;p&gt;
 * &lt;b&gt;Squares, circles, rectangles, and ellipses.&lt;/b&gt; You can draw squares,
 * circles, rectangles, and ellipses using the following methods:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #circle(double x, double y, double radius)}
 * &lt;li&gt;
 * {@link #ellipse(double x, double y, double semiMajorAxis, double semiMinorAxis)}
 * &lt;li&gt;{@link #square(double x, double y, double radius)}
 * &lt;li&gt;
 * {@link #rectangle(double x, double y, double halfWidth, double halfHeight)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * All of these methods take as arguments the location and size of the shape.
 * The location is always specified by the &lt;em&gt;x&lt;/em&gt;- and
 * &lt;em&gt;y&lt;/em&gt;-coordinates of its &lt;em&gt;center&lt;/em&gt;. The size of a circle is
 * specified by its radius and the size of an ellipse is specified by the
 * lengths of its semi-major and semi-minor axes. The size of a square or
 * rectangle is specified by its half-width or half-height. The convention for
 * drawing squares and rectangles is parallel to those for drawing circles and
 * ellipses, but may be unexpected to the uninitiated.
 * &lt;p&gt;
 * The methods above trace outlines of the given shapes. The following methods
 * draw filled versions:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #filledCircle(double x, double y, double radius)}
 * &lt;li&gt;
 * {@link #filledEllipse(double x, double y, double semiMajorAxis, double semiMinorAxis)}
 * &lt;li&gt;{@link #filledSquare(double x, double y, double radius)}
 * &lt;li&gt;
 * {@link #filledRectangle(double x, double y, double halfWidth, double halfHeight)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * &lt;b&gt;Circular arcs.&lt;/b&gt; You can draw circular arcs with the following method:
 * &lt;ul&gt;
 * &lt;li&gt;
 * {@link #arc(double x, double y, double radius, double angle1, double angle2)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * The arc is from the circle centered at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;) of the
 * specified radius. The arc extends from angle1 to angle2. By convention, the
 * angles are &lt;em&gt;polar&lt;/em&gt; (counterclockwise angle from the &lt;em&gt;x&lt;/em&gt;-axis)
 * and represented in degrees. For example,
 * {@code StdDraw.arc(0.0, 0.0, 1.0, 0, 90)} draws the arc of the unit circle
 * from 3 o'clock (0 degrees) to 12 o'clock (90 degrees).
 * &lt;p&gt;
 * &lt;b&gt;Polygons.&lt;/b&gt; You can draw polygons with the following methods:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #polygon(double[] x, double[] y)}
 * &lt;li&gt;{@link #filledPolygon(double[] x, double[] y)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * The points in the polygon are ({@code x[i]}, {@code y[i]}). For example, the
 * following code fragment draws a filled diamond with vertices (0.1, 0.2),
 * (0.2, 0.3), (0.3, 0.2), and (0.2, 0.1):
 * 
 * &lt;pre&gt;
 * double[] x = { 0.1, 0.2, 0.3, 0.2 };
 * double[] y = { 0.2, 0.3, 0.2, 0.1 };
 * StdDraw.filledPolygon(x, y);
 * &lt;/pre&gt;
 * &lt;p&gt;
 * &lt;b&gt;Pen size.&lt;/b&gt; The pen is circular, so that when you set the pen radius to
 * &lt;em&gt;r&lt;/em&gt; and draw a point, you get a circle of radius &lt;em&gt;r&lt;/em&gt;. Also,
 * lines are of thickness 2&lt;em&gt;r&lt;/em&gt; and have rounded ends. The default pen
 * radius is 0.005 and is not affected by coordinate scaling. This default pen
 * radius is about 1/200 the width of the default canvas, so that if you draw
 * 100 points equally spaced along a horizontal or vertical line, you will be
 * able to see individual circles, but if you draw 200 such points, the result
 * will look like a line.
 * &lt;ul&gt;
 * &lt;li&gt;{@link #setPenRadius(double radius)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * For example, {@code StdDraw.setPenRadius(0.025)} makes the thickness of the
 * lines and the size of the points to be five times the 0.005 default. To draw
 * points with the minimum possible radius (one pixel on typical displays), set
 * the pen radius to 0.0.
 * &lt;p&gt;
 * &lt;b&gt;Pen color.&lt;/b&gt; All geometric shapes (such as points, lines, and circles)
 * are drawn using the current pen color. By default, it is black. You can
 * change the pen color with the following methods:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #setPenColor(int red, int green, int blue)}
 * &lt;li&gt;{@link #setPenColor(Color color)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * The first method allows you to specify colors using the RGB color system.
 * This &lt;a href = &quot;http://johndyer.name/lab/colorpicker/&quot;&gt;color picker&lt;/a&gt; is a
 * convenient way to find a desired color. The second method allows you to
 * specify colors using the {@link Color} data type that is discussed in Chapter
 * 3. Until then, you can use this method with one of these predefined colors in
 * standard drawing: {@link #BLACK}, {@link #BLUE}, {@link #CYAN},
 * {@link #DARK_GRAY}, {@link #GRAY}, {@link #GREEN}, {@link #LIGHT_GRAY},
 * {@link #MAGENTA}, {@link #ORANGE}, {@link #PINK}, {@link #RED},
 * {@link #WHITE}, and {@link #YELLOW}. For example,
 * {@code StdDraw.setPenColor(StdDraw.MAGENTA)} sets the pen color to magenta.
 * &lt;p&gt;
 * &lt;b&gt;Canvas size.&lt;/b&gt; By default, all drawing takes places in a 512-by-512
 * canvas. The canvas does not include the window title or window border. You
 * can change the size of the canvas with the following method:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #setCanvasSize(int width, int height)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * This sets the canvas size to be &lt;em&gt;width&lt;/em&gt;-by-&lt;em&gt;height&lt;/em&gt; pixels. It
 * also erases the current drawing and resets the coordinate system, pen radius,
 * pen color, and font back to their default values. Ordinarly, this method is
 * called once, at the very beginning of a program. For example,
 * {@code StdDraw.setCanvasSize(800, 800)} sets the canvas size to be 800-by-800
 * pixels.
 * &lt;p&gt;
 * &lt;b&gt;Canvas scale and coordinate system.&lt;/b&gt; By default, all drawing takes
 * places in the unit square, with (0, 0) at lower left and (1, 1) at upper
 * right. You can change the default coordinate system with the following
 * methods:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #setXscale(double xmin, double xmax)}
 * &lt;li&gt;{@link #setYscale(double ymin, double ymax)}
 * &lt;li&gt;{@link #setScale(double min, double max)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * The arguments are the coordinates of the minimum and maximum &lt;em&gt;x&lt;/em&gt;- or
 * &lt;em&gt;y&lt;/em&gt;-coordinates that will appear in the canvas. For example, if you
 * wish to use the default coordinate system but leave a small margin, you can
 * call {@code StdDraw.setScale(-.05, 1.05)}.
 * &lt;p&gt;
 * These methods change the coordinate system for subsequent drawing commands;
 * they do not affect previous drawings. These methods do not change the canvas
 * size; so, if the &lt;em&gt;x&lt;/em&gt;- and &lt;em&gt;y&lt;/em&gt;-scales are different, squares
 * will become rectangles and circles will become ellipsoidal.
 * &lt;p&gt;
 * &lt;b&gt;Text.&lt;/b&gt; You can use the following methods to annotate your drawings with
 * text:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #text(double x, double y, String text)}
 * &lt;li&gt;{@link #text(double x, double y, String text, double degrees)}
 * &lt;li&gt;{@link #textLeft(double x, double y, String text)}
 * &lt;li&gt;{@link #textRight(double x, double y, String text)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * The first two methods write the specified text in the current font, centered
 * at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;). The second method allows you to rotate the text.
 * The last two methods either left- or right-align the text at (&lt;em&gt;x&lt;/em&gt;,
 * &lt;em&gt;y&lt;/em&gt;).
 * &lt;p&gt;
 * The default font is a Sans Serif font with point size 16. You can use the
 * following method to change the font:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #setFont(Font font)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * You use the {@link Font} data type to specify the font. This allows you to
 * choose the face, size, and style of the font. For example, the following code
 * fragment sets the font to Arial Bold, 60 point.
 * 
 * &lt;pre&gt;
 * Font font = new Font(&quot;Arial&quot;, Font.BOLD, 60);
 * StdDraw.setFont(font);
 * StdDraw.text(0.5, 0.5, &quot;Hello, World&quot;);
 * &lt;/pre&gt;
 * &lt;p&gt;
 * &lt;b&gt;Images.&lt;/b&gt; You can use the following methods to add images to your
 * drawings:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #picture(double x, double y, String filename)}
 * &lt;li&gt;{@link #picture(double x, double y, String filename, double degrees)}
 * &lt;li&gt;{@link #picture(double x, double y, String filename, double width)}
 * &lt;li&gt;
 * {@link #picture(double x, double y, String filename, double width, double degrees)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * These methods draw the specified image, centered at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;).
 * The supported image formats are JPEG, PNG, and GIF. The image will display at
 * its native size, independent of the coordinate system. Optionally, you can
 * rotate the image a specified number of degrees counterclockwise or rescale it
 * to fit inside a width-by-height pixel bounding box.
 * &lt;p&gt;
 * &lt;b&gt;Saving to a file.&lt;/b&gt; You save your image to a file using the &lt;em&gt;File -&gt;
 * Save&lt;/em&gt; menu option. You can also save a file programatically using the
 * following method:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #save(String filename)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * The supported image formats are JPEG and PNG. The filename must have either
 * the extension .jpg or .png. We recommend using PNG for drawing that consist
 * solely of geometric shapes and JPEG for drawings that contains pictures.
 * &lt;p&gt;
 * &lt;b&gt;Clearing the canvas.&lt;/b&gt; To clear the entire drawing canvas, you can use
 * the following methods:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #clear()}
 * &lt;li&gt;{@link #clear(Color color)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * The first method clears the canvas to white; the second method allows you to
 * specify a color of your choice. For example,
 * {@code StdDraw.clear(StdDraw.LIGHT_GRAY)} clears the canvas to a shade of
 * gray. Most often, these two methods are used in conjunction with animation
 * mode.
 * &lt;p&gt;
 * &lt;b&gt;Animations.&lt;/b&gt; Animation mode is one of the trickier features of standard
 * drawing. The following two methods control the way in which objects are
 * drawn:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #show()}
 * &lt;li&gt;{@link #show(int t)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * By default, animation mode is off, which means that as soon as you call a
 * drawing method&amp;mdash;such as {@code point()} or {@code line()}&amp;mdash;the
 * results appear on the screen. {@code StdDraw.show()} turns off animation
 * mode.
 * &lt;p&gt;
 * You can call {@link #show(int t)} to turn on animation mode. This defers all
 * drawing to the screen until you are aready to display them. Once you are
 * ready to display them, you call {@link #show(int t)} again, which transfer
 * the offscreen drawing to the screen and waits for the specified number of
 * milliseconds. In conjuction with {@link #clear()}, you can create the
 * illusion of movement by iterating the following three steps:
 * &lt;ul&gt;
 * &lt;li&gt;Clear the background canvas.
 * &lt;li&gt;Draw geometric objects.
 * &lt;li&gt;Show the drawing and wait for a short while.
 * &lt;/ul&gt;
 * &lt;p&gt;
 * Waiting for a short while is essential; otherwise, the drawing will appear
 * and disappear so quickly that your animation will flicker.
 * &lt;p&gt;
 * Here is a simple example of an animation:
 * &lt;p&gt;
 * &lt;b&gt;Keyboard and mouse inputs.&lt;/b&gt; Standard drawing has very basic support for
 * keyboard and mouse input. It is much less powerful than most user interface
 * libraries provide, but also much simpler. You can use the following methods
 * to intercept mouse events:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #mousePressed()}
 * &lt;li&gt;{@link #mouseX()}
 * &lt;li&gt;{@link #mouseY()}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * The first method tells you whether a mouse button is currently being pressed.
 * The last two methods tells you the &lt;em&gt;x&lt;/em&gt;- and &lt;em&gt;y&lt;/em&gt;-coordinates of
 * the mouse's current position, using the same coordinate system as the canvas
 * (the unit square, by default). You should use these methods in an animation
 * loop that waits a short while before trying to poll the mouse for its current
 * state. You can use the following methods to intercept keyboard events:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #hasNextKeyTyped()}
 * &lt;li&gt;{@link #nextKeyTyped()}
 * &lt;li&gt;{@link #isKeyPressed(int keycode)}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * If the user types lots of keys, they will be saved in a list until you
 * process them. The first method tells you whether the user has typed a key
 * (that your program has not yet processed). The second method returns the next
 * key that the user typed (that your program has not yet processed) and removes
 * it from the list of saved keystrokes. The third method tells you whether a
 * key is currently being pressed.
 * &lt;p&gt;
 * &lt;b&gt;Accessing control parameters.&lt;/b&gt; You can use the following methods to
 * access the current pen color, pen radius, and font:
 * &lt;ul&gt;
 * &lt;li&gt;{@link #getPenColor()}
 * &lt;li&gt;{@link #getPenRadius()}
 * &lt;li&gt;{@link #getFont()}
 * &lt;/ul&gt;
 * &lt;p&gt;
 * These methods are useful when you want to temporarily change a control
 * parameter and reset it back to its original value.
 * &lt;p&gt;
 * &lt;b&gt;Corner cases.&lt;/b&gt; To avoid clutter, the API doesn't explicitly refer to
 * arguments that are null, infinity, or NaN.
 * &lt;ul&gt;
 * &lt;li&gt;Any method that is passed a {@code null} argument will throw a
 * {@link NullPointerException}.
 * &lt;li&gt;Except as noted in the APIs, drawing an object outside (or partly
 * outside) the canvas is permitted&amp;mdash;however, only the part of the object
 * that appears inside the canvas will be visible.
 * &lt;li&gt;Except as noted in the APIs, all methods accept {@link Double#NaN},
 * {@link Double#POSITIVE_INFINITY}, and {@link Double#NEGATIVE_INFINITY} as
 * arugments. An object drawn with an &lt;em&gt;x&lt;/em&gt;- or &lt;em&gt;y&lt;/em&gt;-coordinate that
 * is NaN will behave as if it is outside the canvas, and will not be visible.
 * &lt;/ul&gt;
 * &lt;p&gt;
 * &lt;b&gt;Performance tricks.&lt;/b&gt; Standard drawing is capable of drawing large
 * amounts of data. Here are a few tricks and tips:
 * &lt;ul&gt;
 * &lt;li&gt;Use &lt;em&gt;animation mode&lt;/em&gt; for static drawing with a large number of
 * objects. That is, call {@code StdDraw.show(0)} before and after the sequence
 * of drawing commands. The bottleneck operation is not drawing the geometric
 * shapes but rather drawing them to the screen. By using animation mode, you
 * draw all of the shapes to an offscreen buffer, then copy them all at once to
 * the screen.
 * &lt;li&gt;When using &lt;em&gt;animation mode&lt;/em&gt;, call {@code show()} only once per
 * frame, not after drawing each object.
 * &lt;li&gt;If you call {@code picture()} multiple times with the same filename, Java
 * will cache the image, so you do not incur the cost of reading from a file
 * each time.
 * &lt;li&gt;Do not call {@code setFont()} in an animation loop (unless you really
 * need to change the font in each iteration). It can cause flicker.
 * &lt;/ul&gt;
 * &lt;p&gt;
 * &lt;b&gt;Known bugs and issues.&lt;/b&gt;
 * &lt;ul&gt;
 * &lt;li&gt;The {@code picture()} methods may not draw the portion of the image that
 * is inside the canvas if the center point (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;) is outside
 * the canvas. This bug appears only on some systems.
 * &lt;li&gt;Some methods may not draw the portion of the geometric object that is
 * inside the canvas if the &lt;em&gt;x&lt;/em&gt;- or &lt;em&gt;y&lt;/em&gt;-coordinates are infinite.
 * This bug appears only on some systems.
 * &lt;/ul&gt;
 * &lt;p&gt;
 * &lt;b&gt;Reference.&lt;/b&gt; For additional documentation, see
 * &lt;a href=&quot;http://introcs.cs.princeton.edu/15inout&quot;&gt;Section 1.5&lt;/a&gt; of
 * &lt;em&gt;Introduction to Programming in Java: An Interdisciplinary Approach&lt;/em&gt;
 * by Robert Sedgewick and Kevin Wayne.
 *
 * <AUTHOR> Sedgewick
 * <AUTHOR> Wayne
 */
public final class StdDraw implements ActionListener, MouseListener, MouseMotionListener, KeyListener {

	/**
	 * The color black.
	 */
<span class="nc" id="L440">	public static final Color BLACK = Color.BLACK;</span>

	/**
	 * The color blue.
	 */
<span class="nc" id="L445">	public static final Color BLUE = Color.BLUE;</span>

	/**
	 * The color cyan.
	 */
<span class="nc" id="L450">	public static final Color CYAN = Color.CYAN;</span>

	/**
	 * The color dark gray.
	 */
<span class="nc" id="L455">	public static final Color DARK_GRAY = Color.DARK_GRAY;</span>

	/**
	 * The color gray.
	 */
<span class="nc" id="L460">	public static final Color GRAY = Color.GRAY;</span>

	/**
	 * The color green.
	 */
<span class="nc" id="L465">	public static final Color GREEN = Color.GREEN;</span>

	/**
	 * The color light gray.
	 */
<span class="nc" id="L470">	public static final Color LIGHT_GRAY = Color.LIGHT_GRAY;</span>

	/**
	 * The color magenta.
	 */
<span class="nc" id="L475">	public static final Color MAGENTA = Color.MAGENTA;</span>

	/**
	 * The color orange.
	 */
<span class="nc" id="L480">	public static final Color ORANGE = Color.ORANGE;</span>

	/**
	 * The color pink.
	 */
<span class="nc" id="L485">	public static final Color PINK = Color.PINK;</span>

	/**
	 * The color red.
	 */
<span class="nc" id="L490">	public static final Color RED = Color.RED;</span>

	/**
	 * The color white.
	 */
<span class="nc" id="L495">	public static final Color WHITE = Color.WHITE;</span>

	/**
	 * The color yellow.
	 */
<span class="nc" id="L500">	public static final Color YELLOW = Color.YELLOW;</span>

	/**
	 * Shade of blue used in &lt;em&gt;Introduction to Programming in Java&lt;/em&gt;. It is
	 * Pantone 300U. The RGB values are approximately (9, 90, 166).
	 */
<span class="nc" id="L506">	public static final Color BOOK_BLUE = new Color(9, 90, 166);</span>

	/**
	 * Shade of light blue used in &lt;em&gt;Introduction to Programming in Java&lt;/em&gt;. The
	 * RGB values are approximately (103, 198, 243).
	 */
<span class="nc" id="L512">	public static final Color BOOK_LIGHT_BLUE = new Color(103, 198, 243);</span>

	/**
	 * Shade of red used in &lt;em&gt;Algorithms, 4th edition&lt;/em&gt;. It is Pantone 1805U.
	 * The RGB values are approximately (150, 35, 31).
	 */
<span class="nc" id="L518">	public static final Color BOOK_RED = new Color(150, 35, 31);</span>

	// default colors
<span class="nc" id="L521">	private static final Color DEFAULT_PEN_COLOR = BLACK;</span>
<span class="nc" id="L522">	private static final Color DEFAULT_CLEAR_COLOR = WHITE;</span>

	// current pen color
	private static Color penColor;

	// default canvas size is DEFAULT_SIZE-by-DEFAULT_SIZE
	private static final int DEFAULT_SIZE = 512;
<span class="nc" id="L529">	private static int width = DEFAULT_SIZE;</span>
<span class="nc" id="L530">	private static int height = DEFAULT_SIZE;</span>

	// default pen radius
	private static final double DEFAULT_PEN_RADIUS = 0.002;

	// current pen radius
	private static double penRadius;

	// show we draw immediately or wait until next show?
<span class="nc" id="L539">	private static boolean defer = false;</span>

	// boundary of drawing canvas, 0% border
	// private static final double BORDER = 0.05;
	private static final double BORDER = 0.00;
	private static final double DEFAULT_XMIN = 0.0;
	private static final double DEFAULT_XMAX = 1.0;
	private static final double DEFAULT_YMIN = 0.0;
	private static final double DEFAULT_YMAX = 1.0;
	private static double xmin, ymin, xmax, ymax;

	// for synchronization
<span class="nc" id="L551">	private static Object mouseLock = new Object();</span>
<span class="nc" id="L552">	private static Object keyLock = new Object();</span>

	// default font
<span class="nc" id="L555">	private static final Font DEFAULT_FONT = new Font(&quot;SansSerif&quot;, Font.PLAIN, 16);</span>

	// current font
	private static Font font;

	// double buffered graphics
	private static BufferedImage offscreenImage, onscreenImage;
	private static Graphics2D offscreen, onscreen;

	// singleton for callbacks: avoids generation of extra .class files
<span class="nc" id="L565">	private static StdDraw std = new StdDraw();</span>

	// the frame for drawing to the screen
	private static JFrame frame;

	// mouse state
<span class="nc" id="L571">	private static boolean mousePressed = false;</span>
<span class="nc" id="L572">	private static double mouseX = 0;</span>
<span class="nc" id="L573">	private static double mouseY = 0;</span>

	// queue of typed key characters
<span class="nc" id="L576">	private static LinkedList&lt;Character&gt; keysTyped = new LinkedList&lt;Character&gt;();</span>

	// set of key codes currently pressed down
<span class="nc" id="L579">	private static TreeSet&lt;Integer&gt; keysDown = new TreeSet&lt;Integer&gt;();</span>

	// time in milliseconds (from currentTimeMillis()) when we can draw again
	// used to control the frame rate
<span class="nc" id="L583">	private static long nextDraw = -1;</span>

	// static initializer
	static {
<span class="nc" id="L587">		init();</span>
<span class="nc" id="L588">	}</span>

	/**
	 * Draws a circular arc of the specified radius, centered at (&lt;em&gt;x&lt;/em&gt;,
	 * &lt;em&gt;y&lt;/em&gt;), from angle1 to angle2 (in degrees).
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the center of the circle
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the center of the circle
	 * @param radius
	 *            the radius of the circle
	 * @param angle1
	 *            the starting angle. 0 would mean an arc beginning at 3 o'clock.
	 * @param angle2
	 *            the angle at the end of the arc. For example, if you want a 90
	 *            degree arc, then angle2 should be angle1 + 90.
	 * @throws IllegalArgumentException
	 *             if {@code radius} is negative
	 */
	public static void arc(double x, double y, double radius, double angle1, double angle2) {
<span class="nc bnc" id="L609" title="All 2 branches missed.">		if (radius &lt; 0)</span>
<span class="nc" id="L610">			throw new IllegalArgumentException(&quot;arc radius must be nonnegative&quot;);</span>
<span class="nc bnc" id="L611" title="All 2 branches missed.">		while (angle2 &lt; angle1)</span>
<span class="nc" id="L612">			angle2 += 360;</span>
<span class="nc" id="L613">		double xs = scaleX(x);</span>
<span class="nc" id="L614">		double ys = scaleY(y);</span>
<span class="nc" id="L615">		double ws = factorX(2 * radius);</span>
<span class="nc" id="L616">		double hs = factorY(2 * radius);</span>
<span class="nc bnc" id="L617" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L618">			pixel(x, y);</span>
		else
<span class="nc" id="L620">			offscreen.draw(new Arc2D.Double(xs - ws / 2, ys - hs / 2, ws, hs, angle1, angle2 - angle1, Arc2D.OPEN));</span>
<span class="nc" id="L621">		draw();</span>
<span class="nc" id="L622">	}</span>

	/**
	 * Draws a circle of the specified radius, centered at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;).
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the center of the circle
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the center of the circle
	 * @param radius
	 *            the radius of the circle
	 * @throws IllegalArgumentException
	 *             if {@code radius} is negative
	 */
	public static void circle(double x, double y, double radius) {
<span class="nc bnc" id="L637" title="All 2 branches missed.">		if (!(radius &gt;= 0))</span>
<span class="nc" id="L638">			throw new IllegalArgumentException(&quot;radius must be nonnegative&quot;);</span>
<span class="nc" id="L639">		double xs = scaleX(x);</span>
<span class="nc" id="L640">		double ys = scaleY(y);</span>
<span class="nc" id="L641">		double ws = factorX(2 * radius);</span>
<span class="nc" id="L642">		double hs = factorY(2 * radius);</span>
<span class="nc bnc" id="L643" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L644">			pixel(x, y);</span>
		else
<span class="nc" id="L646">			offscreen.draw(new Ellipse2D.Double(xs - ws / 2, ys - hs / 2, ws, hs));</span>
<span class="nc" id="L647">		draw();</span>
<span class="nc" id="L648">	}</span>

	/**
	 * Clears the screen to the default color (white).
	 */
	public static void clear() {
<span class="nc" id="L654">		clear(DEFAULT_CLEAR_COLOR);</span>
<span class="nc" id="L655">	}</span>

	/**
	 * Clears the screen to the specified color.
	 *
	 * @param color
	 *            the color to make the background
	 */
	public static void clear(Color color) {
<span class="nc" id="L664">		offscreen.setColor(color);</span>
<span class="nc" id="L665">		offscreen.fillRect(0, 0, width, height);</span>
<span class="nc" id="L666">		offscreen.setColor(penColor);</span>
<span class="nc" id="L667">		draw();</span>
<span class="nc" id="L668">	}</span>

	// create the menu bar (changed to private)
	private static JMenuBar createMenuBar() {
<span class="nc" id="L672">		JMenuBar menuBar = new JMenuBar();</span>
<span class="nc" id="L673">		JMenu menu = new JMenu(&quot;File&quot;);</span>
<span class="nc" id="L674">		menuBar.add(menu);</span>
<span class="nc" id="L675">		JMenuItem menuItem1 = new JMenuItem(&quot; Save...   &quot;);</span>
<span class="nc" id="L676">		menuItem1.addActionListener(std);</span>
<span class="nc" id="L677">		menuItem1.setAccelerator(</span>
<span class="nc" id="L678">				KeyStroke.getKeyStroke(KeyEvent.VK_S, Toolkit.getDefaultToolkit().getMenuShortcutKeyMask()));</span>
<span class="nc" id="L679">		menu.add(menuItem1);</span>
<span class="nc" id="L680">		return menuBar;</span>
	}

	/***************************************************************************
	 * User and screen coordinate systems.
	 ***************************************************************************/

	// draw onscreen if defer is false
	private static void draw() {
<span class="nc bnc" id="L689" title="All 2 branches missed.">		if (defer)</span>
<span class="nc" id="L690">			return;</span>
<span class="nc" id="L691">		onscreen.drawImage(offscreenImage, 0, 0, null);</span>
<span class="nc" id="L692">		frame.repaint();</span>
<span class="nc" id="L693">	}</span>

	/**
	 * Draws an ellipse with the specified semimajor and semiminor axes, centered at
	 * (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;).
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the center of the ellipse
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the center of the ellipse
	 * @param semiMajorAxis
	 *            is the semimajor axis of the ellipse
	 * @param semiMinorAxis
	 *            is the semiminor axis of the ellipse
	 * @throws IllegalArgumentException
	 *             if either {@code semiMajorAxis} or {@code semiMinorAxis} is
	 *             negative
	 */
	public static void ellipse(double x, double y, double semiMajorAxis, double semiMinorAxis) {
<span class="nc bnc" id="L712" title="All 2 branches missed.">		if (!(semiMajorAxis &gt;= 0))</span>
<span class="nc" id="L713">			throw new IllegalArgumentException(&quot;ellipse semimajor axis must be nonnegative&quot;);</span>
<span class="nc bnc" id="L714" title="All 2 branches missed.">		if (!(semiMinorAxis &gt;= 0))</span>
<span class="nc" id="L715">			throw new IllegalArgumentException(&quot;ellipse semiminor axis must be nonnegative&quot;);</span>
<span class="nc" id="L716">		double xs = scaleX(x);</span>
<span class="nc" id="L717">		double ys = scaleY(y);</span>
<span class="nc" id="L718">		double ws = factorX(2 * semiMajorAxis);</span>
<span class="nc" id="L719">		double hs = factorY(2 * semiMinorAxis);</span>
<span class="nc bnc" id="L720" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L721">			pixel(x, y);</span>
		else
<span class="nc" id="L723">			offscreen.draw(new Ellipse2D.Double(xs - ws / 2, ys - hs / 2, ws, hs));</span>
<span class="nc" id="L724">		draw();</span>
<span class="nc" id="L725">	}</span>

	private static double factorX(double w) {
<span class="nc" id="L728">		return w * width / Math.abs(xmax - xmin);</span>
	}

	private static double factorY(double h) {
<span class="nc" id="L732">		return h * height / Math.abs(ymax - ymin);</span>
	}

	/**
	 * Draws a filled circle of the specified radius, centered at (&lt;em&gt;x&lt;/em&gt;,
	 * &lt;em&gt;y&lt;/em&gt;).
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the center of the circle
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the center of the circle
	 * @param radius
	 *            the radius of the circle
	 * @throws IllegalArgumentException
	 *             if {@code radius} is negative
	 */
	public static void filledCircle(double x, double y, double radius) {
<span class="nc bnc" id="L749" title="All 2 branches missed.">		if (!(radius &gt;= 0))</span>
<span class="nc" id="L750">			throw new IllegalArgumentException(&quot;radius must be nonnegative&quot;);</span>
<span class="nc" id="L751">		double xs = scaleX(x);</span>
<span class="nc" id="L752">		double ys = scaleY(y);</span>
<span class="nc" id="L753">		double ws = factorX(2 * radius);</span>
<span class="nc" id="L754">		double hs = factorY(2 * radius);</span>
<span class="nc bnc" id="L755" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L756">			pixel(x, y);</span>
		else
<span class="nc" id="L758">			offscreen.fill(new Ellipse2D.Double(xs - ws / 2, ys - hs / 2, ws, hs));</span>
<span class="nc" id="L759">		draw();</span>
<span class="nc" id="L760">	}</span>

	/**
	 * Draws an ellipse with the specified semimajor and semiminor axes, centered at
	 * (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;).
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the center of the ellipse
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the center of the ellipse
	 * @param semiMajorAxis
	 *            is the semimajor axis of the ellipse
	 * @param semiMinorAxis
	 *            is the semiminor axis of the ellipse
	 * @throws IllegalArgumentException
	 *             if either {@code semiMajorAxis} or {@code semiMinorAxis} is
	 *             negative
	 */
	public static void filledEllipse(double x, double y, double semiMajorAxis, double semiMinorAxis) {
<span class="nc bnc" id="L779" title="All 2 branches missed.">		if (!(semiMajorAxis &gt;= 0))</span>
<span class="nc" id="L780">			throw new IllegalArgumentException(&quot;ellipse semimajor axis must be nonnegative&quot;);</span>
<span class="nc bnc" id="L781" title="All 2 branches missed.">		if (!(semiMinorAxis &gt;= 0))</span>
<span class="nc" id="L782">			throw new IllegalArgumentException(&quot;ellipse semiminor axis must be nonnegative&quot;);</span>
<span class="nc" id="L783">		double xs = scaleX(x);</span>
<span class="nc" id="L784">		double ys = scaleY(y);</span>
<span class="nc" id="L785">		double ws = factorX(2 * semiMajorAxis);</span>
<span class="nc" id="L786">		double hs = factorY(2 * semiMinorAxis);</span>
<span class="nc bnc" id="L787" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L788">			pixel(x, y);</span>
		else
<span class="nc" id="L790">			offscreen.fill(new Ellipse2D.Double(xs - ws / 2, ys - hs / 2, ws, hs));</span>
<span class="nc" id="L791">		draw();</span>
<span class="nc" id="L792">	}</span>

	/**
	 * Draws a polygon with the vertices (&lt;em&gt;x&lt;/em&gt;&lt;sub&gt;0&lt;/sub&gt;,
	 * &lt;em&gt;y&lt;/em&gt;&lt;sub&gt;0&lt;/sub&gt;), (&lt;em&gt;x&lt;/em&gt;&lt;sub&gt;1&lt;/sub&gt;, &lt;em&gt;y&lt;/em&gt;&lt;sub&gt;1&lt;/sub&gt;),
	 * ..., (&lt;em&gt;x&lt;/em&gt;&lt;sub&gt;&lt;em&gt;n&lt;/em&gt;&amp;minus;1&lt;/sub&gt;,
	 * &lt;em&gt;y&lt;/em&gt;&lt;sub&gt;&lt;em&gt;n&lt;/em&gt;&amp;minus;1&lt;/sub&gt;).
	 *
	 * @param x
	 *            an array of all the &lt;em&gt;x&lt;/em&gt;-coordinates of the polygon
	 * @param y
	 *            an array of all the &lt;em&gt;y&lt;/em&gt;-coordinates of the polygon
	 * @throws IllegalArgumentException
	 *             unless {@code x[]} and {@code y[]} are of the same length
	 */
	public static void filledPolygon(double[] x, double[] y) {
<span class="nc bnc" id="L808" title="All 2 branches missed.">		if (x == null)</span>
<span class="nc" id="L809">			throw new NullPointerException();</span>
<span class="nc bnc" id="L810" title="All 2 branches missed.">		if (y == null)</span>
<span class="nc" id="L811">			throw new NullPointerException();</span>
<span class="nc" id="L812">		int n1 = x.length;</span>
<span class="nc" id="L813">		int n2 = y.length;</span>
<span class="nc bnc" id="L814" title="All 2 branches missed.">		if (n1 != n2)</span>
<span class="nc" id="L815">			throw new IllegalArgumentException(&quot;arrays must be of the same length&quot;);</span>
<span class="nc" id="L816">		int n = n1;</span>
<span class="nc" id="L817">		GeneralPath path = new GeneralPath();</span>
<span class="nc" id="L818">		path.moveTo((float) scaleX(x[0]), (float) scaleY(y[0]));</span>
<span class="nc bnc" id="L819" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++)</span>
<span class="nc" id="L820">			path.lineTo((float) scaleX(x[i]), (float) scaleY(y[i]));</span>
<span class="nc" id="L821">		path.closePath();</span>
<span class="nc" id="L822">		offscreen.fill(path);</span>
<span class="nc" id="L823">		draw();</span>
<span class="nc" id="L824">	}</span>

	/**
	 * Draws a filled rectangle of the specified size, centered at (&lt;em&gt;x&lt;/em&gt;,
	 * &lt;em&gt;y&lt;/em&gt;).
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the center of the rectangle
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the center of the rectangle
	 * @param halfWidth
	 *            one half the width of the rectangle
	 * @param halfHeight
	 *            one half the height of the rectangle
	 * @throws IllegalArgumentException
	 *             if either {@code halfWidth} or {@code halfHeight} is negative
	 */
	public static void filledRectangle(double x, double y, double halfWidth, double halfHeight) {
<span class="nc bnc" id="L842" title="All 2 branches missed.">		if (!(halfWidth &gt;= 0))</span>
<span class="nc" id="L843">			throw new IllegalArgumentException(&quot;half width must be nonnegative&quot;);</span>
<span class="nc bnc" id="L844" title="All 2 branches missed.">		if (!(halfHeight &gt;= 0))</span>
<span class="nc" id="L845">			throw new IllegalArgumentException(&quot;half height must be nonnegative&quot;);</span>
<span class="nc" id="L846">		double xs = scaleX(x);</span>
<span class="nc" id="L847">		double ys = scaleY(y);</span>
<span class="nc" id="L848">		double ws = factorX(2 * halfWidth);</span>
<span class="nc" id="L849">		double hs = factorY(2 * halfHeight);</span>
<span class="nc bnc" id="L850" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L851">			pixel(x, y);</span>
		else
<span class="nc" id="L853">			offscreen.fill(new Rectangle2D.Double(xs - ws / 2, ys - hs / 2, ws, hs));</span>
<span class="nc" id="L854">		draw();</span>
<span class="nc" id="L855">	}</span>

	/**
	 * Draws a filled square of the specified size, centered at (&lt;em&gt;x&lt;/em&gt;,
	 * &lt;em&gt;y&lt;/em&gt;).
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the center of the square
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the center of the square
	 * @param halfLength
	 *            one half the length of any side of the square
	 * @throws IllegalArgumentException
	 *             if {@code halfLength} is negative
	 */
	public static void filledSquare(double x, double y, double halfLength) {
<span class="nc bnc" id="L871" title="All 2 branches missed.">		if (!(halfLength &gt;= 0))</span>
<span class="nc" id="L872">			throw new IllegalArgumentException(&quot;half length must be nonnegative&quot;);</span>
<span class="nc" id="L873">		double xs = scaleX(x);</span>
<span class="nc" id="L874">		double ys = scaleY(y);</span>
<span class="nc" id="L875">		double ws = factorX(2 * halfLength);</span>
<span class="nc" id="L876">		double hs = factorY(2 * halfLength);</span>
<span class="nc bnc" id="L877" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L878">			pixel(x, y);</span>
		else
<span class="nc" id="L880">			offscreen.fill(new Rectangle2D.Double(xs - ws / 2, ys - hs / 2, ws, hs));</span>
<span class="nc" id="L881">		draw();</span>
<span class="nc" id="L882">	}</span>

	/**
	 * Returns the current font.
	 *
	 * @return the current font
	 */
	public static Font getFont() {
<span class="nc" id="L890">		return font;</span>
	}

	/***************************************************************************
	 * Drawing images.
	 ***************************************************************************/

	// get an image from the given filename
	private static Image getImage(String filename) {
<span class="nc bnc" id="L899" title="All 2 branches missed.">		if (filename == null)</span>
<span class="nc" id="L900">			throw new NullPointerException();</span>

		// to read from file
<span class="nc" id="L903">		ImageIcon icon = new ImageIcon(filename);</span>

		// try to read from URL
<span class="nc bnc" id="L906" title="All 4 branches missed.">		if ((icon == null) || (icon.getImageLoadStatus() != MediaTracker.COMPLETE)) {</span>
			try {
<span class="nc" id="L908">				URL url = new URL(filename);</span>
<span class="nc" id="L909">				icon = new ImageIcon(url);</span>
<span class="nc" id="L910">			} catch (Exception e) {</span>
				/* not a url */
			}
		}

		// in case file is inside a .jar (classpath relative to StdDraw)
<span class="nc bnc" id="L916" title="All 4 branches missed.">		if ((icon == null) || (icon.getImageLoadStatus() != MediaTracker.COMPLETE)) {</span>
<span class="nc" id="L917">			URL url = StdDraw.class.getResource(filename);</span>
<span class="nc bnc" id="L918" title="All 2 branches missed.">			if (url != null)</span>
<span class="nc" id="L919">				icon = new ImageIcon(url);</span>
		}

		// in case file is inside a .jar (classpath relative to root of jar)
<span class="nc bnc" id="L923" title="All 4 branches missed.">		if ((icon == null) || (icon.getImageLoadStatus() != MediaTracker.COMPLETE)) {</span>
<span class="nc" id="L924">			URL url = StdDraw.class.getResource(&quot;/&quot; + filename);</span>
<span class="nc bnc" id="L925" title="All 2 branches missed.">			if (url == null)</span>
<span class="nc" id="L926">				throw new IllegalArgumentException(&quot;image &quot; + filename + &quot; not found&quot;);</span>
<span class="nc" id="L927">			icon = new ImageIcon(url);</span>
		}

<span class="nc" id="L930">		return icon.getImage();</span>
	}

	/**
	 * Returns the current pen color.
	 *
	 * @return the current pen color
	 */
	public static Color getPenColor() {
<span class="nc" id="L939">		return penColor;</span>
	}

	/**
	 * Returns the current pen radius.
	 *
	 * @return the current value of the pen radius
	 */
	public static double getPenRadius() {
<span class="nc" id="L948">		return penRadius;</span>
	}

	/**
	 * Returns true if the user has typed a key (that has not yet been processed).
	 *
	 * @return &lt;tt&gt;true&lt;/tt&gt; if the user has typed a key (that has not yet been
	 *         processed by {@link #nextKeyTyped()}; &lt;tt&gt;false&lt;/tt&gt; otherwise
	 */
	public static boolean hasNextKeyTyped() {
<span class="nc" id="L958">		synchronized (keyLock) {</span>
<span class="nc bnc" id="L959" title="All 2 branches missed.">			return !keysTyped.isEmpty();</span>
		}
	}

	// init
	private static void init() {
<span class="nc bnc" id="L965" title="All 2 branches missed.">		if (frame != null)</span>
<span class="nc" id="L966">			frame.setVisible(false);</span>
<span class="nc" id="L967">		frame = new JFrame();</span>
<span class="nc" id="L968">		offscreenImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);</span>
<span class="nc" id="L969">		onscreenImage = new BufferedImage(width, height, BufferedImage.TYPE_INT_ARGB);</span>
<span class="nc" id="L970">		offscreen = offscreenImage.createGraphics();</span>
<span class="nc" id="L971">		onscreen = onscreenImage.createGraphics();</span>
<span class="nc" id="L972">		setXscale();</span>
<span class="nc" id="L973">		setYscale();</span>
<span class="nc" id="L974">		offscreen.setColor(DEFAULT_CLEAR_COLOR);</span>
<span class="nc" id="L975">		offscreen.fillRect(0, 0, width, height);</span>
<span class="nc" id="L976">		setPenColor();</span>
<span class="nc" id="L977">		setPenRadius();</span>
<span class="nc" id="L978">		setFont();</span>
<span class="nc" id="L979">		clear();</span>

		// add antialiasing
<span class="nc" id="L982">		RenderingHints hints = new RenderingHints(RenderingHints.KEY_ANTIALIASING, RenderingHints.VALUE_ANTIALIAS_ON);</span>
<span class="nc" id="L983">		hints.put(RenderingHints.KEY_RENDERING, RenderingHints.VALUE_RENDER_QUALITY);</span>
<span class="nc" id="L984">		offscreen.addRenderingHints(hints);</span>

		// frame stuff
<span class="nc" id="L987">		ImageIcon icon = new ImageIcon(onscreenImage);</span>
<span class="nc" id="L988">		JLabel draw = new JLabel(icon);</span>

<span class="nc" id="L990">		draw.addMouseListener(std);</span>
<span class="nc" id="L991">		draw.addMouseMotionListener(std);</span>

<span class="nc" id="L993">		frame.setContentPane(draw);</span>
<span class="nc" id="L994">		frame.addKeyListener(std); // JLabel cannot get keyboard focus</span>
<span class="nc" id="L995">		frame.setResizable(false);</span>
<span class="nc" id="L996">		frame.setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE); // closes all windows</span>
		// frame.setDefaultCloseOperation(JFrame.DISPOSE_ON_CLOSE); // closes only
		// current window
<span class="nc" id="L999">		frame.setTitle(&quot;Standard Draw&quot;);</span>
<span class="nc" id="L1000">		frame.setJMenuBar(createMenuBar());</span>
<span class="nc" id="L1001">		frame.pack();</span>
<span class="nc" id="L1002">		frame.requestFocusInWindow();</span>
<span class="nc" id="L1003">		frame.setVisible(true);</span>
<span class="nc" id="L1004">	}</span>

	/**
	 * Returns true if the given key is being pressed.
	 * &lt;p&gt;
	 * This method takes the keycode (corresponding to a physical key) as an
	 * argument. It can handle action keys (such as F1 and arrow keys) and modifier
	 * keys (such as shift and control). See {@link KeyEvent} for a description of
	 * key codes.
	 *
	 * @param keycode
	 *            the key to check if it is being pressed
	 * @return &lt;tt&gt;true&lt;/tt&gt; if {@code keycode} is currently being pressed;
	 *         &lt;tt&gt;false&lt;/tt&gt; otherwise
	 */
	public static boolean isKeyPressed(int keycode) {
<span class="nc" id="L1020">		synchronized (keyLock) {</span>
<span class="nc" id="L1021">			return keysDown.contains(keycode);</span>
		}
	}

	/**
	 * Draws a line segment between (&lt;em&gt;x&lt;/em&gt;&lt;sub&gt;0&lt;/sub&gt;, &lt;em&gt;y&lt;/em&gt;&lt;sub&gt;0&lt;/sub&gt;)
	 * and (&lt;em&gt;x&lt;/em&gt;&lt;sub&gt;1&lt;/sub&gt;, &lt;em&gt;y&lt;/em&gt;&lt;sub&gt;1&lt;/sub&gt;).
	 *
	 * @param x0
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of one endpoint
	 * @param y0
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of one endpoint
	 * @param x1
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the other endpoint
	 * @param y1
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the other endpoint
	 */
	public static void line(double x0, double y0, double x1, double y1) {
<span class="nc" id="L1039">		offscreen.draw(new Line2D.Double(scaleX(x0), scaleY(y0), scaleX(x1), scaleY(y1)));</span>
<span class="nc" id="L1040">		draw();</span>
<span class="nc" id="L1041">	}</span>

	/**
	 * Test client.
	 */
	public static void main(String[] args) {
		// StdDraw.square(.2, .8, .1);
		// StdDraw.filledSquare(.8, .8, .2);
		// StdDraw.circle(.8, .2, .2);

		// StdDraw.setPenColor(StdDraw.BOOK_RED);
		// StdDraw.setPenRadius(.02);
		// StdDraw.arc(.8, .2, .1, 200, 45);
		//
		// // draw a blue diamond
		// StdDraw.setPenRadius();
		// StdDraw.setPenColor(StdDraw.BOOK_BLUE);
		// double[] x = { .1, .2, .3, .2 };
		// double[] y = { .2, .3, .2, .1 };
		// StdDraw.filledPolygon(x, y);
		//
		// // text
		// StdDraw.setPenColor(StdDraw.BLACK);
		// StdDraw.text(0.2, 0.5, &quot;black text&quot;);
		// StdDraw.setPenColor(StdDraw.WHITE);
		// StdDraw.text(0.8, 0.8, &quot;white text&quot;);

<span class="nc" id="L1068">		int N = 50;</span>
<span class="nc" id="L1069">		double points[] = new double[N];</span>
<span class="nc bnc" id="L1070" title="All 2 branches missed.">		for (int i = 0; i &lt; points.length; i++) {</span>
<span class="nc" id="L1071">			points[i] = Math.random();</span>

		}
<span class="nc" id="L1074">		Arrays.sort(points);</span>
<span class="nc bnc" id="L1075" title="All 2 branches missed.">		for (int i = 0; i &lt; points.length; i++) {</span>
<span class="nc" id="L1076">			double x = 1.0 * i / N;</span>
<span class="nc" id="L1077">			double y = points[i] / 2.0;</span>
<span class="nc" id="L1078">			double rw = 0.5 / N;</span>
<span class="nc" id="L1079">			double rh = points[i] / 2.0;</span>
<span class="nc" id="L1080">			StdDraw.filledRectangle(x, y, rw, rh);</span>
		}
<span class="nc" id="L1082">	}</span>

	/**
	 * Returns true if the mouse is being pressed.
	 *
	 * @return &lt;tt&gt;true&lt;/tt&gt; if the mouse is being pressed; &lt;tt&gt;false&lt;/tt&gt; otherwise
	 */
	public static boolean mousePressed() {
<span class="nc" id="L1090">		synchronized (mouseLock) {</span>
<span class="nc" id="L1091">			return mousePressed;</span>
		}
	}

	/**
	 * Returns the &lt;em&gt;x&lt;/em&gt;-coordinate of the mouse.
	 *
	 * @return the &lt;em&gt;x&lt;/em&gt;-coordinate of the mouse
	 */
	public static double mouseX() {
<span class="nc" id="L1101">		synchronized (mouseLock) {</span>
<span class="nc" id="L1102">			return mouseX;</span>
		}
	}

	/**
	 * Returns the &lt;em&gt;y&lt;/em&gt;-coordinate of the mouse.
	 *
	 * @return &lt;em&gt;y&lt;/em&gt;-coordinate of the mouse
	 */
	public static double mouseY() {
<span class="nc" id="L1112">		synchronized (mouseLock) {</span>
<span class="nc" id="L1113">			return mouseY;</span>
		}
	}

	/**
	 * Returns the next key that was typed by the user (that your program has not
	 * already processed). This method should be preceded by a call to
	 * {@link #hasNextKeyTyped()} to ensure that there is a next key to process.
	 * This method returns a Unicode character corresponding to the key typed (such
	 * as {@code 'a'} or {@code 'A'}). It cannot identify action keys (such as F1
	 * and arrow keys) or modifier keys (such as control).
	 *
	 * @return the next key typed by the user (that your program has not already
	 *         processed).
	 * @throws NoSuchElementException
	 *             if there is no remaining key
	 */
	public static char nextKeyTyped() {
<span class="nc" id="L1131">		synchronized (keyLock) {</span>
<span class="nc bnc" id="L1132" title="All 2 branches missed.">			if (keysTyped.isEmpty()) {</span>
<span class="nc" id="L1133">				throw new NoSuchElementException(&quot;your program has already processed all keystrokes&quot;);</span>
			}
<span class="nc" id="L1135">			return keysTyped.removeLast();</span>
		}
	}

	/**
	 * Draws the specified image centered at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;). The supported
	 * image formats are JPEG, PNG, and GIF. As an optimization, the picture is
	 * cached, so there is no performance penalty for redrawing the same image
	 * multiple times (e.g., in an animation). However, if you change the picture
	 * file after drawing it, subsequent calls will draw the original picture.
	 *
	 * @param x
	 *            the center &lt;em&gt;x&lt;/em&gt;-coordinate of the image
	 * @param y
	 *            the center &lt;em&gt;y&lt;/em&gt;-coordinate of the image
	 * @param filename
	 *            the name of the image/picture, e.g., &quot;ball.gif&quot;
	 * @throws IllegalArgumentException
	 *             if the image filename is invalid
	 */
	public static void picture(double x, double y, String filename) {
<span class="nc" id="L1156">		Image image = getImage(filename);</span>
<span class="nc" id="L1157">		double xs = scaleX(x);</span>
<span class="nc" id="L1158">		double ys = scaleY(y);</span>
<span class="nc" id="L1159">		int ws = image.getWidth(null);</span>
<span class="nc" id="L1160">		int hs = image.getHeight(null);</span>
<span class="nc bnc" id="L1161" title="All 4 branches missed.">		if (ws &lt; 0 || hs &lt; 0)</span>
<span class="nc" id="L1162">			throw new IllegalArgumentException(&quot;image &quot; + filename + &quot; is corrupt&quot;);</span>

<span class="nc" id="L1164">		offscreen.drawImage(image, (int) Math.round(xs - ws / 2.0), (int) Math.round(ys - hs / 2.0), null);</span>
<span class="nc" id="L1165">		draw();</span>
<span class="nc" id="L1166">	}</span>

	/**
	 * Draws the specified image centered at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;), rotated given
	 * number of degrees. The supported image formats are JPEG, PNG, and GIF.
	 *
	 * @param x
	 *            the center &lt;em&gt;x&lt;/em&gt;-coordinate of the image
	 * @param y
	 *            the center &lt;em&gt;y&lt;/em&gt;-coordinate of the image
	 * @param filename
	 *            the name of the image/picture, e.g., &quot;ball.gif&quot;
	 * @param degrees
	 *            is the number of degrees to rotate counterclockwise
	 * @throws IllegalArgumentException
	 *             if the image filename is invalid
	 */
	public static void picture(double x, double y, String filename, double degrees) {
<span class="nc" id="L1184">		Image image = getImage(filename);</span>
<span class="nc" id="L1185">		double xs = scaleX(x);</span>
<span class="nc" id="L1186">		double ys = scaleY(y);</span>
<span class="nc" id="L1187">		int ws = image.getWidth(null);</span>
<span class="nc" id="L1188">		int hs = image.getHeight(null);</span>
<span class="nc bnc" id="L1189" title="All 4 branches missed.">		if (ws &lt; 0 || hs &lt; 0)</span>
<span class="nc" id="L1190">			throw new IllegalArgumentException(&quot;image &quot; + filename + &quot; is corrupt&quot;);</span>

<span class="nc" id="L1192">		offscreen.rotate(Math.toRadians(-degrees), xs, ys);</span>
<span class="nc" id="L1193">		offscreen.drawImage(image, (int) Math.round(xs - ws / 2.0), (int) Math.round(ys - hs / 2.0), null);</span>
<span class="nc" id="L1194">		offscreen.rotate(Math.toRadians(+degrees), xs, ys);</span>

<span class="nc" id="L1196">		draw();</span>
<span class="nc" id="L1197">	}</span>

	/***************************************************************************
	 * Drawing geometric shapes.
	 ***************************************************************************/

	/**
	 * Draws the specified image centered at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;), rescaled to
	 * the specified bounding box. The supported image formats are JPEG, PNG, and
	 * GIF.
	 *
	 * @param x
	 *            the center &lt;em&gt;x&lt;/em&gt;-coordinate of the image
	 * @param y
	 *            the center &lt;em&gt;y&lt;/em&gt;-coordinate of the image
	 * @param filename
	 *            the name of the image/picture, e.g., &quot;ball.gif&quot;
	 * @param scaledWidth
	 *            the width of the scaled image in pixels
	 * @param scaledHeight
	 *            the height of the scaled image in pixels
	 * @throws IllegalArgumentException
	 *             if either {@code scaledWidth} or {@code scaledHeight} is negative
	 * @throws IllegalArgumentException
	 *             if the image filename is invalid
	 */
	public static void picture(double x, double y, String filename, double scaledWidth, double scaledHeight) {
<span class="nc" id="L1224">		Image image = getImage(filename);</span>
<span class="nc bnc" id="L1225" title="All 2 branches missed.">		if (scaledWidth &lt; 0)</span>
<span class="nc" id="L1226">			throw new IllegalArgumentException(&quot;width is negative: &quot; + scaledWidth);</span>
<span class="nc bnc" id="L1227" title="All 2 branches missed.">		if (scaledHeight &lt; 0)</span>
<span class="nc" id="L1228">			throw new IllegalArgumentException(&quot;height is negative: &quot; + scaledHeight);</span>
<span class="nc" id="L1229">		double xs = scaleX(x);</span>
<span class="nc" id="L1230">		double ys = scaleY(y);</span>
<span class="nc" id="L1231">		double ws = factorX(scaledWidth);</span>
<span class="nc" id="L1232">		double hs = factorY(scaledHeight);</span>
<span class="nc bnc" id="L1233" title="All 4 branches missed.">		if (ws &lt; 0 || hs &lt; 0)</span>
<span class="nc" id="L1234">			throw new IllegalArgumentException(&quot;image &quot; + filename + &quot; is corrupt&quot;);</span>
<span class="nc bnc" id="L1235" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L1236">			pixel(x, y);</span>
		else {
<span class="nc" id="L1238">			offscreen.drawImage(image, (int) Math.round(xs - ws / 2.0), (int) Math.round(ys - hs / 2.0),</span>
<span class="nc" id="L1239">					(int) Math.round(ws), (int) Math.round(hs), null);</span>
		}
<span class="nc" id="L1241">		draw();</span>
<span class="nc" id="L1242">	}</span>

	/**
	 * Draws the specified image centered at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;), rotated given
	 * number of degrees, and rescaled to the specified bounding box. The supported
	 * image formats are JPEG, PNG, and GIF.
	 *
	 * @param x
	 *            the center &lt;em&gt;x&lt;/em&gt;-coordinate of the image
	 * @param y
	 *            the center &lt;em&gt;y&lt;/em&gt;-coordinate of the image
	 * @param filename
	 *            the name of the image/picture, e.g., &quot;ball.gif&quot;
	 * @param scaledWidth
	 *            the width of the scaled image in pixels
	 * @param scaledHeight
	 *            the height of the scaled image in pixels
	 * @param degrees
	 *            is the number of degrees to rotate counterclockwise
	 * @throws IllegalArgumentException
	 *             if either {@code scaledWidth} or {@code scaledHeight} is negative
	 * @throws IllegalArgumentException
	 *             if the image filename is invalid
	 */
	public static void picture(double x, double y, String filename, double scaledWidth, double scaledHeight,
			double degrees) {
<span class="nc bnc" id="L1268" title="All 2 branches missed.">		if (scaledWidth &lt; 0)</span>
<span class="nc" id="L1269">			throw new IllegalArgumentException(&quot;width is negative: &quot; + scaledWidth);</span>
<span class="nc bnc" id="L1270" title="All 2 branches missed.">		if (scaledHeight &lt; 0)</span>
<span class="nc" id="L1271">			throw new IllegalArgumentException(&quot;height is negative: &quot; + scaledHeight);</span>
<span class="nc" id="L1272">		Image image = getImage(filename);</span>
<span class="nc" id="L1273">		double xs = scaleX(x);</span>
<span class="nc" id="L1274">		double ys = scaleY(y);</span>
<span class="nc" id="L1275">		double ws = factorX(scaledWidth);</span>
<span class="nc" id="L1276">		double hs = factorY(scaledHeight);</span>
<span class="nc bnc" id="L1277" title="All 4 branches missed.">		if (ws &lt; 0 || hs &lt; 0)</span>
<span class="nc" id="L1278">			throw new IllegalArgumentException(&quot;image &quot; + filename + &quot; is corrupt&quot;);</span>
<span class="nc bnc" id="L1279" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L1280">			pixel(x, y);</span>

<span class="nc" id="L1282">		offscreen.rotate(Math.toRadians(-degrees), xs, ys);</span>
<span class="nc" id="L1283">		offscreen.drawImage(image, (int) Math.round(xs - ws / 2.0), (int) Math.round(ys - hs / 2.0),</span>
<span class="nc" id="L1284">				(int) Math.round(ws), (int) Math.round(hs), null);</span>
<span class="nc" id="L1285">		offscreen.rotate(Math.toRadians(+degrees), xs, ys);</span>

<span class="nc" id="L1287">		draw();</span>
<span class="nc" id="L1288">	}</span>

	/**
	 * Draws one pixel at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;). This method is private because
	 * pixels depend on the display. To achieve the same effect, set the pen radius
	 * to 0 and call {@code point()}.
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the pixel
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the pixel
	 */
	private static void pixel(double x, double y) {
<span class="nc" id="L1301">		offscreen.fillRect((int) Math.round(scaleX(x)), (int) Math.round(scaleY(y)), 1, 1);</span>
<span class="nc" id="L1302">	}</span>

	/**
	 * Draws a point centered at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;). The point is a filled
	 * circle whose radius is equal to the pen radius. To draw a single-pixel point,
	 * first set the pen radius to 0.
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the point
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the point
	 */
	public static void point(double x, double y) {
<span class="nc" id="L1315">		double xs = scaleX(x);</span>
<span class="nc" id="L1316">		double ys = scaleY(y);</span>
<span class="nc" id="L1317">		double r = penRadius;</span>
<span class="nc" id="L1318">		float scaledPenRadius = (float) (r * DEFAULT_SIZE);</span>

		// double ws = factorX(2*r);
		// double hs = factorY(2*r);
		// if (ws &lt;= 1 &amp;&amp; hs &lt;= 1) pixel(x, y);
<span class="nc bnc" id="L1323" title="All 2 branches missed.">		if (scaledPenRadius &lt;= 1)</span>
<span class="nc" id="L1324">			pixel(x, y);</span>
		else
<span class="nc" id="L1326">			offscreen.fill(new Ellipse2D.Double(xs - scaledPenRadius / 2, ys - scaledPenRadius / 2, scaledPenRadius,</span>
<span class="nc" id="L1327">					scaledPenRadius));</span>
<span class="nc" id="L1328">		draw();</span>
<span class="nc" id="L1329">	}</span>

	/**
	 * Draws a polygon with the vertices (&lt;em&gt;x&lt;/em&gt;&lt;sub&gt;0&lt;/sub&gt;,
	 * &lt;em&gt;y&lt;/em&gt;&lt;sub&gt;0&lt;/sub&gt;), (&lt;em&gt;x&lt;/em&gt;&lt;sub&gt;1&lt;/sub&gt;, &lt;em&gt;y&lt;/em&gt;&lt;sub&gt;1&lt;/sub&gt;),
	 * ..., (&lt;em&gt;x&lt;/em&gt;&lt;sub&gt;&lt;em&gt;n&lt;/em&gt;&amp;minus;1&lt;/sub&gt;,
	 * &lt;em&gt;y&lt;/em&gt;&lt;sub&gt;&lt;em&gt;n&lt;/em&gt;&amp;minus;1&lt;/sub&gt;).
	 *
	 * @param x
	 *            an array of all the &lt;em&gt;x&lt;/em&gt;-coordinates of the polygon
	 * @param y
	 *            an array of all the &lt;em&gt;y&lt;/em&gt;-coordinates of the polygon
	 * @throws IllegalArgumentException
	 *             unless {@code x[]} and {@code y[]} are of the same length
	 */
	public static void polygon(double[] x, double[] y) {
<span class="nc bnc" id="L1345" title="All 2 branches missed.">		if (x == null)</span>
<span class="nc" id="L1346">			throw new NullPointerException();</span>
<span class="nc bnc" id="L1347" title="All 2 branches missed.">		if (y == null)</span>
<span class="nc" id="L1348">			throw new NullPointerException();</span>
<span class="nc" id="L1349">		int n1 = x.length;</span>
<span class="nc" id="L1350">		int n2 = y.length;</span>
<span class="nc bnc" id="L1351" title="All 2 branches missed.">		if (n1 != n2)</span>
<span class="nc" id="L1352">			throw new IllegalArgumentException(&quot;arrays must be of the same length&quot;);</span>
<span class="nc" id="L1353">		int n = n1;</span>
<span class="nc" id="L1354">		GeneralPath path = new GeneralPath();</span>
<span class="nc" id="L1355">		path.moveTo((float) scaleX(x[0]), (float) scaleY(y[0]));</span>
<span class="nc bnc" id="L1356" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++)</span>
<span class="nc" id="L1357">			path.lineTo((float) scaleX(x[i]), (float) scaleY(y[i]));</span>
<span class="nc" id="L1358">		path.closePath();</span>
<span class="nc" id="L1359">		offscreen.draw(path);</span>
<span class="nc" id="L1360">		draw();</span>
<span class="nc" id="L1361">	}</span>

	/**
	 * Draws a rectangle of the specified size, centered at (&lt;em&gt;x&lt;/em&gt;,
	 * &lt;em&gt;y&lt;/em&gt;).
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the center of the rectangle
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the center of the rectangle
	 * @param halfWidth
	 *            one half the width of the rectangle
	 * @param halfHeight
	 *            one half the height of the rectangle
	 * @throws IllegalArgumentException
	 *             if either {@code halfWidth} or {@code halfHeight} is negative
	 */
	public static void rectangle(double x, double y, double halfWidth, double halfHeight) {
<span class="nc bnc" id="L1379" title="All 2 branches missed.">		if (!(halfWidth &gt;= 0))</span>
<span class="nc" id="L1380">			throw new IllegalArgumentException(&quot;half width must be nonnegative&quot;);</span>
<span class="nc bnc" id="L1381" title="All 2 branches missed.">		if (!(halfHeight &gt;= 0))</span>
<span class="nc" id="L1382">			throw new IllegalArgumentException(&quot;half height must be nonnegative&quot;);</span>
<span class="nc" id="L1383">		double xs = scaleX(x);</span>
<span class="nc" id="L1384">		double ys = scaleY(y);</span>
<span class="nc" id="L1385">		double ws = factorX(2 * halfWidth);</span>
<span class="nc" id="L1386">		double hs = factorY(2 * halfHeight);</span>
<span class="nc bnc" id="L1387" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L1388">			pixel(x, y);</span>
		else
<span class="nc" id="L1390">			offscreen.draw(new Rectangle2D.Double(xs - ws / 2, ys - hs / 2, ws, hs));</span>
<span class="nc" id="L1391">		draw();</span>
<span class="nc" id="L1392">	}</span>

	/**
	 * Saves the drawing to using the specified filename. The supported image
	 * formats are JPEG and PNG; the filename suffix must be &lt;tt&gt;.jpg&lt;/tt&gt; or
	 * &lt;tt&gt;.png&lt;/tt&gt;.
	 *
	 * @param filename
	 *            the name of the file with one of the required suffixes
	 */
	public static void save(String filename) {
<span class="nc bnc" id="L1403" title="All 2 branches missed.">		if (filename == null)</span>
<span class="nc" id="L1404">			throw new NullPointerException();</span>
<span class="nc" id="L1405">		File file = new File(filename);</span>
<span class="nc" id="L1406">		String suffix = filename.substring(filename.lastIndexOf('.') + 1);</span>

		// png files
<span class="nc bnc" id="L1409" title="All 2 branches missed.">		if (suffix.toLowerCase().equals(&quot;png&quot;)) {</span>
			try {
<span class="nc" id="L1411">				ImageIO.write(onscreenImage, suffix, file);</span>
<span class="nc" id="L1412">			} catch (IOException e) {</span>
<span class="nc" id="L1413">				e.printStackTrace();</span>
			}
<span class="nc" id="L1415">		}</span>

		// need to change from ARGB to RGB for JPEG
		// reference:
		// http://archives.java.sun.com/cgi-bin/wa?A2=ind0404&amp;L=java2d-interest&amp;D=0&amp;P=2727
<span class="nc bnc" id="L1420" title="All 2 branches missed.">		else if (suffix.toLowerCase().equals(&quot;jpg&quot;)) {</span>
<span class="nc" id="L1421">			WritableRaster raster = onscreenImage.getRaster();</span>
			WritableRaster newRaster;
<span class="nc" id="L1423">			newRaster = raster.createWritableChild(0, 0, width, height, 0, 0, new int[] { 0, 1, 2 });</span>
<span class="nc" id="L1424">			DirectColorModel cm = (DirectColorModel) onscreenImage.getColorModel();</span>
<span class="nc" id="L1425">			DirectColorModel newCM = new DirectColorModel(cm.getPixelSize(), cm.getRedMask(), cm.getGreenMask(),</span>
<span class="nc" id="L1426">					cm.getBlueMask());</span>
<span class="nc" id="L1427">			BufferedImage rgbBuffer = new BufferedImage(newCM, newRaster, false, null);</span>
			try {
<span class="nc" id="L1429">				ImageIO.write(rgbBuffer, suffix, file);</span>
<span class="nc" id="L1430">			} catch (IOException e) {</span>
<span class="nc" id="L1431">				e.printStackTrace();</span>
			}
<span class="nc" id="L1433">		}</span>

		else {
<span class="nc" id="L1436">			System.out.println(&quot;Invalid image file type: &quot; + suffix);</span>
		}
<span class="nc" id="L1438">	}</span>

	// helper functions that scale from user coordinates to screen coordinates and
	// back
	private static double scaleX(double x) {
<span class="nc" id="L1443">		return width * (x - xmin) / (xmax - xmin);</span>
	}

	private static double scaleY(double y) {
<span class="nc" id="L1447">		return height * (ymax - y) / (ymax - ymin);</span>
	}

	/**
	 * Sets the canvas (drawing area) to be 512-by-512 pixels. This also erases the
	 * current drawing and resets the coordinate system, pen radius, pen color, and
	 * font back to their default values. Ordinarly, this method is called once, at
	 * the very beginning of a program.
	 */
	public static void setCanvasSize() {
<span class="nc" id="L1457">		setCanvasSize(DEFAULT_SIZE, DEFAULT_SIZE);</span>
<span class="nc" id="L1458">	}</span>

	/**
	 * Sets the canvas (drawing area) to be &lt;em&gt;width&lt;/em&gt;-by-&lt;em&gt;height&lt;/em&gt;
	 * pixels. This also erases the current drawing and resets the coordinate
	 * system, pen radius, pen color, and font back to their default values.
	 * Ordinarly, this method is called once, at the very beginning of a program.
	 *
	 * @param canvasWidth
	 *            the width as a number of pixels
	 * @param canvasHeight
	 *            the height as a number of pixels
	 * @throws IllegalArgumentException
	 *             unless both {@code width} and {@code height} are positive
	 */
	public static void setCanvasSize(int canvasWidth, int canvasHeight) {
<span class="nc bnc" id="L1474" title="All 4 branches missed.">		if (canvasWidth &lt;= 0 || canvasHeight &lt;= 0)</span>
<span class="nc" id="L1475">			throw new IllegalArgumentException(&quot;width and height must be positive&quot;);</span>
<span class="nc" id="L1476">		width = canvasWidth;</span>
<span class="nc" id="L1477">		height = canvasHeight;</span>
<span class="nc" id="L1478">		init();</span>
<span class="nc" id="L1479">	}</span>

	/**
	 * Sets the font to the default font (sans serif, 16 point).
	 */
	public static void setFont() {
<span class="nc" id="L1485">		setFont(DEFAULT_FONT);</span>
<span class="nc" id="L1486">	}</span>

	/**
	 * Sets the font to the specified value.
	 *
	 * @param font
	 *            the font
	 */
	public static void setFont(Font font) {
<span class="nc bnc" id="L1495" title="All 2 branches missed.">		if (font == null)</span>
<span class="nc" id="L1496">			throw new NullPointerException();</span>
<span class="nc" id="L1497">		StdDraw.font = font;</span>
<span class="nc" id="L1498">	}</span>

	/**
	 * Set the pen color to the default color (black).
	 */
	public static void setPenColor() {
<span class="nc" id="L1504">		setPenColor(DEFAULT_PEN_COLOR);</span>
<span class="nc" id="L1505">	}</span>

	/**
	 * Sets the pen color to the specified color.
	 * &lt;p&gt;
	 * The predefined pen colors are &lt;tt&gt;StdDraw.BLACK&lt;/tt&gt;, &lt;tt&gt;StdDraw.BLUE&lt;/tt&gt;,
	 * &lt;tt&gt;StdDraw.CYAN&lt;/tt&gt;, &lt;tt&gt;StdDraw.DARK_GRAY&lt;/tt&gt;, &lt;tt&gt;StdDraw.GRAY&lt;/tt&gt;,
	 * &lt;tt&gt;StdDraw.GREEN&lt;/tt&gt;, &lt;tt&gt;StdDraw.LIGHT_GRAY&lt;/tt&gt;,
	 * &lt;tt&gt;StdDraw.MAGENTA&lt;/tt&gt;, &lt;tt&gt;StdDraw.ORANGE&lt;/tt&gt;, &lt;tt&gt;StdDraw.PINK&lt;/tt&gt;,
	 * &lt;tt&gt;StdDraw.RED&lt;/tt&gt;, &lt;tt&gt;StdDraw.WHITE&lt;/tt&gt;, and &lt;tt&gt;StdDraw.YELLOW&lt;/tt&gt;.
	 *
	 * @param color
	 *            the color to make the pen
	 */
	public static void setPenColor(Color color) {
<span class="nc bnc" id="L1520" title="All 2 branches missed.">		if (color == null)</span>
<span class="nc" id="L1521">			throw new NullPointerException();</span>
<span class="nc" id="L1522">		penColor = color;</span>
<span class="nc" id="L1523">		offscreen.setColor(penColor);</span>
<span class="nc" id="L1524">	}</span>

	/**
	 * Sets the pen color to the specified RGB color.
	 *
	 * @param red
	 *            the amount of red (between 0 and 255)
	 * @param green
	 *            the amount of green (between 0 and 255)
	 * @param blue
	 *            the amount of blue (between 0 and 255)
	 * @throws IllegalArgumentException
	 *             if {@code red}, {@code green}, or {@code blue} is outside its
	 *             prescribed range
	 */
	public static void setPenColor(int red, int green, int blue) {
<span class="nc bnc" id="L1540" title="All 4 branches missed.">		if (red &lt; 0 || red &gt;= 256)</span>
<span class="nc" id="L1541">			throw new IllegalArgumentException(&quot;amount of red must be between 0 and 255&quot;);</span>
<span class="nc bnc" id="L1542" title="All 4 branches missed.">		if (green &lt; 0 || green &gt;= 256)</span>
<span class="nc" id="L1543">			throw new IllegalArgumentException(&quot;amount of green must be between 0 and 255&quot;);</span>
<span class="nc bnc" id="L1544" title="All 4 branches missed.">		if (blue &lt; 0 || blue &gt;= 256)</span>
<span class="nc" id="L1545">			throw new IllegalArgumentException(&quot;amount of blue must be between 0 and 255&quot;);</span>
<span class="nc" id="L1546">		setPenColor(new Color(red, green, blue));</span>
<span class="nc" id="L1547">	}</span>

	/**
	 * Sets the pen size to the default size (0.002). The pen is circular, so that
	 * lines have rounded ends, and when you set the pen radius and draw a point,
	 * you get a circle of the specified radius. The pen radius is not affected by
	 * coordinate scaling.
	 */
	public static void setPenRadius() {
<span class="nc" id="L1556">		setPenRadius(DEFAULT_PEN_RADIUS);</span>
<span class="nc" id="L1557">	}</span>

	/**
	 * Sets the radius of the pen to the specified size. The pen is circular, so
	 * that lines have rounded ends, and when you set the pen radius and draw a
	 * point, you get a circle of the specified radius. The pen radius is not
	 * affected by coordinate scaling.
	 *
	 * @param radius
	 *            the radius of the pen
	 * @throws IllegalArgumentException
	 *             if {@code radius} is negative
	 */
	public static void setPenRadius(double radius) {
<span class="nc bnc" id="L1571" title="All 2 branches missed.">		if (!(radius &gt;= 0))</span>
<span class="nc" id="L1572">			throw new IllegalArgumentException(&quot;pen radius must be nonnegative&quot;);</span>
<span class="nc" id="L1573">		penRadius = radius;</span>
<span class="nc" id="L1574">		float scaledPenRadius = (float) (radius * DEFAULT_SIZE);</span>
<span class="nc" id="L1575">		BasicStroke stroke = new BasicStroke(scaledPenRadius, BasicStroke.CAP_ROUND, BasicStroke.JOIN_ROUND);</span>
		// BasicStroke stroke = new BasicStroke(scaledPenRadius);
<span class="nc" id="L1577">		offscreen.setStroke(stroke);</span>
<span class="nc" id="L1578">	}</span>

	/**
	 * Sets the &lt;em&gt;x&lt;/em&gt;-scale and &lt;em&gt;y&lt;/em&gt;-scale to be the default (between 0.0
	 * and 1.0).
	 */
	public static void setScale() {
<span class="nc" id="L1585">		setXscale();</span>
<span class="nc" id="L1586">		setYscale();</span>
<span class="nc" id="L1587">	}</span>

	/***************************************************************************
	 * Drawing text.
	 ***************************************************************************/

	/**
	 * Sets both the &lt;em&gt;x&lt;/em&gt;-scale and &lt;em&gt;y&lt;/em&gt;-scale to the (same) specified
	 * range.
	 *
	 * @param min
	 *            the minimum value of the &lt;em&gt;x&lt;/em&gt;- and &lt;em&gt;y&lt;/em&gt;-scales
	 * @param max
	 *            the maximum value of the &lt;em&gt;x&lt;/em&gt;- and &lt;em&gt;y&lt;/em&gt;-scales
	 * @throws IllegalArgumentException
	 *             if {@code (max == min)}
	 */
	public static void setScale(double min, double max) {
<span class="nc" id="L1605">		double size = max - min;</span>
<span class="nc bnc" id="L1606" title="All 2 branches missed.">		if (size == 0.0)</span>
<span class="nc" id="L1607">			throw new IllegalArgumentException(&quot;the min and max are the same&quot;);</span>
<span class="nc" id="L1608">		synchronized (mouseLock) {</span>
<span class="nc" id="L1609">			xmin = min - BORDER * size;</span>
<span class="nc" id="L1610">			xmax = max + BORDER * size;</span>
<span class="nc" id="L1611">			ymin = min - BORDER * size;</span>
<span class="nc" id="L1612">			ymax = max + BORDER * size;</span>
		}
<span class="nc" id="L1614">	}</span>

	/**
	 * Sets the &lt;em&gt;x&lt;/em&gt;-scale to be the default (between 0.0 and 1.0).
	 */
	public static void setXscale() {
<span class="nc" id="L1620">		setXscale(DEFAULT_XMIN, DEFAULT_XMAX);</span>
<span class="nc" id="L1621">	}</span>

	/**
	 * Sets the &lt;em&gt;x&lt;/em&gt;-scale to the specified range.
	 *
	 * @param min
	 *            the minimum value of the &lt;em&gt;x&lt;/em&gt;-scale
	 * @param max
	 *            the maximum value of the &lt;em&gt;x&lt;/em&gt;-scale
	 * @throws IllegalArgumentException
	 *             if {@code (max == min)}
	 */
	public static void setXscale(double min, double max) {
<span class="nc" id="L1634">		double size = max - min;</span>
<span class="nc bnc" id="L1635" title="All 2 branches missed.">		if (size == 0.0)</span>
<span class="nc" id="L1636">			throw new IllegalArgumentException(&quot;the min and max are the same&quot;);</span>
<span class="nc" id="L1637">		synchronized (mouseLock) {</span>
<span class="nc" id="L1638">			xmin = min - BORDER * size;</span>
<span class="nc" id="L1639">			xmax = max + BORDER * size;</span>
		}
<span class="nc" id="L1641">	}</span>

	/**
	 * Sets the &lt;em&gt;y&lt;/em&gt;-scale to be the default (between 0.0 and 1.0).
	 */
	public static void setYscale() {
<span class="nc" id="L1647">		setYscale(DEFAULT_YMIN, DEFAULT_YMAX);</span>
<span class="nc" id="L1648">	}</span>

	/**
	 * Sets the &lt;em&gt;y&lt;/em&gt;-scale to the specified range.
	 *
	 * @param min
	 *            the minimum value of the &lt;em&gt;y&lt;/em&gt;-scale
	 * @param max
	 *            the maximum value of the &lt;em&gt;y&lt;/em&gt;-scale
	 * @throws IllegalArgumentException
	 *             if {@code (max == min)}
	 */
	public static void setYscale(double min, double max) {
<span class="nc" id="L1661">		double size = max - min;</span>
<span class="nc bnc" id="L1662" title="All 2 branches missed.">		if (size == 0.0)</span>
<span class="nc" id="L1663">			throw new IllegalArgumentException(&quot;the min and max are the same&quot;);</span>
<span class="nc" id="L1664">		synchronized (mouseLock) {</span>
<span class="nc" id="L1665">			ymin = min - BORDER * size;</span>
<span class="nc" id="L1666">			ymax = max + BORDER * size;</span>
		}
<span class="nc" id="L1668">	}</span>

	/**
	 * Display on-screen and turn off animation mode: subsequent calls to drawing
	 * methods such as {@code line()}, {@code circle()}, and {@code square()} will
	 * be displayed on screen when called. This is the default.
	 */
	public static void show() {
<span class="nc" id="L1676">		defer = false;</span>
<span class="nc" id="L1677">		nextDraw = -1;</span>
<span class="nc" id="L1678">		draw();</span>
<span class="nc" id="L1679">	}</span>

	/**
	 * Display on screen, pause for t milliseconds, and turn on &lt;em&gt;animation
	 * mode&lt;/em&gt;: subsequent calls to drawing methods such as {@code line()},
	 * {@code circle()}, and {@code square()} will not be displayed on screen until
	 * the next call to {@code show()}. This is useful for producing animations
	 * (clear the screen, draw a bunch of shapes, display on screen for a fixed
	 * amount of time, and repeat). It also speeds up drawing a huge number of
	 * shapes (call {@code show(0)} to defer drawing on screen, draw the shapes, and
	 * call {@code show(0)} to display them all on screen at once).
	 * 
	 * @param t
	 *            number of milliseconds
	 */
	public static void show(int t) {
		// sleep until the next time we're allowed to draw
<span class="nc" id="L1696">		long millis = System.currentTimeMillis();</span>
<span class="nc bnc" id="L1697" title="All 2 branches missed.">		if (millis &lt; nextDraw) {</span>
			try {
<span class="nc" id="L1699">				Thread.sleep(nextDraw - millis);</span>
<span class="nc" id="L1700">			} catch (InterruptedException e) {</span>
<span class="nc" id="L1701">				System.out.println(&quot;Error sleeping&quot;);</span>
			}
<span class="nc" id="L1703">			millis = nextDraw;</span>
		}

<span class="nc" id="L1706">		defer = false;</span>
<span class="nc" id="L1707">		draw();</span>
<span class="nc" id="L1708">		defer = true;</span>

		// when are we allowed to draw again
<span class="nc" id="L1711">		nextDraw = millis + t;</span>
<span class="nc" id="L1712">	}</span>

	/***************************************************************************
	 * Save drawing to a file.
	 ***************************************************************************/

	/**
	 * Draws a square of side length 2r, centered at (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;).
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the center of the square
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the center of the square
	 * @param halfLength
	 *            one half the length of any side of the square
	 * @throws IllegalArgumentException
	 *             if {@code halfLength} is negative
	 */
	public static void square(double x, double y, double halfLength) {
<span class="nc bnc" id="L1731" title="All 2 branches missed.">		if (!(halfLength &gt;= 0))</span>
<span class="nc" id="L1732">			throw new IllegalArgumentException(&quot;half length must be nonnegative&quot;);</span>
<span class="nc" id="L1733">		double xs = scaleX(x);</span>
<span class="nc" id="L1734">		double ys = scaleY(y);</span>
<span class="nc" id="L1735">		double ws = factorX(2 * halfLength);</span>
<span class="nc" id="L1736">		double hs = factorY(2 * halfLength);</span>
<span class="nc bnc" id="L1737" title="All 4 branches missed.">		if (ws &lt;= 1 &amp;&amp; hs &lt;= 1)</span>
<span class="nc" id="L1738">			pixel(x, y);</span>
		else
<span class="nc" id="L1740">			offscreen.draw(new Rectangle2D.Double(xs - ws / 2, ys - hs / 2, ws, hs));</span>
<span class="nc" id="L1741">		draw();</span>
<span class="nc" id="L1742">	}</span>

	/**
	 * Write the given text string in the current font, centered at (&lt;em&gt;x&lt;/em&gt;,
	 * &lt;em&gt;y&lt;/em&gt;).
	 *
	 * @param x
	 *            the center &lt;em&gt;x&lt;/em&gt;-coordinate of the text
	 * @param y
	 *            the center &lt;em&gt;y&lt;/em&gt;-coordinate of the text
	 * @param text
	 *            the text to write
	 */
	public static void text(double x, double y, String text) {
<span class="nc bnc" id="L1756" title="All 2 branches missed.">		if (text == null)</span>
<span class="nc" id="L1757">			throw new NullPointerException();</span>
<span class="nc" id="L1758">		offscreen.setFont(font);</span>
<span class="nc" id="L1759">		FontMetrics metrics = offscreen.getFontMetrics();</span>
<span class="nc" id="L1760">		double xs = scaleX(x);</span>
<span class="nc" id="L1761">		double ys = scaleY(y);</span>
<span class="nc" id="L1762">		int ws = metrics.stringWidth(text);</span>
<span class="nc" id="L1763">		int hs = metrics.getDescent();</span>
<span class="nc" id="L1764">		offscreen.drawString(text, (float) (xs - ws / 2.0), (float) (ys + hs));</span>
<span class="nc" id="L1765">		draw();</span>
<span class="nc" id="L1766">	}</span>

	/***************************************************************************
	 * Mouse interactions.
	 ***************************************************************************/

	/**
	 * Write the given text string in the current font, centered at (&lt;em&gt;x&lt;/em&gt;,
	 * &lt;em&gt;y&lt;/em&gt;) and rotated by the specified number of degrees.
	 * 
	 * @param x
	 *            the center &lt;em&gt;x&lt;/em&gt;-coordinate of the text
	 * @param y
	 *            the center &lt;em&gt;y&lt;/em&gt;-coordinate of the text
	 * @param text
	 *            the text to write
	 * @param degrees
	 *            is the number of degrees to rotate counterclockwise
	 */
	public static void text(double x, double y, String text, double degrees) {
<span class="nc bnc" id="L1786" title="All 2 branches missed.">		if (text == null)</span>
<span class="nc" id="L1787">			throw new NullPointerException();</span>
<span class="nc" id="L1788">		double xs = scaleX(x);</span>
<span class="nc" id="L1789">		double ys = scaleY(y);</span>
<span class="nc" id="L1790">		offscreen.rotate(Math.toRadians(-degrees), xs, ys);</span>
<span class="nc" id="L1791">		text(x, y, text);</span>
<span class="nc" id="L1792">		offscreen.rotate(Math.toRadians(+degrees), xs, ys);</span>
<span class="nc" id="L1793">	}</span>

	/**
	 * Write the given text string in the current font, left-aligned at (&lt;em&gt;x&lt;/em&gt;,
	 * &lt;em&gt;y&lt;/em&gt;).
	 * 
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the text
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the text
	 * @param text
	 *            the text
	 */
	public static void textLeft(double x, double y, String text) {
<span class="nc bnc" id="L1807" title="All 2 branches missed.">		if (text == null)</span>
<span class="nc" id="L1808">			throw new NullPointerException();</span>
<span class="nc" id="L1809">		offscreen.setFont(font);</span>
<span class="nc" id="L1810">		FontMetrics metrics = offscreen.getFontMetrics();</span>
<span class="nc" id="L1811">		double xs = scaleX(x);</span>
<span class="nc" id="L1812">		double ys = scaleY(y);</span>
<span class="nc" id="L1813">		int hs = metrics.getDescent();</span>
<span class="nc" id="L1814">		offscreen.drawString(text, (float) xs, (float) (ys + hs));</span>
<span class="nc" id="L1815">		draw();</span>
<span class="nc" id="L1816">	}</span>

	/**
	 * Write the given text string in the current font, right-aligned at
	 * (&lt;em&gt;x&lt;/em&gt;, &lt;em&gt;y&lt;/em&gt;).
	 *
	 * @param x
	 *            the &lt;em&gt;x&lt;/em&gt;-coordinate of the text
	 * @param y
	 *            the &lt;em&gt;y&lt;/em&gt;-coordinate of the text
	 * @param text
	 *            the text to write
	 */
	public static void textRight(double x, double y, String text) {
<span class="nc bnc" id="L1830" title="All 2 branches missed.">		if (text == null)</span>
<span class="nc" id="L1831">			throw new NullPointerException();</span>
<span class="nc" id="L1832">		offscreen.setFont(font);</span>
<span class="nc" id="L1833">		FontMetrics metrics = offscreen.getFontMetrics();</span>
<span class="nc" id="L1834">		double xs = scaleX(x);</span>
<span class="nc" id="L1835">		double ys = scaleY(y);</span>
<span class="nc" id="L1836">		int ws = metrics.stringWidth(text);</span>
<span class="nc" id="L1837">		int hs = metrics.getDescent();</span>
<span class="nc" id="L1838">		offscreen.drawString(text, (float) (xs - ws), (float) (ys + hs));</span>
<span class="nc" id="L1839">		draw();</span>
<span class="nc" id="L1840">	}</span>

	private static double userX(double x) {
<span class="nc" id="L1843">		return xmin + x * (xmax - xmin) / width;</span>
	}

	private static double userY(double y) {
<span class="nc" id="L1847">		return ymax - y * (ymax - ymin) / height;</span>
	}

	// singleton pattern: client can't instantiate
<span class="nc" id="L1851">	private StdDraw() {</span>
<span class="nc" id="L1852">	}</span>

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void actionPerformed(ActionEvent e) {
<span class="nc" id="L1859">		FileDialog chooser = new FileDialog(StdDraw.frame, &quot;Use a .png or .jpg extension&quot;, FileDialog.SAVE);</span>
<span class="nc" id="L1860">		chooser.setVisible(true);</span>
<span class="nc" id="L1861">		String filename = chooser.getFile();</span>
<span class="nc bnc" id="L1862" title="All 2 branches missed.">		if (filename != null) {</span>
<span class="nc" id="L1863">			StdDraw.save(chooser.getDirectory() + File.separator + chooser.getFile());</span>
		}
<span class="nc" id="L1865">	}</span>

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void keyPressed(KeyEvent e) {
<span class="nc" id="L1872">		synchronized (keyLock) {</span>
<span class="nc" id="L1873">			keysDown.add(e.getKeyCode());</span>
		}
<span class="nc" id="L1875">	}</span>

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void keyReleased(KeyEvent e) {
<span class="nc" id="L1882">		synchronized (keyLock) {</span>
<span class="nc" id="L1883">			keysDown.remove(e.getKeyCode());</span>
		}
<span class="nc" id="L1885">	}</span>

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void keyTyped(KeyEvent e) {
<span class="nc" id="L1892">		synchronized (keyLock) {</span>
<span class="nc" id="L1893">			keysTyped.addFirst(e.getKeyChar());</span>
		}
<span class="nc" id="L1895">	}</span>

	/***************************************************************************
	 * Keyboard interactions.
	 ***************************************************************************/

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void mouseClicked(MouseEvent e) {
<span class="nc" id="L1906">	}</span>

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void mouseDragged(MouseEvent e) {
<span class="nc" id="L1913">		synchronized (mouseLock) {</span>
<span class="nc" id="L1914">			mouseX = StdDraw.userX(e.getX());</span>
<span class="nc" id="L1915">			mouseY = StdDraw.userY(e.getY());</span>
		}
<span class="nc" id="L1917">	}</span>

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void mouseEntered(MouseEvent e) {
<span class="nc" id="L1924">	}</span>

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void mouseExited(MouseEvent e) {
<span class="nc" id="L1931">	}</span>

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void mouseMoved(MouseEvent e) {
<span class="nc" id="L1938">		synchronized (mouseLock) {</span>
<span class="nc" id="L1939">			mouseX = StdDraw.userX(e.getX());</span>
<span class="nc" id="L1940">			mouseY = StdDraw.userY(e.getY());</span>
		}
<span class="nc" id="L1942">	}</span>

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void mousePressed(MouseEvent e) {
<span class="nc" id="L1949">		synchronized (mouseLock) {</span>
<span class="nc" id="L1950">			mouseX = StdDraw.userX(e.getX());</span>
<span class="nc" id="L1951">			mouseY = StdDraw.userY(e.getY());</span>
<span class="nc" id="L1952">			mousePressed = true;</span>
		}
<span class="nc" id="L1954">	}</span>

	/**
	 * This method cannot be called directly.
	 */
	@Override
	public void mouseReleased(MouseEvent e) {
<span class="nc" id="L1961">		synchronized (mouseLock) {</span>
<span class="nc" id="L1962">			mousePressed = false;</span>
		}
<span class="nc" id="L1964">	}</span>

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>