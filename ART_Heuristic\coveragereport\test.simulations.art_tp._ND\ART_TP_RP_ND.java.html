<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_TP_RP_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.art_tp._ND</a> &gt; <span class="el_source">ART_TP_RP_ND.java</span></div><h1>ART_TP_RP_ND.java</h1><pre class="source lang-java linenums">package test.simulations.art_tp._ND;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import util.PaiLie;
import util.X3;
import util.data.ZeroOneCreator;

/*
 * 	// generate PDF in this max region
	// PDF=C*(x1-u1)*(v1-x1)*(x2-u2)*(v2-x2)*....*(xn-un)*(vn-xn)
	// first we need to get u1...un and v1...vn and
	// int(from[],to[])（积分上下限）
	// second calculate each dimension integral value
	// C=1.0/(intValueX1*intValueX2*...*intValueXn)
	// 生成每一维的边缘分布，fx1=C*IntValue2*...*intValueXn*(x1-u1)*(v1-x1) ....
	// int(fxk,from[k],xk)=T (T is a random Number from 0-1 )
	// set p=([x1,x2...,xn])
 * **/
public class ART_TP_RP_ND extends ART {
	public static void main(String[] args) {
		// 一定要修改这个
<span class="nc" id="L31">		int d = 4;</span>
		// m 维立方体

<span class="nc" id="L34">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>

<span class="nc" id="L36">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L37">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L39">		int times = 3000;</span>
<span class="nc" id="L40">		long sums = 0;</span>
<span class="nc" id="L41">		long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L42">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L43">		failurePattern.fail_rate = 0.01;</span>
<span class="nc bnc" id="L44" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L45">			ART_TP_RP_ND art_TP_RP_ND = new ART_TP_RP_ND(min, max, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L46">			int fm = art_TP_RP_ND.run();</span>
<span class="nc" id="L47">			sums += fm;</span>
		}
<span class="nc" id="L49">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L50">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L51">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L52">	}</span>
<span class="nc" id="L53">	ArrayList&lt;NRectRegion&gt; regions = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L55">	ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>

	public ART_TP_RP_ND(double[] min, double[] max, FailurePattern pattern, Random random) {
<span class="nc" id="L58">		super(min, max, random, pattern);</span>
<span class="nc" id="L59">	}</span>

	public double calAllIntEC(double u[], double v[], double from[], double to[]) {
<span class="nc" id="L62">		double value = 0;</span>
<span class="nc bnc" id="L63" title="All 2 branches missed.">		for (int i = 0; i &lt; u.length; i++) {</span>
<span class="nc bnc" id="L64" title="All 2 branches missed.">			if (i == 0) {</span>
<span class="nc" id="L65">				value = calEachIntEC(u[i], v[i], from[i], to[i]);</span>
<span class="nc" id="L66">			} else</span>
<span class="nc" id="L67">				value *= calEachIntEC(u[i], v[i], from[i], to[i]);</span>
		}
<span class="nc" id="L69">		return value;</span>
	}

	// public void genFail_start() {
	// NPoint start = initRegion.getStart();
	// NPoint end = initRegion.getEnd();
	// double fail_size = initRegion.size() * fail_rate;
	// double each_rate = Math.pow(fail_size, 1.0 / (double) dimension);
	// ///
	// for (int i = 0; i &lt; fail_start.length; i++) {
	// fail_start[i] = random.nextDouble() * (end.getXn()[i] - start.getXn()[i] -
	// each_rate) + start.getXn()[i];
	//// System.out.println(&quot;fail_start&quot; + i + &quot; (&quot; + fail_start[i] + &quot;,&quot; +
	// (fail_start[i] + each_rate) + &quot;)&quot;);
	// }
	// }

	// public NPoint genFirstTC() {
	// NPoint p = new NPoint();
	// NPoint start = initRegion.getStart();
	// NPoint end = initRegion.getEnd();
	// double xn[] = new double[dimension];
	// for (int i = 0; i &lt; xn.length; i++) {
	// xn[i] = random.nextDouble() * (end.getXn()[i] - start.getXn()[i]) +
	// start.getXn()[i];
	// }
	// p.setXn(xn);
	// return p;
	// }

	public double calEachIntEC(double u, double v, double f, double t) {
		// int((x-u)*(v-x),f,t)
<span class="nc" id="L101">		return -(1.0 / 3.0) * (t * t * t - f * f * f) + (0.5 * (u + v) * (t * t - f * f)) - u * v * (t - f);</span>
	}

	public double calEachIntEC2(double s, double e, double f, double t) {
<span class="nc" id="L105">		return (-1.0 / 6.0) * (e - s)</span>
<span class="nc" id="L106">				* (e * (-3 * f + 2 * s - 3 * t) - 3 * f * s + 6 * f * t + 2 * s * s - 3 * s * t + 2 * e * e);</span>
	}

	public int findMaxRegion() {
<span class="nc" id="L110">		double maxsize = 0;</span>
<span class="nc" id="L111">		int maxregion_index = 0;</span>
<span class="nc bnc" id="L112" title="All 2 branches missed.">		for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc" id="L113">			NRectRegion temp = regions.get(i);</span>
<span class="nc bnc" id="L114" title="All 2 branches missed.">			if (temp.size() &gt; maxsize) {</span>
<span class="nc" id="L115">				maxsize = temp.size();</span>
<span class="nc" id="L116">				maxregion_index = i;</span>
			}
		}
<span class="nc" id="L119">		return maxregion_index;</span>
	}

	// public boolean isCorrect(NPoint p) {
	// double xn[] = p.getXn();
	// double fail_size = initRegion.size() * fail_rate;
	// double each_rate = Math.pow(fail_size, 1.0 / (double) dimension);
	// boolean flag = true;
	// for (int i = 0; i &lt; xn.length; i++) {
	// if ((xn[i] &lt; fail_start[i]) || (xn[i] &gt; fail_start[i] + each_rate)) {
	// flag = false;
	// }
	// }
	// return !flag;
	// }

	public double[] genEachIntEC(double u[], double v[], double from[], double to[]) {
<span class="nc" id="L136">		double results[] = new double[u.length];</span>
<span class="nc bnc" id="L137" title="All 2 branches missed.">		for (int i = 0; i &lt; u.length; i++) {</span>
<span class="nc" id="L138">			results[i] = calEachIntEC(u[i], v[i], from[i], to[i]);</span>
		}
<span class="nc" id="L140">		return results;</span>
	}

	public double[] genEachNext(double u[], double v[], double from[], double to[], double[] eachIntValueEC) {
<span class="nc" id="L144">		double eachDimension[] = new double[u.length];</span>
<span class="nc bnc" id="L145" title="All 2 branches missed.">		for (int i = 0; i &lt; u.length; i++) {</span>
<span class="nc" id="L146">			double T = random.nextDouble();</span>
<span class="nc" id="L147">			double Co = 1.0 / eachIntValueEC[i];</span>
			// System.out.println(&quot;Co:&quot;+Co);
			// int(CO*(x-u1)*(v1-x),from,x)=T
			// Co*{(-1.0/3.0)*(x^3)+0.5*(u+v)*x^2-u*v*x}(from.x)=T
<span class="nc" id="L151">			double A = -(1.0 / 3.0) * Co;</span>
<span class="nc" id="L152">			double B = 0.5 * (u[i] + v[i]) * Co;</span>
<span class="nc" id="L153">			double C = -u[i] * v[i] * Co;</span>
<span class="nc" id="L154">			double D = -Co * (-(1.0 / 3.0) * from[i] * from[i] * from[i] + 0.5 * (u[i] + v[i]) * from[i] * from[i]</span>
<span class="nc" id="L155">					- u[i] * v[i] * from[i]) - T;</span>
<span class="nc" id="L156">			double results[] = X3.shengjinFormula(A, B, C, D);</span>
<span class="nc" id="L157">			boolean flag = true;</span>
<span class="nc bnc" id="L158" title="All 2 branches missed.">			for (int j = 0; j &lt; results.length; j++) {</span>
<span class="nc bnc" id="L159" title="All 4 branches missed.">				if (results[j] &gt; from[i] &amp;&amp; results[j] &lt; to[i]) {</span>
<span class="nc" id="L160">					eachDimension[i] = results[j];</span>
<span class="nc" id="L161">					flag = false;</span>
<span class="nc" id="L162">					break;</span>
				}
			}
<span class="nc bnc" id="L165" title="All 2 branches missed.">			if (flag) {</span>
<span class="nc" id="L166">				System.out.println(&quot;genEachNext(&quot; + i + &quot;th dimension) failed&quot;);</span>
			}
		}
<span class="nc" id="L169">		return eachDimension;</span>
	}

	public double[] genUorV(NRectRegion region, String flag) {
<span class="nc" id="L173">		double[] results = null;</span>
<span class="nc" id="L174">		double e1[] = minAllTC();</span>
<span class="nc" id="L175">		double en[] = maxAllTC();</span>
<span class="nc bnc" id="L176" title="All 2 branches missed.">		if (flag.equalsIgnoreCase(&quot;u&quot;)) {</span>
			// double starts[] = initRegion.getStart().getXn();
<span class="nc" id="L178">			results = Arrays.copyOf(region.getStart().getXn(), region.getStart().getXn().length);</span>
<span class="nc bnc" id="L179" title="All 2 branches missed.">			for (int i = 0; i &lt; results.length; i++) {</span>
<span class="nc bnc" id="L180" title="All 2 branches missed.">				if (results[i] == min[i]) {</span>
<span class="nc" id="L181">					results[i] = random.nextDouble() * (e1[i] - min[i]) + (2 * min[i] - e1[i]);</span>
				}
			}
<span class="nc bnc" id="L184" title="All 2 branches missed.">		} else if (flag.equalsIgnoreCase(&quot;v&quot;)) {</span>
<span class="nc" id="L185">			results = Arrays.copyOf(region.getEnd().getXn(), region.getEnd().getXn().length);</span>
			// double ends[] = initRegion.getEnd().getXn();
<span class="nc bnc" id="L187" title="All 2 branches missed.">			for (int i = 0; i &lt; results.length; i++) {</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">				if (results[i] == max[i]) {</span>
<span class="nc" id="L189">					results[i] = random.nextDouble() * (max[i] - en[i]) + max[i];</span>
					// System.out.println(&quot;endsi:&quot;+ends[i]+&quot; &quot;+ en.getXn()[i]);
				}
			}
<span class="nc" id="L193">		} else {</span>
<span class="nc" id="L194">			System.out.println(&quot;genUorV error (not u or v)&quot;);</span>
		}
<span class="nc" id="L196">		return results;</span>
	}

	public double[] maxAllTC() {
<span class="nc" id="L200">		double[][] results = new double[tests.size()][dimension];</span>
		// 得到每一列的值
<span class="nc bnc" id="L202" title="All 2 branches missed.">		for (int i = 0; i &lt; tests.size(); i++) {</span>
<span class="nc bnc" id="L203" title="All 2 branches missed.">			for (int j = 0; j &lt; tests.get(i).getXn().length; j++) {</span>
<span class="nc" id="L204">				results[i][j] = tests.get(i).getXn()[j];</span>
			}
		}
<span class="nc" id="L207">		double[] max = results[0];</span>
<span class="nc bnc" id="L208" title="All 2 branches missed.">		for (int i = 0; i &lt; results.length; i++) {</span>
<span class="nc bnc" id="L209" title="All 2 branches missed.">			for (int j = 0; j &lt; results[i].length; j++) {</span>
<span class="nc bnc" id="L210" title="All 2 branches missed.">				if (max[j] &lt; results[i][j]) {</span>
<span class="nc" id="L211">					max[j] = results[i][j];</span>
				}
			}
		}
<span class="nc" id="L215">		return max;</span>
	}

	public double[] minAllTC() {
<span class="nc" id="L219">		double[][] results = new double[tests.size()][dimension];</span>
		// 得到每一列的值
<span class="nc bnc" id="L221" title="All 2 branches missed.">		for (int i = 0; i &lt; tests.size(); i++) {</span>
<span class="nc bnc" id="L222" title="All 2 branches missed.">			for (int j = 0; j &lt; tests.get(i).getXn().length; j++) {</span>
<span class="nc" id="L223">				results[i][j] = tests.get(i).getXn()[j];</span>
			}
		}
<span class="nc" id="L226">		double[] min = results[0];</span>
<span class="nc bnc" id="L227" title="All 2 branches missed.">		for (int i = 0; i &lt; results.length; i++) {</span>
<span class="nc bnc" id="L228" title="All 2 branches missed.">			for (int j = 0; j &lt; results[i].length; j++) {</span>
<span class="nc bnc" id="L229" title="All 2 branches missed.">				if (min[j] &gt; results[i][j]) {</span>
<span class="nc" id="L230">					min[j] = results[i][j];</span>
				}
			}
		}
<span class="nc" id="L234">		return min;</span>
	}

	public int run() {
<span class="nc" id="L238">		int count = 0;</span>
		// 设置最大区域

		// 添加初始区域
<span class="nc" id="L242">		NRectRegion initRegion = new NRectRegion();</span>
<span class="nc" id="L243">		initRegion.setStart(new NPoint(min));</span>
<span class="nc" id="L244">		initRegion.setEnd(new NPoint(max));</span>
<span class="nc" id="L245">		regions.add(initRegion);</span>
		// first test case
<span class="nc" id="L247">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc bnc" id="L248" title="All 2 branches missed.">		if (!this.failPattern.isCorrect(p)) {</span>
<span class="nc" id="L249">			return count;</span>
		}
<span class="nc" id="L251">		tests.add(p);</span>
<span class="nc" id="L252">		updateRegions(0, p);</span>

<span class="nc" id="L254">		boolean flag = true;</span>
<span class="nc bnc" id="L255" title="All 2 branches missed.">		while (flag) {</span>
<span class="nc" id="L256">			count++;</span>
			// System.out.println(&quot;----------------------&quot;);
			// find the largest region
<span class="nc" id="L259">			int indexOfLargest = findMaxRegion();</span>
<span class="nc" id="L260">			NRectRegion maxregion = regions.get(indexOfLargest);</span>
			// System.out.println(&quot;max region:&quot; + maxregion);

<span class="nc" id="L263">			double u[] = genUorV(maxregion, &quot;U&quot;);</span>
<span class="nc" id="L264">			double v[] = genUorV(maxregion, &quot;V&quot;);</span>
			// double u[] = maxregion.getStart().getXn();
			// double v[] = maxregion.getEnd().getXn();
			// System.out.println(maxregion);
			// 积分上下限就是max region的边界
<span class="nc" id="L269">			double from[] = maxregion.getStart().getXn();</span>
<span class="nc" id="L270">			double to[] = maxregion.getEnd().getXn();</span>
			// 计算每一维的积分值
<span class="nc" id="L272">			double eachIntValueEC[] = genEachIntEC(u, v, from, to);</span>
			// double C = 1.0 / calAllIntEC(u, v, from, to);//没什么用处。。。
			// 每一维的边缘密度函数 fx1=1.0/(intValueX1)*(x1-u1)*(v1-x1)
<span class="nc" id="L275">			double results[] = genEachNext(u, v, from, to, eachIntValueEC);</span>
			// new NPoint
<span class="nc" id="L277">			p = new NPoint();</span>
<span class="nc" id="L278">			p.setXn(results);</span>

<span class="nc bnc" id="L280" title="All 2 branches missed.">			if (!this.failPattern.isCorrect(p)) {</span>
<span class="nc" id="L281">				flag = false;</span>
			}

<span class="nc" id="L284">			updateRegions(indexOfLargest, p);</span>
		}
<span class="nc" id="L286">		return count;</span>
	}

	public void updateRegions(int IndexOfMaxRegion, NPoint p) {
<span class="nc" id="L290">		NRectRegion maxregion = regions.get(IndexOfMaxRegion);</span>
<span class="nc" id="L291">		regions.remove(IndexOfMaxRegion);</span>
		/// update new regions
<span class="nc" id="L293">		int quyus = (int) Math.pow(2, dimension);</span>
<span class="nc" id="L294">		ArrayList&lt;double[]&gt; unFormedRegion = PaiLie.GetAll(maxregion.getStart().getXn(), maxregion.getEnd().getXn());</span>
		// System.out.println(&quot;new P:&quot;+p);
<span class="nc" id="L296">		ArrayList&lt;List&lt;double[]&gt;&gt; newRegionArea = PaiLie.reOrder(p.getXn(), unFormedRegion);</span>
<span class="nc bnc" id="L297" title="All 2 branches missed.">		for (int i = 0; i &lt; quyus; i++) {</span>
<span class="nc" id="L298">			NPoint start = new NPoint(newRegionArea.get(0).get(i));</span>
<span class="nc" id="L299">			NPoint end = new NPoint(newRegionArea.get(1).get(i));</span>
<span class="nc" id="L300">			NRectRegion region = new NRectRegion(start, end);</span>
			// System.out.println(&quot;regions:&quot; + region);
<span class="nc" id="L302">			regions.add(region);</span>
		}
<span class="nc" id="L304">	}</span>

	@Override
	public int em() {
		// TODO Auto-generated method stub
<span class="nc" id="L309">		return 0;</span>
	}

	@Override
	public NPoint generateNextTC() {
		// TODO Auto-generated method stub
<span class="nc" id="L315">		return null;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>