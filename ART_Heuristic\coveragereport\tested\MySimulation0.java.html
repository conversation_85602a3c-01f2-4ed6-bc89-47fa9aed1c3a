<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MySimulation0.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">MySimulation0.java</span></div><h1>MySimulation0.java</h1><pre class="source lang-java linenums">package tested;

/*
 * Input Domain:(0,1)
 * failure rate:0.005
 *errors: 1 errors(1 xor)
 * */
<span class="nc" id="L8">public class MySimulation0 {</span>
	public static void main(String[] args) {
<span class="nc" id="L10">		MySimulation0 sim = new MySimulation0();</span>
<span class="nc" id="L11">		System.out.println(sim.wrong(0.346));</span>
		;
<span class="nc" id="L13">		System.out.println(sim.correct(0.346));</span>
<span class="nc" id="L14">		System.out.println(sim.isCorrect(0.346));</span>
<span class="nc" id="L15">	}</span>
<span class="nc" id="L16">	public static  double min = 0.0;</span>
<span class="nc" id="L17">	public static  double max = 1.0;</span>

<span class="nc" id="L19">	public static  double failurerate = 0.005;</span>

	public String correct(double x) {
<span class="nc" id="L22">		StringBuffer buffer = new StringBuffer();</span>
<span class="nc bnc" id="L23" title="All 4 branches missed.">		if (x &gt; 0 &amp;&amp; x &lt; 1.0) {</span>
<span class="nc" id="L24">			buffer.append(&quot;yes1&quot;);</span>
<span class="nc bnc" id="L25" title="All 4 branches missed.">			if (x &gt; 0.5 &amp;&amp; x &lt; 0.95) {</span>
<span class="nc" id="L26">				buffer.append(&quot;yes11&quot;);</span>
<span class="nc" id="L27">				buffer.append(&quot;yes22&quot;);</span>
<span class="nc bnc" id="L28" title="All 4 branches missed.">				if ((x &gt; 0.65 &amp;&amp; x &lt; 0.655)) {</span>
<span class="nc" id="L29">					buffer.append(&quot;yes111&quot;);</span>
<span class="nc" id="L30">					buffer.append(&quot;yes222&quot;);</span>
<span class="nc" id="L31">					buffer.append(&quot;yes333&quot;);</span>
<span class="nc" id="L32">					buffer.append(&quot;yes444&quot;);</span>
<span class="nc" id="L33">					buffer.append(&quot;yes555&quot;);</span>
<span class="nc" id="L34">					buffer.append(&quot;yes666&quot;);</span>
<span class="nc" id="L35">					buffer.append(&quot;yes777&quot;);</span>
<span class="nc" id="L36">					buffer.append(&quot;yes888&quot;);</span>
<span class="nc" id="L37">					buffer.append(&quot;yes999&quot;);</span>
<span class="nc" id="L38">					buffer.append(&quot;yes111&quot;);</span>
<span class="nc" id="L39">					buffer.append(&quot;yes222&quot;);</span>
<span class="nc" id="L40">					buffer.append(&quot;yes333&quot;);</span>
<span class="nc" id="L41">					buffer.append(&quot;yes444&quot;);</span>
<span class="nc" id="L42">					buffer.append(&quot;yes555&quot;);</span>
<span class="nc" id="L43">					buffer.append(&quot;yes666&quot;);</span>
<span class="nc" id="L44">					buffer.append(&quot;yes777&quot;);</span>
<span class="nc" id="L45">					buffer.append(&quot;yes888&quot;);</span>
<span class="nc" id="L46">					buffer.append(&quot;yes999&quot;);</span>
<span class="nc" id="L47">					buffer.append(&quot;yes101010&quot;);</span>
<span class="nc" id="L48">					buffer.append(&quot;yes11111&quot;);</span>
				}
			}
<span class="nc" id="L51">		} else {</span>
<span class="nc" id="L52">			buffer.append(&quot;no1&quot;);</span>
		}
<span class="nc" id="L54">		return buffer.toString();</span>
	}

	public boolean isCorrect(double x) {
		// System.out.println(&quot;correct:&quot;+correct(x));
		// System.out.println(&quot;wrong:&quot;+wrong(x));
<span class="nc" id="L60">		return correct(x).equals(wrong(x));</span>
	}

	public String wrong(double x) {
<span class="nc" id="L64">		StringBuffer buffer = new StringBuffer();</span>
<span class="nc bnc" id="L65" title="All 4 branches missed.">		if (x &gt; 0 &amp;&amp; x &lt; 1.0) {</span>
<span class="nc" id="L66">			buffer.append(&quot;yes1&quot;);</span>
<span class="nc bnc" id="L67" title="All 4 branches missed.">			if (x &gt; 0.5 &amp;&amp; x &lt; 0.95) {</span>
<span class="nc" id="L68">				buffer.append(&quot;yes11&quot;);</span>
<span class="nc" id="L69">				buffer.append(&quot;yes22&quot;);</span>
<span class="nc bnc" id="L70" title="All 4 branches missed.">				if ((x &gt; 0.65 &amp;&amp; x &lt; 0.655)) {</span>
<span class="nc" id="L71">					buffer.append(&quot;yes111&quot;);</span>
<span class="nc" id="L72">					buffer.append(&quot;yes222&quot;);</span>
<span class="nc" id="L73">					buffer.append(&quot;yes333&quot;);</span>
<span class="nc" id="L74">					buffer.append(&quot;yes444&quot;);</span>
<span class="nc" id="L75">					buffer.append(&quot;yes555&quot;);</span>
<span class="nc" id="L76">					buffer.append(&quot;yes666&quot;);</span>
<span class="nc" id="L77">					buffer.append(&quot;yes777&quot;);</span>
<span class="nc" id="L78">					buffer.append(&quot;yes888&quot;);</span>
<span class="nc" id="L79">					buffer.append(&quot;yes999&quot;);</span>
<span class="nc" id="L80">					buffer.append(&quot;yes111&quot;);</span>
<span class="nc" id="L81">					buffer.append(&quot;yes222&quot;);</span>
<span class="nc" id="L82">					buffer.append(&quot;yes333&quot;);</span>
<span class="nc" id="L83">					buffer.append(&quot;yes444&quot;);</span>
<span class="nc" id="L84">					buffer.append(&quot;yes555&quot;);</span>
<span class="nc" id="L85">					buffer.append(&quot;yes666&quot;);</span>
<span class="nc" id="L86">					buffer.append(&quot;yes777&quot;);</span>
<span class="nc" id="L87">					buffer.append(&quot;yes888&quot;);</span>
<span class="nc" id="L88">					buffer.append(&quot;yes999&quot;);</span>
<span class="nc" id="L89">					buffer.append(&quot;yes101010&quot;);</span>
<span class="nc" id="L90">					buffer.append(&quot;yes111111&quot;);</span>
				}
			}
<span class="nc" id="L93">		} else {</span>
<span class="nc" id="L94">			buffer.append(&quot;no1&quot;);</span>
		}
<span class="nc" id="L96">		return buffer.toString();</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>