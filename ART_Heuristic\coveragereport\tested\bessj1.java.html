<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>bessj1.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">tested</a> &gt; <span class="el_source">bessj1.java</span></div><h1>bessj1.java</h1><pre class="source lang-java linenums">package tested;

/**
 * made by others Created by xijiaxiang on 2016/3/25.
 */
<span class="nc" id="L6">public class bessj1 {</span>

	public double wrong(double x) {
		float ax, z;
		double xx, y, ans, ans1, ans2;

		/* Correct */
		/* if ((ax=fabs(x)) &lt; 8.0) { */
		/* ERROR */// if ((ax=(float) Math.abs(x)) &lt;= 10.0)
<span class="nc bnc" id="L15" title="All 2 branches missed.">		if ((ax = (float) Math.abs(x)) &lt;= 10.0) {</span>
<span class="nc" id="L16">			y = x * x;</span>
<span class="nc" id="L17">			ans1 = 57568490574.0 + y * (-13362590354.0</span>
<span class="nc" id="L18">					+ y * (651619640.7 + y * (-11214424.18 + y * (77392.33017 + y * (-184.9052456)))));</span>
<span class="nc" id="L19">			ans2 = 57568490411.0 + y * (1029532985.0 + y * (9494680.718</span>
					/* Correct */
					/* +y*(59272.64853+y*(267.8532712+y*1.0)))); */
					/* Wrong */// +y*(59272.64853+y*(267.8532712+y+1.0))))
<span class="nc" id="L23">					+ y * (59272.64853 + y * (267.8532712 + y + 1.0))));</span>
<span class="nc" id="L24">			ans = ans1 / ans2;</span>
<span class="nc" id="L25">		} else {</span>
<span class="nc" id="L26">			z = (float) (8.0 / ax);</span>
<span class="nc" id="L27">			y = z * z;</span>
<span class="nc" id="L28">			xx = ax - 0.785398164;</span>
<span class="nc" id="L29">			ans1 = 1.0 + y * (-0.1098628627e-2 + y * (0.2734510407e-4 + y * (-0.2073370639e-5 + y * 0.2093887211e-6)));</span>
<span class="nc" id="L30">			ans2 = -0.1562499995e-1 + y * (0.1430488765e-3</span>
					/* ERROR */
					/* +y*(-0.6911147651e-5+y*(0.7621095161e-6 */
<span class="nc" id="L33">					+ y * (-0.6911147651e-5 + z * (0.7621095161e-6</span>
							/* ERROR */
							/* -y*0.934935152e-7))); */
<span class="nc" id="L36">							+ y * 0.934935152e-7)));</span>
<span class="nc" id="L37">			ans = Math.sqrt(0.636619772 / ax) * (Math.cos(xx) * ans1 - z * Math.sin(xx) * ans2);</span>
		}
<span class="nc" id="L39">		return ans;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>