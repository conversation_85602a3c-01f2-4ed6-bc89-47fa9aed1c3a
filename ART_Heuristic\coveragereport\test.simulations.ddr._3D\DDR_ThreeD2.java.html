<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>DDR_ThreeD2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.ddr._3D</a> &gt; <span class="el_source">DDR_ThreeD2.java</span></div><h1>DDR_ThreeD2.java</h1><pre class="source lang-java linenums">package test.simulations.ddr._3D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

public class DDR_ThreeD2 {
	public static void main(String[] args) {
<span class="nc" id="L10">		int times = 100;</span>
<span class="nc" id="L11">		long sums = 0;</span>
<span class="nc" id="L12">		int temp = 0;</span>
<span class="nc" id="L13">		int s = 10;</span>
		//////////////
<span class="nc" id="L15">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L16" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L17">			double min[] = { 0.0, 0.0, 0.0 };</span>
<span class="nc" id="L18">			double max[] = { 1.0, 1.0, 1.0 };</span>
<span class="nc" id="L19">			DDR_ThreeD2 rrt_od = new DDR_ThreeD2(min, max, 0.75, s, 0.001, i * 3);</span>
<span class="nc" id="L20">			temp = rrt_od.run();</span>
<span class="nc" id="L21">			sums += temp;</span>
		}
<span class="nc" id="L23">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L24">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L25">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L26">	}</span>
	double[] min;
	double[] max;
	double[] fail_start;
	double fail_rate;
	double R;
	int s;
	int randomseed;
	double fail_regionS;
	double periodLength;

<span class="nc" id="L37">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

	public DDR_ThreeD2(double[] min, double[] max, double r, int s, double fail_rate, int randomseed) {
<span class="nc" id="L40">		super();</span>
<span class="nc" id="L41">		this.min = min;</span>
<span class="nc" id="L42">		this.max = max;</span>
<span class="nc" id="L43">		R = r;</span>
<span class="nc" id="L44">		this.s = s;</span>
<span class="nc" id="L45">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L46">		this.randomseed = randomseed;</span>
<span class="nc" id="L47">		this.fail_start = new double[this.min.length];</span>
<span class="nc" id="L48">		this.fail_regionS = fail_rate;</span>
<span class="nc bnc" id="L49" title="All 2 branches missed.">		for (int i = 0; i &lt; this.min.length; i++) {</span>
<span class="nc" id="L50">			this.fail_regionS *= (max[i] - min[i]);</span>
		}
<span class="nc" id="L52">		periodLength = Math.pow(this.fail_regionS, 1.0 / (double) this.min.length);</span>
<span class="nc" id="L53">	}</span>

	public boolean isCorrect(TestCase p) {
<span class="nc" id="L56">		boolean isCorrect = true;</span>
<span class="nc" id="L57">		double periodLength = Math.pow(fail_regionS, 1.0 / (double) this.min.length);</span>
<span class="nc bnc" id="L58" title="All 4 branches missed.">		if (p.p &gt; fail_start[0] &amp;&amp; p.p &lt; fail_start[0] + periodLength) {</span>
<span class="nc bnc" id="L59" title="All 4 branches missed.">			if (p.q &gt; fail_start[1] &amp;&amp; p.q &lt; fail_start[1] + periodLength) {</span>
<span class="nc bnc" id="L60" title="All 4 branches missed.">				if (p.m &gt; fail_start[2] &amp;&amp; p.m &lt; fail_start[2] + periodLength) {</span>
<span class="nc" id="L61">					isCorrect = false;</span>
				}
			}
		}
<span class="nc" id="L65">		return isCorrect;</span>
	}

	public TestCase randomTC(Random random) {
<span class="nc" id="L69">		TestCase temp = new TestCase();</span>
<span class="nc" id="L70">		double p = random.nextDouble() * (max[0] - min[0]) + min[0];</span>
<span class="nc" id="L71">		double q = random.nextDouble() * (max[1] - min[1]) + min[1];</span>
<span class="nc" id="L72">		double m = random.nextDouble() * (max[2] - min[2]) + min[2];</span>
<span class="nc" id="L73">		temp.p = p;</span>
<span class="nc" id="L74">		temp.q = q;</span>
<span class="nc" id="L75">		temp.m = m;</span>
<span class="nc" id="L76">		return temp;</span>
	}

	public int run() {
<span class="nc" id="L80">		Random random = new Random(randomseed);</span>
<span class="nc bnc" id="L81" title="All 2 branches missed.">		for (int i = 0; i &lt; this.min.length; i++) {</span>
<span class="nc" id="L82">			fail_start[i] = random.nextDouble() * (max[i] - min[i] - periodLength) + min[i];</span>
<span class="nc" id="L83">			System.out.print(fail_start[i] + &quot; &quot;);</span>
		}
<span class="nc" id="L85">		System.out.println();</span>
<span class="nc" id="L86">		int count = 0;</span>
<span class="nc" id="L87">		int _10CandidateCount = 0;</span>
<span class="nc" id="L88">		TestCase p = randomTC(random);// randomTC</span>
<span class="nc" id="L89">		System.out.println(&quot;p0:&quot; + p.toString() + isCorrect(p));</span>
<span class="nc bnc" id="L90" title="All 2 branches missed.">		while (isCorrect(p)) {</span>
<span class="nc" id="L91">			double InputDomainA = 1.0;</span>
<span class="nc bnc" id="L92" title="All 2 branches missed.">			for (int i = 0; i &lt; this.min.length; i++) {</span>
<span class="nc" id="L93">				InputDomainA *= max[i] - min[i];</span>
			}
<span class="nc" id="L95">			double radius = Math.pow((0.75 * (InputDomainA * R)) / ((_10CandidateCount + s) * (Math.PI)), 1.0 / 3.0);</span>
<span class="nc" id="L96">			System.out.println(&quot;radius:&quot; + radius);</span>
<span class="nc" id="L97">			p = new TestCase();</span>
<span class="nc" id="L98">			boolean all_s_has_E_flag = true;</span>
<span class="nc" id="L99">			double TS2C[] = new double[s];</span>
<span class="nc" id="L100">			double Pvalue[] = new double[s];</span>
<span class="nc" id="L101">			double Qvalue[] = new double[s];</span>
<span class="nc" id="L102">			double Mvalue[] = new double[s];</span>

<span class="nc bnc" id="L104" title="All 2 branches missed.">			for (int k = 0; k &lt; s; k++) {</span>
<span class="nc" id="L105">				TestCase ck = randomTC(random);</span>
<span class="nc" id="L106">				boolean this_ck_has_E_flag = false;</span>
<span class="nc" id="L107">				double min = Double.MAX_VALUE;</span>
<span class="nc bnc" id="L108" title="All 2 branches missed.">				for (int i = 0; i &lt; tests.size(); i++) {</span>
					// 没有在圈之中
<span class="nc" id="L110">					double distance = Math.pow((Math.pow((ck.p - tests.get(i).p), 2.0))</span>
<span class="nc" id="L111">							+ (Math.pow(ck.q - tests.get(i).q, 2.0)) + (Math.pow(ck.m - tests.get(i).m, 2.0)), 0.5);// distance</span>
<span class="nc bnc" id="L112" title="All 2 branches missed.">					if (distance &lt; radius) {</span>
<span class="nc bnc" id="L113" title="All 2 branches missed.">						if (min &gt; distance) {</span>
<span class="nc" id="L114">							min = distance;</span>
<span class="nc" id="L115">							TS2C[k] = min;</span>
<span class="nc" id="L116">							Pvalue[k] = ck.p;</span>
<span class="nc" id="L117">							Qvalue[k] = ck.q;</span>
<span class="nc" id="L118">							Mvalue[k] = ck.m;</span>
						}
<span class="nc" id="L120">						this_ck_has_E_flag = true;</span>
					}
				}
<span class="nc bnc" id="L123" title="All 2 branches missed.">				if (!this_ck_has_E_flag) {</span>
<span class="nc" id="L124">					all_s_has_E_flag = false;</span>
<span class="nc" id="L125">					p = new TestCase();</span>
<span class="nc" id="L126">					p.p = ck.p;</span>
<span class="nc" id="L127">					p.q = ck.q;</span>
<span class="nc" id="L128">					p.m = ck.m;</span>
<span class="nc" id="L129">					System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.toString() + isCorrect(p) + &quot; rrt&quot;);</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">					if (!isCorrect(p)) {</span>
<span class="nc" id="L131">						return count;</span>
					} else {
<span class="nc" id="L133">						count++;</span>
<span class="nc" id="L134">						tests.add(p);</span>
					}
				}
			}
<span class="nc bnc" id="L138" title="All 2 branches missed.">			if (all_s_has_E_flag) {</span>
<span class="nc" id="L139">				double max = 0;</span>
<span class="nc" id="L140">				int index = 0;</span>
<span class="nc bnc" id="L141" title="All 2 branches missed.">				for (int i = 0; i &lt; TS2C.length; i++) {</span>
<span class="nc bnc" id="L142" title="All 2 branches missed.">					if (max &lt; TS2C[i]) {</span>
<span class="nc" id="L143">						max = TS2C[i];</span>
<span class="nc" id="L144">						index = i;</span>
					}
				}
<span class="nc" id="L147">				p = new TestCase();</span>
<span class="nc" id="L148">				p.p = Pvalue[index];</span>
<span class="nc" id="L149">				p.q = Qvalue[index];</span>
<span class="nc" id="L150">				p.m = Mvalue[index];</span>
<span class="nc" id="L151">				System.out.println(&quot;p&quot; + count + &quot;:&quot; + p.toString() + isCorrect(p) + &quot; fscs&quot;);</span>
<span class="nc bnc" id="L152" title="All 2 branches missed.">				if (!isCorrect(p)) {</span>
<span class="nc" id="L153">					return count;</span>
				} else {
<span class="nc" id="L155">					count++;</span>
<span class="nc" id="L156">					tests.add(p);</span>
				}
			}
<span class="nc" id="L159">			_10CandidateCount++;</span>
		}
<span class="nc" id="L161">		return count;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>