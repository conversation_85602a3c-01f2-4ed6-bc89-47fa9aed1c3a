<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>StdDraw</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">util.draw</a> &gt; <span class="el_class">StdDraw</span></div><h1>StdDraw</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">2,362 of 2,362</td><td class="ctr2">0%</td><td class="bar">190 of 190</td><td class="ctr2">0%</td><td class="ctr1">170</td><td class="ctr2">170</td><td class="ctr1">535</td><td class="ctr2">535</td><td class="ctr1">75</td><td class="ctr2">75</td></tr></tfoot><tbody><tr><td id="a41"><a href="StdDraw.java.html#L1268" class="el_method">picture(double, double, String, double, double, double)</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="117" alt="117"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="80" height="10" title="12" alt="12"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h3">19</td><td class="ctr2" id="i3">19</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a40"><a href="StdDraw.java.html#L1224" class="el_method">picture(double, double, String, double, double)</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="107" height="10" title="105" alt="105"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="80" height="10" title="12" alt="12"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f2">7</td><td class="ctr2" id="g2">7</td><td class="ctr1" id="h4">17</td><td class="ctr2" id="i4">17</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a46"><a href="StdDraw.java.html#L1403" class="el_method">save(String)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="107" height="10" title="105" alt="105"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d11"><img src="../.resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f11">4</td><td class="ctr2" id="g11">4</td><td class="ctr1" id="h2">22</td><td class="ctr2" id="i2">22</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a20"><a href="StdDraw.java.html#L965" class="el_method">init()</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="103" height="10" title="101" alt="101"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d20"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f20">2</td><td class="ctr2" id="g20">2</td><td class="ctr1" id="h1">32</td><td class="ctr2" id="i1">32</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a67"><a href="StdDraw.java.html#L440" class="el_method">static {...}</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="96" height="10" title="94" alt="94"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d37"/><td class="ctr2" id="e37">n/a</td><td class="ctr1" id="f37">1</td><td class="ctr2" id="g37">1</td><td class="ctr1" id="h0">33</td><td class="ctr2" id="i0">33</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a16"><a href="StdDraw.java.html#L899" class="el_method">getImage(String)</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="87" height="10" title="85" alt="85"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="18" alt="18"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f0">10</td><td class="ctr2" id="g0">10</td><td class="ctr1" id="h5">17</td><td class="ctr2" id="i5">17</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a12"><a href="StdDraw.java.html#L808" class="el_method">filledPolygon(double[], double[])</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="71" height="10" title="70" alt="70"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f4">5</td><td class="ctr2" id="g4">5</td><td class="ctr1" id="h6">17</td><td class="ctr2" id="i6">17</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a39"><a href="StdDraw.java.html#L1184" class="el_method">picture(double, double, String, double)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="71" height="10" title="70" alt="70"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d16"><img src="../.resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f16">3</td><td class="ctr2" id="g16">3</td><td class="ctr1" id="h13">12</td><td class="ctr2" id="i13">12</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a44"><a href="StdDraw.java.html#L1345" class="el_method">polygon(double[], double[])</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="71" height="10" title="70" alt="70"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f5">5</td><td class="ctr2" id="g5">5</td><td class="ctr1" id="h7">17</td><td class="ctr2" id="i7">17</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a1"><a href="StdDraw.java.html#L609" class="el_method">arc(double, double, double, double, double)</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="70" height="10" title="69" alt="69"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f6">5</td><td class="ctr2" id="g6">5</td><td class="ctr1" id="h8">13</td><td class="ctr2" id="i8">13</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a7"><a href="StdDraw.java.html#L712" class="el_method">ellipse(double, double, double, double)</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="66" height="10" title="65" alt="65"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f7">5</td><td class="ctr2" id="g7">5</td><td class="ctr1" id="h9">13</td><td class="ctr2" id="i9">13</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a11"><a href="StdDraw.java.html#L779" class="el_method">filledEllipse(double, double, double, double)</a></td><td class="bar" id="b11"><img src="../.resources/redbar.gif" width="66" height="10" title="65" alt="65"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d8"><img src="../.resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f8">5</td><td class="ctr2" id="g8">5</td><td class="ctr1" id="h10">13</td><td class="ctr2" id="i10">13</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a13"><a href="StdDraw.java.html#L842" class="el_method">filledRectangle(double, double, double, double)</a></td><td class="bar" id="b12"><img src="../.resources/redbar.gif" width="66" height="10" title="65" alt="65"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d9"><img src="../.resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e11">0%</td><td class="ctr1" id="f9">5</td><td class="ctr2" id="g9">5</td><td class="ctr1" id="h11">13</td><td class="ctr2" id="i11">13</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a45"><a href="StdDraw.java.html#L1379" class="el_method">rectangle(double, double, double, double)</a></td><td class="bar" id="b13"><img src="../.resources/redbar.gif" width="66" height="10" title="65" alt="65"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d10"><img src="../.resources/redbar.gif" width="53" height="10" title="8" alt="8"/></td><td class="ctr2" id="e12">0%</td><td class="ctr1" id="f10">5</td><td class="ctr2" id="g10">5</td><td class="ctr1" id="h12">13</td><td class="ctr2" id="i12">13</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a26"><a href="StdDraw.java.html#L1068" class="el_method">main(String[])</a></td><td class="bar" id="b14"><img src="../.resources/redbar.gif" width="59" height="10" title="58" alt="58"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d17"><img src="../.resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e13">0%</td><td class="ctr1" id="f17">3</td><td class="ctr2" id="g17">3</td><td class="ctr1" id="h14">12</td><td class="ctr2" id="i14">12</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a38"><a href="StdDraw.java.html#L1156" class="el_method">picture(double, double, String)</a></td><td class="bar" id="b15"><img src="../.resources/redbar.gif" width="58" height="10" title="57" alt="57"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d18"><img src="../.resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e14">0%</td><td class="ctr1" id="f18">3</td><td class="ctr2" id="g18">3</td><td class="ctr1" id="h22">10</td><td class="ctr2" id="i22">10</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a2"><a href="StdDraw.java.html#L637" class="el_method">circle(double, double, double)</a></td><td class="bar" id="b16"><img src="../.resources/redbar.gif" width="57" height="10" title="56" alt="56"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d12"><img src="../.resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e15">0%</td><td class="ctr1" id="f12">4</td><td class="ctr2" id="g12">4</td><td class="ctr1" id="h15">11</td><td class="ctr2" id="i15">11</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a10"><a href="StdDraw.java.html#L749" class="el_method">filledCircle(double, double, double)</a></td><td class="bar" id="b17"><img src="../.resources/redbar.gif" width="57" height="10" title="56" alt="56"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d13"><img src="../.resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e16">0%</td><td class="ctr1" id="f13">4</td><td class="ctr2" id="g13">4</td><td class="ctr1" id="h16">11</td><td class="ctr2" id="i16">11</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a14"><a href="StdDraw.java.html#L871" class="el_method">filledSquare(double, double, double)</a></td><td class="bar" id="b18"><img src="../.resources/redbar.gif" width="57" height="10" title="56" alt="56"/></td><td class="ctr2" id="c18">0%</td><td class="bar" id="d14"><img src="../.resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e17">0%</td><td class="ctr1" id="f14">4</td><td class="ctr2" id="g14">4</td><td class="ctr1" id="h17">11</td><td class="ctr2" id="i17">11</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr><tr><td id="a66"><a href="StdDraw.java.html#L1731" class="el_method">square(double, double, double)</a></td><td class="bar" id="b19"><img src="../.resources/redbar.gif" width="57" height="10" title="56" alt="56"/></td><td class="ctr2" id="c19">0%</td><td class="bar" id="d15"><img src="../.resources/redbar.gif" width="40" height="10" title="6" alt="6"/></td><td class="ctr2" id="e18">0%</td><td class="ctr1" id="f15">4</td><td class="ctr2" id="g15">4</td><td class="ctr1" id="h18">11</td><td class="ctr2" id="i18">11</td><td class="ctr1" id="j19">1</td><td class="ctr2" id="k19">1</td></tr><tr><td id="a59"><a href="StdDraw.java.html#L1605" class="el_method">setScale(double, double)</a></td><td class="bar" id="b20"><img src="../.resources/redbar.gif" width="49" height="10" title="48" alt="48"/></td><td class="ctr2" id="c20">0%</td><td class="bar" id="d21"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e19">0%</td><td class="ctr1" id="f21">2</td><td class="ctr2" id="g21">2</td><td class="ctr1" id="h25">9</td><td class="ctr2" id="i25">9</td><td class="ctr1" id="j20">1</td><td class="ctr2" id="k20">1</td></tr><tr><td id="a43"><a href="StdDraw.java.html#L1315" class="el_method">point(double, double)</a></td><td class="bar" id="b21"><img src="../.resources/redbar.gif" width="45" height="10" title="44" alt="44"/></td><td class="ctr2" id="c21">0%</td><td class="bar" id="d22"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e20">0%</td><td class="ctr1" id="f22">2</td><td class="ctr2" id="g22">2</td><td class="ctr1" id="h23">10</td><td class="ctr2" id="i23">10</td><td class="ctr1" id="j21">1</td><td class="ctr2" id="k21">1</td></tr><tr><td id="a69"><a href="StdDraw.java.html#L1756" class="el_method">text(double, double, String)</a></td><td class="bar" id="b22"><img src="../.resources/redbar.gif" width="43" height="10" title="42" alt="42"/></td><td class="ctr2" id="c22">0%</td><td class="bar" id="d23"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e21">0%</td><td class="ctr1" id="f23">2</td><td class="ctr2" id="g23">2</td><td class="ctr1" id="h19">11</td><td class="ctr2" id="i19">11</td><td class="ctr1" id="j22">1</td><td class="ctr2" id="k22">1</td></tr><tr><td id="a72"><a href="StdDraw.java.html#L1830" class="el_method">textRight(double, double, String)</a></td><td class="bar" id="b23"><img src="../.resources/redbar.gif" width="41" height="10" title="40" alt="40"/></td><td class="ctr2" id="c23">0%</td><td class="bar" id="d24"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e22">0%</td><td class="ctr1" id="f24">2</td><td class="ctr2" id="g24">2</td><td class="ctr1" id="h20">11</td><td class="ctr2" id="i20">11</td><td class="ctr1" id="j23">1</td><td class="ctr2" id="k23">1</td></tr><tr><td id="a55"><a href="StdDraw.java.html#L1540" class="el_method">setPenColor(int, int, int)</a></td><td class="bar" id="b24"><img src="../.resources/redbar.gif" width="38" height="10" title="38" alt="38"/></td><td class="ctr2" id="c24">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="80" height="10" title="12" alt="12"/></td><td class="ctr2" id="e23">0%</td><td class="ctr1" id="f3">7</td><td class="ctr2" id="g3">7</td><td class="ctr1" id="h27">8</td><td class="ctr2" id="i27">8</td><td class="ctr1" id="j24">1</td><td class="ctr2" id="k24">1</td></tr><tr><td id="a61"><a href="StdDraw.java.html#L1634" class="el_method">setXscale(double, double)</a></td><td class="bar" id="b25"><img src="../.resources/redbar.gif" width="36" height="10" title="36" alt="36"/></td><td class="ctr2" id="c25">0%</td><td class="bar" id="d25"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e24">0%</td><td class="ctr1" id="f25">2</td><td class="ctr2" id="g25">2</td><td class="ctr1" id="h29">7</td><td class="ctr2" id="i29">7</td><td class="ctr1" id="j25">1</td><td class="ctr2" id="k25">1</td></tr><tr><td id="a63"><a href="StdDraw.java.html#L1661" class="el_method">setYscale(double, double)</a></td><td class="bar" id="b26"><img src="../.resources/redbar.gif" width="36" height="10" title="36" alt="36"/></td><td class="ctr2" id="c26">0%</td><td class="bar" id="d26"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e25">0%</td><td class="ctr1" id="f26">2</td><td class="ctr2" id="g26">2</td><td class="ctr1" id="h30">7</td><td class="ctr2" id="i30">7</td><td class="ctr1" id="j26">1</td><td class="ctr2" id="k26">1</td></tr><tr><td id="a5"><a href="StdDraw.java.html#L672" class="el_method">createMenuBar()</a></td><td class="bar" id="b27"><img src="../.resources/redbar.gif" width="33" height="10" title="33" alt="33"/></td><td class="ctr2" id="c27">0%</td><td class="bar" id="d38"/><td class="ctr2" id="e38">n/a</td><td class="ctr1" id="f38">1</td><td class="ctr2" id="g38">1</td><td class="ctr1" id="h26">9</td><td class="ctr2" id="i26">9</td><td class="ctr1" id="j27">1</td><td class="ctr2" id="k27">1</td></tr><tr><td id="a71"><a href="StdDraw.java.html#L1807" class="el_method">textLeft(double, double, String)</a></td><td class="bar" id="b28"><img src="../.resources/redbar.gif" width="33" height="10" title="33" alt="33"/></td><td class="ctr2" id="c28">0%</td><td class="bar" id="d27"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e26">0%</td><td class="ctr1" id="f27">2</td><td class="ctr2" id="g27">2</td><td class="ctr1" id="h24">10</td><td class="ctr2" id="i24">10</td><td class="ctr1" id="j28">1</td><td class="ctr2" id="k28">1</td></tr><tr><td id="a70"><a href="StdDraw.java.html#L1786" class="el_method">text(double, double, String, double)</a></td><td class="bar" id="b29"><img src="../.resources/redbar.gif" width="30" height="10" title="30" alt="30"/></td><td class="ctr2" id="c29">0%</td><td class="bar" id="d28"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e27">0%</td><td class="ctr1" id="f28">2</td><td class="ctr2" id="g28">2</td><td class="ctr1" id="h28">8</td><td class="ctr2" id="i28">8</td><td class="ctr1" id="j29">1</td><td class="ctr2" id="k29">1</td></tr><tr><td id="a0"><a href="StdDraw.java.html#L1859" class="el_method">actionPerformed(ActionEvent)</a></td><td class="bar" id="b30"><img src="../.resources/redbar.gif" width="29" height="10" title="29" alt="29"/></td><td class="ctr2" id="c30">0%</td><td class="bar" id="d29"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e28">0%</td><td class="ctr1" id="f29">2</td><td class="ctr2" id="g29">2</td><td class="ctr1" id="h32">6</td><td class="ctr2" id="i32">6</td><td class="ctr1" id="j30">1</td><td class="ctr2" id="k30">1</td></tr><tr><td id="a65"><a href="StdDraw.java.html#L1696" class="el_method">show(int)</a></td><td class="bar" id="b31"><img src="../.resources/redbar.gif" width="28" height="10" title="28" alt="28"/></td><td class="ctr2" id="c31">0%</td><td class="bar" id="d30"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e29">0%</td><td class="ctr1" id="f30">2</td><td class="ctr2" id="g30">2</td><td class="ctr1" id="h21">11</td><td class="ctr2" id="i21">11</td><td class="ctr1" id="j31">1</td><td class="ctr2" id="k31">1</td></tr><tr><td id="a57"><a href="StdDraw.java.html#L1571" class="el_method">setPenRadius(double)</a></td><td class="bar" id="b32"><img src="../.resources/redbar.gif" width="27" height="10" title="27" alt="27"/></td><td class="ctr2" id="c32">0%</td><td class="bar" id="d31"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e30">0%</td><td class="ctr1" id="f31">2</td><td class="ctr2" id="g31">2</td><td class="ctr1" id="h31">7</td><td class="ctr2" id="i31">7</td><td class="ctr1" id="j32">1</td><td class="ctr2" id="k32">1</td></tr><tr><td id="a33"><a href="StdDraw.java.html#L1949" class="el_method">mousePressed(MouseEvent)</a></td><td class="bar" id="b33"><img src="../.resources/redbar.gif" width="23" height="10" title="23" alt="23"/></td><td class="ctr2" id="c33">0%</td><td class="bar" id="d39"/><td class="ctr2" id="e39">n/a</td><td class="ctr1" id="f39">1</td><td class="ctr2" id="g39">1</td><td class="ctr1" id="h34">5</td><td class="ctr2" id="i34">5</td><td class="ctr1" id="j33">1</td><td class="ctr2" id="k33">1</td></tr><tr><td id="a37"><a href="StdDraw.java.html#L1131" class="el_method">nextKeyTyped()</a></td><td class="bar" id="b34"><img src="../.resources/redbar.gif" width="22" height="10" title="22" alt="22"/></td><td class="ctr2" id="c34">0%</td><td class="bar" id="d32"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e31">0%</td><td class="ctr1" id="f32">2</td><td class="ctr2" id="g32">2</td><td class="ctr1" id="h38">4</td><td class="ctr2" id="i38">4</td><td class="ctr1" id="j34">1</td><td class="ctr2" id="k34">1</td></tr><tr><td id="a28"><a href="StdDraw.java.html#L1913" class="el_method">mouseDragged(MouseEvent)</a></td><td class="bar" id="b35"><img src="../.resources/redbar.gif" width="21" height="10" title="21" alt="21"/></td><td class="ctr2" id="c35">0%</td><td class="bar" id="d40"/><td class="ctr2" id="e40">n/a</td><td class="ctr1" id="f40">1</td><td class="ctr2" id="g40">1</td><td class="ctr1" id="h39">4</td><td class="ctr2" id="i39">4</td><td class="ctr1" id="j35">1</td><td class="ctr2" id="k35">1</td></tr><tr><td id="a31"><a href="StdDraw.java.html#L1938" class="el_method">mouseMoved(MouseEvent)</a></td><td class="bar" id="b36"><img src="../.resources/redbar.gif" width="21" height="10" title="21" alt="21"/></td><td class="ctr2" id="c36">0%</td><td class="bar" id="d41"/><td class="ctr2" id="e41">n/a</td><td class="ctr1" id="f41">1</td><td class="ctr2" id="g41">1</td><td class="ctr1" id="h40">4</td><td class="ctr2" id="i40">4</td><td class="ctr1" id="j36">1</td><td class="ctr2" id="k36">1</td></tr><tr><td id="a22"><a href="StdDraw.java.html#L1872" class="el_method">keyPressed(KeyEvent)</a></td><td class="bar" id="b37"><img src="../.resources/redbar.gif" width="17" height="10" title="17" alt="17"/></td><td class="ctr2" id="c37">0%</td><td class="bar" id="d42"/><td class="ctr2" id="e42">n/a</td><td class="ctr1" id="f42">1</td><td class="ctr2" id="g42">1</td><td class="ctr1" id="h43">3</td><td class="ctr2" id="i43">3</td><td class="ctr1" id="j37">1</td><td class="ctr2" id="k37">1</td></tr><tr><td id="a23"><a href="StdDraw.java.html#L1882" class="el_method">keyReleased(KeyEvent)</a></td><td class="bar" id="b38"><img src="../.resources/redbar.gif" width="17" height="10" title="17" alt="17"/></td><td class="ctr2" id="c38">0%</td><td class="bar" id="d43"/><td class="ctr2" id="e43">n/a</td><td class="ctr1" id="f43">1</td><td class="ctr2" id="g43">1</td><td class="ctr1" id="h44">3</td><td class="ctr2" id="i44">3</td><td class="ctr1" id="j38">1</td><td class="ctr2" id="k38">1</td></tr><tr><td id="a19"><a href="StdDraw.java.html#L958" class="el_method">hasNextKeyTyped()</a></td><td class="bar" id="b39"><img src="../.resources/redbar.gif" width="16" height="10" title="16" alt="16"/></td><td class="ctr2" id="c39">0%</td><td class="bar" id="d33"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e32">0%</td><td class="ctr1" id="f33">2</td><td class="ctr2" id="g33">2</td><td class="ctr1" id="h49">2</td><td class="ctr2" id="i49">2</td><td class="ctr1" id="j39">1</td><td class="ctr2" id="k39">1</td></tr><tr><td id="a24"><a href="StdDraw.java.html#L1892" class="el_method">keyTyped(KeyEvent)</a></td><td class="bar" id="b40"><img src="../.resources/redbar.gif" width="16" height="10" title="16" alt="16"/></td><td class="ctr2" id="c40">0%</td><td class="bar" id="d44"/><td class="ctr2" id="e44">n/a</td><td class="ctr1" id="f44">1</td><td class="ctr2" id="g44">1</td><td class="ctr1" id="h45">3</td><td class="ctr2" id="i45">3</td><td class="ctr1" id="j40">1</td><td class="ctr2" id="k40">1</td></tr><tr><td id="a25"><a href="StdDraw.java.html#L1039" class="el_method">line(double, double, double, double)</a></td><td class="bar" id="b41"><img src="../.resources/redbar.gif" width="15" height="10" title="15" alt="15"/></td><td class="ctr2" id="c41">0%</td><td class="bar" id="d45"/><td class="ctr2" id="e45">n/a</td><td class="ctr1" id="f45">1</td><td class="ctr2" id="g45">1</td><td class="ctr1" id="h46">3</td><td class="ctr2" id="i46">3</td><td class="ctr1" id="j41">1</td><td class="ctr2" id="k41">1</td></tr><tr><td id="a50"><a href="StdDraw.java.html#L1474" class="el_method">setCanvasSize(int, int)</a></td><td class="bar" id="b42"><img src="../.resources/redbar.gif" width="15" height="10" title="15" alt="15"/></td><td class="ctr2" id="c42">0%</td><td class="bar" id="d19"><img src="../.resources/redbar.gif" width="26" height="10" title="4" alt="4"/></td><td class="ctr2" id="e33">0%</td><td class="ctr1" id="f19">3</td><td class="ctr2" id="g19">3</td><td class="ctr1" id="h33">6</td><td class="ctr2" id="i33">6</td><td class="ctr1" id="j42">1</td><td class="ctr2" id="k42">1</td></tr><tr><td id="a4"><a href="StdDraw.java.html#L664" class="el_method">clear(Color)</a></td><td class="bar" id="b43"><img src="../.resources/redbar.gif" width="14" height="10" title="14" alt="14"/></td><td class="ctr2" id="c43">0%</td><td class="bar" id="d46"/><td class="ctr2" id="e46">n/a</td><td class="ctr1" id="f46">1</td><td class="ctr2" id="g46">1</td><td class="ctr1" id="h35">5</td><td class="ctr2" id="i35">5</td><td class="ctr1" id="j43">1</td><td class="ctr2" id="k43">1</td></tr><tr><td id="a21"><a href="StdDraw.java.html#L1020" class="el_method">isKeyPressed(int)</a></td><td class="bar" id="b44"><img src="../.resources/redbar.gif" width="14" height="10" title="14" alt="14"/></td><td class="ctr2" id="c44">0%</td><td class="bar" id="d47"/><td class="ctr2" id="e47">n/a</td><td class="ctr1" id="f47">1</td><td class="ctr2" id="g47">1</td><td class="ctr1" id="h50">2</td><td class="ctr2" id="i50">2</td><td class="ctr1" id="j44">1</td><td class="ctr2" id="k44">1</td></tr><tr><td id="a6"><a href="StdDraw.java.html#L689" class="el_method">draw()</a></td><td class="bar" id="b45"><img src="../.resources/redbar.gif" width="13" height="10" title="13" alt="13"/></td><td class="ctr2" id="c45">0%</td><td class="bar" id="d34"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e34">0%</td><td class="ctr1" id="f34">2</td><td class="ctr2" id="g34">2</td><td class="ctr1" id="h36">5</td><td class="ctr2" id="i36">5</td><td class="ctr1" id="j45">1</td><td class="ctr2" id="k45">1</td></tr><tr><td id="a42"><a href="StdDraw.java.html#L1301" class="el_method">pixel(double, double)</a></td><td class="bar" id="b46"><img src="../.resources/redbar.gif" width="13" height="10" title="13" alt="13"/></td><td class="ctr2" id="c46">0%</td><td class="bar" id="d48"/><td class="ctr2" id="e48">n/a</td><td class="ctr1" id="f48">1</td><td class="ctr2" id="g48">1</td><td class="ctr1" id="h51">2</td><td class="ctr2" id="i51">2</td><td class="ctr1" id="j46">1</td><td class="ctr2" id="k46">1</td></tr><tr><td id="a34"><a href="StdDraw.java.html#L1961" class="el_method">mouseReleased(MouseEvent)</a></td><td class="bar" id="b47"><img src="../.resources/redbar.gif" width="13" height="10" title="13" alt="13"/></td><td class="ctr2" id="c47">0%</td><td class="bar" id="d49"/><td class="ctr2" id="e49">n/a</td><td class="ctr1" id="f49">1</td><td class="ctr2" id="g49">1</td><td class="ctr1" id="h47">3</td><td class="ctr2" id="i47">3</td><td class="ctr1" id="j47">1</td><td class="ctr2" id="k47">1</td></tr><tr><td id="a54"><a href="StdDraw.java.html#L1520" class="el_method">setPenColor(Color)</a></td><td class="bar" id="b48"><img src="../.resources/redbar.gif" width="12" height="10" title="12" alt="12"/></td><td class="ctr2" id="c48">0%</td><td class="bar" id="d35"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e35">0%</td><td class="ctr1" id="f35">2</td><td class="ctr2" id="g35">2</td><td class="ctr1" id="h37">5</td><td class="ctr2" id="i37">5</td><td class="ctr1" id="j48">1</td><td class="ctr2" id="k48">1</td></tr><tr><td id="a32"><a href="StdDraw.java.html#L1090" class="el_method">mousePressed()</a></td><td class="bar" id="b49"><img src="../.resources/redbar.gif" width="11" height="10" title="11" alt="11"/></td><td class="ctr2" id="c49">0%</td><td class="bar" id="d50"/><td class="ctr2" id="e50">n/a</td><td class="ctr1" id="f50">1</td><td class="ctr2" id="g50">1</td><td class="ctr1" id="h52">2</td><td class="ctr2" id="i52">2</td><td class="ctr1" id="j49">1</td><td class="ctr2" id="k49">1</td></tr><tr><td id="a35"><a href="StdDraw.java.html#L1101" class="el_method">mouseX()</a></td><td class="bar" id="b50"><img src="../.resources/redbar.gif" width="11" height="10" title="11" alt="11"/></td><td class="ctr2" id="c50">0%</td><td class="bar" id="d51"/><td class="ctr2" id="e51">n/a</td><td class="ctr1" id="f51">1</td><td class="ctr2" id="g51">1</td><td class="ctr1" id="h53">2</td><td class="ctr2" id="i53">2</td><td class="ctr1" id="j50">1</td><td class="ctr2" id="k50">1</td></tr><tr><td id="a36"><a href="StdDraw.java.html#L1112" class="el_method">mouseY()</a></td><td class="bar" id="b51"><img src="../.resources/redbar.gif" width="11" height="10" title="11" alt="11"/></td><td class="ctr2" id="c51">0%</td><td class="bar" id="d52"/><td class="ctr2" id="e52">n/a</td><td class="ctr1" id="f52">1</td><td class="ctr2" id="g52">1</td><td class="ctr1" id="h54">2</td><td class="ctr2" id="i54">2</td><td class="ctr1" id="j51">1</td><td class="ctr2" id="k51">1</td></tr><tr><td id="a47"><a href="StdDraw.java.html#L1443" class="el_method">scaleX(double)</a></td><td class="bar" id="b52"><img src="../.resources/redbar.gif" width="11" height="10" title="11" alt="11"/></td><td class="ctr2" id="c52">0%</td><td class="bar" id="d53"/><td class="ctr2" id="e53">n/a</td><td class="ctr1" id="f53">1</td><td class="ctr2" id="g53">1</td><td class="ctr1" id="h63">1</td><td class="ctr2" id="i63">1</td><td class="ctr1" id="j52">1</td><td class="ctr2" id="k52">1</td></tr><tr><td id="a48"><a href="StdDraw.java.html#L1447" class="el_method">scaleY(double)</a></td><td class="bar" id="b53"><img src="../.resources/redbar.gif" width="11" height="10" title="11" alt="11"/></td><td class="ctr2" id="c53">0%</td><td class="bar" id="d54"/><td class="ctr2" id="e54">n/a</td><td class="ctr1" id="f54">1</td><td class="ctr2" id="g54">1</td><td class="ctr1" id="h64">1</td><td class="ctr2" id="i64">1</td><td class="ctr1" id="j53">1</td><td class="ctr2" id="k53">1</td></tr><tr><td id="a73"><a href="StdDraw.java.html#L1843" class="el_method">userX(double)</a></td><td class="bar" id="b54"><img src="../.resources/redbar.gif" width="11" height="10" title="11" alt="11"/></td><td class="ctr2" id="c54">0%</td><td class="bar" id="d55"/><td class="ctr2" id="e55">n/a</td><td class="ctr1" id="f55">1</td><td class="ctr2" id="g55">1</td><td class="ctr1" id="h65">1</td><td class="ctr2" id="i65">1</td><td class="ctr1" id="j54">1</td><td class="ctr2" id="k54">1</td></tr><tr><td id="a74"><a href="StdDraw.java.html#L1847" class="el_method">userY(double)</a></td><td class="bar" id="b55"><img src="../.resources/redbar.gif" width="11" height="10" title="11" alt="11"/></td><td class="ctr2" id="c55">0%</td><td class="bar" id="d56"/><td class="ctr2" id="e56">n/a</td><td class="ctr1" id="f56">1</td><td class="ctr2" id="g56">1</td><td class="ctr1" id="h66">1</td><td class="ctr2" id="i66">1</td><td class="ctr1" id="j55">1</td><td class="ctr2" id="k55">1</td></tr><tr><td id="a8"><a href="StdDraw.java.html#L728" class="el_method">factorX(double)</a></td><td class="bar" id="b56"><img src="../.resources/redbar.gif" width="10" height="10" title="10" alt="10"/></td><td class="ctr2" id="c56">0%</td><td class="bar" id="d57"/><td class="ctr2" id="e57">n/a</td><td class="ctr1" id="f57">1</td><td class="ctr2" id="g57">1</td><td class="ctr1" id="h67">1</td><td class="ctr2" id="i67">1</td><td class="ctr1" id="j56">1</td><td class="ctr2" id="k56">1</td></tr><tr><td id="a9"><a href="StdDraw.java.html#L732" class="el_method">factorY(double)</a></td><td class="bar" id="b57"><img src="../.resources/redbar.gif" width="10" height="10" title="10" alt="10"/></td><td class="ctr2" id="c57">0%</td><td class="bar" id="d58"/><td class="ctr2" id="e58">n/a</td><td class="ctr1" id="f58">1</td><td class="ctr2" id="g58">1</td><td class="ctr1" id="h68">1</td><td class="ctr2" id="i68">1</td><td class="ctr1" id="j57">1</td><td class="ctr2" id="k57">1</td></tr><tr><td id="a52"><a href="StdDraw.java.html#L1495" class="el_method">setFont(Font)</a></td><td class="bar" id="b58"><img src="../.resources/redbar.gif" width="9" height="10" title="9" alt="9"/></td><td class="ctr2" id="c58">0%</td><td class="bar" id="d36"><img src="../.resources/redbar.gif" width="13" height="10" title="2" alt="2"/></td><td class="ctr2" id="e36">0%</td><td class="ctr1" id="f36">2</td><td class="ctr2" id="g36">2</td><td class="ctr1" id="h41">4</td><td class="ctr2" id="i41">4</td><td class="ctr1" id="j58">1</td><td class="ctr2" id="k58">1</td></tr><tr><td id="a64"><a href="StdDraw.java.html#L1676" class="el_method">show()</a></td><td class="bar" id="b59"><img src="../.resources/redbar.gif" width="6" height="10" title="6" alt="6"/></td><td class="ctr2" id="c59">0%</td><td class="bar" id="d59"/><td class="ctr2" id="e59">n/a</td><td class="ctr1" id="f59">1</td><td class="ctr2" id="g59">1</td><td class="ctr1" id="h42">4</td><td class="ctr2" id="i42">4</td><td class="ctr1" id="j59">1</td><td class="ctr2" id="k59">1</td></tr><tr><td id="a49"><a href="StdDraw.java.html#L1457" class="el_method">setCanvasSize()</a></td><td class="bar" id="b60"><img src="../.resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c60">0%</td><td class="bar" id="d60"/><td class="ctr2" id="e60">n/a</td><td class="ctr1" id="f60">1</td><td class="ctr2" id="g60">1</td><td class="ctr1" id="h55">2</td><td class="ctr2" id="i55">2</td><td class="ctr1" id="j60">1</td><td class="ctr2" id="k60">1</td></tr><tr><td id="a60"><a href="StdDraw.java.html#L1620" class="el_method">setXscale()</a></td><td class="bar" id="b61"><img src="../.resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c61">0%</td><td class="bar" id="d61"/><td class="ctr2" id="e61">n/a</td><td class="ctr1" id="f61">1</td><td class="ctr2" id="g61">1</td><td class="ctr1" id="h56">2</td><td class="ctr2" id="i56">2</td><td class="ctr1" id="j61">1</td><td class="ctr2" id="k61">1</td></tr><tr><td id="a62"><a href="StdDraw.java.html#L1647" class="el_method">setYscale()</a></td><td class="bar" id="b62"><img src="../.resources/redbar.gif" width="4" height="10" title="4" alt="4"/></td><td class="ctr2" id="c62">0%</td><td class="bar" id="d62"/><td class="ctr2" id="e62">n/a</td><td class="ctr1" id="f62">1</td><td class="ctr2" id="g62">1</td><td class="ctr1" id="h57">2</td><td class="ctr2" id="i57">2</td><td class="ctr1" id="j62">1</td><td class="ctr2" id="k62">1</td></tr><tr><td id="a3"><a href="StdDraw.java.html#L654" class="el_method">clear()</a></td><td class="bar" id="b63"><img src="../.resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c63">0%</td><td class="bar" id="d63"/><td class="ctr2" id="e63">n/a</td><td class="ctr1" id="f63">1</td><td class="ctr2" id="g63">1</td><td class="ctr1" id="h58">2</td><td class="ctr2" id="i58">2</td><td class="ctr1" id="j63">1</td><td class="ctr2" id="k63">1</td></tr><tr><td id="a51"><a href="StdDraw.java.html#L1485" class="el_method">setFont()</a></td><td class="bar" id="b64"><img src="../.resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c64">0%</td><td class="bar" id="d64"/><td class="ctr2" id="e64">n/a</td><td class="ctr1" id="f64">1</td><td class="ctr2" id="g64">1</td><td class="ctr1" id="h59">2</td><td class="ctr2" id="i59">2</td><td class="ctr1" id="j64">1</td><td class="ctr2" id="k64">1</td></tr><tr><td id="a53"><a href="StdDraw.java.html#L1504" class="el_method">setPenColor()</a></td><td class="bar" id="b65"><img src="../.resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c65">0%</td><td class="bar" id="d65"/><td class="ctr2" id="e65">n/a</td><td class="ctr1" id="f65">1</td><td class="ctr2" id="g65">1</td><td class="ctr1" id="h60">2</td><td class="ctr2" id="i60">2</td><td class="ctr1" id="j65">1</td><td class="ctr2" id="k65">1</td></tr><tr><td id="a56"><a href="StdDraw.java.html#L1556" class="el_method">setPenRadius()</a></td><td class="bar" id="b66"><img src="../.resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c66">0%</td><td class="bar" id="d66"/><td class="ctr2" id="e66">n/a</td><td class="ctr1" id="f66">1</td><td class="ctr2" id="g66">1</td><td class="ctr1" id="h61">2</td><td class="ctr2" id="i61">2</td><td class="ctr1" id="j66">1</td><td class="ctr2" id="k66">1</td></tr><tr><td id="a58"><a href="StdDraw.java.html#L1585" class="el_method">setScale()</a></td><td class="bar" id="b67"><img src="../.resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c67">0%</td><td class="bar" id="d67"/><td class="ctr2" id="e67">n/a</td><td class="ctr1" id="f67">1</td><td class="ctr2" id="g67">1</td><td class="ctr1" id="h48">3</td><td class="ctr2" id="i48">3</td><td class="ctr1" id="j67">1</td><td class="ctr2" id="k67">1</td></tr><tr><td id="a68"><a href="StdDraw.java.html#L1851" class="el_method">StdDraw()</a></td><td class="bar" id="b68"><img src="../.resources/redbar.gif" width="3" height="10" title="3" alt="3"/></td><td class="ctr2" id="c68">0%</td><td class="bar" id="d68"/><td class="ctr2" id="e68">n/a</td><td class="ctr1" id="f68">1</td><td class="ctr2" id="g68">1</td><td class="ctr1" id="h62">2</td><td class="ctr2" id="i62">2</td><td class="ctr1" id="j68">1</td><td class="ctr2" id="k68">1</td></tr><tr><td id="a15"><a href="StdDraw.java.html#L890" class="el_method">getFont()</a></td><td class="bar" id="b69"><img src="../.resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c69">0%</td><td class="bar" id="d69"/><td class="ctr2" id="e69">n/a</td><td class="ctr1" id="f69">1</td><td class="ctr2" id="g69">1</td><td class="ctr1" id="h69">1</td><td class="ctr2" id="i69">1</td><td class="ctr1" id="j69">1</td><td class="ctr2" id="k69">1</td></tr><tr><td id="a17"><a href="StdDraw.java.html#L939" class="el_method">getPenColor()</a></td><td class="bar" id="b70"><img src="../.resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c70">0%</td><td class="bar" id="d70"/><td class="ctr2" id="e70">n/a</td><td class="ctr1" id="f70">1</td><td class="ctr2" id="g70">1</td><td class="ctr1" id="h70">1</td><td class="ctr2" id="i70">1</td><td class="ctr1" id="j70">1</td><td class="ctr2" id="k70">1</td></tr><tr><td id="a18"><a href="StdDraw.java.html#L948" class="el_method">getPenRadius()</a></td><td class="bar" id="b71"><img src="../.resources/redbar.gif" width="2" height="10" title="2" alt="2"/></td><td class="ctr2" id="c71">0%</td><td class="bar" id="d71"/><td class="ctr2" id="e71">n/a</td><td class="ctr1" id="f71">1</td><td class="ctr2" id="g71">1</td><td class="ctr1" id="h71">1</td><td class="ctr2" id="i71">1</td><td class="ctr1" id="j71">1</td><td class="ctr2" id="k71">1</td></tr><tr><td id="a27"><a href="StdDraw.java.html#L1906" class="el_method">mouseClicked(MouseEvent)</a></td><td class="bar" id="b72"><img src="../.resources/redbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="c72">0%</td><td class="bar" id="d72"/><td class="ctr2" id="e72">n/a</td><td class="ctr1" id="f72">1</td><td class="ctr2" id="g72">1</td><td class="ctr1" id="h72">1</td><td class="ctr2" id="i72">1</td><td class="ctr1" id="j72">1</td><td class="ctr2" id="k72">1</td></tr><tr><td id="a29"><a href="StdDraw.java.html#L1924" class="el_method">mouseEntered(MouseEvent)</a></td><td class="bar" id="b73"><img src="../.resources/redbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="c73">0%</td><td class="bar" id="d73"/><td class="ctr2" id="e73">n/a</td><td class="ctr1" id="f73">1</td><td class="ctr2" id="g73">1</td><td class="ctr1" id="h73">1</td><td class="ctr2" id="i73">1</td><td class="ctr1" id="j73">1</td><td class="ctr2" id="k73">1</td></tr><tr><td id="a30"><a href="StdDraw.java.html#L1931" class="el_method">mouseExited(MouseEvent)</a></td><td class="bar" id="b74"><img src="../.resources/redbar.gif" width="1" height="10" title="1" alt="1"/></td><td class="ctr2" id="c74">0%</td><td class="bar" id="d74"/><td class="ctr2" id="e74">n/a</td><td class="ctr1" id="f74">1</td><td class="ctr2" id="g74">1</td><td class="ctr1" id="h74">1</td><td class="ctr2" id="i74">1</td><td class="ctr1" id="j74">1</td><td class="ctr2" id="k74">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>