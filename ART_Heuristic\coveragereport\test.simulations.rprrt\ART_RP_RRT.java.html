<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_RP_RRT.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rprrt</a> &gt; <span class="el_source">ART_RP_RRT.java</span></div><h1>ART_RP_RRT.java</h1><pre class="source lang-java linenums">package test.simulations.rprrt;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.ART;
import test.simulations.art_center.ART_Center_ND;
import util.PaiLie;
import util.data.ZeroOneCreator;

public class ART_RP_RRT extends ART {

<span class="nc" id="L18">	public ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L19">	public ArrayList&lt;NRectRegion&gt; regions = new ArrayList&lt;&gt;();</span>

	double R;

	public ART_RP_RRT(double[] min, double[] max, Random random, FailurePattern failurePattern, double R) {
<span class="nc" id="L24">		super(min, max, random, failurePattern);</span>
<span class="nc" id="L25">		this.R = R;</span>
<span class="nc" id="L26">	}</span>

	@Override
	public int run() {
<span class="nc" id="L30">		int count = 0;</span>
<span class="nc" id="L31">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc" id="L32">		NRectRegion maxRegion = new NRectRegion(new NPoint(min), new NPoint(max));</span>
<span class="nc" id="L33">		this.regions.add(maxRegion);</span>
<span class="nc" id="L34">		int index = 0;</span>

<span class="nc bnc" id="L36" title="All 2 branches missed.">		while (this.failPattern.isCorrect(p)) {</span>
			// count++;
<span class="nc" id="L38">			count++;</span>

			// add tests
<span class="nc" id="L41">			this.tests.add(p);</span>

			// split region
<span class="nc" id="L44">			splitRegion(index, p);</span>
			// max region
<span class="nc" id="L46">			index = maxRegion();</span>
			// make the region min
<span class="nc" id="L48">			NRectRegion[] smallerRegions = makeMaxRegionSmall(this.regions.get(index));</span>
			// another point
<span class="nc" id="L50">			p = randomCreator.randomPoint(smallerRegions);</span>
		}
<span class="nc" id="L52">		return count;</span>
	}

	// 只生成测试用例的方法
	public NPoint generateTC() {
<span class="nc" id="L57">		NPoint p = null;</span>

<span class="nc bnc" id="L59" title="All 2 branches missed.">		if (this.tests.size() == 0) {</span>
<span class="nc" id="L60">			p = randomCreator.randomPoint();</span>
<span class="nc" id="L61">			this.regions.add(new NRectRegion(new NPoint(min), new NPoint(max)));</span>
<span class="nc" id="L62">			splitRegion(0, p);</span>
<span class="nc" id="L63">		} else {</span>
<span class="nc" id="L64">			int index = maxRegion();</span>
<span class="nc" id="L65">			p = randomCreator.randomPoint(makeMaxRegionSmall(this.regions.get(index)));</span>
<span class="nc" id="L66">			splitRegion(index, p);</span>
		}
<span class="nc" id="L68">		this.tests.add(p);</span>

<span class="nc" id="L70">		return p;</span>
	}

	public NRectRegion[] makeMaxRegionSmall(NRectRegion maxregion) {
<span class="nc" id="L74">		double[] start = maxregion.getStart().getXn();</span>
<span class="nc" id="L75">		double[] end = maxregion.getEnd().getXn();</span>

<span class="nc" id="L77">		double eachExcludedSize = (maxregion.size() * R) / (double) (Math.pow(2, this.dimension));</span>

		// 四个角上都去掉(针对2维)
		// 敲定每个方块的每一维的长度。按比例缩小
<span class="nc" id="L81">		double[] xn = new double[this.dimension];</span>
<span class="nc" id="L82">		double xlength = Math.sqrt(eachExcludedSize * (end[0] - start[0]) / (end[1] - start[1]));</span>
<span class="nc" id="L83">		double ylength = xlength * ((end[1] - start[1])) / (end[0] - start[0]);</span>

<span class="nc" id="L85">		NRectRegion[] results = new NRectRegion[5];</span>
<span class="nc" id="L86">		results[0] = new NRectRegion(new NPoint(new double[] { start[0] + xlength, start[1] }),</span>
<span class="nc" id="L87">				new NPoint(new double[] { end[0] - xlength, start[1] + ylength }));</span>
<span class="nc" id="L88">		results[1] = new NRectRegion(new NPoint(new double[] { end[0] - xlength, start[1] + ylength }),</span>
<span class="nc" id="L89">				new NPoint(new double[] { end[0], end[1] - ylength }));</span>
<span class="nc" id="L90">		results[2] = new NRectRegion(new NPoint(new double[] { start[0] + xlength, end[1] - ylength }),</span>
<span class="nc" id="L91">				new NPoint(new double[] { end[0] - xlength, end[1] }));</span>
<span class="nc" id="L92">		results[3] = new NRectRegion(new NPoint(new double[] { start[0], start[1] + ylength }),</span>
<span class="nc" id="L93">				new NPoint(new double[] { start[0] + xlength, end[1] - ylength }));</span>
<span class="nc" id="L94">		results[4] = new NRectRegion(new NPoint(new double[] { start[0] + xlength, start[1] + ylength }),</span>
<span class="nc" id="L95">				new NPoint(new double[] { end[0] - xlength, end[1] - ylength }));</span>
		// return new NRectRegion(new NPoint(s1),new NPoint(e1));
<span class="nc" id="L97">		return results;</span>
	}

	public void splitRegion(int index, NPoint p) {
<span class="nc bnc" id="L101" title="All 4 branches missed.">		if (index &lt; 0 || index &gt;= this.regions.size()) {</span>
<span class="nc" id="L102">			System.out.println(&quot;split region error! index not correct!&quot;);</span>
<span class="nc" id="L103">			return;</span>
		}
		// first remove it
<span class="nc" id="L106">		NRectRegion region = this.regions.remove(index);</span>
		try {
			// add regions;
<span class="nc" id="L109">			addRegionsInND(region, p);</span>
<span class="nc" id="L110">		} catch (Exception e) {</span>
<span class="nc" id="L111">			System.out.println(&quot;split region error in split region rec&quot;);</span>
		}
<span class="nc" id="L113">	}</span>

	public void addRegionsInND(NRectRegion region, NPoint p) throws Exception {
<span class="nc" id="L116">		double[] start = region.getStart().getXn();</span>
<span class="nc" id="L117">		double[] end = region.getEnd().getXn();</span>
<span class="nc" id="L118">		double[] pxn = p.getXn();</span>
<span class="nc" id="L119">		List&lt;List&lt;Double&gt;&gt; result1 = splitRegions(start, pxn);</span>
<span class="nc" id="L120">		List&lt;List&lt;Double&gt;&gt; result2 = splitRegions(pxn, end);</span>
		// System.out.println(result1.size());
<span class="nc bnc" id="L122" title="All 2 branches missed.">		if (result1.size() != result2.size()) {</span>
<span class="nc" id="L123">			throw new Exception(&quot;result1's size!=result2's size ,split region wrong&quot;);</span>
		}
<span class="nc bnc" id="L125" title="All 2 branches missed.">		for (int i = 0; i &lt; result1.size(); i++) {</span>
<span class="nc" id="L126">			List&lt;Double&gt; temp1 = result1.get(i);</span>
<span class="nc" id="L127">			List&lt;Double&gt; temp2 = result2.get(i);</span>
<span class="nc" id="L128">			double[] newStart = new double[temp1.size()];</span>
<span class="nc" id="L129">			double[] newEnd = new double[temp2.size()];</span>
<span class="nc bnc" id="L130" title="All 2 branches missed.">			for (int j = 0; j &lt; temp1.size(); j++) {</span>
<span class="nc" id="L131">				newStart[j] = temp1.get(j);</span>
<span class="nc" id="L132">				newEnd[j] = temp2.get(j);</span>
			}

<span class="nc" id="L135">			NRectRegion tempRegion = new NRectRegion(new NPoint(newStart), new NPoint(newEnd));</span>
<span class="nc" id="L136">			this.regions.add(tempRegion);</span>
		}
<span class="nc" id="L138">	}</span>

	public List&lt;List&lt;Double&gt;&gt; splitRegions(double[] start, double[] end) {
<span class="nc" id="L141">		ArrayList&lt;double[]&gt; values = new ArrayList&lt;&gt;();</span>
<span class="nc bnc" id="L142" title="All 2 branches missed.">		for (int i = 0; i &lt; start.length; i++) {</span>
<span class="nc" id="L143">			double[] temp = new double[2];</span>

<span class="nc" id="L145">			temp[0] = start[i];</span>
<span class="nc" id="L146">			temp[1] = end[i];</span>
<span class="nc" id="L147">			values.add(temp);</span>
		}

<span class="nc" id="L150">		ArrayList&lt;List&lt;Double&gt;&gt; result = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L151">		PaiLie.per(values, 0, new ArrayList&lt;&gt;(), result);</span>
<span class="nc" id="L152">		return result;</span>
	}

	public int maxRegion() {
<span class="nc" id="L156">		int index = 0;</span>
<span class="nc" id="L157">		double maxRegionSize = 0.0;</span>
<span class="nc bnc" id="L158" title="All 2 branches missed.">		for (int i = 0; i &lt; this.regions.size(); i++) {</span>
<span class="nc" id="L159">			double tempRegionSize = this.regions.get(i).size();</span>
<span class="nc bnc" id="L160" title="All 2 branches missed.">			if (tempRegionSize &gt; maxRegionSize) {</span>
<span class="nc" id="L161">				maxRegionSize = tempRegionSize;</span>
<span class="nc" id="L162">				index = i;</span>
			}
		}
<span class="nc" id="L165">		return index;</span>
	}

	public static void main(String[] args) {
<span class="nc" id="L169">		int d=2;//only in 2dimension</span>
<span class="nc" id="L170">		int times=3000;</span>
<span class="nc" id="L171">		double failure_rate=0.001;</span>
<span class="nc" id="L172">		double R=0.49999;</span>
<span class="nc" id="L173">		int fm=0;</span>
		
<span class="nc" id="L175">		ZeroOneCreator dataCreator=new ZeroOneCreator();</span>
<span class="nc" id="L176">		double[] min=dataCreator.minCreator(d);</span>
<span class="nc" id="L177">		double[] max=dataCreator.maxCreator(d);</span>
		
<span class="nc bnc" id="L179" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L180">			FailurePattern pattern=new BlockPattern();</span>
<span class="nc" id="L181">			pattern.fail_rate=failure_rate;</span>
<span class="nc" id="L182">			ART_RP_RRT center = new ART_RP_RRT(min, max, new Random(i * 3), pattern, R);</span>
<span class="nc" id="L183">			int temp=center.run();</span>
<span class="nc" id="L184">			fm+=temp;</span>
		}
<span class="nc" id="L186">		System.out.println(fm/(double)times);</span>
<span class="nc" id="L187">	}</span>

	@Override
	public int em() {
		// TODO Auto-generated method stub
<span class="nc" id="L192">		return 0;</span>
	}

	@Override
	public NPoint generateNextTC() {
		// TODO Auto-generated method stub
<span class="nc" id="L198">		return null;</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>