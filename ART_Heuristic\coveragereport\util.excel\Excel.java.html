<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>Excel.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">util.excel</a> &gt; <span class="el_source">Excel.java</span></div><h1>Excel.java</h1><pre class="source lang-java linenums">package util.excel;

import org.apache.poi.hssf.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.IOException;
import java.text.NumberFormat;
import java.util.Map;

/**
 * Created by xijiaxiang on 2017/6/8.
 */
<span class="nc" id="L16">public class Excel {</span>

<span class="nc" id="L18">	private HSSFWorkbook wb = new HSSFWorkbook();</span>

	public HSSFSheet createSheet(String sheetName) {
<span class="nc" id="L21">		return wb.createSheet(sheetName);</span>
	}

	public void writeCell2Sheet(HSSFSheet sheet,String[][] values ){
		//(x,y) refers to each cell
<span class="nc bnc" id="L26" title="All 4 branches missed.">		if(values==null||values.length==0){</span>
<span class="nc" id="L27">			return;</span>
		}
		
		
<span class="nc bnc" id="L31" title="All 2 branches missed.">		for(int i=0;i&lt;values.length;i++){</span>
<span class="nc" id="L32">			HSSFRow row = sheet.createRow(i);</span>
<span class="nc bnc" id="L33" title="All 2 branches missed.">			for(int j=0;j&lt;values[i].length;j++){</span>
<span class="nc" id="L34">				HSSFCell cell = row.createCell(j);</span>
<span class="nc" id="L35">				cell.setCellValue(values[i][j]);</span>
			}
		}
<span class="nc" id="L38">	}</span>
	public void writeCell2Sheet(HSSFSheet sheet,String[][] values,int rowPadding,int columnPadding){
		//x y 偏移量
<span class="nc bnc" id="L41" title="All 2 branches missed.">		for(int i=0;i&lt;values.length;i++){</span>
<span class="nc" id="L42">			HSSFRow row = sheet.getRow(i+rowPadding);</span>
<span class="nc bnc" id="L43" title="All 2 branches missed.">			for(int j=0;j&lt;values[i].length;j++){</span>
<span class="nc" id="L44">				HSSFCell cell = row.createCell(j+columnPadding);</span>
<span class="nc" id="L45">				cell.setCellValue(values[i][j]);</span>
			}
		}
<span class="nc" id="L48">	}</span>
	
	public File saveAsFile(String fileName){
<span class="nc" id="L51">		File file = new File(fileName);</span>
<span class="nc bnc" id="L52" title="All 2 branches missed.">		if (file.exists()) {</span>
<span class="nc" id="L53">			file.delete();</span>
<span class="nc" id="L54">		} else {</span>
			try {
<span class="nc" id="L56">				file.createNewFile();</span>
<span class="nc" id="L57">			} catch (IOException e) {</span>
<span class="nc" id="L58">				e.printStackTrace();</span>
			}
		}
		FileOutputStream os;
		try {
<span class="nc" id="L63">			os = new FileOutputStream(file);</span>
<span class="nc" id="L64">			wb.write(os);</span>
<span class="nc" id="L65">			os.close();</span>
<span class="nc" id="L66">		} catch (Exception e) {</span>
<span class="nc" id="L67">			e.printStackTrace();</span>
		}
		
<span class="nc" id="L70">		return file;</span>
	}
	public static void main(String[] args) {
		
<span class="nc" id="L74">	}</span>
	
//	public static File createExcel() throws IOException {
//		// Map&lt;String, AreaCount&gt; maps = dayDeal.getAreas();//每日成交量数据
//		NumberFormat numberFormat = NumberFormat.getNumberInstance();//
//		numberFormat.setMaximumFractionDigits(2);// 保留两位小数
//		numberFormat.setGroupingUsed(false);
//		//
//		HSSFWorkbook wb = new HSSFWorkbook();
//		HSSFSheet sheet = wb.createSheet(&quot;成交量&quot;);
//
//		// 设置六列的宽度
//		for (int i = 0; i &lt; dayDeal.getAreas().size() + 2; i++) {
//			sheet.setColumnWidth(i, 4000);
//		}
//
//		// 创建字体样式Verdana
//		HSSFFont font = new FontAndStyle().new Font(wb, &quot;Verdana&quot;).getFont();
//		// 创建单元格样式24 47
//		HSSFCellStyle styleblue = new FontAndStyle().new CellStyle(wb, 24).getStyle();
//		HSSFCellStyle styleyellow = new FontAndStyle().new CellStyle(wb, 47).getStyle();
//
//		HSSFCellStyle styledefault = wb.createCellStyle();
//		styledefault.setAlignment(HSSFCellStyle.ALIGN_CENTER);
//		styledefault.setVerticalAlignment(HSSFCellStyle.VERTICAL_CENTER);
//		// styledefault.setFillBackgroundColor(HSSFColor.WHITE.index);
//		// styledefault.setFillForegroundColor(HSSFColor.WHITE.index);
//		// styledefault.setFillPattern(HSSFCellStyle.SOLID_FOREGROUND);
//
//		// 设置边框
//		/*
//		 * style.setBottomBorderColor(HSSFColor.RED.index);
//		 * style.setBorderBottom(HSSFCellStyle.BORDER_THIN);
//		 * style.setBorderLeft(HSSFCellStyle.BORDER_THIN);
//		 * style.setBorderRight(HSSFCellStyle.BORDER_THIN);
//		 * style.setBorderTop(HSSFCellStyle.BORDER_THIN);
//		 */
//
//		styleblue.setFont(font);// 设置字体
//		styleyellow.setFont(font);
//		styledefault.setFont(font);
//
//		// 创建标题一行，styleblue
//		HSSFRow row = sheet.createRow(0);
//		row.setHeight((short) 500);// 设定行的高度
//		HSSFCell cell = row.createCell(0);
//		cell.setCellStyle(styleblue);
//		cell.setCellValue(&quot;住宅&quot;);
//		int count = 1;
//		for (String key : dayDeal.getAreas().keySet()) {
//			cell = row.createCell(count);
//			cell.setCellStyle(styleblue);
//			cell.setCellValue(dayDeal.getAreas().get(key).getAreaName());
//			count++;
//		}
//		cell = row.createCell(count);
//		cell.setCellStyle(styleblue);
//		cell.setCellValue(&quot;合计&quot;);
//
//		// 四行数据
//		row = sheet.createRow(1);// 套数
//		row.setHeight((short) 500);// 设定行的高度
//
//		cell = row.createCell(0);
//		cell.setCellStyle(styleyellow);
//		cell.setCellValue(&quot;成交套数&quot;);
//		count = 1;
//		int counttaoshu = 0;
//		for (String key : dayDeal.getAreas().keySet()) {
//			cell = row.createCell(count);
//			cell.setCellStyle(styledefault);
//			int temp = dayDeal.getAreas().get(key).getZhu();
//			cell.setCellValue(temp);
//			counttaoshu += temp;
//			count++;
//		}
//		cell = row.createCell(count);
//		cell.setCellStyle(styledefault);
//		cell.setCellValue(counttaoshu);
//
//		row = sheet.createRow(2);// 面积
//		row.setHeight((short) 500);// 设定行的高度
//
//		cell = row.createCell(0);
//		cell.setCellStyle(styleyellow);
//		cell.setCellValue(&quot;成交面积&quot;);
//		count = 1;
//		double countarea = 0.0;
//		for (String key : dayDeal.getAreas().keySet()) {
//			cell = row.createCell(count);
//			cell.setCellStyle(styledefault);
//			double temp = dayDeal.getAreas().get(key).getZhu_Area();
//			cell.setCellValue(numberFormat.format(temp));
//			countarea += temp;
//			count++;
//		}
//		cell = row.createCell(count);
//		cell.setCellStyle(styledefault);
//		cell.setCellValue(numberFormat.format(countarea));
//
//		row = sheet.createRow(3);// 均套面积
//		row.setHeight((short) 500);// 设定行的高度
//
//		cell = row.createCell(0);
//		cell.setCellStyle(styleyellow);
//		cell.setCellValue(&quot;均套面积&quot;);
//		count = 1;
//		for (String key : dayDeal.getAreas().keySet()) {
//			cell = row.createCell(count);
//			cell.setCellStyle(styledefault);
//			if (dayDeal.getAreas().get(key).getZhu() == 0) {
//				cell.setCellValue(0.00);
//			} else {
//				cell.setCellValue(numberFormat.format(
//						dayDeal.getAreas().get(key).getZhu_Area() / (double) dayDeal.getAreas().get(key).getZhu()));
//			}
//			count++;
//		}
//		cell = row.createCell(count);
//		cell.setCellStyle(styledefault);
//		if (counttaoshu != 0) {
//			cell.setCellValue(numberFormat.format(countarea / (double) counttaoshu));
//		} else {
//			cell.setCellValue(0.00);
//		}
//
//		// 下面非住宅
//		row = sheet.createRow(6);
//		row.setHeight((short) 500);// 设定行的高度
//		// row.setRowStyle(styleblue);
//		cell = row.createCell(0);
//		cell.setCellStyle(styleblue);
//		cell.setCellValue(&quot;非住宅&quot;);
//		count = 1;
//		for (String key : dayDeal.getAreas().keySet()) {
//			cell = row.createCell(count);
//			cell.setCellStyle(styleblue);
//			cell.setCellValue(dayDeal.getAreas().get(key).getAreaName());
//			count++;
//		}
//		cell = row.createCell(count);
//		cell.setCellStyle(styleblue);
//		cell.setCellValue(&quot;合计&quot;);
//
//		// 四行数据
//		row = sheet.createRow(7);// 套数
//		row.setHeight((short) 500);// 设定行的高度
//
//		cell = row.createCell(0);
//		cell.setCellStyle(styleyellow);
//		cell.setCellValue(&quot;成交套数&quot;);
//		count = 1;
//		counttaoshu = 0;
//		for (String key : dayDeal.getAreas().keySet()) {
//			cell = row.createCell(count);
//			cell.setCellStyle(styledefault);
//			counttaoshu += dayDeal.getAreas().get(key).getFeiZhu();
//			cell.setCellValue(dayDeal.getAreas().get(key).getFeiZhu());
//			count++;
//		}
//		cell = row.createCell(count);
//		cell.setCellStyle(styledefault);
//		cell.setCellValue(counttaoshu);
//
//		row = sheet.createRow(8);// 面积
//		row.setHeight((short) 500);// 设定行的高度
//
//		cell = row.createCell(0);
//		cell.setCellStyle(styleyellow);
//		cell.setCellValue(&quot;成交面积&quot;);
//		count = 1;
//		countarea = 0;
//		for (String key : dayDeal.getAreas().keySet()) {
//			cell = row.createCell(count);
//			cell.setCellStyle(styledefault);
//			countarea += dayDeal.getAreas().get(key).getFeiZhu_Area();
//			cell.setCellValue(numberFormat.format(dayDeal.getAreas().get(key).getFeiZhu_Area()));
//			count++;
//		}
//		cell = row.createCell(count);
//		cell.setCellStyle(styledefault);
//		cell.setCellValue(numberFormat.format(countarea));
//
//		row = sheet.createRow(9);// 均套面积
//		row.setHeight((short) 500);// 设定行的高度
//
//		cell = row.createCell(0);
//		cell.setCellStyle(styleyellow);
//		cell.setCellValue(&quot;均套面积&quot;);
//		count = 1;
//		for (String key : dayDeal.getAreas().keySet()) {
//			cell = row.createCell(count);
//			cell.setCellStyle(styledefault);
//			if (dayDeal.getAreas().get(key).getFeiZhu() == 0) {
//				cell.setCellValue(0.00);
//			} else {
//				cell.setCellValue(numberFormat.format(dayDeal.getAreas().get(key).getFeiZhu_Area()
//						/ (double) dayDeal.getAreas().get(key).getFeiZhu()));
//			}
//			count++;
//		}
//		cell = row.createCell(count);
//		cell.setCellStyle(styledefault);
//		if (counttaoshu != 0) {
//			cell.setCellValue(numberFormat.format(countarea / (counttaoshu)));
//		} else {
//			cell.setCellValue(0.00);
//		}
//
//		String filePath = Property.getProperty(&quot;fileLocation&quot;, &quot;excelLocation&quot;);
//		File file = new File(filePath + fileName);
//		if (file.exists()) {
//			file.delete();
//		} else {
//			file.createNewFile();
//		}
//		FileOutputStream os = new FileOutputStream(file);
//		wb.write(os);
//		os.close();
//
//		return file;
//	}

	
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>