<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MyART_E_OD.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.myart._1D</a> &gt; <span class="el_source">MyART_E_OD.java</span></div><h1>MyART_E_OD.java</h1><pre class="source lang-java linenums">package test.simulations.myart._1D;

import java.util.ArrayList;
import java.util.Random;

import datastructure.TD.TestCase;

/*
 * CCofMyART2 with exclusion region （R）
 * maybe have some problems because the result is not very correct
 * */
public class MyART_E_OD {
<span class="nc" id="L13">	public static int ZUOQUXIAN = 1;</span>
<span class="nc" id="L14">	public static int YOUQUXIAN = 2;</span>
	public static void main(String[] args) throws Exception {
<span class="nc" id="L16">		int times = 1;</span>
<span class="nc" id="L17">		long sums = 0;</span>
<span class="nc" id="L18">		int temp = 0;</span>
<span class="nc" id="L19">		long startTime = System.currentTimeMillis();</span>

<span class="nc bnc" id="L21" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L22">			MyART_E_OD ccrr = new MyART_E_OD(0, 1, 0.001, 10, 0.75, i * 9);</span>
<span class="nc" id="L23">			temp = ccrr.test();</span>
<span class="nc" id="L24">			sums += temp;</span>
		}
<span class="nc" id="L26">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L27">		System.out.println(&quot;Fm: &quot; + sums / (double) times);</span>
<span class="nc" id="L28">		System.out.println(&quot;Time: &quot; + (endTime - startTime) / (double) times);</span>
<span class="nc" id="L29">	}</span>
	double min;
	double max;
	int seed;
	double R;
	double beta;
	double fail_rate;
	double fail_start;
<span class="nc" id="L37">	double coverage = 10;</span>

<span class="nc" id="L39">	ArrayList&lt;TestCase&gt; tests = new ArrayList&lt;&gt;();</span>

<span class="nc" id="L41">	public MyART_E_OD(double min, double max, double fail_rate, double beta, double R, int seed) {</span>
<span class="nc" id="L42">		this.min = min;</span>
<span class="nc" id="L43">		this.max = max;</span>
<span class="nc" id="L44">		this.seed = seed;</span>
<span class="nc" id="L45">		this.R = R;</span>
<span class="nc" id="L46">		this.fail_rate = fail_rate;</span>
<span class="nc" id="L47">		this.beta = beta;</span>
<span class="nc" id="L48">	}</span>

	public boolean isCorrect(double p) {
<span class="nc bnc" id="L51" title="All 4 branches missed.">		if (p &gt; fail_start &amp;&amp; p &lt; (fail_start + fail_rate)) {</span>
<span class="nc" id="L52">			return false;</span>
		} else {
<span class="nc" id="L54">			return true;</span>
		}
	}

	public void sortTestCases(TestCase p) {
<span class="nc" id="L59">		int low = 0, high = tests.size() - 1, mid = -1;</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">		while (low &lt;= high) {</span>
<span class="nc" id="L61">			mid = (low + high) / 2;</span>
<span class="nc bnc" id="L62" title="All 2 branches missed.">			if (p.p &gt; tests.get(mid).p) {</span>
<span class="nc" id="L63">				low = mid + 1;</span>
<span class="nc" id="L64">			} else {</span>
<span class="nc" id="L65">				high = mid - 1;</span>
			}
		}
<span class="nc bnc" id="L68" title="All 2 branches missed.">		if (p.p &lt; tests.get(mid).p) {</span>
<span class="nc" id="L69">			mid = mid - 1;</span>
		}
<span class="nc" id="L71">		tests.add(mid + 1, p);</span>
<span class="nc" id="L72">	}</span>

	/**
	 * @return F-measure
	 *
	 */
	public int test() {
<span class="nc" id="L79">		Random random = new Random(seed);</span>
<span class="nc" id="L80">		fail_start = random.nextDouble() * (max - min - fail_rate);</span>
<span class="nc" id="L81">		TestCase p = new TestCase();</span>
		// 执行第一个测试用例
<span class="nc" id="L83">		p.p = random.nextDouble() * (max - min) + min;</span>
<span class="nc" id="L84">		p.coverage = coverage;// useless</span>
<span class="nc" id="L85">		int count = 0;</span>
<span class="nc bnc" id="L86" title="All 2 branches missed.">		while (isCorrect(p.p)) {</span>
<span class="nc" id="L87">			count++;</span>
<span class="nc bnc" id="L88" title="All 2 branches missed.">			if (tests.size() == 0) {</span>
<span class="nc" id="L89">				tests.add(p);</span>
<span class="nc" id="L90">			} else</span>
<span class="nc" id="L91">				sortTestCases(p);</span>
			double datum_line;// 基准线，待会求出
<span class="nc" id="L93">			double tempProbability = 0.0;</span>
<span class="nc" id="L94">			double sumProbability = 0.0;</span>
<span class="nc" id="L95">			double radius = R / (2 * tests.size());</span>
			// System.out.println(&quot;radius:&quot;+radius);
<span class="nc" id="L97">			ArrayList&lt;double[]&gt; integrals = new ArrayList&lt;&gt;();</span>
<span class="nc" id="L98">			double[] informations = null;</span>
			/// 下面产生下一个测试用例,根据自己的概率曲线图
			// 先求第一段
<span class="nc bnc" id="L101" title="All 2 branches missed.">			if (tests.get(0).p - radius &gt; min) {</span>
				// calcu by wolfamalpha d(e1^(b+1)-r^(b+1))/((b+1)*m)
<span class="nc" id="L103">				double m = tests.get(0).p - min;// m表示间距</span>
<span class="nc" id="L104">				double n = tests.get(0).p;// n表示对应的点</span>
<span class="nc" id="L105">				double from = min;// 积分下限</span>
<span class="nc" id="L106">				double to = tests.get(0).p - radius;// 积分上限</span>
<span class="nc" id="L107">				double temp2 = beta + 1.0;//</span>
<span class="nc" id="L108">				tempProbability = ((-m * m) / (temp2))</span>
<span class="nc" id="L109">						* (Math.pow((n - to) / m, temp2) - Math.pow((n - from) / m, temp2));</span>
<span class="nc" id="L110">				sumProbability += tempProbability;</span>
<span class="nc" id="L111">				informations = new double[7];</span>
<span class="nc" id="L112">				informations[0] = tempProbability;</span>
<span class="nc" id="L113">				informations[1] = ZUOQUXIAN;</span>
<span class="nc" id="L114">				informations[2] = from;</span>
<span class="nc" id="L115">				informations[3] = to;</span>
<span class="nc" id="L116">				informations[4] = beta;</span>
<span class="nc" id="L117">				informations[5] = m;</span>
<span class="nc" id="L118">				informations[6] = n;</span>
<span class="nc" id="L119">				integrals.add(informations);</span>
			}
			// 求中间一段的积分值
<span class="nc bnc" id="L122" title="All 2 branches missed.">			for (int i = 0; i &lt; tests.size() - 1; i++) {</span>
				// 右
<span class="nc bnc" id="L124" title="All 2 branches missed.">				if (tests.get(i + 1).p - tests.get(i).p &gt; 2 * radius) {</span>
<span class="nc" id="L125">					double m = (tests.get(i + 1).p - tests.get(i).p) / 2.0;</span>
<span class="nc" id="L126">					double n = tests.get(i).p;</span>
<span class="nc" id="L127">					double from = tests.get(i).p + radius;</span>
<span class="nc" id="L128">					double to = (tests.get(i + 1).p + tests.get(i).p) / 2.0;</span>
<span class="nc" id="L129">					double temp2 = beta + 1.0;</span>
<span class="nc" id="L130">					tempProbability = (m * m / temp2)</span>
<span class="nc" id="L131">							* (Math.pow((to - n) / m, temp2) - Math.pow((from - n) / m, temp2));</span>
<span class="nc" id="L132">					sumProbability += tempProbability;</span>
<span class="nc" id="L133">					informations = new double[7];</span>
<span class="nc" id="L134">					informations[0] = tempProbability;</span>
<span class="nc" id="L135">					informations[1] = YOUQUXIAN;</span>
<span class="nc" id="L136">					informations[2] = from;</span>
<span class="nc" id="L137">					informations[3] = to;</span>
<span class="nc" id="L138">					informations[4] = beta;</span>
<span class="nc" id="L139">					informations[5] = m;</span>
<span class="nc" id="L140">					informations[6] = n;</span>
<span class="nc" id="L141">					integrals.add(informations);</span>
					// 下一个点的左边
<span class="nc" id="L143">					n = tests.get(i + 1).p;</span>
<span class="nc" id="L144">					from = (tests.get(i + 1).p + tests.get(i).p) / 2.0;</span>
<span class="nc" id="L145">					to = tests.get(i + 1).p - radius;</span>
<span class="nc" id="L146">					temp2 = beta + 1.0;</span>
<span class="nc" id="L147">					tempProbability = ((-m * m) / (temp2))</span>
<span class="nc" id="L148">							* (Math.pow((n - to) / m, temp2) - Math.pow((n - from) / m, temp2));</span>
<span class="nc" id="L149">					sumProbability += tempProbability;</span>
<span class="nc" id="L150">					informations = new double[7];</span>
<span class="nc" id="L151">					informations[0] = tempProbability;</span>
<span class="nc" id="L152">					informations[1] = ZUOQUXIAN;</span>
<span class="nc" id="L153">					informations[2] = from;</span>
<span class="nc" id="L154">					informations[3] = to;</span>
<span class="nc" id="L155">					informations[4] = beta;</span>
<span class="nc" id="L156">					informations[5] = m;</span>
<span class="nc" id="L157">					informations[6] = n;</span>
<span class="nc" id="L158">					integrals.add(informations);</span>
				}
			}
<span class="nc bnc" id="L161" title="All 2 branches missed.">			if (tests.get(tests.size() - 1).p + radius &lt; max) {</span>
<span class="nc" id="L162">				int indexofLast = tests.size() - 1;</span>
<span class="nc" id="L163">				double m = max - tests.get(indexofLast).p;</span>
<span class="nc" id="L164">				double n = tests.get(indexofLast).p;</span>
<span class="nc" id="L165">				double from = n + radius;</span>
<span class="nc" id="L166">				double to = max;</span>
<span class="nc" id="L167">				double temp2 = beta + 1.0;</span>
<span class="nc" id="L168">				tempProbability = (m * m / temp2) * (Math.pow((to - n) / m, temp2) - Math.pow((from - n) / m, temp2));</span>
				;
<span class="nc" id="L170">				sumProbability += tempProbability;</span>
<span class="nc" id="L171">				informations = new double[7];</span>
<span class="nc" id="L172">				informations[0] = tempProbability;</span>
<span class="nc" id="L173">				informations[1] = YOUQUXIAN;</span>
<span class="nc" id="L174">				informations[2] = from;</span>
<span class="nc" id="L175">				informations[3] = to;</span>
<span class="nc" id="L176">				informations[4] = beta;</span>
<span class="nc" id="L177">				informations[5] = m;</span>
<span class="nc" id="L178">				informations[6] = n;</span>
<span class="nc" id="L179">				integrals.add(informations);</span>
				// logger.info(&quot;end part&quot;);
			}
<span class="nc" id="L182">			datum_line = 1.0 / sumProbability;</span>
<span class="nc" id="L183">			double T = random.nextDouble() * 1.0;</span>
<span class="nc" id="L184">			double SumIntegral = 0.0;</span>
<span class="nc" id="L185">			double PreIntegral = 0.0;</span>
<span class="nc" id="L186">			int temp = 0;</span>
<span class="nc bnc" id="L187" title="All 2 branches missed.">			for (int i = 0; i &lt; integrals.size(); i++) {</span>
<span class="nc bnc" id="L188" title="All 2 branches missed.">				if (SumIntegral &lt; T) {</span>
<span class="nc" id="L189">					PreIntegral = SumIntegral;</span>
<span class="nc" id="L190">					temp = i;</span>
				}
<span class="nc" id="L192">				SumIntegral += integrals.get(i)[0] * datum_line;</span>
			}
			// 求下一个测试用例
<span class="nc" id="L195">			int type = (int) integrals.get(temp)[1];</span>
<span class="nc" id="L196">			double start = integrals.get(temp)[2];// 积分下限 from to x</span>
<span class="nc" id="L197">			double end = integrals.get(temp)[3];</span>
<span class="nc" id="L198">			double m = integrals.get(temp)[5];// 间距</span>
<span class="nc" id="L199">			double n = integrals.get(temp)[6];// 关联的点</span>
<span class="nc bnc" id="L200" title="All 2 branches missed.">			if (type == ZUOQUXIAN) {</span>
<span class="nc" id="L201">				double temp2 = integrals.get(temp)[4] + 1.0;</span>
<span class="nc" id="L202">				p = new TestCase();</span>
<span class="nc" id="L203">				double temp3 = ((T - PreIntegral) * temp2 / (-datum_line * m * m)) + Math.pow((n - start) / m, temp2);</span>
<span class="nc" id="L204">				p.p = n - m * (Math.pow(temp3, 1.0 / temp2));</span>
<span class="nc bnc" id="L205" title="All 4 branches missed.">				if (!(p.p &gt; start &amp;&amp; p.p &lt; end)) {</span>
<span class="nc" id="L206">					System.out.println(&quot;error left&quot;);</span>
				}
<span class="nc" id="L208">			} else {</span>
<span class="nc" id="L209">				double temp2 = integrals.get(temp)[4] + 1.0;</span>
<span class="nc" id="L210">				p = new TestCase();</span>
<span class="nc" id="L211">				double temp3 = ((temp2 * (T - PreIntegral)) / (datum_line * m * m)) + Math.pow((start - n) / m, temp2);</span>
<span class="nc" id="L212">				p.p = n + m * (Math.pow(temp3, 1.0 / temp2));</span>
<span class="nc bnc" id="L213" title="All 4 branches missed.">				if (!(p.p &gt; start &amp;&amp; p.p &lt; end)) {</span>
<span class="nc" id="L214">					System.out.println(&quot;error right&quot;);</span>
				}
			}
<span class="nc bnc" id="L217" title="All 6 branches missed.">			if (Double.isNaN(p.p) || p.p &lt; min || p.p &gt; max) {</span>
<span class="nc" id="L218">				System.out.println(&quot;Interrupt!!&quot;);</span>
			}
		}
<span class="nc" id="L221">		return count;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>