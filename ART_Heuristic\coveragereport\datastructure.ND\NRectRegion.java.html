<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>NRectRegion.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">datastructure.ND</a> &gt; <span class="el_source">NRectRegion.java</span></div><h1>NRectRegion.java</h1><pre class="source lang-java linenums">package datastructure.ND;

public class NRectRegion {
	public static void main(String[] args) {
<span class="nc" id="L5">		NRectRegion region = new NRectRegion();</span>
<span class="nc" id="L6">		region.start = new NPoint(new double[] { 0.1, 0.1, 0.1 });</span>
<span class="nc" id="L7">		region.end = new NPoint(new double[] { 0.4, 0.2, 0.2 });</span>
<span class="nc" id="L8">		System.out.println(region.size());</span>
<span class="nc" id="L9">	}</span>

	NPoint start;

	NPoint end;

	public NRectRegion() {
<span class="nc" id="L16">		super();</span>
<span class="nc" id="L17">	}</span>

	public NRectRegion(NPoint start, NPoint end) {
<span class="nc" id="L20">		super();</span>
<span class="nc" id="L21">		this.start = start;</span>
<span class="nc" id="L22">		this.end = end;</span>
<span class="nc" id="L23">	}</span>

	public NPoint getEnd() {
<span class="nc" id="L26">		return end;</span>
	}

	public NPoint getStart() {
<span class="nc" id="L30">		return start;</span>
	}

	public void setEnd(NPoint end) {
<span class="nc" id="L34">		this.end = end;</span>
<span class="nc" id="L35">	}</span>

	public void setStart(NPoint start) {
<span class="nc" id="L38">		this.start = start;</span>
<span class="nc" id="L39">	}</span>

	public double size() {
<span class="nc" id="L42">		double size = 1.0;</span>
<span class="nc bnc" id="L43" title="All 4 branches missed.">		if (start.xn == null || end.xn == null) {</span>
<span class="nc" id="L44">			return -1;</span>
		}
<span class="nc bnc" id="L46" title="All 2 branches missed.">		for (int i = 0; i &lt; start.xn.length; i++) {</span>
			// if(i==0){size=Math.abs(end.xn[i]-start.xn[i]);}
<span class="nc" id="L48">			size *= Math.abs(end.xn[i] - start.xn[i]);</span>
		}
<span class="nc" id="L50">		return size;</span>
	}

	public boolean isPointInRegion(NPoint p) {
<span class="nc" id="L54">		boolean result = false;</span>
<span class="nc" id="L55">		double[] start = getStart().getXn();</span>
<span class="nc" id="L56">		double[] end = getEnd().getXn();</span>

<span class="nc" id="L58">		double[] pxn = p.getXn();</span>
<span class="nc" id="L59">		boolean isPIn = true;</span>
<span class="nc bnc" id="L60" title="All 2 branches missed.">		for (int j = 0; j &lt; pxn.length; j++) {</span>
<span class="nc bnc" id="L61" title="All 4 branches missed.">			if (pxn[j] &lt; start[j] || pxn[j] &gt; end[j]) {</span>
<span class="nc" id="L62">				isPIn = false;</span>
			}
		}

<span class="nc" id="L66">		return isPIn;</span>
	}

	@Override
	public String toString() {
<span class="nc" id="L71">		return &quot;NRectRegion [start=&quot; + start + &quot;, end=&quot; + end + &quot;]&quot;;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>