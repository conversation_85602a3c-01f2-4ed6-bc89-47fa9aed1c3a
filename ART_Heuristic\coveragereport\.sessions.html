<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href=".resources/report.css" type="text/css"/><link rel="shortcut icon" href=".resources/report.gif" type="image/gif"/><title>Sessions</title></head><body><div class="breadcrumb" id="breadcrumb"><span class="info"><a href=".sessions.html" class="el_session">Sessions</a></span><a href="index.html" class="el_report">ART</a> &gt; <span class="el_session">Sessions</span></div><h1>Sessions</h1><p>This coverage report is based on execution data from the following sessions:</p><table class="coverage" cellspacing="0"><thead><tr><td>Session</td><td>Start Time</td><td>Dump Time</td></tr></thead><tbody><tr><td><span class="el_session">xijiaxiang-PC-6659b3c2</span></td><td>2018-1-26 17:08:44</td><td>2018-1-26 17:08:45</td></tr><tr><td><span class="el_session">xijiaxiang-PC-656f7c80</span></td><td>2018-1-26 17:55:42</td><td>2018-1-26 17:55:43</td></tr></tbody></table><p>Execution data for the following classes is considered in this report:</p><table class="coverage" cellspacing="0"><thead><tr><td>Class</td><td>Id</td></tr></thead><tbody><tr><td><span class="el_class">RealityFailureRate.Testbessj0</span></td><td><code>52479e47e25f1a5c</code></td></tr><tr><td><span class="el_class">RealityFailureRate.Testbessj0</span></td><td><code>1e6ca8f66f2d8b73</code></td></tr><tr><td><a href="datastructure.ND/NPoint.html" class="el_class">datastructure.ND.NPoint</a></td><td><code>7ab74be2ebf3997d</code></td></tr><tr><td><a href="datastructure.failurepattern/FailurePattern.html" class="el_class">datastructure.failurepattern.FailurePattern</a></td><td><code>e342f010bfdf23e7</code></td></tr><tr><td><a href="datastructure.failurepattern.impl/BlockPattern.html" class="el_class">datastructure.failurepattern.impl.BlockPattern</a></td><td><code>1d964bc404908cd2</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit.runner.DefaultClassifier</span></td><td><code>6529b18d989020de</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit.runner.FirstRunExecutionListener</span></td><td><code>72e7d6d31b2dca1e</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit.runner.RemoteTestRunner</span></td><td><code>9eff80f617bc9ab2</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit.runner.RemoteTestRunner.ReaderThread</span></td><td><code>b178f2452a3665d3</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit.runner.TestExecution</span></td><td><code>e652a18f68b6d4d4</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit.runner.TestIdMap</span></td><td><code>92a1552568c37473</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit4.runner.DescriptionMatcher</span></td><td><code>bf52ca5a050e2974</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit4.runner.DescriptionMatcher.CompositeMatcher</span></td><td><code>a9b1997010941f78</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit4.runner.DescriptionMatcher.ExactMatcher</span></td><td><code>dbee85cdfb3f5512</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit4.runner.DescriptionMatcher.LeadingIdentifierMatcher</span></td><td><code>48357a821ccc4598</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit4.runner.JUnit4Identifier</span></td><td><code>594205fb27f73919</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit4.runner.JUnit4TestListener</span></td><td><code>2a7658cee04f69e3</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit4.runner.JUnit4TestLoader</span></td><td><code>abd187362af39c42</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit4.runner.JUnit4TestReference</span></td><td><code>573f2e7aed6dec69</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit4.runner.JUnit4TestReference.1</span></td><td><code>5cb1946e627d5daf</code></td></tr><tr><td><span class="el_class">org.eclipse.jdt.internal.junit4.runner.SubForestFilter</span></td><td><code>06f13e56b88f06c8</code></td></tr><tr><td><span class="el_class">org.junit.Assert</span></td><td><code>e2bac9fc5ef3a4be</code></td></tr><tr><td><span class="el_class">org.junit.internal.MethodSorter</span></td><td><code>e7c1106d3801ff54</code></td></tr><tr><td><span class="el_class">org.junit.internal.MethodSorter.1</span></td><td><code>81fdc65e8fe19b52</code></td></tr><tr><td><span class="el_class">org.junit.internal.MethodSorter.2</span></td><td><code>bfe6560dc3722ab0</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.AllDefaultPossibilitiesBuilder</span></td><td><code>84f7fffb8cd30ad9</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.AnnotatedBuilder</span></td><td><code>0faf353d180c9332</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.IgnoredBuilder</span></td><td><code>e152f333c53967a6</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.JUnit3Builder</span></td><td><code>4a2cc8e608e1275e</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.JUnit4Builder</span></td><td><code>5902b7da0403f55c</code></td></tr><tr><td><span class="el_class">org.junit.internal.builders.NullBuilder</span></td><td><code>2c8f3561ed8ea9c5</code></td></tr><tr><td><span class="el_class">org.junit.internal.requests.ClassRequest</span></td><td><code>f1e703dd2591ce5c</code></td></tr><tr><td><span class="el_class">org.junit.internal.requests.FilterRequest</span></td><td><code>4bc584d702371a84</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.model.EachTestNotifier</span></td><td><code>0cb318e674165ac8</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.model.ReflectiveCallable</span></td><td><code>d591724635588bcb</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator</span></td><td><code>95b5ee2068ec6875</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.Builder</span></td><td><code>f24845fa6fd065af</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.DeclaringClassMustBePublic</span></td><td><code>1de994463c748d89</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.FieldMustBeARule</span></td><td><code>e24e9f59de6fe5b7</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.FieldMustBeATestRule</span></td><td><code>690823bd2992f52e</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.MemberMustBeNonStaticOrAlsoClassRule</span></td><td><code>1e703fb3e7f4e533</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.MemberMustBePublic</span></td><td><code>806c174eb921b478</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.MemberMustBeStatic</span></td><td><code>ac28a03dd36b2b5a</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.MethodMustBeARule</span></td><td><code>88ea4a2237de2b8b</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.rules.RuleMemberValidator.MethodMustBeATestRule</span></td><td><code>9f4dd18a26005c18</code></td></tr><tr><td><span class="el_class">org.junit.internal.runners.statements.InvokeMethod</span></td><td><code>05a7aa636afa2c39</code></td></tr><tr><td><span class="el_class">org.junit.runner.Description</span></td><td><code>c7f1b09126c24b2b</code></td></tr><tr><td><span class="el_class">org.junit.runner.Request</span></td><td><code>4f785af929bd628a</code></td></tr><tr><td><span class="el_class">org.junit.runner.Result</span></td><td><code>3a364b299d905039</code></td></tr><tr><td><span class="el_class">org.junit.runner.Result.Listener</span></td><td><code>bbae11d09f5b5a09</code></td></tr><tr><td><span class="el_class">org.junit.runner.Runner</span></td><td><code>f5abacc70e2e08a4</code></td></tr><tr><td><span class="el_class">org.junit.runner.manipulation.Filter</span></td><td><code>106639ba05436698</code></td></tr><tr><td><span class="el_class">org.junit.runner.manipulation.Filter.1</span></td><td><code>ebd8f8e7075b6616</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunListener</span></td><td><code>a740fd873cf92a63</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier</span></td><td><code>ba709a76760379c2</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.1</span></td><td><code>6eb5e06975b1ea02</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.2</span></td><td><code>dc4db4223d160c08</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.3</span></td><td><code>7a903d9d1caf7673</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.7</span></td><td><code>304ecd1b313cb650</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.RunNotifier.SafeNotifier</span></td><td><code>3b3dc2f2fc8cfc56</code></td></tr><tr><td><span class="el_class">org.junit.runner.notification.SynchronizedRunListener</span></td><td><code>0f89c0c6a77088e5</code></td></tr><tr><td><span class="el_class">org.junit.runners.BlockJUnit4ClassRunner</span></td><td><code>673d2df2f68a9490</code></td></tr><tr><td><span class="el_class">org.junit.runners.BlockJUnit4ClassRunner.1</span></td><td><code>b3af68717b17ffc6</code></td></tr><tr><td><span class="el_class">org.junit.runners.ParentRunner</span></td><td><code>df303f19df248a10</code></td></tr><tr><td><span class="el_class">org.junit.runners.ParentRunner.1</span></td><td><code>89f115a2214a3636</code></td></tr><tr><td><span class="el_class">org.junit.runners.ParentRunner.2</span></td><td><code>0fc04165488ae7c7</code></td></tr><tr><td><span class="el_class">org.junit.runners.ParentRunner.3</span></td><td><code>65f7d637ed11f8f4</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.FrameworkField</span></td><td><code>d6d3c27befd6f49d</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.FrameworkMember</span></td><td><code>83f9d72bb2731cf1</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.FrameworkMethod</span></td><td><code>b9c1cccbfa624e4a</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.FrameworkMethod.1</span></td><td><code>87d2600c48ade534</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.RunnerBuilder</span></td><td><code>0281d51b4f8328d4</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.Statement</span></td><td><code>9a75aa5de27bf4d5</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.TestClass</span></td><td><code>90136128a3e4d163</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.TestClass.FieldComparator</span></td><td><code>261449f31a730808</code></td></tr><tr><td><span class="el_class">org.junit.runners.model.TestClass.MethodComparator</span></td><td><code>5a734d8eaadb6011</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationValidatorFactory</span></td><td><code>e736331fde301341</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationsValidator</span></td><td><code>51f829810937d72f</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationsValidator.AnnotatableValidator</span></td><td><code>d211a963f22be103</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationsValidator.ClassValidator</span></td><td><code>1b463c4e6642e880</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationsValidator.FieldValidator</span></td><td><code>64068b954dc56a31</code></td></tr><tr><td><span class="el_class">org.junit.validator.AnnotationsValidator.MethodValidator</span></td><td><code>f16b57f17c787036</code></td></tr><tr><td><span class="el_class">org.junit.validator.PublicClassValidator</span></td><td><code>3bac248cf06b18e4</code></td></tr><tr><td><span class="el_class">sun.text.resources.zh.FormatData_zh</span></td><td><code>5625dead6acf27de</code></td></tr><tr><td><span class="el_class">sun.text.resources.zh.FormatData_zh_CN</span></td><td><code>6cda43eb50c5edd5</code></td></tr><tr><td><span class="el_class">sun.util.resources.zh.CurrencyNames_zh_CN</span></td><td><code>9b502962fe808cdb</code></td></tr><tr><td><a href="test/ART.html" class="el_class">test.ART</a></td><td><code>3195bc9ac1ce42d6</code></td></tr><tr><td><a href="test.simulations.rrt/RRT_ND.html" class="el_class">test.simulations.rrt.RRT_ND</a></td><td><code>b9782fe11732135b</code></td></tr><tr><td><a href="util/RandomCreator.html" class="el_class">util.RandomCreator</a></td><td><code>9025bbafd976255c</code></td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>