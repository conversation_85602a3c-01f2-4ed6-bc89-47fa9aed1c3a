<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>StripPatternIn2D</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">datastructure.failurepattern.impl</a> &gt; <span class="el_class">StripPatternIn2D</span></div><h1>StripPatternIn2D</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">1,067 of 1,067</td><td class="ctr2">0%</td><td class="bar">40 of 40</td><td class="ctr2">0%</td><td class="ctr1">44</td><td class="ctr2">44</td><td class="ctr1">150</td><td class="ctr2">150</td><td class="ctr1">19</td><td class="ctr2">19</td></tr></tfoot><tbody><tr><td id="a13"><a href="StripPatternIn2D.java.html#L11" class="el_method">main(String[])</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="195" alt="195"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d11"/><td class="ctr2" id="e11">n/a</td><td class="ctr1" id="f11">1</td><td class="ctr2" id="g11">1</td><td class="ctr1" id="h0">27</td><td class="ctr2" id="i0">27</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a6"><a href="StripPatternIn2D.java.html#L128" class="el_method">gen3()</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="65" height="10" title="106" alt="106"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h3">10</td><td class="ctr2" id="i3">10</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="StripPatternIn2D.java.html#L115" class="el_method">gen2()</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="62" height="10" title="102" alt="102"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d6"><img src="../.resources/redbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f6">2</td><td class="ctr2" id="g6">2</td><td class="ctr1" id="h4">10</td><td class="ctr2" id="i4">10</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a8"><a href="StripPatternIn2D.java.html#L154" class="el_method">gen5()</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="62" height="10" title="102" alt="102"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d7"><img src="../.resources/redbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f7">2</td><td class="ctr2" id="g7">2</td><td class="ctr1" id="h5">10</td><td class="ctr2" id="i5">10</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a2"><a href="StripPatternIn2D.java.html#L73" class="el_method">check2(NPoint)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="60" height="10" title="98" alt="98"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="53" height="10" title="4" alt="4"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f2">3</td><td class="ctr2" id="g2">3</td><td class="ctr1" id="h9">9</td><td class="ctr2" id="i9">9</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a7"><a href="StripPatternIn2D.java.html#L141" class="el_method">gen4()</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="60" height="10" title="98" alt="98"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d8"><img src="../.resources/redbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f8">2</td><td class="ctr2" id="g8">2</td><td class="ctr1" id="h6">10</td><td class="ctr2" id="i6">10</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a3"><a href="StripPatternIn2D.java.html#L89" class="el_method">gen0()</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="55" height="10" title="90" alt="90"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d9"><img src="../.resources/redbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f9">2</td><td class="ctr2" id="g9">2</td><td class="ctr1" id="h7">10</td><td class="ctr2" id="i7">10</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a4"><a href="StripPatternIn2D.java.html#L102" class="el_method">gen1()</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="55" height="10" title="90" alt="90"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d10"><img src="../.resources/redbar.gif" width="26" height="10" title="2" alt="2"/></td><td class="ctr2" id="e6">0%</td><td class="ctr1" id="f10">2</td><td class="ctr2" id="g10">2</td><td class="ctr1" id="h8">10</td><td class="ctr2" id="i8">10</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a9"><a href="StripPatternIn2D.java.html#L169" class="el_method">genFailurePattern()</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="34" height="10" title="56" alt="56"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="9" alt="9"/></td><td class="ctr2" id="e7">0%</td><td class="ctr1" id="f0">8</td><td class="ctr2" id="g0">8</td><td class="ctr1" id="h1">19</td><td class="ctr2" id="i1">19</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr><tr><td id="a12"><a href="StripPatternIn2D.java.html#L226" class="el_method">isCorrect(NPoint)</a></td><td class="bar" id="b9"><img src="../.resources/redbar.gif" width="24" height="10" title="39" alt="39"/></td><td class="ctr2" id="c9">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="93" height="10" title="7" alt="7"/></td><td class="ctr2" id="e8">0%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h2">16</td><td class="ctr2" id="i2">16</td><td class="ctr1" id="j9">1</td><td class="ctr2" id="k9">1</td></tr><tr><td id="a0"><a href="StripPatternIn2D.java.html#L55" class="el_method">check0(NPoint)</a></td><td class="bar" id="b10"><img src="../.resources/redbar.gif" width="16" height="10" title="27" alt="27"/></td><td class="ctr2" id="c10">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="53" height="10" title="4" alt="4"/></td><td class="ctr2" id="e9">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h10">4</td><td class="ctr2" id="i10">4</td><td class="ctr1" id="j10">1</td><td class="ctr2" id="k10">1</td></tr><tr><td id="a1"><a href="StripPatternIn2D.java.html#L64" class="el_method">check1(NPoint)</a></td><td class="bar" id="b11"><img src="../.resources/redbar.gif" width="16" height="10" title="27" alt="27"/></td><td class="ctr2" id="c11">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="53" height="10" title="4" alt="4"/></td><td class="ctr2" id="e10">0%</td><td class="ctr1" id="f4">3</td><td class="ctr2" id="g4">3</td><td class="ctr1" id="h11">4</td><td class="ctr2" id="i11">4</td><td class="ctr1" id="j11">1</td><td class="ctr2" id="k11">1</td></tr><tr><td id="a14"><a href="StripPatternIn2D.java.html#L262" class="el_method">random()</a></td><td class="bar" id="b12"><img src="../.resources/redbar.gif" width="11" height="10" title="19" alt="19"/></td><td class="ctr2" id="c12">0%</td><td class="bar" id="d12"/><td class="ctr2" id="e12">n/a</td><td class="ctr1" id="f12">1</td><td class="ctr2" id="g12">1</td><td class="ctr1" id="h12">3</td><td class="ctr2" id="i12">3</td><td class="ctr1" id="j12">1</td><td class="ctr2" id="k12">1</td></tr><tr><td id="a15"><a href="StripPatternIn2D.java.html#L268" class="el_method">setX(double)</a></td><td class="bar" id="b13"><img src="../.resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c13">0%</td><td class="bar" id="d13"/><td class="ctr2" id="e13">n/a</td><td class="ctr1" id="f13">1</td><td class="ctr2" id="g13">1</td><td class="ctr1" id="h13">2</td><td class="ctr2" id="i13">2</td><td class="ctr1" id="j13">1</td><td class="ctr2" id="k13">1</td></tr><tr><td id="a16"><a href="StripPatternIn2D.java.html#L272" class="el_method">setY(double)</a></td><td class="bar" id="b14"><img src="../.resources/redbar.gif" width="2" height="10" title="4" alt="4"/></td><td class="ctr2" id="c14">0%</td><td class="bar" id="d14"/><td class="ctr2" id="e14">n/a</td><td class="ctr1" id="f14">1</td><td class="ctr2" id="g14">1</td><td class="ctr1" id="h14">2</td><td class="ctr2" id="i14">2</td><td class="ctr1" id="j14">1</td><td class="ctr2" id="k14">1</td></tr><tr><td id="a18"><a href="StripPatternIn2D.java.html#L9" class="el_method">StripPatternIn2D()</a></td><td class="bar" id="b15"><img src="../.resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c15">0%</td><td class="bar" id="d15"/><td class="ctr2" id="e15">n/a</td><td class="ctr1" id="f15">1</td><td class="ctr2" id="g15">1</td><td class="ctr1" id="h15">1</td><td class="ctr2" id="i15">1</td><td class="ctr1" id="j15">1</td><td class="ctr2" id="k15">1</td></tr><tr><td id="a10"><a href="StripPatternIn2D.java.html#L217" class="el_method">getX()</a></td><td class="bar" id="b16"><img src="../.resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c16">0%</td><td class="bar" id="d16"/><td class="ctr2" id="e16">n/a</td><td class="ctr1" id="f16">1</td><td class="ctr2" id="g16">1</td><td class="ctr1" id="h16">1</td><td class="ctr2" id="i16">1</td><td class="ctr1" id="j16">1</td><td class="ctr2" id="k16">1</td></tr><tr><td id="a11"><a href="StripPatternIn2D.java.html#L221" class="el_method">getY()</a></td><td class="bar" id="b17"><img src="../.resources/redbar.gif" width="1" height="10" title="3" alt="3"/></td><td class="ctr2" id="c17">0%</td><td class="bar" id="d17"/><td class="ctr2" id="e17">n/a</td><td class="ctr1" id="f17">1</td><td class="ctr2" id="g17">1</td><td class="ctr1" id="h17">1</td><td class="ctr2" id="i17">1</td><td class="ctr1" id="j17">1</td><td class="ctr2" id="k17">1</td></tr><tr><td id="a17"><a href="StripPatternIn2D.java.html#L279" class="el_method">showFailurePattern()</a></td><td class="bar" id="b18"/><td class="ctr2" id="c18">0%</td><td class="bar" id="d18"/><td class="ctr2" id="e18">n/a</td><td class="ctr1" id="f18">1</td><td class="ctr2" id="g18">1</td><td class="ctr1" id="h18">1</td><td class="ctr2" id="i18">1</td><td class="ctr1" id="j18">1</td><td class="ctr2" id="k18">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>