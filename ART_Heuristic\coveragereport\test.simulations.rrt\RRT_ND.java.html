<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRT_ND.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rrt</a> &gt; <span class="el_source">RRT_ND.java</span></div><h1>RRT_ND.java</h1><pre class="source lang-java linenums">package test.simulations.rrt;

import java.util.ArrayList;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.PointPatternIn1D;
import test.ART;
import test.simulations.art_b.ART_B_ND;
import test.simulations.rt.RT_ND;
import util.data.ZeroOneCreator;

/*
 * n维实现，包含了1,2,3,4维，以及其他维度
 * */
public class RRT_ND extends ART {
	public static void main(String[] args) {

		// rt.pmCount=69314;
		// System.out.println(rt.pm());
<span class="nc" id="L23">		testEm(2,0.01);</span>
		//testTCTime(2, 5000);
		 //System.out.println(testPm(2, 0.01));
		//testFm();
<span class="nc" id="L27">	}</span>

	public double R;
<span class="fc" id="L30">	public ArrayList&lt;NPoint&gt; tests = new ArrayList&lt;&gt;();</span>

	public RRT_ND(double[] min, double[] max, FailurePattern pattern, Random random, double r) {
<span class="fc" id="L33">		super(min, max, random, pattern);</span>
<span class="fc" id="L34">		this.R = r;</span>
<span class="fc" id="L35">	}</span>

	public double calculateRadius(int count) {
<span class="pc bpc" id="L38" title="1 of 2 branches missed.">		if (this.dimension % 2 == 0) {</span>
<span class="nc" id="L39">			int k = this.dimension / 2;</span>
<span class="nc" id="L40">			double kjie = 1;</span>
<span class="nc bnc" id="L41" title="All 2 branches missed.">			for (int i = k; i &gt; 0; i--) {</span>
<span class="nc" id="L42">				kjie *= i;</span>
			}
<span class="nc" id="L44">			double temp = (this.R * totalArea * kjie) / (count * Math.pow(Math.PI, k));</span>

<span class="nc" id="L46">			return Math.pow(temp, 1 / (double) this.dimension);</span>
		} else {
<span class="fc" id="L48">			int k = this.dimension / 2;</span>
<span class="fc" id="L49">			double kjie = 1;</span>
<span class="fc" id="L50">			double k2jie = 1;</span>
<span class="pc bpc" id="L51" title="1 of 2 branches missed.">			for (int i = k; i &gt; 0; i--) {</span>
<span class="nc" id="L52">				kjie *= i;</span>
			}
<span class="fc bfc" id="L54" title="All 2 branches covered.">			for (int i = (2 * k + 1); i &gt; 0; i--) {</span>
<span class="fc" id="L55">				k2jie *= i;</span>
			}
<span class="fc" id="L57">			double temp = (this.R * totalArea * k2jie) / (kjie * Math.pow(2, 2 * k + 1) * Math.pow(Math.PI, k) * count);</span>
			// System.out.println(&quot;return R&quot;);
<span class="fc" id="L59">			return Math.pow(temp, 1 / (double) this.dimension);</span>
		}
	}

	public NPoint generateNextTC() {
<span class="fc" id="L64">		NPoint p = null;</span>
<span class="fc bfc" id="L65" title="All 2 branches covered.">		if (tests.size() == 0) {</span>
<span class="fc" id="L66">			p = randomCreator.randomPoint();</span>
<span class="fc" id="L67">			tests.add(p);</span>
			// return p;
<span class="fc" id="L69">		} else {</span>
<span class="fc" id="L70">			double radius = calculateRadius(tests.size());</span>
<span class="fc" id="L71">			boolean flag = true;</span>
<span class="fc bfc" id="L72" title="All 2 branches covered.">			while (flag) {</span>
<span class="fc" id="L73">				flag = false;</span>
<span class="fc" id="L74">				p = randomCreator.randomPoint();</span>
<span class="fc bfc" id="L75" title="All 2 branches covered.">				for (int i = 0; i &lt; tests.size(); i++) {</span>
					// 排除区域是圆
					// 计算距离
<span class="fc" id="L78">					double[] tested = tests.get(i).getXn();</span>
<span class="fc" id="L79">					double distance = 0;</span>
<span class="fc" id="L80">					double[] untested = p.getXn();</span>
<span class="fc bfc" id="L81" title="All 2 branches covered.">					for (int j = 0; j &lt; this.dimension; j++) {</span>
<span class="fc" id="L82">						distance += Math.pow((tests.get(i).getXn()[j] - untested[j]), 2);</span>
					}
<span class="fc" id="L84">					distance = Math.sqrt(distance);</span>
<span class="fc bfc" id="L85" title="All 2 branches covered.">					if (distance &lt; radius) {</span>
<span class="fc" id="L86">						flag = true;</span>
<span class="fc" id="L87">						break;</span>
					}
					/*
					 * //排除区域是正方形 if(Math.abs(p.p-tests.get(i).p)&lt;radius){
					 * if(Math.abs(p.q-tests.get(i).q)&lt;radius){ flag=true; } }
					 */
				}
			}
<span class="fc" id="L95">			tests.add(p);</span>
		}
<span class="fc" id="L97">		return p;</span>
	}

	

	

	public void time() {
<span class="nc" id="L105">		int count = 0;</span>
<span class="nc" id="L106">		NPoint p = randomCreator.randomPoint();</span>
<span class="nc bnc" id="L107" title="All 2 branches missed.">		while (count &lt; tcCount) {</span>
<span class="nc" id="L108">			count++;</span>
<span class="nc" id="L109">			tests.add(p);</span>
<span class="nc" id="L110">			double radius = calculateRadius(tests.size());</span>
<span class="nc" id="L111">			boolean flag = true;</span>
<span class="nc bnc" id="L112" title="All 2 branches missed.">			while (flag) {</span>
<span class="nc" id="L113">				flag = false;</span>
<span class="nc" id="L114">				p = randomCreator.randomPoint();</span>
<span class="nc bnc" id="L115" title="All 2 branches missed.">				for (int i = 0; i &lt; tests.size(); i++) {</span>
					// 排除区域是圆
					// 计算距离
<span class="nc" id="L118">					double[] tested = tests.get(i).getXn();</span>
<span class="nc" id="L119">					double distance = 0;</span>
<span class="nc" id="L120">					double[] untested = p.getXn();</span>
<span class="nc bnc" id="L121" title="All 2 branches missed.">					for (int j = 0; j &lt; this.dimension; j++) {</span>
<span class="nc" id="L122">						distance += Math.pow((tests.get(i).getXn()[j] - untested[j]), 2);</span>
					}
<span class="nc" id="L124">					distance = Math.sqrt(distance);</span>
<span class="nc bnc" id="L125" title="All 2 branches missed.">					if (distance &lt; radius) {</span>
<span class="nc" id="L126">						flag = true;</span>
						// break;
					}
					/*
					 * //排除区域是正方形 if(Math.abs(p.p-tests.get(i).p)&lt;radius){
					 * if(Math.abs(p.q-tests.get(i).q)&lt;radius){ flag=true; } }
					 */
				}
			}
		}
<span class="nc" id="L136">	}</span>

	
	

	public static double testFm() {
<span class="nc" id="L142">		int d = 2;</span>
<span class="nc" id="L143">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L144">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L145">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L147">		int times = 2000;</span>

<span class="nc" id="L149">		double R = 0;</span>
<span class="nc bnc" id="L150" title="All 2 branches missed.">		if (d == 1) {</span>
<span class="nc" id="L151">			R = 0.75;</span>
<span class="nc" id="L152">		} else {</span>
<span class="nc" id="L153">			R = 1.5;</span>
		}

<span class="nc" id="L156">		int temp = 0;</span>
<span class="nc" id="L157">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L158">		failurePattern.fail_rate = 0.005;</span>
<span class="nc" id="L159">		long sums = 0;</span>
<span class="nc" id="L160">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L161" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L162">			RRT_ND rt = new RRT_ND(min, max, failurePattern, new Random(i * 5), R);</span>
<span class="nc" id="L163">			temp = rt.run();</span>
<span class="nc" id="L164">			sums += temp;</span>
		}
<span class="nc" id="L166">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L167">		double fm = sums / (double) times;</span>
<span class="nc" id="L168">		System.out.println(&quot;fm:&quot; + fm + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
<span class="nc" id="L169">		return fm;</span>
	}

	public static double[] testEm(int dimension, double failrate) {
<span class="nc" id="L173">		int d = dimension;</span>
<span class="nc" id="L174">		int emTime = 6;</span>
<span class="nc" id="L175">		double[] result = new double[emTime];</span>
<span class="nc" id="L176">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L177">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L178">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L180">		int times = 2000;</span>

<span class="nc" id="L182">		int temp = 0;</span>
<span class="nc" id="L183">		double R = 0;</span>
<span class="nc bnc" id="L184" title="All 2 branches missed.">		if (d == 1) {</span>
<span class="nc" id="L185">			R = 0.75;</span>
<span class="nc" id="L186">		} else {</span>
<span class="nc" id="L187">			R = 1.5;</span>
		}
<span class="nc" id="L189">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L190">		failurePattern.fail_rate = failrate;</span>
<span class="nc bnc" id="L191" title="All 2 branches missed.">		for (int k = 0; k &lt; emTime; k++) {</span>
<span class="nc" id="L192">			long sums = 0;</span>
<span class="nc" id="L193">			long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L194" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L195">				RRT_ND nd = new RRT_ND(min, max, failurePattern, new Random(i * 5), R);</span>
<span class="nc" id="L196">				nd.emCount = (k + 1) * 500;</span>
<span class="nc" id="L197">				temp = nd.em();</span>
<span class="nc" id="L198">				sums += temp;</span>
			}
<span class="nc" id="L200">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L201">			double em = sums / (double) times;</span>
<span class="nc" id="L202">			result[k] = em;</span>
<span class="nc" id="L203">			System.out.println(&quot;em:&quot; + em + &quot; time:&quot; + ((endTime - startTime) / (double) times));</span>
		}
<span class="nc" id="L205">		System.out.println();</span>
<span class="nc" id="L206">		return result;</span>
	}

	public static double testTCTime(int d, int tcCount) {
<span class="nc bnc" id="L210" title="All 2 branches missed.">		double R = d == 1 ? 0.75 : 1.5;</span>
<span class="nc" id="L211">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L212">		double min[] = dataCreator.minCreator(d);</span>
<span class="nc" id="L213">		double max[] = dataCreator.maxCreator(d);</span>

<span class="nc" id="L215">		int times = 1;</span>

<span class="nc" id="L217">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L218">		failurePattern.fail_rate = 0.0001;</span>
<span class="nc" id="L219">		long startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L220" title="All 2 branches missed.">		for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L221">			RRT_ND nd = new RRT_ND(min, max, failurePattern, new Random(i * 5), R);</span>
<span class="nc" id="L222">			nd.tcCount = tcCount;</span>
<span class="nc" id="L223">			nd.time2();</span>
		}
<span class="nc" id="L225">		long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L226">		System.out.println((endTime - startTime) / (double) times);</span>
<span class="nc" id="L227">		return ((endTime - startTime) / (double) times);</span>
	}

	public static double testPm(int d, double failrate) {
<span class="nc" id="L231">		ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L232">		double[] min = dataCreator.minCreator(d);</span>
<span class="nc" id="L233">		double[] max = dataCreator.maxCreator(d);</span>
<span class="nc" id="L234">		FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L235">		failurePattern.fail_rate = failrate;</span>
<span class="nc" id="L236">		double R = 0.75;</span>
<span class="nc bnc" id="L237" title="All 2 branches missed.">		if (d &gt; 1) {</span>
<span class="nc" id="L238">			R = 1.5;</span>
		}
<span class="nc" id="L240">		int n = 1000;</span>
<span class="nc" id="L241">		int pm = 0;</span>
<span class="nc bnc" id="L242" title="All 2 branches missed.">		for (int i = 0; i &lt; n; i++) {</span>
<span class="nc" id="L243">			RRT_ND nd = new RRT_ND(min, max, failurePattern, new Random(i * 5), R);</span>
<span class="nc bnc" id="L244" title="All 2 branches missed.">			if (nd.pm()) {</span>
<span class="nc" id="L245">				pm++;</span>
			}
		}
<span class="nc" id="L248">		return pm / (double) n;</span>
	}

}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>