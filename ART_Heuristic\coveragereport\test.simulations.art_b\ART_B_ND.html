<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>ART_B_ND</title><script type="text/javascript" src="../.resources/sort.js"></script></head><body onload="initialSort(['breadcrumb'])"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.html" class="el_package">test.simulations.art_b</a> &gt; <span class="el_class">ART_B_ND</span></div><h1>ART_B_ND</h1><table class="coverage" cellspacing="0" id="coveragetable"><thead><tr><td class="sortable" id="a" onclick="toggleSort(this)">Element</td><td class="down sortable bar" id="b" onclick="toggleSort(this)">Missed Instructions</td><td class="sortable ctr2" id="c" onclick="toggleSort(this)">Cov.</td><td class="sortable bar" id="d" onclick="toggleSort(this)">Missed Branches</td><td class="sortable ctr2" id="e" onclick="toggleSort(this)">Cov.</td><td class="sortable ctr1" id="f" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="g" onclick="toggleSort(this)">Cxty</td><td class="sortable ctr1" id="h" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="i" onclick="toggleSort(this)">Lines</td><td class="sortable ctr1" id="j" onclick="toggleSort(this)">Missed</td><td class="sortable ctr2" id="k" onclick="toggleSort(this)">Methods</td></tr></thead><tfoot><tr><td>Total</td><td class="bar">785 of 785</td><td class="ctr2">0%</td><td class="bar">46 of 46</td><td class="ctr2">0%</td><td class="ctr1">32</td><td class="ctr2">32</td><td class="ctr1">159</td><td class="ctr2">159</td><td class="ctr1">9</td><td class="ctr2">9</td></tr></tfoot><tbody><tr><td id="a2"><a href="ART_B_ND.java.html#L62" class="el_method">generateNextTC()</a></td><td class="bar" id="b0"><img src="../.resources/redbar.gif" width="120" height="10" title="214" alt="214"/></td><td class="ctr2" id="c0">0%</td><td class="bar" id="d1"><img src="../.resources/redbar.gif" width="90" height="10" title="12" alt="12"/></td><td class="ctr2" id="e0">0%</td><td class="ctr1" id="f1">7</td><td class="ctr2" id="g1">7</td><td class="ctr1" id="h0">43</td><td class="ctr2" id="i0">43</td><td class="ctr1" id="j0">1</td><td class="ctr2" id="k0">1</td></tr><tr><td id="a8"><a href="ART_B_ND.java.html#L120" class="el_method">time()</a></td><td class="bar" id="b1"><img src="../.resources/redbar.gif" width="112" height="10" title="200" alt="200"/></td><td class="ctr2" id="c1">0%</td><td class="bar" id="d0"><img src="../.resources/redbar.gif" width="120" height="10" title="16" alt="16"/></td><td class="ctr2" id="e1">0%</td><td class="ctr1" id="f0">9</td><td class="ctr2" id="g0">9</td><td class="ctr1" id="h1">40</td><td class="ctr2" id="i1">40</td><td class="ctr1" id="j1">1</td><td class="ctr2" id="k1">1</td></tr><tr><td id="a5"><a href="ART_B_ND.java.html#L202" class="el_method">testEm(int, double)</a></td><td class="bar" id="b2"><img src="../.resources/redbar.gif" width="57" height="10" title="102" alt="102"/></td><td class="ctr2" id="c2">0%</td><td class="bar" id="d3"><img src="../.resources/redbar.gif" width="30" height="10" title="4" alt="4"/></td><td class="ctr2" id="e2">0%</td><td class="ctr1" id="f3">3</td><td class="ctr2" id="g3">3</td><td class="ctr1" id="h2">21</td><td class="ctr2" id="i2">21</td><td class="ctr1" id="j2">1</td><td class="ctr2" id="k2">1</td></tr><tr><td id="a6"><a href="ART_B_ND.java.html#L178" class="el_method">testFm()</a></td><td class="bar" id="b3"><img src="../.resources/redbar.gif" width="48" height="10" title="87" alt="87"/></td><td class="ctr2" id="c3">0%</td><td class="bar" id="d4"><img src="../.resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e3">0%</td><td class="ctr1" id="f4">2</td><td class="ctr2" id="g4">2</td><td class="ctr1" id="h3">18</td><td class="ctr2" id="i3">18</td><td class="ctr1" id="j3">1</td><td class="ctr2" id="k3">1</td></tr><tr><td id="a7"><a href="ART_B_ND.java.html#L229" class="el_method">testTCTime(int, int)</a></td><td class="bar" id="b4"><img src="../.resources/redbar.gif" width="38" height="10" title="68" alt="68"/></td><td class="ctr2" id="c4">0%</td><td class="bar" id="d5"><img src="../.resources/redbar.gif" width="15" height="10" title="2" alt="2"/></td><td class="ctr2" id="e4">0%</td><td class="ctr1" id="f5">2</td><td class="ctr2" id="g5">2</td><td class="ctr1" id="h4">14</td><td class="ctr2" id="i4">14</td><td class="ctr1" id="j4">1</td><td class="ctr2" id="k4">1</td></tr><tr><td id="a3"><a href="ART_B_ND.java.html#L40" class="el_method">hasPointInRegion(NRectRegion)</a></td><td class="bar" id="b5"><img src="../.resources/redbar.gif" width="34" height="10" title="61" alt="61"/></td><td class="ctr2" id="c5">0%</td><td class="bar" id="d2"><img src="../.resources/redbar.gif" width="75" height="10" title="10" alt="10"/></td><td class="ctr2" id="e5">0%</td><td class="ctr1" id="f2">6</td><td class="ctr2" id="g2">6</td><td class="ctr1" id="h5">13</td><td class="ctr2" id="i5">13</td><td class="ctr1" id="j5">1</td><td class="ctr2" id="k5">1</td></tr><tr><td id="a0"><a href="ART_B_ND.java.html#L24" class="el_method">ART_B_ND(double[], double[], Random, FailurePattern)</a></td><td class="bar" id="b6"><img src="../.resources/redbar.gif" width="20" height="10" title="37" alt="37"/></td><td class="ctr2" id="c6">0%</td><td class="bar" id="d6"/><td class="ctr2" id="e6">n/a</td><td class="ctr1" id="f6">1</td><td class="ctr2" id="g6">1</td><td class="ctr1" id="h6">6</td><td class="ctr2" id="i6">6</td><td class="ctr1" id="j6">1</td><td class="ctr2" id="k6">1</td></tr><tr><td id="a1"><a href="ART_B_ND.java.html#L35" class="el_method">findRandomRegionAndDelete(ArrayList)</a></td><td class="bar" id="b7"><img src="../.resources/redbar.gif" width="6" height="10" title="11" alt="11"/></td><td class="ctr2" id="c7">0%</td><td class="bar" id="d7"/><td class="ctr2" id="e7">n/a</td><td class="ctr1" id="f7">1</td><td class="ctr2" id="g7">1</td><td class="ctr1" id="h7">2</td><td class="ctr2" id="i7">2</td><td class="ctr1" id="j7">1</td><td class="ctr2" id="k7">1</td></tr><tr><td id="a4"><a href="ART_B_ND.java.html#L19" class="el_method">main(String[])</a></td><td class="bar" id="b8"><img src="../.resources/redbar.gif" width="2" height="10" title="5" alt="5"/></td><td class="ctr2" id="c8">0%</td><td class="bar" id="d8"/><td class="ctr2" id="e8">n/a</td><td class="ctr1" id="f8">1</td><td class="ctr2" id="g8">1</td><td class="ctr1" id="h8">2</td><td class="ctr2" id="i8">2</td><td class="ctr1" id="j8">1</td><td class="ctr2" id="k8">1</td></tr></tbody></table><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>