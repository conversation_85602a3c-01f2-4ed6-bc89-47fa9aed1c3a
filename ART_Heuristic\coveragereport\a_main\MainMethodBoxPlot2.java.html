<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>MainMethodBoxPlot2.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">a_main</a> &gt; <span class="el_source">MainMethodBoxPlot2.java</span></div><h1>MainMethodBoxPlot2.java</h1><pre class="source lang-java linenums">package a_main;

import java.io.BufferedWriter;
import java.io.File;
import java.io.FileNotFoundException;
import java.io.FileOutputStream;
import java.io.OutputStreamWriter;
import java.util.Random;

import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import test.simulations.art_b.ART_B_ND;
import test.simulations.art_rp.ART_RP_ND;
import test.simulations.art_tp._ND.ART_TP_ND;
import test.simulations.art_tpp.ART_TPP;
import test.simulations.fscs.FSCS_ND;
import test.simulations.rrt.RRT_ND;
import test.simulations.rrttp.hilbert.RRTtpND_H;
import test.simulations.rt.RT_ND;
import util.data.ZeroOneCreator;
import util.file.FileUtils;

<span class="nc" id="L23">public class MainMethodBoxPlot2 {</span>
	// TODO 完成参数化
	public static void main(String[] args) throws Exception {
<span class="nc" id="L26">		int times = 100;</span>
<span class="nc" id="L27">		double failrates[] = { 0.0015 };</span>
<span class="nc" id="L28">		int d = 4;</span>
<span class="nc" id="L29">		String path=&quot;C:\\Users\\<USER>\\Desktop\\研究方向和内容\\小论文\\补充数据\\boxplot\\&quot;+d+&quot;维&quot;+failrates[0];</span>
		//File tempFile=new File(&quot;C:\\Users\\<USER>\\Desktop\\研究方向和内容\\小论文\\补充数据\\boxplot&quot;);
<span class="nc bnc" id="L31" title="All 2 branches missed.">		for (int j = 0; j &lt; failrates.length; j++) {</span>
<span class="nc" id="L32">			double failrate = failrates[j];</span>
			//int d = 2;
			
			
<span class="nc" id="L36">			ZeroOneCreator dataCreator = new ZeroOneCreator();</span>
<span class="nc" id="L37">			double[] min = dataCreator.minCreator(d);</span>
<span class="nc" id="L38">			double[] max = dataCreator.maxCreator(d);</span>
<span class="nc" id="L39">			FailurePattern failurePattern = new BlockPattern();</span>
<span class="nc" id="L40">			failurePattern.fail_rate = failrate;</span>
<span class="nc" id="L41">			failurePattern.min = min;</span>
<span class="nc" id="L42">			failurePattern.max = max;</span>
<span class="nc" id="L43">			failurePattern.dimension = d;</span>

			//rt
<span class="nc" id="L46">			File rtf=FileUtils.createNewFile(path, &quot;all.txt&quot;);</span>
<span class="nc" id="L47">			BufferedWriter writer=get(rtf);</span>
<span class="nc" id="L48">			int fm = 0;</span>
<span class="nc" id="L49">			long startTime = System.currentTimeMillis();</span>
<span class="nc" id="L50">			writer.write(&quot;rt=[&quot;);</span>
<span class="nc bnc" id="L51" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L52">				RT_ND rt = new RT_ND(min, max, new Random(i * 3), failurePattern);</span>
<span class="nc" id="L53">				int temp = rt.run();</span>
<span class="nc" id="L54">				fm += temp;</span>
<span class="nc" id="L55">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L56">				writer.newLine();</span>
<span class="nc" id="L57">				writer.flush();</span>
			}
<span class="nc" id="L59">			long endTime = System.currentTimeMillis();</span>
<span class="nc" id="L60">			writer.write(&quot;];&quot;);</span>
<span class="nc" id="L61">			writer.newLine();</span>
			//writer.close();
<span class="nc" id="L63">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
		
			//rrt
			//File rrtf=FileUtils.createNewFile(path, &quot;rrt.txt&quot;);
			// writer=get(rrtf);
<span class="nc" id="L68">			double r=0.75;</span>
<span class="nc" id="L69">			fm=0;</span>
<span class="nc" id="L70">			startTime = System.currentTimeMillis();</span>
<span class="nc" id="L71">			writer.write(&quot;rrt=[&quot;);</span>
<span class="nc bnc" id="L72" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L73">				RRT_ND rt = new RRT_ND(min, max,  failurePattern,new Random(i * 3),r);</span>
<span class="nc" id="L74">				int temp = rt.run();</span>
<span class="nc" id="L75">				fm += temp;</span>
<span class="nc" id="L76">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L77">				writer.newLine();</span>
<span class="nc" id="L78">				writer.flush();</span>
			}
<span class="nc" id="L80">			writer.write(&quot;];&quot;);</span>
<span class="nc" id="L81">			writer.newLine();</span>
			//writer.close();
<span class="nc" id="L83">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L84">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//fscs
			//File fscs=FileUtils.createNewFile(path, &quot;fscs.txt&quot;);
			 //w//riter=get(fscs);
<span class="nc" id="L89">			fm=0;</span>
<span class="nc" id="L90">			int s=10;</span>
<span class="nc" id="L91">			startTime = System.currentTimeMillis();</span>
<span class="nc" id="L92">			writer.write(&quot;fscs=[&quot;);</span>
<span class="nc bnc" id="L93" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L94">				FSCS_ND rt = new FSCS_ND(min, max, s, failurePattern,new Random(i * 3));</span>
<span class="nc" id="L95">				int temp = rt.run();</span>
<span class="nc" id="L96">				fm += temp;</span>
<span class="nc" id="L97">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L98">				writer.newLine();</span>
<span class="nc" id="L99">				writer.flush();</span>
			}
<span class="nc" id="L101">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L102">			writer.write(&quot;];&quot;);</span>
<span class="nc" id="L103">			writer.newLine();</span>
			//writer.close();
<span class="nc" id="L105">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			
			//art_b
			//File artbf=FileUtils.createNewFile(path, &quot;artb.txt&quot;);
			// writer=get(artbf);
<span class="nc" id="L111">			fm=0;</span>
<span class="nc" id="L112">			startTime = System.currentTimeMillis();</span>
<span class="nc" id="L113">			writer.write(&quot;artb=[&quot;);</span>
<span class="nc bnc" id="L114" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L115">				ART_B_ND rt = new ART_B_ND(min, max, new Random(i * 3),failurePattern);</span>
<span class="nc" id="L116">				int temp = rt.run();</span>
<span class="nc" id="L117">				fm += temp;</span>
<span class="nc" id="L118">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L119">				writer.newLine();</span>
<span class="nc" id="L120">				writer.newLine();</span>
			}
<span class="nc" id="L122">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L123">			writer.write(&quot;];&quot;);</span>
<span class="nc" id="L124">			writer.newLine();</span>
			//writer.close();
<span class="nc" id="L126">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//art_rp
			//File artrpf=FileUtils.createNewFile(path, &quot;artrp.txt&quot;);
			 //writer=get(artrpf);
<span class="nc" id="L131">			fm=0;</span>
<span class="nc" id="L132">			writer.write(&quot;artrp=[&quot;);</span>
<span class="nc" id="L133">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L134" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L135">				ART_RP_ND rt = new ART_RP_ND(min, max, new Random(i * 3),failurePattern);</span>
<span class="nc" id="L136">				int temp = rt.run();</span>
<span class="nc" id="L137">				fm += temp;</span>
<span class="nc" id="L138">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L139">				writer.newLine();</span>
<span class="nc" id="L140">				writer.flush();</span>
			}
<span class="nc" id="L142">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L143">			writer.write(&quot;];&quot;);</span>
<span class="nc" id="L144">			writer.newLine();</span>
<span class="nc" id="L145">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//art_tp
			//File arttpf=FileUtils.createNewFile(path, &quot;arttp.txt&quot;);
			 //writer=get(arttpf);
<span class="nc" id="L150">			fm=0;</span>
<span class="nc" id="L151">			startTime = System.currentTimeMillis();</span>
<span class="nc" id="L152">			writer.write(&quot;arttp=[&quot;);</span>
<span class="nc bnc" id="L153" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L154">				ART_TP_ND rt = new ART_TP_ND(min, max, failurePattern,new Random(i * 3));</span>
<span class="nc" id="L155">				int temp = rt.run();</span>
<span class="nc" id="L156">				fm += temp;</span>
<span class="nc" id="L157">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L158">				writer.newLine();</span>
<span class="nc" id="L159">				writer.flush();</span>
			}
<span class="nc" id="L161">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L162">			writer.write(&quot;];&quot;);</span>
<span class="nc" id="L163">			writer.newLine();</span>
			//writer.close();
<span class="nc" id="L165">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//art_tpp
			//File arttppf=FileUtils.createNewFile(path, &quot;arttpp.txt&quot;);
			 //writer=get(arttppf);
<span class="nc" id="L170">			fm=0;</span>
<span class="nc" id="L171">			int k=10;</span>
<span class="nc" id="L172">			startTime = System.currentTimeMillis();</span>
<span class="nc" id="L173">			writer.write(&quot;arttpp=[&quot;);</span>
<span class="nc bnc" id="L174" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
<span class="nc" id="L175">				ART_TPP rt = new ART_TPP(min, max, new Random(i * 3),failurePattern,k);</span>
<span class="nc" id="L176">				int temp = rt.run();</span>
<span class="nc" id="L177">				fm += temp;</span>
<span class="nc" id="L178">				writer.write(temp+&quot;&quot;);</span>
<span class="nc" id="L179">				writer.newLine();</span>
<span class="nc" id="L180">				writer.flush();</span>
			}
<span class="nc" id="L182">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L183">			writer.write(&quot;];&quot;);</span>
<span class="nc" id="L184">			writer.newLine();</span>
			//writer.close();
<span class="nc" id="L186">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
			//my method
			//1dimension
			//File lazf=FileUtils.createNewFile(path, &quot;laz.txt&quot;);
			// writer=get(lazf);
<span class="nc" id="L192">			r=0.75;</span>
<span class="nc" id="L193">			fm=0;</span>
<span class="nc" id="L194">			writer.write(&quot;laz=[&quot;);</span>
<span class="nc" id="L195">			startTime = System.currentTimeMillis();</span>
<span class="nc bnc" id="L196" title="All 2 branches missed.">			for (int i = 0; i &lt; times; i++) {</span>
//				/min, max, 0.75, pattern, new Random(i * 3+v*5)
<span class="nc" id="L198">				int temp2=0;</span>
<span class="nc" id="L199">				int timessss=1;</span>
<span class="nc bnc" id="L200" title="All 2 branches missed.">				for(int l=0;l&lt;timessss;l++){</span>
<span class="nc" id="L201">				RRTtpND_H rt = new RRTtpND_H(min, max, r, failurePattern, new Random(i * 3));</span>
<span class="nc" id="L202">				int temp = rt.run();</span>
<span class="nc" id="L203">				temp2+=temp;</span>
				}
<span class="nc" id="L205">				fm += temp2/(double)timessss;</span>
<span class="nc" id="L206">				writer.write((temp2/(double)timessss)+&quot;&quot;);</span>
<span class="nc" id="L207">				writer.newLine();</span>
<span class="nc" id="L208">				writer.flush();</span>
			}
<span class="nc" id="L210">			endTime = System.currentTimeMillis();</span>
<span class="nc" id="L211">			writer.write(&quot;];&quot;);</span>
<span class="nc" id="L212">			writer.newLine();</span>
<span class="nc" id="L213">			writer.close();</span>
<span class="nc" id="L214">			System.out.println(&quot;Fm:&quot; + (fm / (double) times) + &quot; times:&quot; + ((endTime - startTime) / (double) times));</span>
			
		}
<span class="nc" id="L217">	}</span>
	public static BufferedWriter get(File f) throws Exception{
<span class="nc" id="L219">		return new BufferedWriter(new OutputStreamWriter(new FileOutputStream(f),&quot;UTF-8&quot;));</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>