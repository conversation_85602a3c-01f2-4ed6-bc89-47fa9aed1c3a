<?xml version="1.0" encoding="UTF-8"?><!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd"><html xmlns="http://www.w3.org/1999/xhtml" lang="zh"><head><meta http-equiv="Content-Type" content="text/html;charset=UTF-8"/><link rel="stylesheet" href="../.resources/report.css" type="text/css"/><link rel="shortcut icon" href="../.resources/report.gif" type="image/gif"/><title>RRTtpAll2D.java</title><link rel="stylesheet" href="../.resources/prettify.css" type="text/css"/><script type="text/javascript" src="../.resources/prettify.js"></script></head><body onload="window['PR_TAB_WIDTH']=4;prettyPrint()"><div class="breadcrumb" id="breadcrumb"><span class="info"><a href="../.sessions.html" class="el_session">Sessions</a></span><a href="../index.html" class="el_report">ART</a> &gt; <a href="index.source.html" class="el_package">test.simulations.rrttp.partion</a> &gt; <span class="el_source">RRTtpAll2D.java</span></div><h1>RRTtpAll2D.java</h1><pre class="source lang-java linenums">package test.simulations.rrttp.partion;

import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import datastructure.ND.NPoint;
import datastructure.ND.NRectRegion;
import datastructure.failurepattern.FailurePattern;
import datastructure.failurepattern.impl.BlockPattern;
import datastructure.failurepattern.impl.RealityFailPattern;
import test.ART;
import tested.bessj;
import tested.gammq;
import util.data.ZeroOneCreator;

/**
 * generate a point from all the available zone;
 * <AUTHOR>
 * @date   2017/12/07 
 */

public class RRTtpAll2D extends ART {

	double R;
	double radius;
<span class="nc" id="L27">	List&lt;RRTtpRegion&gt; regions=new ArrayList&lt;&gt;();</span>
	
	public RRTtpAll2D(double[] min, double[] max, Random random, FailurePattern failurePattern,double r) {
<span class="nc" id="L30">		super(min, max, random, failurePattern);</span>
<span class="nc" id="L31">		this.R=r;</span>
<span class="nc" id="L32">	}</span>

	@Override
	public int run() {
<span class="nc" id="L36">		int count=0;</span>
<span class="nc" id="L37">		NPoint p=randomCreator.randomPoint();</span>
		//System.out.println(&quot;p0:&quot;+p.toString());
<span class="nc" id="L39">		regions.add(new RRTtpRegion(new NRectRegion(new NPoint(min), new NPoint(max))));</span>
<span class="nc" id="L40">		int tempRegionIndex=0;</span>
		
<span class="nc bnc" id="L42" title="All 2 branches missed.">		while(this.failPattern.isCorrect(p)){</span>
<span class="nc" id="L43">			count++;</span>
			
<span class="nc" id="L45">			radius=calculateRadius(count);</span>
//			System.out.println(&quot;radius:&quot;+radius);
//			System.out.println(&quot;split region:&quot;+tempRegionIndex);
<span class="nc" id="L48">			splitRegions(tempRegionIndex,p);</span>
<span class="nc" id="L49">			ArrayList result=genNextFromAvailableRegion();</span>
<span class="nc" id="L50">			tempRegionIndex=(int)result.get(0);</span>
<span class="nc" id="L51">			p=(NPoint)result.get(1);</span>
		}
<span class="nc" id="L53">		return count;</span>
	}
	private void splitRegions(int regionIndex,NPoint p){
<span class="nc" id="L56">		addRegionsIn2D(regionIndex, p);</span>
<span class="nc" id="L57">	}</span>
	private void addRegionsIn2D(int regionIndex, NPoint p) {
		//first delete it
<span class="nc" id="L60">		RRTtpRegion region=this.regions.remove(regionIndex);</span>
		
<span class="nc" id="L62">		double xmin = region.region.getStart().getXn()[0];</span>
<span class="nc" id="L63">		double ymin = region.region.getStart().getXn()[1];</span>
<span class="nc" id="L64">		double xmax = region.region.getEnd().getXn()[0];</span>
<span class="nc" id="L65">		double ymax = region.region.getEnd().getXn()[1];</span>
<span class="nc" id="L66">		double pp = p.getXn()[0];</span>
<span class="nc" id="L67">		double qq = p.getXn()[1];</span>

<span class="nc" id="L69">		NRectRegion first = new NRectRegion(new NPoint(new double[] { xmin, ymin }),</span>
<span class="nc" id="L70">				new NPoint(new double[] { pp, qq }));</span>
		
<span class="nc" id="L72">		RRTtpRegion one=new RRTtpRegion(first);</span>
<span class="nc" id="L73">		one.p0=region.p0;</span>
<span class="nc" id="L74">		one.p2=p;</span>
		
		
<span class="nc" id="L77">		NRectRegion second = new NRectRegion(new NPoint(new double[] { pp, ymin }),</span>
<span class="nc" id="L78">				new NPoint(new double[] { xmax, qq }));</span>
		
<span class="nc" id="L80">		RRTtpRegion two=new RRTtpRegion(second);</span>
<span class="nc" id="L81">		two.p1=region.p1;</span>
<span class="nc" id="L82">		two.p3=p;</span>
		
<span class="nc" id="L84">		NRectRegion third = new NRectRegion(new NPoint(new double[] { pp, qq }),</span>
<span class="nc" id="L85">				new NPoint(new double[] { xmax, ymax }));</span>
<span class="nc" id="L86">		RRTtpRegion three=new RRTtpRegion(third);</span>
<span class="nc" id="L87">		three.p0=p;</span>
<span class="nc" id="L88">		three.p2=region.p2;</span>
		
		
<span class="nc" id="L91">		NRectRegion fourth = new NRectRegion(new NPoint(new double[] { xmin, qq }),</span>
<span class="nc" id="L92">				new NPoint(new double[] { pp, ymax }));</span>

<span class="nc" id="L94">		RRTtpRegion four=new RRTtpRegion(fourth);</span>
<span class="nc" id="L95">		four.p1=p;</span>
<span class="nc" id="L96">		four.p3=region.p3;</span>
		
		
//		System.out.println(&quot;four region:&quot;);
//		System.out.println(one);
//		System.out.println(two);
//		System.out.println(three);
//		System.out.println(four);
		
<span class="nc" id="L105">		this.regions.add(one);</span>
<span class="nc" id="L106">		this.regions.add(two);</span>
<span class="nc" id="L107">		this.regions.add(three);</span>
<span class="nc" id="L108">		this.regions.add(four);</span>
<span class="nc" id="L109">	}</span>
	
	private ArrayList genNextFromAvailableRegion(){
<span class="nc" id="L112">		ArrayList result=new ArrayList&lt;&gt;();</span>
<span class="nc" id="L113">		double allSize=0;</span>
<span class="nc bnc" id="L114" title="All 2 branches missed.">		for(int i=0;i&lt;regions.size();i++){</span>
			//System.out.println(&quot;region &quot;+i+&quot; size:&quot;+regions.get(i).calAvailSize(radius));
<span class="nc" id="L116">			allSize+=regions.get(i).calAvailSize(radius);</span>
		}
<span class="nc" id="L118">		double T=random.nextDouble()*allSize;</span>
<span class="nc" id="L119">		double SumIntegral = 0.0;// 积分值总和</span>
<span class="nc" id="L120">		double PreIntegral = 0.0;</span>
<span class="nc" id="L121">		int temp = 0;</span>
<span class="nc bnc" id="L122" title="All 2 branches missed.">		for (int i = 0; i &lt; regions.size(); i++) {</span>
<span class="nc" id="L123">			double availSize=regions.get(i).calAvailSize(radius);</span>
<span class="nc bnc" id="L124" title="All 2 branches missed.">			if(availSize!=0.0){</span>
<span class="nc bnc" id="L125" title="All 2 branches missed.">			if (SumIntegral &lt; T) {</span>
<span class="nc" id="L126">				PreIntegral = SumIntegral;</span>
<span class="nc" id="L127">				temp = i;</span>
			}
<span class="nc" id="L129">			SumIntegral += regions.get(i).calAvailSize(radius);</span>
			}else{
				continue;
			}
		}
		
		//System.out.println(&quot;gen next test case from :&quot;+temp);
		//temp处生成测试用例
<span class="nc" id="L137">		RRTtpRegion tempRegion=this.regions.get(temp);</span>
<span class="nc" id="L138">		ArrayList&lt;NRectRegion&gt; regions=tempRegion.availRegions(radius);</span>
<span class="nc" id="L139">		NPoint p=randomCreator.randomPoint(regions);</span>
		//System.out.println(&quot;p:&quot;+p);
		//0 序号 1是p
<span class="nc" id="L142">		result.add(temp);</span>
<span class="nc" id="L143">		result.add(p);</span>
<span class="nc" id="L144">		return result;</span>
	}
	public boolean inAvailRegion(RRTtpRegion region,NPoint p){
<span class="nc" id="L147">		boolean result=false;</span>
		
<span class="nc" id="L149">		return result;</span>
	}
	public double calculateRadius(int count) {
<span class="nc bnc" id="L152" title="All 2 branches missed.">		if (this.dimension % 2 == 0) {</span>
<span class="nc" id="L153">			int k = this.dimension / 2;</span>
<span class="nc" id="L154">			double kjie = 1;</span>
<span class="nc bnc" id="L155" title="All 2 branches missed.">			for (int i = k; i &gt; 0; i--) {</span>
<span class="nc" id="L156">				kjie *= i;</span>
			}
<span class="nc" id="L158">			double temp = (this.R * totalArea * kjie) / (count * Math.pow(Math.PI, k));</span>

<span class="nc" id="L160">			return Math.pow(temp, 1 / (double) this.dimension);</span>
		} else {
<span class="nc" id="L162">			int k = this.dimension / 2;</span>
<span class="nc" id="L163">			double kjie = 1;</span>
<span class="nc" id="L164">			double k2jie = 1;</span>
<span class="nc bnc" id="L165" title="All 2 branches missed.">			for (int i = k; i &gt; 0; i--) {</span>
<span class="nc" id="L166">				kjie *= i;</span>
			}
<span class="nc bnc" id="L168" title="All 2 branches missed.">			for (int i = (2 * k + 1); i &gt; 0; i--) {</span>
<span class="nc" id="L169">				k2jie *= i;</span>
			}
<span class="nc" id="L171">			double temp = (this.R * totalArea * k2jie) / (kjie * Math.pow(2, 2 * k + 1) * Math.pow(Math.PI, k) * count);</span>
			// System.out.println(&quot;return R&quot;);
<span class="nc" id="L173">			return Math.pow(temp, 1 / (double) this.dimension);</span>
		}
	}

	public static void main(String[] args) {
		/*int d=2;
		int times=2000;
		
		double fail_rate=0.01;
		
		ZeroOneCreator dataCreator=new ZeroOneCreator();
		double[] min=dataCreator.minCreator(d);
		double[] max=dataCreator.maxCreator(d);
		
		int fm=0;
		for(int i=0;i&lt;times;i++){
			FailurePattern pattern=new BlockPattern();
			pattern.fail_rate=fail_rate;
			RRTtpMZ2D tp=new RRTtpMZ2D(min, max, new Random(i*3), pattern,0.8);
			int temp=tp.run();
			fm+=temp;
		}
		System.out.println(fm/(double)times);*/
		
<span class="nc" id="L197">		testReality();</span>
<span class="nc" id="L198">	}</span>
	
	public static void testReality(){
		//int d=2;
<span class="nc" id="L202">		int times=2000;</span>
		
		//double fail_rate=0.01;
<span class="nc" id="L205">		bessj bessj=new bessj();</span>
		
		//ZeroOneCreator dataCreator=new ZeroOneCreator();
<span class="nc" id="L208">		double[] min=bessj.min;</span>
<span class="nc" id="L209">		double[] max=bessj.max;</span>
		
<span class="nc" id="L211">		int fm=0;</span>
<span class="nc" id="L212">		long starttime=System.currentTimeMillis();</span>
<span class="nc bnc" id="L213" title="All 2 branches missed.">		for(int i=0;i&lt;times;i++){</span>
<span class="nc" id="L214">			FailurePattern pattern=new RealityFailPattern(bessj.getClass().getSimpleName());</span>
<span class="nc" id="L215">			pattern.fail_rate=bessj.failureRate;</span>
<span class="nc" id="L216">			RRTtpAll2D tp=new RRTtpAll2D(min, max, new Random(i*3), pattern,0.8);</span>
<span class="nc" id="L217">			int temp=tp.run();</span>
<span class="nc" id="L218">			fm+=temp;</span>
		}
<span class="nc" id="L220">		long endtime=System.currentTimeMillis();</span>
<span class="nc" id="L221">		System.out.println((fm/(double)times)+&quot; time:&quot;+((endtime-starttime)/(double)times));</span>
<span class="nc" id="L222">	}</span>

	@Override
	public int em() {
		// TODO Auto-generated method stub
<span class="nc" id="L227">		return 0;</span>
	}

	@Override
	public NPoint generateNextTC() {
		// TODO Auto-generated method stub
<span class="nc" id="L233">		return null;</span>
	}
}
</pre><div class="footer"><span class="right">Created with <a href="http://www.eclemma.org/jacoco">JaCoCo</a> 0.7.6.201602180812</span></div></body></html>